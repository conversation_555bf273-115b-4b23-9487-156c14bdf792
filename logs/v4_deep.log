2025-08-19 16:40:02,898 - INFO - ==================================================
2025-08-19 16:40:02,899 - INFO - Stage 4 V4: Deep Optimization Training
2025-08-19 16:40:02,899 - INFO - ==================================================
2025-08-19 16:40:02,899 - INFO - 配置: {
  "d_model": 256,
  "n_heads": 16,
  "n_mamba_layers": 3,
  "n_transformer_layers": 3,
  "n_refinement_layers": 2,
  "ff_dim": 1024,
  "dropout": 0.15,
  "label_smoothing": 0.1,
  "batch_size": 16,
  "learning_rate": 0.0001,
  "num_epochs": 20,
  "early_stopping_patience": 5,
  "gradient_clip": 1.0,
  "weight_decay": 0.01,
  "seq_len": 5,
  "n_channels": 4,
  "n_classes": 5,
  "coarse_classes": 3,
  "progressive_epochs": [
    5,
    10,
    15
  ],
  "progressive_weights": [
    0.5,
    0.3,
    0.1
  ],
  "use_augmentation": true,
  "augmentation_prob": 0.4,
  "mixup_alpha": 0.3,
  "use_tta": true,
  "tta_augmentations": 5,
  "device": "cuda",
  "num_workers": 4,
  "data_dir": "/media/main/ypf/eeg/Cross-Modal-Transformer/prepared_data",
  "checkpoint_dir": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v4_deep_20250819_164002/checkpoints"
}
2025-08-19 16:40:02,899 - INFO - 训练文件数: 0
2025-08-19 16:40:02,899 - INFO - 验证文件数: 0
2025-08-19 16:40:02,899 - INFO - 测试文件数: 0
2025-08-19 16:40:02,899 - INFO - 加载数据集...
2025-08-19 16:40:02,899 - INFO - 从 0 个文件加载了 0 个epochs, 创建了 0 个序列
2025-08-19 16:40:02,899 - INFO - 创建序列数据集: 0个序列, 序列长度=5, 通道数=0, 总epochs=0
2025-08-19 16:40:02,899 - INFO - 从 0 个文件加载了 0 个epochs, 创建了 0 个序列
2025-08-19 16:40:02,899 - INFO - 创建序列数据集: 0个序列, 序列长度=5, 通道数=0, 总epochs=0
2025-08-19 16:40:02,900 - INFO - 从 0 个文件加载了 0 个epochs, 创建了 0 个序列
2025-08-19 16:40:02,900 - INFO - 创建序列数据集: 0个序列, 序列长度=5, 通道数=0, 总epochs=0
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_v4_deep_optimize.py", line 943, in <module>
    accuracy = main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_v4_deep_optimize.py", line 741, in main
    train_loader = DataLoader(
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/dataloader.py", line 385, in __init__
    sampler = RandomSampler(dataset, generator=generator)  # type: ignore[arg-type]
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/sampler.py", line 156, in __init__
    raise ValueError(
ValueError: num_samples should be a positive integer value, but got num_samples=0
