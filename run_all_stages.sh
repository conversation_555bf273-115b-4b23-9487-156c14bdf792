#!/bin/bash

# 自动运行所有5个阶段的实验
# 每个阶段完成后自动进入下一阶段

echo "=========================================="
echo "开始渐进式MAMBAFORMER全阶段实验"
echo "目标：ICASSP 2026论文实验"
echo "=========================================="

# 配置
LOG_DIR="mambaformer/logs"
PYTHON="python"

# 等待第1阶段完成
echo "等待第1阶段完成..."
while pgrep -f "stage1_full_training" > /dev/null; do
    sleep 30
done
echo "✅ 第1阶段完成"

# 运行第2阶段
echo "开始第2阶段：EEG中心的跨模态注意力..."
nohup $PYTHON mambaformer/training/stage2_cross_attention.py > $LOG_DIR/stage2.log 2>&1 &
STAGE2_PID=$!

# 等待第2阶段完成
wait $STAGE2_PID
echo "✅ 第2阶段完成"

# 运行第3阶段
echo "开始第3阶段：自适应门控融合..."
nohup $PYTHON mambaformer/training/stage3_adaptive_gating.py > $LOG_DIR/stage3.log 2>&1 &
STAGE3_PID=$!

# 等待第3阶段完成
wait $STAGE3_PID
echo "✅ 第3阶段完成"

# 运行第4阶段（需要先安装mamba-ssm）
echo "检查并安装Mamba依赖..."
pip list | grep mamba-ssm || pip install mamba-ssm

echo "开始第4阶段：Mamba长程依赖建模..."
nohup $PYTHON mambaformer/training/stage4_mamba_modeling.py > $LOG_DIR/stage4.log 2>&1 &
STAGE4_PID=$!

# 等待第4阶段完成
wait $STAGE4_PID
echo "✅ 第4阶段完成"

# 运行第5阶段
echo "开始第5阶段：渐进式分类策略..."
nohup $PYTHON mambaformer/training/stage5_progressive_classification.py > $LOG_DIR/stage5.log 2>&1 &
STAGE5_PID=$!

# 等待第5阶段完成
wait $STAGE5_PID
echo "✅ 第5阶段完成"

echo "=========================================="
echo "所有阶段实验完成！"
echo "请查看 $LOG_DIR 目录获取详细结果"
echo "=========================================="

# 生成最终报告
$PYTHON generate_final_report.py