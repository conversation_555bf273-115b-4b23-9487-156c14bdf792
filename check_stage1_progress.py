#!/usr/bin/env python3
"""
检查第1阶段训练进度的脚本
"""

import os
import glob
import json
from datetime import datetime

def check_latest_log():
    """查找并分析最新的训练日志"""
    log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/"
    
    # 查找所有stage1开头的文件夹
    stage1_dirs = glob.glob(os.path.join(log_dir, "stage1_*"))
    
    if not stage1_dirs:
        print("未找到第1阶段训练日志")
        return
    
    # 获取最新的文件夹
    latest_dir = max(stage1_dirs, key=os.path.getctime)
    print(f"最新训练目录: {latest_dir}")
    
    # 检查training.log
    log_file = os.path.join(latest_dir, "training.log")
    if os.path.exists(log_file):
        # 读取最后50行
        with open(log_file, 'r') as f:
            lines = f.readlines()
            last_lines = lines[-50:] if len(lines) > 50 else lines
            
        print("\n=== 最新训练日志 ===")
        for line in last_lines:
            if any(keyword in line for keyword in ['Epoch', 'Loss', 'Acc', '验证', '测试', '准确率', 'F1']):
                print(line.strip())
    
    # 检查是否有结果文件
    result_file = os.path.join(latest_dir, "result_summary.json")
    if os.path.exists(result_file):
        with open(result_file, 'r') as f:
            results = json.load(f)
        
        print("\n=== 训练结果 ===")
        print(f"测试准确率: {results.get('test_accuracy', 'N/A'):.2f}%")
        print(f"N1召回率: {results.get('n1_recall', 'N/A'):.3f}")
        print(f"N1 F1分数: {results.get('n1_f1', 'N/A'):.3f}")
        print(f"最佳验证准确率: {results.get('best_val_acc', 'N/A'):.2f}%")
    
    # 检查最佳模型
    best_model = os.path.join(latest_dir, "best_model.pth")
    if os.path.exists(best_model):
        file_size = os.path.getsize(best_model) / (1024 * 1024)  # MB
        mod_time = datetime.fromtimestamp(os.path.getmtime(best_model))
        print(f"\n最佳模型已保存: {file_size:.1f}MB, 更新时间: {mod_time}")

if __name__ == "__main__":
    check_latest_log()