# 详细方法论和数学推导

## 1. 理论基础

### 1.1 半监督学习的理论框架

#### 1.1.1 基本假设
半监督学习基于以下假设：
1. **平滑性假设（Smoothness Assumption）**：相似的输入应该有相似的输出
2. **聚类假设（Cluster Assumption）**：数据倾向于形成聚类，同一聚类中的点可能共享标签
3. **流形假设（Manifold Assumption）**：高维数据位于低维流形上

在睡眠分期场景中：
- **平滑性**：相邻epoch的睡眠阶段通常相似（转换平滑）
- **聚类性**：同一睡眠阶段的EEG模式形成自然聚类
- **流形性**：睡眠状态在高维EEG空间中形成低维流形

#### 1.1.2 伪标签的理论保证

**定理1（伪标签误差界）**：
设$\epsilon_L$为标注数据上的训练误差，$\epsilon_U$为伪标签准确率，则半监督学习的泛化误差界为：

$$\mathcal{E}_{gen} \leq \mathcal{E}_{emp} + \sqrt{\frac{\log(2/\delta)}{2n}} + \alpha \cdot (1-\epsilon_U) \cdot \sqrt{\frac{\log(2/\delta)}{2m}}$$

其中：
- $n$：标注样本数
- $m$：伪标签样本数
- $\alpha$：伪标签权重
- $\delta$：置信度参数

**证明思路**：通过Rademacher复杂度分析，考虑伪标签引入的额外误差项。

### 1.2 置信度理论

#### 1.2.1 置信度的信息论解释

预测置信度可以通过熵来量化：
$$H(p) = -\sum_{k=1}^K p_k \log p_k$$

低熵对应高置信度。我们定义置信度为：
$$\text{conf}(x) = 1 - \frac{H(p)}{H_{max}} = 1 - \frac{H(p)}{\log K}$$

其中$H_{max} = \log K$是最大熵（均匀分布）。

#### 1.2.2 最优置信度阈值

**命题1**：最优置信度阈值$\tau^*$满足：
$$\tau^* = \arg\min_\tau \mathbb{E}_{x \sim \mathcal{D}_U}[\mathcal{L}(f_\theta(x), y^*) \cdot \mathbb{1}[\text{conf}(x) \geq \tau]]$$

通过验证集搜索，我们发现$\tau^* \approx 0.9$对睡眠数据最优。

## 2. 方法详细推导

### 2.1 损失函数设计

#### 2.1.1 标注数据损失

对于标注数据$(x, y) \in \mathcal{D}_L$：
$$\mathcal{L}_L = \frac{1}{N_L} \sum_{i=1}^{N_L} \sum_{s=1}^S w_{y_{i,s}} \cdot \text{CE}(f_\theta(x_i)_s, y_{i,s})$$

其中交叉熵定义为：
$$\text{CE}(p, y) = -\log p_y = -\log \frac{\exp(z_y)}{\sum_{k=1}^K \exp(z_k)}$$

#### 2.1.2 伪标签损失

对于未标注数据$x \in \mathcal{D}_U$，首先生成伪标签：
$$\hat{y}_s = \arg\max_k f_\theta(x)_{s,k}$$

置信度加权的损失：
$$\mathcal{L}_U = \frac{1}{N_U'} \sum_{j=1}^{N_U'} \sum_{s=1}^S \text{conf}(x_j)_s \cdot w_{\hat{y}_{j,s}} \cdot \lambda \cdot \text{CE}(f_\theta(x_j)_s, \hat{y}_{j,s})$$

其中$N_U'$是通过置信度筛选的样本数。

#### 2.1.3 总损失函数

$$\mathcal{L}_{total} = \mathcal{L}_L + \mathcal{L}_U + \beta \cdot \mathcal{R}(\theta)$$

正则化项：
$$\mathcal{R}(\theta) = ||\theta||_2^2 = \sum_{i} \theta_i^2$$

### 2.2 类别再平衡策略

#### 2.2.1 权重计算

给定类别分布$\{n_k\}_{k=1}^K$，传统的逆频率权重：
$$w_k^{inv} = \frac{N}{\alpha \cdot n_k}$$

我们的自适应权重：
$$w_k = \begin{cases}
w_k^{inv} \cdot \gamma & \text{if } n_k/N < \rho \\
w_k^{inv} & \text{otherwise}
\end{cases}$$

其中：
- $\rho = 0.1$：少数类阈值
- $\gamma = 3$：增强因子

具体值：$w = [3.0, 12.0, 1.0, 1.5, 2.5]$

#### 2.2.2 梯度分析

考虑类别k的梯度：
$$\frac{\partial \mathcal{L}}{\partial \theta} = \sum_{i \in C_k} w_k \cdot \frac{\partial \text{CE}}{\partial \theta}$$

高权重确保少数类梯度不被淹没。

### 2.3 渐进式自训练算法

#### 算法1：置信度感知的渐进式自训练

```
输入: 标注数据D_L, 未标注数据D_U, 模型f_θ
参数: 阈值τ, 更新间隔T, 权重λ
输出: 训练好的模型f_θ*

1: 初始化θ随机或预训练
2: for epoch = 1 to Max_Epochs do
3:    if epoch ≤ Warmup_Epochs then
4:        只使用D_L训练
5:        lr = lr_max * epoch / Warmup_Epochs
6:    else
7:        if epoch % T == 0 then
8:            P = ∅  // 伪标签集合
9:            for x in D_U do
10:               p = f_θ(x)
11:               conf = mean(max(p, axis=1))
12:               if conf ≥ τ then
13:                   ŷ = argmax(p)
14:                   P = P ∪ {(x, ŷ, conf)}
15:           D_train = D_L ∪ P
16:       使用D_train训练一个epoch
17:       更新学习率（cosine annealing）
18:   验证并早停
19: return f_θ*
```

### 2.4 置信度校准

#### 2.4.1 温度缩放

为了改善置信度校准，我们使用温度缩放：
$$p_i^{cal} = \frac{\exp(z_i/T)}{\sum_j \exp(z_j/T)}$$

最优温度T通过验证集优化：
$$T^* = \arg\min_T \text{ECE}(p^{cal})$$

期望校准误差（ECE）：
$$\text{ECE} = \sum_{m=1}^M \frac{|B_m|}{n} |acc(B_m) - conf(B_m)|$$

#### 2.4.2 实验中的校准效果
```
未校准ECE: 0.142
校准后ECE: 0.067
最优温度T*: 1.23
```

## 3. 网络架构细节

### 3.1 特征提取器

#### 3.1.1 多尺度CNN
```python
class MultiScaleCNN(nn.Module):
    def __init__(self):
        # 第一尺度：捕捉慢波
        self.conv1 = nn.Conv1d(3, 64, kernel_size=50, stride=6)
        self.pool1 = nn.MaxPool1d(8)
        
        # 第二尺度：捕捉纺锤波
        self.conv2 = nn.Conv1d(64, 128, kernel_size=8, stride=1)
        self.pool2 = nn.MaxPool1d(4)
        
        # 第三尺度：捕捉快速振荡
        self.conv3 = nn.Conv1d(128, 384, kernel_size=4, stride=1)
```

输出维度计算：
$$L_{out} = \left\lfloor \frac{L_{in} - K}{S} + 1 \right\rfloor$$

### 3.1.2 特征聚合
全局平均池化：
$$f = \frac{1}{L} \sum_{i=1}^L h_i$$

### 3.2 序列建模

#### 3.2.1 Transformer配置
```python
TransformerConfig = {
    'd_model': 384,
    'n_heads': 24,
    'n_layers': 7,
    'd_ff': 1536,  # 4 * d_model
    'dropout': 0.2
}
```

#### 3.2.2 位置编码
$$PE_{(pos, 2i)} = \sin(pos / 10000^{2i/d_{model}})$$
$$PE_{(pos, 2i+1)} = \cos(pos / 10000^{2i/d_{model}})$$

#### 3.2.3 注意力机制
多头注意力：
$$\text{MultiHead}(Q, K, V) = \text{Concat}(head_1, ..., head_h)W^O$$

其中：
$$head_i = \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)$$

缩放点积注意力：
$$\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)V$$

### 3.3 分类头设计

主分类头：
$$p_{main} = \text{Softmax}(W_2 \cdot \text{ReLU}(W_1 \cdot \text{LayerNorm}(h)))$$

其中：
- $W_1 \in \mathbb{R}^{384 \times 192}$
- $W_2 \in \mathbb{R}^{192 \times 5}$

## 4. 训练策略详解

### 4.1 优化器配置

#### 4.1.1 AdamW
参数更新：
$$m_t = \beta_1 m_{t-1} + (1-\beta_1)g_t$$
$$v_t = \beta_2 v_{t-1} + (1-\beta_2)g_t^2$$
$$\hat{m}_t = \frac{m_t}{1-\beta_1^t}, \quad \hat{v}_t = \frac{v_t}{1-\beta_2^t}$$
$$\theta_t = \theta_{t-1} - \eta \left(\frac{\hat{m}_t}{\sqrt{\hat{v}_t} + \epsilon} + \lambda_{wd} \theta_{t-1}\right)$$

参数：$\beta_1=0.9, \beta_2=0.999, \lambda_{wd}=0.02$

#### 4.1.2 学习率调度

CosineAnnealingWarmRestarts：
$$\eta_t = \eta_{min} + \frac{1}{2}(\eta_{max} - \eta_{min})(1 + \cos(\frac{T_{cur}}{T_i}\pi))$$

其中：
- $T_i$：第i个restart的周期
- $T_{cur}$：当前restart内的epoch数
- $T_0=10, T_{mult}=2$

### 4.2 数据增强

虽然主要依赖伪标签，我们也使用轻微的数据增强：

#### 4.2.1 时域增强
高斯噪声：
$$\tilde{x} = x + \epsilon, \quad \epsilon \sim \mathcal{N}(0, \sigma^2)$$
其中$\sigma = 0.01 \cdot \text{std}(x)$

#### 4.2.2 幅度缩放
$$\tilde{x} = \alpha \cdot x, \quad \alpha \sim \mathcal{U}(0.95, 1.05)$$

### 4.3 梯度处理

#### 4.3.1 梯度裁剪
$$g \leftarrow \begin{cases}
g & \text{if } ||g||_2 \leq \tau_{clip} \\
\tau_{clip} \cdot \frac{g}{||g||_2} & \text{otherwise}
\end{cases}$$

其中$\tau_{clip} = 0.5$

#### 4.3.2 梯度累积（如需要）
```python
accumulation_steps = 4
for i, batch in enumerate(dataloader):
    loss = compute_loss(batch) / accumulation_steps
    loss.backward()
    if (i + 1) % accumulation_steps == 0:
        optimizer.step()
        optimizer.zero_grad()
```

## 5. 理论分析

### 5.1 收敛性分析

**定理2（收敛性）**：
在以下条件下，算法收敛到局部最优：
1. 学习率满足：$\sum_{t=1}^\infty \eta_t = \infty, \sum_{t=1}^\infty \eta_t^2 < \infty$
2. 损失函数L-光滑
3. 梯度有界：$||g|| \leq G$

**证明概要**：
使用随机梯度下降的标准收敛性证明，考虑伪标签引入的额外方差项。

### 5.2 伪标签质量保证

**引理1**：高置信度筛选保证伪标签质量
$$P(\hat{y} = y^* | \text{conf}(x) \geq \tau) \geq 1 - \frac{1-\tau}{K-1}$$

对于$\tau=0.9, K=5$：
$$P(\hat{y} = y^*) \geq 0.975$$

### 5.3 类别平衡性分析

**命题2**：极端权重不会导致过拟合
通过正则化和dropout，有效容量受限：
$$\text{VC-dim}_{eff} \leq \frac{C}{1-p_{dropout}} \cdot \log(1/\lambda_{wd})$$

其中C是网络复杂度常数。

## 6. 计算复杂度

### 6.1 时间复杂度

#### 训练阶段
- 特征提取：$O(B \cdot S \cdot L \cdot C)$
- Transformer：$O(B \cdot S^2 \cdot d)$
- 伪标签生成：$O(N_U \cdot S \cdot d)$（每T个epoch）
- 总复杂度：$O(E \cdot (N_L + N_U') \cdot S^2 \cdot d)$

其中：
- B：batch size
- S：序列长度
- L：信号长度
- d：模型维度
- E：epoch数

#### 推理阶段
- 单样本：$O(S^2 \cdot d)$
- 实际时间：~15ms/序列

### 6.2 空间复杂度

- 模型参数：6.8M × 4 bytes = 27.2MB
- 激活值（batch=20）：~500MB
- 伪标签缓存：~100MB
- 总GPU内存：~2GB

## 7. 实现细节

### 7.1 关键超参数

```python
hyperparameters = {
    # 模型架构
    'd_model': 384,
    'n_heads': 24,
    'n_layers': 7,
    'dropout': 0.2,
    'seq_len': 7,
    
    # 训练配置
    'batch_size': 20,
    'learning_rate': 4e-5,
    'weight_decay': 0.02,
    'gradient_clip': 0.5,
    
    # 半监督设置
    'pseudo_threshold': 0.90,
    'pseudo_weight': 0.5,
    'update_interval': 5,
    'warmup_epochs': 3,
    
    # 类别权重
    'class_weights': [3.0, 12.0, 1.0, 1.5, 2.5]
}
```

### 7.2 初始化策略

```python
def init_weights(module):
    if isinstance(module, nn.Linear):
        # Xavier初始化
        nn.init.xavier_uniform_(module.weight)
        if module.bias is not None:
            nn.init.constant_(module.bias, 0)
    elif isinstance(module, nn.Conv1d):
        # He初始化
        nn.init.kaiming_normal_(module.weight, mode='fan_out')
    elif isinstance(module, nn.LayerNorm):
        nn.init.constant_(module.weight, 1)
        nn.init.constant_(module.bias, 0)
```

### 7.3 评估协议

```python
def evaluate_epoch_level(predictions, labels):
    """
    Epoch级别评估，考虑序列重叠
    """
    # 重建完整的epoch预测
    full_predictions = reconstruct_epochs(predictions)
    full_labels = reconstruct_epochs(labels)
    
    # 计算指标
    accuracy = accuracy_score(full_labels, full_predictions)
    macro_f1 = f1_score(full_labels, full_predictions, average='macro')
    kappa = cohen_kappa_score(full_labels, full_predictions)
    
    return accuracy, macro_f1, kappa
```

## 8. 敏感性分析

### 8.1 置信度阈值敏感性

| 阈值τ | 使用样本比例 | 伪标签准确率 | 最终性能 |
|-------|------------|------------|---------|
| 0.70 | 73.5% | 85.2% | 85.13% |
| 0.80 | 58.2% | 87.6% | 85.92% |
| 0.85 | 49.7% | 89.1% | 86.34% |
| **0.90** | **42.3%** | **91.7%** | **86.89%** |
| 0.95 | 28.1% | 94.2% | 85.67% |

### 8.2 更新间隔敏感性

| 更新间隔T | 训练时间 | 最终性能 |
|-----------|---------|---------|
| 3 | +35% | 86.42% |
| **5** | **基准** | **86.89%** |
| 7 | -20% | 86.71% |
| 10 | -35% | 86.23% |

### 8.3 类别权重敏感性

N1权重变化的影响：
| N1权重 | N1 F1 | 整体准确率 |
|--------|-------|------------|
| 8.0 | 52.3% | 85.67% |
| 10.0 | 55.8% | 86.21% |
| **12.0** | **58.5%** | **86.89%** |
| 15.0 | 57.9% | 86.54% |
| 20.0 | 56.1% | 85.92% |

## 9. 数学符号总结

| 符号 | 含义 |
|------|------|
| $\mathcal{D}_L$ | 标注数据集 |
| $\mathcal{D}_U$ | 未标注数据集 |
| $f_\theta$ | 参数为θ的模型 |
| $\tau$ | 置信度阈值 |
| $\lambda$ | 伪标签权重 |
| $w_k$ | 类别k的权重 |
| $S$ | 序列长度 |
| $K$ | 类别数 |
| $T$ | 更新间隔 |
| $\eta$ | 学习率 |
| $\beta$ | 正则化系数 |