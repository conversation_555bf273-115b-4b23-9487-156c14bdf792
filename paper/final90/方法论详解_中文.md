# 详细方法论和数学公式

## 1. 数学基础

### 1.1 问题定义

设 $\mathcal{D} = \{(\mathbf{X}^{(i)}, \mathbf{Y}^{(i)})\}_{i=1}^N$ 为我们的数据集，其中：
- $N$ 是睡眠记录的数量
- $\mathbf{X}^{(i)} = \{x_1^{(i)}, x_2^{(i)}, ..., x_{T_i}^{(i)}\}$ 是EEG epoch序列
- $x_t^{(i)} \in \mathbb{R}^{C \times L}$ 其中 $C=3$ 个通道，$L=3000$ 个样本（30秒 @ 100Hz）
- $\mathbf{Y}^{(i)} = \{y_1^{(i)}, y_2^{(i)}, ..., y_{T_i}^{(i)}\}$ 是对应的标签
- $y_t^{(i)} \in \{0, 1, 2, 3, 4\}$ 代表 {清醒, N1, N2, N3, REM}

### 1.2 序列窗口构建

我们以长度 $S=7$ 的滑动窗口处理数据：
- 输入窗口：$\mathbf{W}_k = [x_{k}, x_{k+1}, ..., x_{k+S-1}] \in \mathbb{R}^{S \times C \times L}$
- 目标窗口：$\mathbf{L}_k = [y_{k}, y_{k+1}, ..., y_{k+S-1}] \in \mathbb{Z}^S$

## 2. 特征提取模块

### 2.1 多尺度CNN架构

对于序列中的每个epoch $x_t$，我们应用分层卷积：

#### 第1层：粗尺度特征
$$\mathbf{h}_1 = \text{MaxPool}_{k=8,s=8}(\text{ReLU}(\text{BN}(\text{Conv1D}_{k=50,s=6,c_{out}=64}(x_t))))$$

其中：
- $\text{Conv1D}_{k,s,c_{out}}$：一维卷积，核大小 $k$，步长 $s$，输出通道 $c_{out}$
- $\text{BN}$：批归一化，带可学习参数 $\gamma, \beta$：
  $$\text{BN}(x) = \gamma \frac{x - \mu_B}{\sqrt{\sigma_B^2 + \epsilon}} + \beta$$
- $\text{ReLU}(x) = \max(0, x)$
- $\text{MaxPool}_{k,s}$：最大池化，核大小 $k$，步长 $s$

输出维度：$\mathbf{h}_1 \in \mathbb{R}^{64 \times L_1}$ 其中 $L_1 = \lfloor\frac{\lfloor\frac{L-50}{6}\rfloor + 1 - 8}{8}\rfloor + 1$

#### 第2层：中尺度特征
$$\mathbf{h}_2 = \text{MaxPool}_{k=4,s=4}(\text{Dropout}_{p=0.075}(\text{ReLU}(\text{BN}(\text{Conv1D}_{k=8,s=1,c_{out}=128}(\mathbf{h}_1)))))$$

输出维度：$\mathbf{h}_2 \in \mathbb{R}^{128 \times L_2}$

#### 第3层：细尺度特征
$$\mathbf{h}_3 = \text{Dropout}_{p=0.045}(\text{ReLU}(\text{BN}(\text{Conv1D}_{k=4,s=1,c_{out}=d_{model}}(\mathbf{h}_2))))$$

其中 $d_{model} = 512$ 是Transformer维度。

### 2.2 全局特征聚合

应用自适应平均池化获得固定大小表示：
$$\mathbf{f}_t = \text{GlobalAvgPool}(\mathbf{h}_3) = \frac{1}{L_3}\sum_{i=1}^{L_3} \mathbf{h}_3[:, i] \in \mathbb{R}^{d_{model}}$$

这为每个epoch $x_t$ 生成特征向量 $\mathbf{f}_t$。

## 3. Transformer序列建模

### 3.1 位置编码

为了序列位置感知，我们添加正弦位置编码：

$$\text{PE}(pos, 2i) = \sin\left(\frac{pos}{10000^{2i/d_{model}}}\right)$$
$$\text{PE}(pos, 2i+1) = \cos\left(\frac{pos}{10000^{2i/d_{model}}}\right)$$

其中 $pos \in [0, S-1]$ 是位置，$i \in [0, d_{model}/2-1]$ 是维度索引。

带位置编码的特征序列：
$$\mathbf{F}' = [\mathbf{f}_1 + \text{PE}(0), \mathbf{f}_2 + \text{PE}(1), ..., \mathbf{f}_S + \text{PE}(S-1)]$$

### 3.2 多头自注意力

每个Transformer层计算：

#### 注意力机制
对于头 $h \in [1, H]$ 其中 $H=32$：

$$\mathbf{Q}_h = \mathbf{F}'\mathbf{W}_h^Q, \quad \mathbf{K}_h = \mathbf{F}'\mathbf{W}_h^K, \quad \mathbf{V}_h = \mathbf{F}'\mathbf{W}_h^V$$

其中 $\mathbf{W}_h^Q, \mathbf{W}_h^K, \mathbf{W}_h^V \in \mathbb{R}^{d_{model} \times d_k}$ 且 $d_k = d_{model}/H = 16$。

缩放点积注意力：
$$\text{Attention}_h(\mathbf{Q}_h, \mathbf{K}_h, \mathbf{V}_h) = \text{softmax}\left(\frac{\mathbf{Q}_h\mathbf{K}_h^T}{\sqrt{d_k}}\right)\mathbf{V}_h$$

多头注意力：
$$\text{MultiHead}(\mathbf{F}') = \text{Concat}(\text{head}_1, ..., \text{head}_H)\mathbf{W}^O$$

其中 $\mathbf{W}^O \in \mathbb{R}^{Hd_k \times d_{model}}$ 是输出投影。

#### 前馈网络
$$\text{FFN}(\mathbf{x}) = \text{ReLU}(\mathbf{x}\mathbf{W}_1 + \mathbf{b}_1)\mathbf{W}_2 + \mathbf{b}_2$$

其中 $\mathbf{W}_1 \in \mathbb{R}^{d_{model} \times 4d_{model}}$，$\mathbf{W}_2 \in \mathbb{R}^{4d_{model} \times d_{model}}$。

#### 完整Transformer层
带残差连接和层归一化：
$$\mathbf{z}' = \text{LayerNorm}(\mathbf{F}' + \text{Dropout}_{p=0.2}(\text{MultiHead}(\mathbf{F}')))$$
$$\mathbf{z} = \text{LayerNorm}(\mathbf{z}' + \text{Dropout}_{p=0.2}(\text{FFN}(\mathbf{z}')))$$

这重复 $L=12$ 层。

## 4. 分类头

### 4.1 主分类头

用于5类睡眠阶段预测：
$$\mathbf{p}_{main} = \text{Softmax}(\mathbf{W}_{c2}(\text{ReLU}(\text{Dropout}_{p=0.1}(\mathbf{W}_{c1}(\text{LayerNorm}(\mathbf{z}))))))$$

其中：
- $\mathbf{W}_{c1} \in \mathbb{R}^{d_{model} \times d_{model}/2}$
- $\mathbf{W}_{c2} \in \mathbb{R}^{d_{model}/2 \times 5}$
- 输出：$\mathbf{p}_{main} \in \mathbb{R}^{S \times 5}$

### 4.2 辅助分类头

用于二元深/浅睡眠分类：
$$\mathbf{p}_{aux} = \text{Softmax}(\mathbf{W}_{a2}(\text{ReLU}(\text{Dropout}_{p=0.1}(\mathbf{W}_{a1}(\text{LayerNorm}(\mathbf{z}))))))$$

其中：
- $\mathbf{W}_{a1} \in \mathbb{R}^{d_{model} \times d_{model}/4}$
- $\mathbf{W}_{a2} \in \mathbb{R}^{d_{model}/4 \times 2}$
- 二元映射：类0 = {N3, REM}，类1 = {清醒, N1, N2}

## 5. 损失函数公式

### 5.1 Focal损失组件

用于解决类别不平衡：
$$\mathcal{L}_{focal} = -\frac{1}{S \cdot B}\sum_{b=1}^B\sum_{s=1}^S \alpha_{y_{b,s}}(1-p_{b,s,y_{b,s}})^\gamma \log(p_{b,s,y_{b,s}})$$

其中：
- $B$ 是批大小
- $p_{b,s,y_{b,s}}$ 是真实类别的预测概率
- $\alpha = [2.0, 2.5, 1.0, 1.5, 2.0]$ 是[W, N1, N2, N3, REM]的类权重
- $\gamma = 2$ 是聚焦参数

相对于logits $z$ 的梯度：
$$\frac{\partial \mathcal{L}_{focal}}{\partial z} = \alpha_t(1-p_t)^\gamma[\gamma p_t\log(p_t) + p_t - 1]\frac{\partial p}{\partial z}$$

### 5.2 标签平滑组件

用于改善泛化：
$$\mathcal{L}_{smooth} = -\frac{1}{S \cdot B}\sum_{b=1}^B\sum_{s=1}^S\left[(1-\epsilon)\log(p_{b,s,y_{b,s}}) + \frac{\epsilon}{K-1}\sum_{k \neq y_{b,s}}\log(p_{b,s,k})\right]$$

其中：
- $\epsilon = 0.1$ 是平滑参数
- $K = 5$ 是类别数

实际上，我们用以下替换硬目标：
$$\tilde{y}_k = \begin{cases}
1 - \epsilon & \text{如果 } k = y \\
\frac{\epsilon}{K-1} & \text{否则}
\end{cases}$$

### 5.3 带正则化的组合损失

总损失函数：
$$\mathcal{L}_{total} = 0.6 \cdot \mathcal{L}_{focal} + 0.4 \cdot \mathcal{L}_{smooth} + \lambda \sum_{\theta \in \Theta} ||\theta||_2^2$$

其中：
- $\lambda = 10^{-4}$ 是L2正则化系数
- $\Theta$ 表示所有模型参数

### 5.4 辅助损失

对于二元分类头：
$$\mathcal{L}_{aux} = -\frac{1}{S \cdot B}\sum_{b=1}^B\sum_{s=1}^S \log(p_{b,s,y_{b,s}}^{aux})$$

其中 $y_{b,s}^{aux} \in \{0, 1\}$ 是二元标签。

最终训练目标：
$$\mathcal{L} = \mathcal{L}_{total} + 0.1 \cdot \mathcal{L}_{aux}$$

## 6. 训练优化

### 6.1 AdamW优化器

使用AdamW的参数更新：
$$m_t = \beta_1 m_{t-1} + (1-\beta_1)g_t$$
$$v_t = \beta_2 v_{t-1} + (1-\beta_2)g_t^2$$
$$\hat{m}_t = \frac{m_t}{1-\beta_1^t}, \quad \hat{v}_t = \frac{v_t}{1-\beta_2^t}$$
$$\theta_t = \theta_{t-1} - \eta\left(\frac{\hat{m}_t}{\sqrt{\hat{v}_t} + \epsilon} + \lambda_{wd}\theta_{t-1}\right)$$

其中：
- $\beta_1 = 0.9$，$\beta_2 = 0.999$
- $\lambda_{wd} = 0.03$（权重衰减）
- $\epsilon = 10^{-8}$

### 6.2 OneCycleLR调度

学习率调度：
$$\eta(t) = \begin{cases}
\eta_{min} + \frac{t}{T_{warm}}(\eta_{max} - \eta_{min}) & \text{如果 } t < T_{warm} \\
\eta_{max} - \frac{t-T_{warm}}{T_{total}-T_{warm}}(\eta_{max} - \eta_{final}) & \text{否则}
\end{cases}$$

其中：
- $\eta_{max} = 2 \times 10^{-4}$
- $\eta_{min} = \eta_{max}/10 = 2 \times 10^{-5}$
- $\eta_{final} = \eta_{max}/100 = 2 \times 10^{-6}$
- $T_{warm} = 0.2 \times T_{total}$（20%预热）
- $T_{total} = \text{epochs} \times \text{每epoch批次数}$

### 6.3 梯度裁剪

为防止梯度爆炸：
$$g \leftarrow \begin{cases}
g & \text{如果 } ||g||_2 \leq \tau \\
\tau \cdot \frac{g}{||g||_2} & \text{否则}
\end{cases}$$

其中 $\tau = 1.0$ 是裁剪阈值。

## 7. 数据增强

### 7.1 训练时增强

以概率 $p_{aug} = 0.2$ 应用：

#### 高斯噪声添加
$$\tilde{x} = x + \mathcal{N}(0, \sigma^2\mathbf{I})$$
其中 $\sigma = 0.005 \times \text{std}(x)$

#### 幅度缩放
$$\tilde{x} = \alpha \cdot x$$
其中 $\alpha \sim \mathcal{U}(0.95, 1.05)$

### 7.2 测试时增强

生成 $M=3$ 个预测：
1. $\mathbf{p}_1 = f_\theta(x)$（原始）
2. $\mathbf{p}_2 = f_\theta(x + \mathcal{N}(0, 0.003^2\mathbf{I}))$
3. $\mathbf{p}_3 = f_\theta(x + \mathcal{N}(0, 0.003^2\mathbf{I}))$

最终预测：
$$\hat{y} = \arg\max_k \frac{1}{M}\sum_{m=1}^M \mathbf{p}_m^{(k)}$$

## 8. 权重初始化

### 8.1 线性层（Xavier均匀）
$$W_{ij} \sim \mathcal{U}\left(-\sqrt{\frac{6g}{n_{in} + n_{out}}}, \sqrt{\frac{6g}{n_{in} + n_{out}}}\right)$$
其中 $g = 0.8$（保守增益），$n_{in}$、$n_{out}$ 是输入/输出维度。

### 8.2 卷积层（Kaiming正态）
$$W \sim \mathcal{N}\left(0, \sqrt{\frac{2}{n_{in} \times k}}\right)$$
其中 $k$ 是核大小。

### 8.3 归一化层
- 权重：$\gamma = 1$
- 偏置：$\beta = 0$

## 9. 评估指标

### 9.1 准确率
$$\text{准确率} = \frac{1}{N}\sum_{i=1}^N \mathbb{1}[\hat{y}_i = y_i]$$

### 9.2 宏F1分数
$$\text{F1}_{macro} = \frac{1}{K}\sum_{k=1}^K \frac{2 \cdot \text{精确率}_k \cdot \text{召回率}_k}{\text{精确率}_k + \text{召回率}_k}$$

### 9.3 Cohen's Kappa
$$\kappa = \frac{p_o - p_e}{1 - p_e}$$
其中：
- $p_o$ = 观察一致性（准确率）
- $p_e$ = 期望偶然一致性

### 9.4 混淆矩阵
$$C_{ij} = \sum_{n=1}^N \mathbb{1}[\hat{y}_n = i \land y_n = j]$$

## 10. 计算复杂度分析

### 10.1 CNN特征提取
- 第1层：$O(C \cdot 64 \cdot 50 \cdot L/6)$
- 第2层：$O(64 \cdot 128 \cdot 8 \cdot L_1)$
- 第3层：$O(128 \cdot d_{model} \cdot 4 \cdot L_2)$
- 总计：每epoch $O(L \cdot d_{model})$

### 10.2 Transformer
- 自注意力：每层 $O(S^2 \cdot d_{model})$
- 前馈：每层 $O(S \cdot d_{model}^2)$
- 总计：$O(L_{layers} \cdot S \cdot (S + d_{model}) \cdot d_{model})$

### 10.3 整体复杂度
- 时间：$O(S \cdot (L \cdot d_{model} + L_{layers} \cdot (S + d_{model}) \cdot d_{model}))$
- 空间：$O(S \cdot d_{model} + L_{layers} \cdot d_{model}^2)$

对于我们的配置：
- 每序列时间：GPU上~15ms
- 内存：批大小16时~8GB