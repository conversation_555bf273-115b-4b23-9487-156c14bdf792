# Detailed Methodology and Mathematical Formulations

## 1. Mathematical Foundations

### 1.1 Problem Definition

Let $\mathcal{D} = \{(\mathbf{X}^{(i)}, \mathbf{Y}^{(i)})\}_{i=1}^N$ be our dataset where:
- $N$ is the number of sleep recordings
- $\mathbf{X}^{(i)} = \{x_1^{(i)}, x_2^{(i)}, ..., x_{T_i}^{(i)}\}$ is a sequence of EEG epochs
- $x_t^{(i)} \in \mathbb{R}^{C \times L}$ where $C=3$ channels, $L=3000$ samples (30s @ 100Hz)
- $\mathbf{Y}^{(i)} = \{y_1^{(i)}, y_2^{(i)}, ..., y_{T_i}^{(i)}\}$ are corresponding labels
- $y_t^{(i)} \in \{0, 1, 2, 3, 4\}$ representing {Wake, N1, N2, N3, REM}

### 1.2 Sequence Window Formulation

We process data in sliding windows of length $S=7$:
- Input window: $\mathbf{W}_k = [x_{k}, x_{k+1}, ..., x_{k+S-1}] \in \mathbb{R}^{S \times C \times L}$
- Target window: $\mathbf{L}_k = [y_{k}, y_{k+1}, ..., y_{k+S-1}] \in \mathbb{Z}^S$

## 2. Feature Extraction Module

### 2.1 Multi-Scale CNN Architecture

For each epoch $x_t$ in the sequence, we apply hierarchical convolution:

#### Layer 1: Coarse-Scale Features
$$\mathbf{h}_1 = \text{MaxPool}_{k=8,s=8}(\text{ReLU}(\text{BN}(\text{Conv1D}_{k=50,s=6,c_{out}=64}(x_t))))$$

Where:
- $\text{Conv1D}_{k,s,c_{out}}$: 1D convolution with kernel size $k$, stride $s$, output channels $c_{out}$
- $\text{BN}$: Batch normalization with learnable parameters $\gamma, \beta$:
  $$\text{BN}(x) = \gamma \frac{x - \mu_B}{\sqrt{\sigma_B^2 + \epsilon}} + \beta$$
- $\text{ReLU}(x) = \max(0, x)$
- $\text{MaxPool}_{k,s}$: Max pooling with kernel size $k$, stride $s$

Output dimension: $\mathbf{h}_1 \in \mathbb{R}^{64 \times L_1}$ where $L_1 = \lfloor\frac{\lfloor\frac{L-50}{6}\rfloor + 1 - 8}{8}\rfloor + 1$

#### Layer 2: Mid-Scale Features
$$\mathbf{h}_2 = \text{MaxPool}_{k=4,s=4}(\text{Dropout}_{p=0.075}(\text{ReLU}(\text{BN}(\text{Conv1D}_{k=8,s=1,c_{out}=128}(\mathbf{h}_1)))))$$

Output dimension: $\mathbf{h}_2 \in \mathbb{R}^{128 \times L_2}$

#### Layer 3: Fine-Scale Features
$$\mathbf{h}_3 = \text{Dropout}_{p=0.045}(\text{ReLU}(\text{BN}(\text{Conv1D}_{k=4,s=1,c_{out}=d_{model}}(\mathbf{h}_2))))$$

Where $d_{model} = 512$ is the Transformer dimension.

### 2.2 Global Feature Aggregation

Apply adaptive average pooling to obtain fixed-size representation:
$$\mathbf{f}_t = \text{GlobalAvgPool}(\mathbf{h}_3) = \frac{1}{L_3}\sum_{i=1}^{L_3} \mathbf{h}_3[:, i] \in \mathbb{R}^{d_{model}}$$

This produces a feature vector $\mathbf{f}_t$ for each epoch $x_t$.

## 3. Sequence Modeling with Transformer

### 3.1 Positional Encoding

For sequence position awareness, we add sinusoidal positional encodings:

$$\text{PE}(pos, 2i) = \sin\left(\frac{pos}{10000^{2i/d_{model}}}\right)$$
$$\text{PE}(pos, 2i+1) = \cos\left(\frac{pos}{10000^{2i/d_{model}}}\right)$$

Where $pos \in [0, S-1]$ is the position and $i \in [0, d_{model}/2-1]$ is the dimension index.

Feature sequence with positional encoding:
$$\mathbf{F}' = [\mathbf{f}_1 + \text{PE}(0), \mathbf{f}_2 + \text{PE}(1), ..., \mathbf{f}_S + \text{PE}(S-1)]$$

### 3.2 Multi-Head Self-Attention

Each Transformer layer computes:

#### Attention Mechanism
For head $h \in [1, H]$ where $H=32$:

$$\mathbf{Q}_h = \mathbf{F}'\mathbf{W}_h^Q, \quad \mathbf{K}_h = \mathbf{F}'\mathbf{W}_h^K, \quad \mathbf{V}_h = \mathbf{F}'\mathbf{W}_h^V$$

Where $\mathbf{W}_h^Q, \mathbf{W}_h^K, \mathbf{W}_h^V \in \mathbb{R}^{d_{model} \times d_k}$ and $d_k = d_{model}/H = 16$.

Scaled dot-product attention:
$$\text{Attention}_h(\mathbf{Q}_h, \mathbf{K}_h, \mathbf{V}_h) = \text{softmax}\left(\frac{\mathbf{Q}_h\mathbf{K}_h^T}{\sqrt{d_k}}\right)\mathbf{V}_h$$

Multi-head attention:
$$\text{MultiHead}(\mathbf{F}') = \text{Concat}(\text{head}_1, ..., \text{head}_H)\mathbf{W}^O$$

Where $\mathbf{W}^O \in \mathbb{R}^{Hd_k \times d_{model}}$ is the output projection.

#### Feed-Forward Network
$$\text{FFN}(\mathbf{x}) = \text{ReLU}(\mathbf{x}\mathbf{W}_1 + \mathbf{b}_1)\mathbf{W}_2 + \mathbf{b}_2$$

Where $\mathbf{W}_1 \in \mathbb{R}^{d_{model} \times 4d_{model}}$, $\mathbf{W}_2 \in \mathbb{R}^{4d_{model} \times d_{model}}$.

#### Complete Transformer Layer
With residual connections and layer normalization:
$$\mathbf{z}' = \text{LayerNorm}(\mathbf{F}' + \text{Dropout}_{p=0.2}(\text{MultiHead}(\mathbf{F}')))$$
$$\mathbf{z} = \text{LayerNorm}(\mathbf{z}' + \text{Dropout}_{p=0.2}(\text{FFN}(\mathbf{z}')))$$

This is repeated for $L=12$ layers.

## 4. Classification Heads

### 4.1 Main Classification Head

For 5-class sleep stage prediction:
$$\mathbf{p}_{main} = \text{Softmax}(\mathbf{W}_{c2}(\text{ReLU}(\text{Dropout}_{p=0.1}(\mathbf{W}_{c1}(\text{LayerNorm}(\mathbf{z}))))))$$

Where:
- $\mathbf{W}_{c1} \in \mathbb{R}^{d_{model} \times d_{model}/2}$
- $\mathbf{W}_{c2} \in \mathbb{R}^{d_{model}/2 \times 5}$
- Output: $\mathbf{p}_{main} \in \mathbb{R}^{S \times 5}$

### 4.2 Auxiliary Classification Head

For binary deep/light sleep classification:
$$\mathbf{p}_{aux} = \text{Softmax}(\mathbf{W}_{a2}(\text{ReLU}(\text{Dropout}_{p=0.1}(\mathbf{W}_{a1}(\text{LayerNorm}(\mathbf{z}))))))$$

Where:
- $\mathbf{W}_{a1} \in \mathbb{R}^{d_{model} \times d_{model}/4}$
- $\mathbf{W}_{a2} \in \mathbb{R}^{d_{model}/4 \times 2}$
- Binary mapping: Class 0 = {N3, REM}, Class 1 = {Wake, N1, N2}

## 5. Loss Function Formulation

### 5.1 Focal Loss Component

For addressing class imbalance:
$$\mathcal{L}_{focal} = -\frac{1}{S \cdot B}\sum_{b=1}^B\sum_{s=1}^S \alpha_{y_{b,s}}(1-p_{b,s,y_{b,s}})^\gamma \log(p_{b,s,y_{b,s}})$$

Where:
- $B$ is batch size
- $p_{b,s,y_{b,s}}$ is predicted probability for true class
- $\alpha = [2.0, 2.5, 1.0, 1.5, 2.0]$ are class weights for [W, N1, N2, N3, REM]
- $\gamma = 2$ is the focusing parameter

The gradient with respect to logits $z$:
$$\frac{\partial \mathcal{L}_{focal}}{\partial z} = \alpha_t(1-p_t)^\gamma[\gamma p_t\log(p_t) + p_t - 1]\frac{\partial p}{\partial z}$$

### 5.2 Label Smoothing Component

For improved generalization:
$$\mathcal{L}_{smooth} = -\frac{1}{S \cdot B}\sum_{b=1}^B\sum_{s=1}^S\left[(1-\epsilon)\log(p_{b,s,y_{b,s}}) + \frac{\epsilon}{K-1}\sum_{k \neq y_{b,s}}\log(p_{b,s,k})\right]$$

Where:
- $\epsilon = 0.1$ is the smoothing parameter
- $K = 5$ is the number of classes

Effectively, we replace hard targets with:
$$\tilde{y}_k = \begin{cases}
1 - \epsilon & \text{if } k = y \\
\frac{\epsilon}{K-1} & \text{otherwise}
\end{cases}$$

### 5.3 Combined Loss with Regularization

Total loss function:
$$\mathcal{L}_{total} = 0.6 \cdot \mathcal{L}_{focal} + 0.4 \cdot \mathcal{L}_{smooth} + \lambda \sum_{\theta \in \Theta} ||\theta||_2^2$$

Where:
- $\lambda = 10^{-4}$ is the L2 regularization coefficient
- $\Theta$ represents all model parameters

### 5.4 Auxiliary Loss

For the binary classification head:
$$\mathcal{L}_{aux} = -\frac{1}{S \cdot B}\sum_{b=1}^B\sum_{s=1}^S \log(p_{b,s,y_{b,s}}^{aux})$$

Where $y_{b,s}^{aux} \in \{0, 1\}$ is the binary label.

Final training objective:
$$\mathcal{L} = \mathcal{L}_{total} + 0.1 \cdot \mathcal{L}_{aux}$$

## 6. Training Optimization

### 6.1 AdamW Optimizer

Parameter updates using AdamW:
$$m_t = \beta_1 m_{t-1} + (1-\beta_1)g_t$$
$$v_t = \beta_2 v_{t-1} + (1-\beta_2)g_t^2$$
$$\hat{m}_t = \frac{m_t}{1-\beta_1^t}, \quad \hat{v}_t = \frac{v_t}{1-\beta_2^t}$$
$$\theta_t = \theta_{t-1} - \eta\left(\frac{\hat{m}_t}{\sqrt{\hat{v}_t} + \epsilon} + \lambda_{wd}\theta_{t-1}\right)$$

Where:
- $\beta_1 = 0.9$, $\beta_2 = 0.999$
- $\lambda_{wd} = 0.03$ (weight decay)
- $\epsilon = 10^{-8}$

### 6.2 OneCycleLR Schedule

Learning rate schedule:
$$\eta(t) = \begin{cases}
\eta_{min} + \frac{t}{T_{warm}}(\eta_{max} - \eta_{min}) & \text{if } t < T_{warm} \\
\eta_{max} - \frac{t-T_{warm}}{T_{total}-T_{warm}}(\eta_{max} - \eta_{final}) & \text{otherwise}
\end{cases}$$

Where:
- $\eta_{max} = 2 \times 10^{-4}$
- $\eta_{min} = \eta_{max}/10 = 2 \times 10^{-5}$
- $\eta_{final} = \eta_{max}/100 = 2 \times 10^{-6}$
- $T_{warm} = 0.2 \times T_{total}$ (20% warmup)
- $T_{total} = \text{epochs} \times \text{batches\_per\_epoch}$

### 6.3 Gradient Clipping

To prevent gradient explosion:
$$g \leftarrow \begin{cases}
g & \text{if } ||g||_2 \leq \tau \\
\tau \cdot \frac{g}{||g||_2} & \text{otherwise}
\end{cases}$$

Where $\tau = 1.0$ is the clipping threshold.

## 7. Data Augmentation

### 7.1 Training-Time Augmentation

Applied with probability $p_{aug} = 0.2$:

#### Gaussian Noise Addition
$$\tilde{x} = x + \mathcal{N}(0, \sigma^2\mathbf{I})$$
Where $\sigma = 0.005 \times \text{std}(x)$

#### Amplitude Scaling
$$\tilde{x} = \alpha \cdot x$$
Where $\alpha \sim \mathcal{U}(0.95, 1.05)$

### 7.2 Test-Time Augmentation

Generate $M=3$ predictions:
1. $\mathbf{p}_1 = f_\theta(x)$ (original)
2. $\mathbf{p}_2 = f_\theta(x + \mathcal{N}(0, 0.003^2\mathbf{I}))$
3. $\mathbf{p}_3 = f_\theta(x + \mathcal{N}(0, 0.003^2\mathbf{I}))$

Final prediction:
$$\hat{y} = \arg\max_k \frac{1}{M}\sum_{m=1}^M \mathbf{p}_m^{(k)}$$

## 8. Weight Initialization

### 8.1 Linear Layers (Xavier Uniform)
$$W_{ij} \sim \mathcal{U}\left(-\sqrt{\frac{6g}{n_{in} + n_{out}}}, \sqrt{\frac{6g}{n_{in} + n_{out}}}\right)$$
Where $g = 0.8$ (conservative gain), $n_{in}$, $n_{out}$ are input/output dimensions.

### 8.2 Convolutional Layers (Kaiming Normal)
$$W \sim \mathcal{N}\left(0, \sqrt{\frac{2}{n_{in} \times k}}\right)$$
Where $k$ is the kernel size.

### 8.3 Normalization Layers
- Weights: $\gamma = 1$
- Biases: $\beta = 0$

## 9. Evaluation Metrics

### 9.1 Accuracy
$$\text{Accuracy} = \frac{1}{N}\sum_{i=1}^N \mathbb{1}[\hat{y}_i = y_i]$$

### 9.2 Macro F1-Score
$$\text{F1}_{macro} = \frac{1}{K}\sum_{k=1}^K \frac{2 \cdot \text{Precision}_k \cdot \text{Recall}_k}{\text{Precision}_k + \text{Recall}_k}$$

### 9.3 Cohen's Kappa
$$\kappa = \frac{p_o - p_e}{1 - p_e}$$
Where:
- $p_o$ = observed agreement (accuracy)
- $p_e$ = expected agreement by chance

### 9.4 Confusion Matrix
$$C_{ij} = \sum_{n=1}^N \mathbb{1}[\hat{y}_n = i \land y_n = j]$$

## 10. Computational Complexity Analysis

### 10.1 CNN Feature Extraction
- Layer 1: $O(C \cdot 64 \cdot 50 \cdot L/6)$
- Layer 2: $O(64 \cdot 128 \cdot 8 \cdot L_1)$
- Layer 3: $O(128 \cdot d_{model} \cdot 4 \cdot L_2)$
- Total: $O(L \cdot d_{model})$ per epoch

### 10.2 Transformer
- Self-attention: $O(S^2 \cdot d_{model})$ per layer
- Feed-forward: $O(S \cdot d_{model}^2)$ per layer
- Total: $O(L_{layers} \cdot S \cdot (S + d_{model}) \cdot d_{model})$

### 10.3 Overall Complexity
- Time: $O(S \cdot (L \cdot d_{model} + L_{layers} \cdot (S + d_{model}) \cdot d_{model}))$
- Space: $O(S \cdot d_{model} + L_{layers} \cdot d_{model}^2)$

For our configuration:
- Time per sequence: ~15ms on GPU
- Memory: ~8GB for batch size 16