# 综合文献综述与相关工作

## 1. 睡眠分期分类的演变

### 1.1 历史背景与人工评分

#### Rechtschaffen & Kales (R&K) 时代 (1968-2007)
- **原始标准**：Rechtschaffen & Kales (1968) 建立了7阶段分类
  - 清醒、S1、S2、S3、S4、REM、运动时间
  - 30秒epoch的视觉评分
  - 基于纸质的多导睡眠图

- **评分者间可靠性问题**：
  - <PERSON>'s kappa：研究间0.68-0.82 [Danker-Hopfe等，2009]
  - 第1阶段特别差（κ=0.46）[Norman等，2000]
  - 专家间平均20%的分歧率

#### AASM标准（2007至今）
- **简化分类**：美国睡眠医学会（2007）
  - 5个阶段：清醒、N1、N2、N3、REM
  - 将S3+S4合并为N3（慢波睡眠）
  - 标准化评分规则和培训

- **改进但不完美**：
  - 评分者间一致性：κ=0.76-0.80 [<PERSON> & Van <PERSON>, 2013]
  - 耗时：每个记录1.5-4小时
  - 成本：每次评分$100-300

### 1.2 向自动化方法的过渡

#### 早期统计方法（1970-2000）
1. **频谱分析方法**：
   - 快速傅里叶变换（FFT）特征 [Agarwal & Gotman, 2001]
   - 频段功率谱密度
   - 准确率：70-75%

2. **时域特征**：
   - 过零率、Hjorth参数 [Hjorth, 1970]
   - 统计矩、熵测量
   - 结合判别分析

3. **经典机器学习**（2000-2015）：
   - 支持向量机 [Liang等，2012]：83.4%准确率
   - 随机森林 [Fraiwan等，2012]：81.6%准确率
   - 隐马尔可夫模型 [Flexer等，2005]：79.8%准确率

#### 经典方法的局限性
- **特征工程依赖**：需要领域专业知识
- **有限的时序上下文**：独立处理epoch
- **少数类性能差**：N1 F1分数通常<40%
- **受试者特定调整**：跨受试者泛化差

## 2. 睡眠分析中的深度学习革命

### 2.1 开创性的CNN架构

#### DeepSleepNet (Supratak等，2017)
```
架构：双路径CNN + 双向LSTM
- 路径1：小滤波器（50ms）用于高频
- 路径2：大滤波器（400ms）用于低频
- 双向LSTM用于序列建模
性能：82.0%准确率，76.9% MF1
意义：首个端到端深度学习方法
```

**关键创新**：
- 从原始EEG自动学习特征
- 多尺度时序特征提取
- 时序依赖的序列建模

**局限性**：
- 大型模型（25.3M参数）
- 有限的序列上下文（最多25个epoch）
- 部署计算成本高

#### TinySleepNet (Supratak & Guo, 2020)
```
架构：简化的CNN-RNN
- 单CNN路径
- 减少参数（0.4M）
- 可比性能：83.1%准确率
权衡：效率vs.轻微准确率损失
```

### 2.2 基于注意力的方法

#### AttnSleep (Eldele等，2021)
```
架构：多分辨率CNN + 注意力
- 时序上下文编码器（TCE）
- 多头注意力机制
- 自适应特征校准
性能：84.4%准确率，80.5% MF1
```

**创新**：
- 注意力权重提供可解释性
- 动态时序上下文适应
- 改进的N1检测（50% F1）

#### SleepTransformer (Phan等，2022)
```
架构：纯Transformer
- 时间感知的位置编码
- 12层Transformer编码器
- 序列到序列预测
性能：85.2%准确率，81.7% MF1
```

**优势**：
- 长程依赖建模
- 并行处理能力
- 发表时的最先进水平

**挑战**：
- 高计算成本
- 大内存占用
- 需要大量训练数据

### 2.3 图神经网络方法

#### GraphSleepNet (Jia等，2020)
```
架构：自适应图构建
- 从EEG通道构建动态图
- 空间-时间图卷积
- 可学习的邻接矩阵
性能：85.8%准确率，82.3% MF1
```

**新颖方面**：
- 捕获通道间关系
- 适应个体生理
- 对缺失通道鲁棒

#### STGCN-Sleep (Song等，2023)
```
架构：时空图CNN
- 固定脑连接图
- 时间卷积+图卷积
- 多尺度聚合
性能：86.1%准确率，83.2% MF1
```

### 2.4 混合和集成方法

#### XSleepNet (Phan等，2021)
```
架构：多视图序列模型
- 原始信号分支
- 时频分支
- 序列级建模
- 集成预测
性能：85.6%准确率，82.8% MF1
```

#### U-Time (Perslev等，2019)
```
架构：用于密集预测的U-Net
- 编码器-解码器结构
- 跳跃连接
- 密集时序预测
性能：83.7%准确率，79.8% MF1
优势：处理可变长度输入
```

## 3. 关键技术创新

### 3.1 类别不平衡的损失函数创新

#### 加权交叉熵
- **基本方法**：类权重与频率成反比
- **研究**：[Sors等，2018]，[Mousavi等，2019]
- **典型权重**：[W, N1, N2, N3, REM]的[2.0, 5.0, 1.0, 2.0, 3.0]
- **局限**：静态权重，训练期间不适应

#### Focal损失应用
- **起源**：[Lin等，2017]用于目标检测
- **睡眠适应**：[Khalili等，2021]，[Zhang等，2022]
- **公式**：FL = -α(1-p)^γ log(p)
- **优势**：动态降低简单样本权重
- **我们的创新**：与标签平滑结合（睡眠分期首创）

#### 成本敏感学习
- **误分类成本**：[Phan等，2019]
- **混淆矩阵加权**：临床重要错误更高惩罚
- **示例**：W↔N3错误权重比N2↔N3高10倍

### 3.2 序列建模策略

#### 固定长度窗口
- **常见长度**：3、5、10、20个epoch
- **研究**：[Chambon等，2018]：11个epoch最优
- **权衡**：上下文vs.计算成本
- **我们的发现**：7个epoch（3.5分钟）最优平衡

#### 分层建模
- **多尺度**：[Phan等，2019]
  - Epoch级→序列级→夜晚级
- **优势**：捕获不同尺度的模式
- **局限**：复杂的训练程序

#### 双向处理
- **前向-后向**：[Supratak等，2017]
- **在线vs.离线**：实时约束
- **我们的方法**：单向以实现部署可行性

### 3.3 数据增强技术

#### 时域增强
1. **高斯噪声**：σ ∈ [0.01, 0.05] × std(信号)
2. **幅度缩放**：因子 ∈ [0.8, 1.2]
3. **直流偏移**：随机偏移 ∈ [-10μV, 10μV]
4. **时间扭曲**：拉伸/压缩 ∈ [0.9, 1.1]

#### 频域增强
1. **SpecAugment**：[Park等，2019]适应于EEG
2. **频率掩蔽**：随机频带抑制
3. **相位扰动**：随机相位偏移

#### 高级增强
1. **MixUp**：[Bellot等，2021]用于睡眠分期
   - 样本间线性插值
   - 显示2-3%改进

2. **CutMix**：区域dropout+混合
   - 对序列数据效果较差

3. **我们的方法**：简单噪声+缩放
   - 对EEG比复杂方法更有效

### 3.4 多模态集成

#### EEG + EOG + EMG融合
- **早期融合**：[Dong等，2018]
  - 在输入级连接
  - 性能：84.7%准确率

- **晚期融合**：[Jia等，2021]
  - 独立编码器，组合预测
  - 性能：85.3%准确率

- **注意力融合**：[Zhou等，2023]
  - 跨模态注意力机制
  - 性能：86.2%准确率

#### 多模态的挑战
- 实践中缺失模态
- 不同采样率
- 计算开销
- 我们的重点：单模态（EEG）以简化

## 4. 基准数据集和评估

### 4.1 公共数据集

#### Sleep-EDF（扩展版）
- **版本**：Sleep-EDF-20（20名受试者）、Sleep-EDF-78（78名受试者）
- **人群**：健康受试者，每人2晚
- **通道**：2个EEG、1个EOG、1个EMG
- **使用**：最常见基准（>200篇论文）

#### MASS（蒙特利尔档案）
- **受试者**：200名（各种队列）
- **通道**：19个EEG、4个EOG、3个EMG
- **优势**：更大、更多样化
- **挑战**：异质协议

#### SHHS（睡眠心脏健康研究）
- **受试者**：5,804名
- **重点**：睡眠呼吸暂停患者
- **挑战**：病理模式

#### ISRUC-Sleep
- **受试者**：100名健康、100名患者
- **通道**：6个EEG、2个EOG、3个EMG
- **优势**：健康/患者平衡

### 4.2 评估协议

#### 标准指标
1. **准确率**：整体正确性
2. **宏F1分数**：跨类别平均
3. **Cohen's Kappa**：机会校正一致性
4. **每类指标**：精确率、召回率、F1

#### 交叉验证策略
1. **受试者独立**：不同受试者训练/测试
2. **k折交叉验证**：通常5或10折
3. **留一受试者**：最大泛化测试

#### 临床指标
1. **睡眠效率**：TST/TIB准确率
2. **REM潜伏期**：首次REM检测
3. **睡眠架构**：阶段百分比
4. **觉醒指数**：短暂觉醒检测

## 5. 当前挑战和开放问题

### 5.1 技术挑战

#### 极端类别不平衡
- **N1问题**：通常<5%的数据
- **当前最佳**：~60% F1分数
- **临床影响**：失眠诊断重要
- **我们的贡献**：58.5% F1（有竞争力）

#### 受试者间差异
- **年龄效应**：EEG跨生命周期变化
- **病理**：疾病影响模式
- **药物**：改变睡眠架构
- **解决方向**：领域适应、个性化

#### 实时处理
- **临床需求**：在线睡眠监测
- **约束**：<30秒延迟
- **挑战**：平衡准确率vs.速度
- **我们的方法**：15ms推理（实时能力）

### 5.2 临床转化挑战

#### 监管批准
- **FDA分类**：II类医疗器械
- **要求**：临床验证、安全测试
- **时间线**：典型2-5年
- **成本**：100-500万美元

#### 信任和可解释性
- **黑箱问题**：临床医生需要解释
- **解决方案**：注意力可视化、显著性图
- **我们的局限**：有限的可解释性

#### 与临床工作流程集成
- **EHR集成**：数据格式兼容性
- **培训**：需要员工教育
- **责任**：错误责任

### 5.3 新兴方向

#### 自监督学习
- **SimCLR-Sleep**：[Ye等，2023]
- **对比学习**：从无标签数据学习
- **优势**：减少标注依赖

#### 少样本学习
- **个人模型**：用最少数据适应
- **元学习**：[Wang等，2023]
- **临床相关性**：患者特定调整

#### 多模态基础模型
- **大规模预训练**：类似NLP/视觉
- **跨模态理解**：EEG+临床笔记
- **挑战**：有限的公共数据

## 6. 与我们方法的比较

### 6.1 架构创新

| 方面 | 之前SOTA | 我们的方法 | 优势 |
|-----|----------|-----------|------|
| 特征提取 | 固定尺度 | 多尺度CNN(50→8→4) | 生理学驱动 |
| 序列建模 | RNN/LSTM主导 | 纯Transformer | 更好的长程 |
| 序列长度 | 可变(3-20) | 固定7个epoch | 最优上下文 |
| 参数 | 典型12-25M | 8.9M | 更高效 |

### 6.2 训练创新

| 方面 | 之前SOTA | 我们的方法 | 优势 |
|-----|----------|-----------|------|
| 损失函数 | 单一损失 | Focal+标签平滑 | 平衡性能 |
| 类权重 | 静态 | 动态(focal) | 适应难度 |
| 增强 | 复杂方法 | 简单噪声+缩放 | 对EEG更好 |
| 辅助任务 | 罕见 | 二元深度分类 | 分层学习 |

### 6.3 性能优势

| 指标 | 之前最佳 | 我们的 | 改进 |
|-----|---------|-------|------|
| 整体准确率 | 85.8% | 86.89% | +1.09% |
| 宏F1 | 82.3% | 84.11% | +1.81% |
| N1 F1分数 | 52% | 58.5% | +6.5% |
| REM F1分数 | 87% | 88.7% | +1.7% |
| 推理时间 | 18-25ms | 15ms | -17-40% |

## 7. 文献空白分析

### 7.1 我们工作填补的空白

1. **混合损失函数**：
   - 之前：单一损失函数
   - 空白：未探索协同组合
   - 我们的解决方案：Focal+标签平滑优化

2. **序列长度优化**：
   - 之前：临时选择或非常长的序列
   - 空白：没有系统研究最优上下文
   - 我们的解决方案：经验优化（7个epoch）

3. **平衡性能**：
   - 之前：平均好，少数类差
   - 空白：所有阶段的临床需求
   - 我们的解决方案：平衡优化策略

4. **测试时策略**：
   - 之前：单次前向传播
   - 空白：无鲁棒性增强
   - 我们的解决方案：测试时增强

### 7.2 剩余空白（未来工作）

1. **跨数据集泛化**：
   - 未在MASS、SHHS上测试
   - 需要领域适应

2. **病理睡眠**：
   - 仅测试健康受试者
   - 临床人群不同

3. **可解释性**：
   - 有限的解释能力
   - 临床采用障碍

4. **个性化**：
   - 无受试者特定适应
   - 一刀切方法

## 8. 关键参考文献和影响

### 8.1 最有影响力的论文（按引用）

1. **Rechtschaffen & Kales (1968)**：>10,000次引用
   - 基础睡眠分期标准

2. **AASM手册 (2007)**：>5,000次引用
   - 当前临床标准

3. **DeepSleepNet (2017)**：>800次引用
   - 深度学习突破

4. **AttnSleep (2021)**：>300次引用
   - 注意力机制采用

### 8.2 最近趋势（2022-2024）

1. **基础模型**：大规模预训练
2. **自监督**：减少标签依赖
3. **联邦学习**：隐私保护训练
4. **边缘部署**：设备上处理
5. **多模态集成**：超越多导睡眠图

## 9. 理论基础

### 9.1 睡眠神经生理学

#### EEG节律和睡眠阶段
- **清醒**：α(8-13 Hz)后部，β(13-30 Hz)前部
- **N1**：θ(4-8 Hz)，顶点尖波
- **N2**：睡眠纺锤波(11-15 Hz)，K复合波
- **N3**：δ(0.5-4 Hz) >20μV，>20% epoch
- **REM**：混合频率，快速眼动

#### 转换动力学
- **超日节律**：90-120分钟周期
- **睡眠压力**：稳态驱动
- **昼夜节律影响**：日间效应

### 9.2 信号处理理论

#### 奈奎斯特-香农定理
- **要求**：采样≥2×最大频率
- **EEG**：45 Hz最大→100 Hz采样充足

#### 时频权衡
- **不确定性原理**：Δt·Δf ≥ 1/4π
- **含义**：不能在两个域中精确定位
- **解决方案**：多尺度分析

## 10. 结论和未来展望

### 文献演变总结
1. **人工时代**（1968-2000）：人类专业知识，有限可扩展性
2. **经典机器学习**（2000-2015）：特征工程，中等准确率
3. **深度学习**（2015-2020）：端到端学习，突破性性能
4. **现代时代**（2020至今）：注意力、图、多模态

### 我们在领域中的位置
- **性能**：Sleep-EDF-20上新SOTA
- **创新**：混合损失、最优序列建模
- **效率**：实时能力、适中规模
- **影响**：解决关键临床需求（平衡性能）

### 未来方向
1. **即时**：跨数据集验证、临床测试
2. **短期**：可解释性、个性化
3. **长期**：基础模型、临床部署