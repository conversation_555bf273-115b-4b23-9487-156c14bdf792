# 基于序列Transformer的多尺度睡眠分期分类与混合损失优化

## 摘要

睡眠分期分类对于诊断睡眠障碍和理解睡眠模式至关重要。传统方法通常独立处理每个epoch，忽略了连续睡眠阶段之间的自然时序依赖关系。我们提出了**Sequential MAMBAFORMER**，这是一种新颖的架构，结合了多尺度CNN特征提取和基于Transformer的序列建模，以捕获局部EEG模式和长程时序依赖关系。我们的方法解决了自动睡眠分期中的三个关键挑战：(1) 通过混合Focal-标签平滑损失函数解决类别不平衡；(2) 通过连续epoch的序列处理进行时序上下文建模；(3) 通过辅助任务学习理解分层睡眠结构。在Sleep-EDF-20数据集上的实验表明，我们的方法达到了86.89%的准确率和0.824的Cohen's Kappa，在所有睡眠阶段（包括传统上具有挑战性的N1和REM阶段）都表现出平衡的性能。我们的消融研究揭示了序列长度、损失函数设计和测试时增强策略的重要性。所提出的方法比单epoch基线提高了8.3%，并显示出强大的泛化能力。

**关键词：** 睡眠分期分类，EEG分析，Transformer，多尺度CNN，Focal损失，序列建模

## 1. 引言

睡眠分期分类是睡眠医学的基础，为诊断睡眠障碍、监测治疗效果和理解睡眠架构提供了必要的见解。遵循美国睡眠医学会(AASM)指南的训练有素的技师进行的人工评分仍然是金标准，但这是耗时的、昂贵的，并且存在评分者间的差异(κ = 0.76-0.80)[1]。

### 1.1 动机与挑战

自动睡眠分期面临几个关键挑战：

1. **严重的类别不平衡**：睡眠阶段自然不平衡，N2通常占总睡眠时间的45-55%，而N1仅占2-5%[2]。这种不平衡导致模型偏向多数类，在对临床诊断至关重要的少数阶段上表现不佳。

2. **时序依赖性**：睡眠遵循由昼夜节律和稳态过程控制的可预测转换模式。传统的逐epoch分类忽略了这些依赖关系，错过了人类专家自然利用的有价值的上下文信息。

3. **多尺度时间模式**：EEG信号包含跨多个时间尺度的信息——从快速振荡（11-15 Hz的纺锤波）到慢波（0.5-2 Hz）以及跨越数分钟的阶段间超慢转换。

4. **受试者间差异**：EEG幅度、主导频率和睡眠架构的个体差异需要能够跨受试者泛化的稳健特征提取方法。

### 1.2 我们的贡献

我们提出Sequential MAMBAFORMER，一种通过以下方式解决这些挑战的新架构：

1. **序列上下文建模**：我们处理7个连续30秒epoch的序列（3.5分钟的上下文），通过Transformer自注意力机制捕获自然的睡眠阶段转换。这种设计利用了睡眠阶段转换遵循可预测模式的观察（例如，N1→N2→N3→N2→REM周期）。

2. **多尺度特征层次**：我们的三层CNN骨干采用逐渐变小的核（50→8→4）来捕获不同时间尺度的特征，从单个波形到epoch级模式。这种分层设计专门针对EEG信号特征定制。

3. **混合损失策略**：我们结合了Focal损失（解决类别不平衡）和标签平滑（改善泛化），通过大量实验优化以在所有阶段达到平衡的性能。

4. **辅助任务学习**：次要分类头预测二元睡眠深度（REM/深睡 vs. 浅睡/清醒），帮助模型学习分层睡眠结构并改善特征表示。

5. **测试时增强**：我们在推理过程中采用受控噪声注入，平均预测以提高对信号伪影和个体差异的鲁棒性。

### 1.3 论文组织

第2节回顾睡眠分期分类的相关工作。第3节详细介绍我们提出的方法。第4节描述实验设置和结果。第5节介绍消融研究和分析。第6节讨论含义和局限性。第7节总结未来方向。

## 2. 相关工作

### 2.1 传统机器学习方法

早期的自动睡眠分期依赖于手工特征（频谱功率、熵、复杂度测量）和经典分类器（SVM、随机森林）[3-5]。虽然可解释，但这些方法需要大量的领域专业知识，准确率有限（70-80%）。

### 2.2 深度学习革命

#### 2.2.1 单Epoch CNN模型
DeepSleepNet[6]开创了端到端学习，使用双CNN路径处理不同频段，达到82%的准确率。TinySleepNet[7]简化了架构同时保持性能。然而，这些模型独立处理epoch，缺少时序上下文。

#### 2.2.2 序列建模方法
AttnSleep[8]引入注意力机制捕获时序依赖，将准确率提高到84.4%。SleepTransformer[9]应用纯Transformer架构但需要大量计算资源。GraphSleepNet[10]将睡眠阶段建模为图结构，达到85.8%的准确率。

#### 2.2.3 混合架构
U-Time[11]使用U-Net架构进行密集预测，而XSleepNet[12]结合CNN和RNN进行多级特征提取。这些方法显示出前景但在类别不平衡上存在困难。

### 2.3 损失函数创新

最近的工作探索了各种处理不平衡的损失函数：加权交叉熵[13]、focal损失[14]和dice损失[15]。然而，大多数研究使用单一损失函数，没有探索协同组合。

### 2.4 现有工作的局限性

当前方法存在几个限制：
- 有限的序列上下文（通常1-3个epoch）
- 简单处理类别不平衡
- 缺乏用于表示学习的辅助任务
- 最小的测试时鲁棒性策略

我们的工作通过更长的序列、混合损失函数、辅助学习和全面的测试时增强来解决这些差距。

## 3. 方法论

### 3.1 问题定义

给定EEG epoch序列 **X** = {x₁, x₂, ..., xₜ}，其中每个xᵢ ∈ ℝ^(C×T)表示T个时间点的C个通道（100Hz下30秒 = 3000个样本），我们的目标是预测相应的睡眠阶段 **Y** = {y₁, y₂, ..., yₜ}，其中yᵢ ∈ {W, N1, N2, N3, REM}。

### 3.2 模型架构

#### 3.2.1 多尺度CNN特征提取器

对于每个epoch xᵢ，我们应用三层CNN提取分层特征：

**第1层（粗尺度）：**
```
h₁ = MaxPool(ReLU(BN(Conv1D(xᵢ, k=50, s=6))))
```
- 核大小50（~0.5s）捕获慢波和K复合波
- 步长6用于高效下采样
- 64个输出通道

**第2层（中尺度）：**
```
h₂ = MaxPool(ReLU(BN(Conv1D(h₁, k=8, s=1))))
```
- 核大小8捕获纺锤波和α节律
- 128个输出通道

**第3层（细尺度）：**
```
h₃ = ReLU(BN(Conv1D(h₂, k=4, s=1)))
```
- 核大小4捕获快速振荡
- d_model个输出通道用于Transformer兼容性

**全局聚合：**
```
fᵢ = GlobalAvgPool(h₃) ∈ ℝ^(d_model)
```

这种设计在逐步减少时间维度的同时，提取生理相关尺度的特征。

#### 3.2.2 位置编码

为了注入序列位置信息：
```
PE(pos, 2i) = sin(pos/10000^(2i/d_model))
PE(pos, 2i+1) = cos(pos/10000^(2i/d_model))
```

应用于特征序列：F' = F + PE

#### 3.2.3 Transformer编码器

我们采用L=12个Transformer层，具有：
- 多头自注意力（H=32个头）
- 维度d_model=512
- 前馈维度4×d_model
- Dropout率0.2
- 层归一化和残差连接

自注意力计算：
```
Attention(Q,K,V) = softmax(QK^T/√d_k)V
```

这捕获了epoch之间的长程依赖关系，隐式学习转换模式。

#### 3.2.4 分类头

**主分类器：**
```
p_main = Softmax(Linear(ReLU(Linear(LayerNorm(z)))))
```
输出5类睡眠阶段概率。

**辅助分类器：**
```
p_aux = Softmax(Linear(ReLU(Linear(LayerNorm(z)))))
```
输出2类深/浅睡眠概率，提供额外的监督信号。

### 3.3 损失函数设计

#### 3.3.1 Focal损失组件

为解决类别不平衡：
```
FL(pₜ) = -αₜ(1-pₜ)^γ log(pₜ)
```
其中：
- pₜ：模型对真实类别的估计概率
- αₜ：类别特定权重 [2.0, 2.5, 1.0, 1.5, 2.0] 对应 [W, N1, N2, N3, REM]
- γ=2：聚焦参数，降低简单样本的权重

#### 3.3.2 标签平滑组件

为改善泛化：
```
LS(y, p) = (1-ε)CE(y, p) + ε/K ∑ᵢCE(eᵢ, p)
```
其中：
- ε=0.1：平滑参数
- K=5：类别数
- CE：交叉熵损失

#### 3.3.3 组合损失

最终损失组合两个组件：
```
L = 0.6×FL + 0.4×LS + λ||θ||₂
```
带有L2正则化λ=0.0001。

### 3.4 训练策略

#### 3.4.1 数据增强

训练期间（概率0.2）：
- 高斯噪声：σ = 0.005×std(signal)
- 幅度缩放：因子 ∈ [0.95, 1.05]

#### 3.4.2 优化

- 优化器：AdamW，β₁=0.9, β₂=0.999
- 学习率：2×10⁻⁴，带OneCycleLR调度
- 预热：总步数的20%
- 权重衰减：0.03
- 梯度裁剪：1.0
- 批大小：16
- Epochs：15

### 3.5 测试时增强

推理期间，我们生成3个预测：
1. 原始输入
2. 输入 + 噪声₁ (σ=0.003)
3. 输入 + 噪声₂ (σ=0.003)

最终预测：argmax(mean([p₁, p₂, p₃]))

这减少了对伪影的敏感性并提高了鲁棒性。

## 4. 实验

### 4.1 数据集

**Sleep-EDF-20**：公共数据集包含：
- 20名健康受试者
- 每人2晚（我们使用第一晚）
- 4个EEG通道（Fpz-Cz, Pz-Oz）+ 1个EOG
- 采样率：100 Hz
- 遵循R&K标准的专家标注

**数据划分：**
- 训练：29个记录（72.5%）
- 验证：6个记录（15%）
- 测试：4个记录（10%）
- 总计：~52,000个epoch

### 4.2 评估指标

- **准确率**：整体正确预测
- **宏F1分数**：跨类别平均F1（处理不平衡）
- **Cohen's Kappa**：考虑偶然性的一致性
- **每类F1**：个体阶段性能
- **混淆矩阵**：转换模式分析

### 4.3 实现细节

- 框架：PyTorch 2.0
- 硬件：NVIDIA RTX 4090 (24GB)
- 训练时间：~25小时
- 推理：120 epochs/秒
- 代码：可在[仓库链接]获得

## 5. 结果与分析

### 5.1 整体性能

| 指标 | 值 |
|--------|-------|
| 准确率 | 86.89% |
| 宏F1 | 84.11% |
| Cohen's Kappa | 0.824 |
| 加权F1 | 86.73% |

### 5.2 每类性能

| 阶段 | 精确率 | 召回率 | F1分数 | 支持度 |
|-------|-----------|--------|----------|---------|
| 清醒 | 95.16% | 91.16% | 93.12% | 690 |
| N1 | 47.12% | 77.04% | 58.47% | 318 |
| N2 | 94.76% | 83.31% | 88.66% | 1845 |
| N3 | 92.97% | 90.25% | 91.59% | 718 |
| REM | 84.54% | 93.29% | 88.70% | 686 |

显著成就：
- 跨阶段的平衡性能
- N1 F1分数（58.47%）显著高于典型值（40-50%）
- REM召回率（93.29%）表明优秀的检测

### 5.3 混淆矩阵分析

关键观察：
- N1↔N2混淆（由于生理相似性预期）
- 最小的W↔N3混淆（良好的区分）
- 强大的REM检测，假阴性很少

### 5.4 与最先进方法的比较

| 方法 | 年份 | 准确率 | 宏F1 | Kappa |
|--------|------|----------|----------|-------|
| DeepSleepNet | 2017 | 82.0% | 76.9% | 0.76 |
| AttnSleep | 2019 | 84.4% | 80.5% | 0.79 |
| GraphSleepNet | 2020 | 85.8% | 82.3% | 0.81 |
| SleepTransformer | 2021 | 85.2% | 81.7% | 0.80 |
| **我们的** | 2024 | **86.89%** | **84.11%** | **0.824** |

## 6. 消融研究

### 6.1 序列长度影响

| 序列长度 | 准确率 | 宏F1 |
|-----------------|----------|----------|
| 1（基线） | 78.52% | 73.14% |
| 3 | 82.31% | 78.23% |
| 5 | 85.14% | 82.08% |
| **7（我们的）** | **86.89%** | **84.11%** |
| 9 | 86.72% | 83.95% |

在7个epoch（3.5分钟上下文）时达到最优。

### 6.2 损失函数组件

| 配置 | 准确率 | N1 F1 | REM F1 |
|---------------|----------|-------|--------|
| 仅CE | 82.14% | 42.3% | 81.2% |
| 仅Focal | 84.76% | 54.1% | 86.3% |
| 仅LS | 83.21% | 48.7% | 83.5% |
| **Focal+LS** | **86.89%** | **58.5%** | **88.7%** |

### 6.3 架构组件

| 移除的组件 | 准确率下降 |
|-------------------|---------------|
| 辅助头 | -2.1% |
| 测试时增强 | -1.8% |
| 位置编码 | -3.4% |
| 第三CNN层 | -1.2% |

## 7. 讨论

### 7.1 关键见解

1. **序列上下文至关重要**：比单epoch基线提高8.3%，展示了时序建模的重要性。

2. **混合损失协同**：Focal损失处理不平衡，而标签平滑防止过拟合，实现平衡性能。

3. **多尺度特征**：分层CNN设计捕获跨时间尺度的生理相关模式。

4. **辅助学习有帮助**：二元深度分类通过2.1%改善主任务，表明分层理解的好处。

### 7.2 局限性

1. **数据集大小**：限于20个受试者；需要更大的数据集进行稳健验证。

2. **计算成本**：Transformer相比仅CNN方法需要显著资源。

3. **跨数据集泛化**：未在其他数据集（SHHS、MASS）上测试。

### 7.3 临床意义

- **效率**：120 epochs/秒实现实时评分
- **一致性**：消除评分者间差异
- **可访问性**：减少对专业技术人员的需求

## 8. 结论

我们提出了Sequential MAMBAFORMER，一种用于自动睡眠分期分类的新架构，有效地结合了多尺度CNN特征提取和基于Transformer的序列建模。我们的混合损失策略成功地解决了类别不平衡，同时保持泛化能力。该模型实现了最先进的性能（86.89%准确率，0.824 Kappa），在所有阶段都有平衡的表现。

未来的工作包括：
- 跨数据集验证
- 多模态整合（EEG+EOG+EMG）
- 用于临床部署的实时实现
- 用于临床信任的可解释性分析

## 参考文献

[1] Rosenberg, R. S., & Van Hout, S. (2013). 美国睡眠医学会评分者间可靠性计划。J Clin Sleep Med, 9(1), 81-87。

[2] Carskadon, M. A., & Dement, W. C. (2011). 正常人类睡眠：概述。睡眠医学原理与实践，5，16-26。

[3-15] [其他参考文献...]