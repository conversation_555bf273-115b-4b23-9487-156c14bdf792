2025-08-18 14:47:38,729 - INFO - ================================================================================
2025-08-18 14:47:38,730 - INFO - 🎯 FINAL TEST FIXED - Fixing REM accuracy issue
2025-08-18 14:47:38,730 - INFO - ================================================================================
2025-08-18 14:47:38,775 - INFO - Device: cuda
2025-08-18 14:47:38,775 - INFO - 🔧 FIXED: Loading ALL data with max_samples_per_file=None
2025-08-18 14:47:49,093 - INFO - 从 29 个文件加载了 31905 个epochs, 创建了 31731 个序列
2025-08-18 14:47:49,093 - INFO - 创建序列数据集: 31731个序列, 序列长度=7, 通道数=4, 总epochs=31905
2025-08-18 14:47:49,728 - INFO - 从 6 个文件加载了 6146 个epochs, 创建了 6110 个序列
2025-08-18 14:47:49,728 - INFO - 创建序列数据集: 6110个序列, 序列长度=7, 通道数=4, 总epochs=6146
2025-08-18 14:47:50,636 - INFO - 从 4 个文件加载了 4257 个epochs, 创建了 4233 个序列
2025-08-18 14:47:50,636 - INFO - 创建序列数据集: 4233个序列, 序列长度=7, 通道数=4, 总epochs=4257
2025-08-18 14:47:50,636 - INFO - Dataset sizes: Train=31731, Val=6110, Test=4233
2025-08-18 14:47:50,636 - INFO - 
Calculating class distribution...
2025-08-18 14:47:58,506 - INFO - Train class counts: W=6360, N1=1929, N2=13326, N3=4198, REM=5915
2025-08-18 14:47:58,508 - INFO - Auto-calculated weights: [0.67904697 2.2380403  0.32411029 1.02867773 0.73012471]
2025-08-18 14:47:58,942 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=38,368,583, d_model=512, n_heads=32, n_layers=12
2025-08-18 14:47:59,006 - INFO - 
Model Parameters: 38,368,583
2025-08-18 14:47:59,007 - INFO - Using class weights: W=2.0, N1=2.5, N2=1.0, N3=1.5, REM=2.0
2025-08-18 14:48:00,387 - INFO - 
🏋️ Starting Fixed Training...
2025-08-18 14:48:00,387 - INFO - ================================================================================
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/final_test_90_fixed.py", line 560, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/final_test_90_fixed.py", line 457, in main
    train_loss, train_acc, train_f1 = train_one_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/final_test_90_fixed.py", line 133, in train_one_epoch
    output, _ = model(data)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/models/sequential_mambaformer_v2.py", line 178, in forward
    features = self.feature_extractor(x_reshaped)  # (batch*seq_len, d_model)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/models/sequential_mambaformer_v2.py", line 74, in forward
    x = self.conv_layers(x)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/container.py", line 240, in forward
    input = module(input)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Given groups=1, weight of size [64, 3, 50], expected input[112, 4, 3000] to have 3 channels, but got 4 channels instead
