"""
Progressive MAMBAFORMER V1 - 第1阶段：多模态特征精炼
目标：实现模态内特征精炼，让每个模态先"审视自身"
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging


class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class LocalAttention(nn.Module):
    """局部注意力机制 - 用于EEG短程事件捕捉"""
    def __init__(self, d_model, window_size=100, n_heads=4, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        self.window_size = window_size
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        self.W_q = nn.Linear(d_model, d_model)
        self.W_k = nn.Linear(d_model, d_model)
        self.W_v = nn.Linear(d_model, d_model)
        self.W_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        """
        Args:
            x: (batch, time, d_model)
        Returns:
            output: (batch, time, d_model)
        """
        batch_size, seq_len, d_model = x.shape
        residual = x
        
        # Linear transformations and split into heads
        Q = self.W_q(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.W_k(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        V = self.W_v(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        
        # Compute attention scores with local window
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        # Create local attention mask
        mask = self._create_local_mask(seq_len, self.window_size).to(x.device)
        scores = scores.masked_fill(mask == 0, -1e9)
        
        # Apply softmax and dropout
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # Apply attention to values
        context = torch.matmul(attn_weights, V)
        
        # Reshape and apply output projection
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, d_model)
        output = self.W_o(context)
        
        # Add residual and normalize
        output = self.layer_norm(output + residual)
        
        return output
    
    def _create_local_mask(self, seq_len, window_size):
        """创建局部注意力掩码"""
        mask = torch.zeros(seq_len, seq_len)
        for i in range(seq_len):
            start = max(0, i - window_size // 2)
            end = min(seq_len, i + window_size // 2 + 1)
            mask[i, start:end] = 1
        return mask.unsqueeze(0).unsqueeze(0)  # (1, 1, seq_len, seq_len)


class LightweightCNN(nn.Module):
    """轻量级CNN - 用于EOG/EMG特征提取"""
    def __init__(self, input_channels=1, d_model=128, dropout=0.15):
        super().__init__()
        
        self.conv_layers = nn.Sequential(
            # 第一层 - 捕捉粗粒度模式
            nn.Conv1d(input_channels, 32, kernel_size=100, stride=10),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            # 第二层 - 进一步抽象
            nn.Conv1d(32, 64, kernel_size=10, stride=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(2, stride=2),
            
            # 第三层 - 映射到d_model
            nn.Conv1d(64, d_model, kernel_size=5, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
    
    def forward(self, x):
        """
        Args:
            x: (batch, 1, time_steps) - 单通道信号
        Returns:
            features: (batch, d_model)
        """
        x = self.conv_layers(x)
        x = self.global_pool(x)
        x = x.squeeze(-1)
        return x


class MultiModalFeatureExtractor(nn.Module):
    """多模态特征精炼器 - 第1阶段核心创新"""
    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()
        
        # EEG特征提取 - 使用CNN + 局部注意力
        self.eeg_cnn = nn.Sequential(
            nn.Conv1d(1, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(128, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # EEG局部注意力
        self.eeg_local_attention = LocalAttention(d_model, window_size=50, n_heads=4, dropout=dropout)
        
        # EOG特征提取 - 轻量级CNN
        self.eog_extractor = LightweightCNN(input_channels=1, d_model=d_model, dropout=dropout)
        
        # EMG特征提取 - 轻量级CNN
        self.emg_extractor = LightweightCNN(input_channels=1, d_model=d_model, dropout=dropout)
        
        # 特征归一化
        self.eeg_norm = nn.LayerNorm(d_model)
        self.eog_norm = nn.LayerNorm(d_model)
        self.emg_norm = nn.LayerNorm(d_model)
        
        # 特征融合（暂时简单拼接，第3阶段会改为门控融合）
        self.fusion_layer = nn.Sequential(
            nn.Linear(d_model * 3, d_model * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 2, d_model),
            nn.LayerNorm(d_model)
        )
        
    def forward(self, x):
        """
        Args:
            x: (batch, channels=3, time_steps) 
               channels顺序: [EEG, EOG, EMG]
        Returns:
            refined_features: (batch, d_model)
        """
        batch_size = x.size(0)
        
        # 分离三个模态
        eeg = x[:, 0:1, :]  # (batch, 1, time_steps)
        eog = x[:, 1:2, :]  # (batch, 1, time_steps)
        emg = x[:, 2:3, :]  # (batch, 1, time_steps)
        
        # EEG特征精炼：CNN + 局部注意力
        eeg_features = self.eeg_cnn(eeg)  # (batch, d_model, time')
        # 转换维度用于注意力
        eeg_features = eeg_features.transpose(1, 2)  # (batch, time', d_model)
        eeg_features = self.eeg_local_attention(eeg_features)  # (batch, time', d_model)
        # 全局池化
        eeg_features = eeg_features.mean(dim=1)  # (batch, d_model)
        eeg_features = self.eeg_norm(eeg_features)
        
        # EOG特征精炼：轻量级CNN
        eog_features = self.eog_extractor(eog)  # (batch, d_model)
        eog_features = self.eog_norm(eog_features)
        
        # EMG特征精炼：轻量级CNN
        emg_features = self.emg_extractor(emg)  # (batch, d_model)
        emg_features = self.emg_norm(emg_features)
        
        # 特征融合（简单版本，后续会改进）
        concat_features = torch.cat([eeg_features, eog_features, emg_features], dim=-1)
        refined_features = self.fusion_layer(concat_features)
        
        return refined_features, eeg_features, eog_features, emg_features


class ProgressiveMAMBAFORMER_V1(nn.Module):
    """
    渐进式MAMBAFORMER模型 V1 - 第1阶段
    融入多模态特征精炼
    """
    def __init__(self, input_channels=3, n_classes=5, d_model=128, 
                 n_heads=8, n_layers=4, dropout=0.15, seq_len=5):
        super().__init__()
        
        self.input_channels = input_channels
        self.n_classes = n_classes
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 多模态特征精炼器（第1阶段核心）
        self.multimodal_extractor = MultiModalFeatureExtractor(d_model, dropout)
        
        # 时序位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # Transformer编码器（处理时序关系）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=n_layers
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 辅助分类器
        self.auxiliary_head = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 4, 2)  # REM/SWS vs 其他
        )
        
        # 权重初始化
        self._init_weights()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建ProgressiveMAMBAFORMER_V1: 参数量={total_params:,}, "
                    f"d_model={d_model}, n_heads={n_heads}, n_layers={n_layers}")
        logging.info("第1阶段特性：多模态特征精炼 - EEG局部注意力 + EOG/EMG轻量CNN")
    
    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.8)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, time_steps, channels)
        Returns:
            main_output: (batch, seq_len, n_classes)
            aux_output: (batch, seq_len, 2)
        """
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 重塑为 (batch*seq_len, channels, time_steps) 进行特征提取
        x_reshaped = x.view(batch_size * seq_len, time_steps, channels).transpose(1, 2)
        
        # 多模态特征精炼（第1阶段核心）
        refined_features, eeg_feat, eog_feat, emg_feat = self.multimodal_extractor(x_reshaped)
        
        # 重塑回序列形式
        features = refined_features.view(batch_size, seq_len, self.d_model)
        
        # 添加位置编码
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # 转回 (batch, seq_len, d_model)
        
        # Transformer编码
        encoded_features = self.transformer_encoder(features)
        
        # 分类预测
        main_output = self.classifier(encoded_features)
        aux_output = self.auxiliary_head(encoded_features)
        
        return main_output, aux_output


# 损失函数继承自V2
class SequentialFocalLoss(nn.Module):
    """序列Focal损失"""
    def __init__(self, alpha=1, gamma=2):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, inputs, targets):
        inputs = inputs.view(-1, inputs.size(-1))
        targets = targets.view(-1)
        
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if isinstance(self.alpha, torch.Tensor):
            alpha_t = self.alpha.gather(0, targets)
            focal_loss = alpha_t * focal_loss
        else:
            focal_loss = self.alpha * focal_loss
        
        return focal_loss.mean()


class TemporalConsistencyLoss(nn.Module):
    """时序一致性损失"""
    def __init__(self, weight=0.1):
        super().__init__()
        self.weight = weight
    
    def forward(self, predictions):
        if predictions.size(1) <= 1:
            return torch.tensor(0.0, device=predictions.device)
        
        diff = predictions[:, 1:] - predictions[:, :-1]
        consistency_loss = torch.mean(torch.sum(diff ** 2, dim=-1))
        
        return self.weight * consistency_loss