"""
序列MAMBAFORMER模型 V2 - Phase 1改进版
目标：在保持Phase 1优秀性能的基础上，适度解决过拟合问题
策略：温和的正则化，避免过度工程化
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging


class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class EnhancedEpochFeatureExtractor(nn.Module):
    """增强的单个epoch特征提取器 - 增加适度正则化"""
    def __init__(self, input_channels=3, d_model=128, dropout=0.15):
        super().__init__()
        
        # CNN特征提取 - 与Phase 1保持一致，但加入dropout
        self.conv_layers = nn.Sequential(
            # 第一层
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),  # 轻微dropout
            nn.MaxPool1d(8, stride=8),
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            # 第三层
            nn.Conv1d(128, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)  # 更轻的dropout
        )
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
    
    def forward(self, x):
        """
        Args:
            x: (batch, channels, time_steps)
        Returns:
            features: (batch, d_model)
        """
        x = self.conv_layers(x)
        x = self.global_pool(x)
        x = x.squeeze(-1)
        return x


class SequentialMAMBAFORMER_V2(nn.Module):
    """
    序列MAMBAFORMER模型 V2 - Phase 1改进版
    输入: (batch, seq_len, time_steps, channels)
    输出: (batch, seq_len, n_classes)
    
    主要改进：
    1. 适度增加dropout (0.1 -> 0.15)
    2. 增强数据增强兼容性
    3. 改进权重初始化
    4. 保持Phase 1的核心优势
    """
    def __init__(self, input_channels=3, n_classes=5, d_model=128, 
                 n_heads=8, n_layers=4, dropout=0.15, seq_len=5):
        super().__init__()
        
        self.input_channels = input_channels
        self.n_classes = n_classes
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 增强的Epoch特征提取器
        self.feature_extractor = EnhancedEpochFeatureExtractor(
            input_channels, d_model, dropout
        )
        
        # 时序位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # Transformer编码器（处理时序关系）- 与Phase 1保持一致
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=n_layers
        )
        
        # 分类器 - 适度增加正则化
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 辅助分类器 - 保持简单
        self.auxiliary_head = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 4, 2)  # REM/SWS vs 其他
        )
        
        # 改进权重初始化
        self._init_weights()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建SequentialMAMBAFORMER_V2: 参数量={total_params:,}, "
                    f"d_model={d_model}, n_heads={n_heads}, n_layers={n_layers}")
    
    def _init_weights(self):
        """改进的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.8)  # 稍微保守
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, time_steps, channels)
        Returns:
            main_output: (batch, seq_len, n_classes)
            aux_output: (batch, seq_len, 2)
        """
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 重塑为 (batch*seq_len, channels, time_steps) 进行特征提取
        x_reshaped = x.view(batch_size * seq_len, time_steps, channels).transpose(1, 2)
        
        # 提取每个epoch的特征
        features = self.feature_extractor(x_reshaped)  # (batch*seq_len, d_model)

        # 重塑回序列形式
        features = features.view(batch_size, seq_len, self.d_model)
        
        # 添加位置编码并转换为 (seq_len, batch, d_model) 格式
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # 转回 (batch, seq_len, d_model)
        
        # Transformer编码
        encoded_features = self.transformer_encoder(features)
        
        # 分类预测
        main_output = self.classifier(encoded_features)  # (batch, seq_len, n_classes)
        aux_output = self.auxiliary_head(encoded_features)  # (batch, seq_len, 2)
        
        return main_output, aux_output


# 损失函数保持与Phase 1一致
class SequentialFocalLoss(nn.Module):
    """序列Focal损失"""
    def __init__(self, alpha=1, gamma=2):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, inputs, targets):
        # 重塑输入
        inputs = inputs.view(-1, inputs.size(-1))  # (batch*seq_len, n_classes)
        targets = targets.view(-1)  # (batch*seq_len,)
        
        # 计算focal loss
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        # 如果alpha是tensor（类权重），需要根据targets索引
        if isinstance(self.alpha, torch.Tensor):
            alpha_t = self.alpha.gather(0, targets)
            focal_loss = alpha_t * focal_loss
        else:
            focal_loss = self.alpha * focal_loss
        
        return focal_loss.mean()


class TemporalConsistencyLoss(nn.Module):
    """时序一致性损失 - 保持与Phase 1一致"""
    def __init__(self, weight=0.1):
        super().__init__()
        self.weight = weight
    
    def forward(self, predictions):
        """
        Args:
            predictions: (batch, seq_len, n_classes)
        """
        if predictions.size(1) <= 1:
            return torch.tensor(0.0, device=predictions.device)
        
        # 计算相邻预测的差异
        diff = predictions[:, 1:] - predictions[:, :-1]
        consistency_loss = torch.mean(torch.sum(diff ** 2, dim=-1))
        
        return self.weight * consistency_loss


# 温和的数据增强
class MildDataAugmentation:
    """温和的数据增强 - 避免破坏数据结构"""
    def __init__(self, p=0.5):
        self.p = p
    
    def __call__(self, data, labels):
        if torch.rand(1).item() > self.p:
            return data, labels
        
        # 只使用安全的增强方法
        # 1. 轻微的高斯噪声
        if torch.rand(1).item() < 0.3:
            noise_std = data.std() * 0.02  # 很小的噪声
            noise = torch.randn_like(data) * noise_std
            data = data + noise
        
        # 2. 轻微的幅度缩放
        if torch.rand(1).item() < 0.3:
            scale = torch.FloatTensor(1).uniform_(0.95, 1.05).to(data.device)
            data = data * scale
        
        return data, labels