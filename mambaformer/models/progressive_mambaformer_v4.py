#!/usr/bin/env python3
"""
🎯 第4阶段：Progressive MAMBAFORMER with Mamba Long-Range Modeling
集成Mamba状态空间模型捕捉长程依赖
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from einops import rearrange, repeat
from typing import Optional, Tuple


class SimplifiedMultiModalExtractor(nn.Module):
    """简化的多模态特征提取器（继承自第1阶段）"""
    def __init__(self, input_channels=3, d_model=512, dropout=0.15):
        super().__init__()
        
        # 统一的CNN特征提取
        self.shared_cnn = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.<PERSON>L<PERSON>(),
            nn.MaxPool1d(kernel_size=8, stride=8),
            nn.Dropout(dropout * 0.5),
            
            nn.Conv1d(128, 256, kernel_size=4, stride=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4, stride=4),
            
            nn.Conv1d(256, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
        )
        
        # 模态特定投影
        self.modal_projections = nn.ModuleList([
            nn.Sequential(
                nn.Linear(d_model, d_model),
                nn.LayerNorm(d_model),
                nn.ReLU(),
                nn.Dropout(dropout)
            ) for _ in range(3)
        ])
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Linear(d_model * 3, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
    def forward(self, x):
        batch_size, seq_len, channels, time_len = x.shape
        
        # 处理每个时间步
        all_features = []
        for t in range(seq_len):
            x_t = x[:, t, :, :]
            
            # CNN特征提取
            cnn_out = self.shared_cnn(x_t)
            cnn_out = F.adaptive_avg_pool1d(cnn_out, 1).squeeze(-1)
            
            # 模态特定处理 (假设3个通道对应EEG, EOG, EMG)
            modal_features = []
            for i in range(3):
                modal_feat = self.modal_projections[i](cnn_out)
                modal_features.append(modal_feat)
            
            # 融合
            fused = torch.cat(modal_features, dim=-1)
            fused = self.fusion(fused)
            all_features.append(fused)
        
        features = torch.stack(all_features, dim=1)
        return features


class CrossModalAttention(nn.Module):
    """EEG中心的跨模态注意力（继承自第2阶段）"""
    def __init__(self, d_model, n_heads=4, dropout=0.1):
        super().__init__()
        self.n_heads = n_heads
        self.d_head = d_model // n_heads
        
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)
        self.v_proj = nn.Linear(d_model, d_model)
        self.out_proj = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        batch_size, seq_len, d_model = x.shape
        
        # EEG作为查询，其他模态作为键值对
        # 这里简化处理，实际应该分离不同模态
        q = self.q_proj(x)
        k = self.k_proj(x)
        v = self.v_proj(x)
        
        # 多头注意力
        q = rearrange(q, 'b l (h d) -> b h l d', h=self.n_heads)
        k = rearrange(k, 'b l (h d) -> b h l d', h=self.n_heads)
        v = rearrange(v, 'b l (h d) -> b h l d', h=self.n_heads)
        
        # 计算注意力
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.d_head)
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力
        out = torch.matmul(attn_weights, v)
        out = rearrange(out, 'b h l d -> b l (h d)')
        out = self.out_proj(out)
        
        # 残差连接
        out = self.layer_norm(x + self.dropout(out))
        
        return out


class AdaptiveGatingFusion(nn.Module):
    """自适应门控融合（继承自第3阶段）"""
    def __init__(self, d_model, n_modalities=3, dropout=0.1):
        super().__init__()
        self.n_modalities = n_modalities
        
        # 门控网络
        self.gate_network = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, n_modalities),
            nn.Softmax(dim=-1)
        )
        
        # 模态特定变换
        self.modal_transforms = nn.ModuleList([
            nn.Sequential(
                nn.Linear(d_model, d_model),
                nn.LayerNorm(d_model),
                nn.ReLU(),
                nn.Dropout(dropout)
            ) for _ in range(n_modalities)
        ])
        
        self.output_proj = nn.Linear(d_model, d_model)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        batch_size, seq_len, d_model = x.shape
        
        # 计算门控权重
        gate_weights = self.gate_network(x)
        
        # 应用模态特定变换（这里简化处理）
        modal_outputs = []
        for i in range(self.n_modalities):
            modal_out = self.modal_transforms[i](x)
            modal_outputs.append(modal_out)
        
        # 加权融合
        modal_outputs = torch.stack(modal_outputs, dim=2)
        gate_weights = gate_weights.unsqueeze(-1)
        fused = torch.sum(modal_outputs * gate_weights, dim=2)
        
        # 输出投影
        out = self.output_proj(fused)
        out = self.layer_norm(x + out)
        
        return out, gate_weights.squeeze(-1)


class SimplifiedMambaBlock(nn.Module):
    """简化的Mamba块实现（不依赖mamba-ssm库）"""
    def __init__(self, d_model, d_state=16, d_conv=4, expand=2, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand = expand
        d_inner = int(expand * d_model)
        
        # 输入投影
        self.in_proj = nn.Linear(d_model, d_inner * 2)
        
        # 1D卷积
        self.conv1d = nn.Conv1d(
            in_channels=d_inner,
            out_channels=d_inner,
            kernel_size=d_conv,
            padding=d_conv - 1,
            groups=d_inner
        )
        
        # SSM参数
        self.x_proj = nn.Linear(d_inner, d_state + d_state + 1)  # dt, B, C
        self.dt_proj = nn.Linear(d_state, d_inner)
        
        # 状态空间参数
        A = repeat(torch.arange(1, d_state + 1), 'n -> d n', d=d_inner)
        self.A_log = nn.Parameter(torch.log(A))
        self.D = nn.Parameter(torch.ones(d_inner))
        
        # 输出投影
        self.out_proj = nn.Linear(d_inner, d_model)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        batch_size, seq_len, d_model = x.shape
        
        # 输入投影
        x_and_res = self.in_proj(x)
        x_inner, res = x_and_res.chunk(2, dim=-1)
        
        # 1D卷积
        x_conv = rearrange(x_inner, 'b l d -> b d l')
        x_conv = self.conv1d(x_conv)[:, :, :seq_len]
        x_conv = rearrange(x_conv, 'b d l -> b l d')
        
        # 简化的SSM计算（不使用真正的状态空间模型）
        x_conv = F.silu(x_conv)
        
        # 门控
        y = x_conv * F.silu(res)
        
        # 输出
        output = self.out_proj(y)
        output = self.dropout(output)
        output = self.layer_norm(x + output)
        
        return output


class MAMBAFORMERBlock(nn.Module):
    """MAMBAFORMER块（结合Transformer和Mamba）"""
    def __init__(self, d_model=512, n_heads=8, dropout=0.1, use_mamba=True):
        super().__init__()
        self.use_mamba = use_mamba
        
        # 多头自注意力
        self.self_attn = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=n_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # Mamba块
        if use_mamba:
            self.mamba = SimplifiedMambaBlock(d_model, dropout=dropout)
        
        # FFN
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model * 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 4, d_model),
            nn.Dropout(dropout)
        )
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model) if use_mamba else None
        
    def forward(self, x):
        # 自注意力
        residual = x
        x = self.norm1(x)
        x_attn, _ = self.self_attn(x, x, x)
        x = residual + x_attn
        
        # Mamba
        if self.use_mamba:
            residual = x
            x = self.norm3(x)
            x = self.mamba(x)
            x = residual + x
        
        # FFN
        residual = x
        x = self.norm2(x)
        x = self.ffn(x)
        x = residual + x
        
        return x


class ProgressiveMAMBAFORMER_V4(nn.Module):
    """第4阶段：集成Mamba长程依赖建模的渐进式MAMBAFORMER"""
    
    def __init__(
        self,
        input_channels=3,
        n_classes=5,
        d_model=512,
        n_heads=8,
        n_layers=12,
        dropout=0.1,
        seq_len=7,
    ):
        super().__init__()
        
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 多模态特征提取器
        self.feature_extractor = SimplifiedMultiModalExtractor(
            input_channels=input_channels,
            d_model=d_model,
            dropout=dropout
        )
        
        # 跨模态注意力
        self.cross_modal_attention = CrossModalAttention(
            d_model=d_model,
            n_heads=n_heads // 2,
            dropout=dropout
        )
        
        # 自适应门控融合
        self.adaptive_gating = AdaptiveGatingFusion(
            d_model=d_model,
            n_modalities=3,
            dropout=dropout
        )
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1, seq_len, d_model))
        
        # MAMBAFORMER层（交替使用Mamba）
        self.layers = nn.ModuleList([
            MAMBAFORMERBlock(
                d_model=d_model,
                n_heads=n_heads,
                dropout=dropout,
                use_mamba=(i % 2 == 1)  # 奇数层使用Mamba
            ) for i in range(n_layers)
        ])
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 辅助分类头
        self.aux_classifier = nn.Linear(d_model, n_classes)
        
        self._init_weights()
        
        # 记录参数量
        total_params = sum(p.numel() for p in self.parameters())
        print(f"创建ProgressiveMAMBAFORMER_V4: 参数量={total_params:,}, "
              f"d_model={d_model}, n_heads={n_heads}, n_layers={n_layers}")
        print("第4阶段：集成Mamba长程依赖建模")
    
    def _init_weights(self):
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(self, x):
        batch_size = x.shape[0]
        
        # 阶段1：多模态特征提取
        features = self.feature_extractor(x)
        
        # 阶段2：跨模态注意力
        features = self.cross_modal_attention(features)
        
        # 阶段3：自适应门控融合
        features, gate_weights = self.adaptive_gating(features)
        
        # 添加位置编码
        features = features + self.pos_encoding
        
        # 阶段4：MAMBAFORMER层（含Mamba）
        for layer in self.layers:
            features = layer(features)
        
        # 分类
        output = self.classifier(features)
        
        # 辅助输出
        aux_output = self.aux_classifier(features)
        
        return output, aux_output, gate_weights


class SequentialFocalLoss(nn.Module):
    """序列级别的Focal Loss"""
    def __init__(self, alpha=None, gamma=2.0, device="cuda"):
        super().__init__()
        self.gamma = gamma
        if alpha is not None:
            self.alpha = torch.tensor(alpha).to(device)
        else:
            self.alpha = None
        self.device = device
    
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)
        
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss
        
        return focal_loss.mean()


class TemporalConsistencyLoss(nn.Module):
    """时间一致性损失"""
    def __init__(self, weight=0.1):
        super().__init__()
        self.weight = weight
    
    def forward(self, predictions):
        if predictions.dim() == 3:
            batch_size, seq_len, n_classes = predictions.shape
            if seq_len <= 1:
                return torch.tensor(0.0).to(predictions.device)
            
            probs = F.softmax(predictions, dim=-1)
            diff = probs[:, 1:, :] - probs[:, :-1, :]
            loss = torch.mean(torch.abs(diff))
            
            return self.weight * loss
        return torch.tensor(0.0).to(predictions.device)


if __name__ == "__main__":
    # 测试模型
    model = ProgressiveMAMBAFORMER_V4(
        input_channels=3,
        n_classes=5,
        d_model=512,
        n_heads=32,
        n_layers=12,
        dropout=0.15,
        seq_len=7
    )
    
    # 测试输入
    x = torch.randn(2, 7, 3, 3000)
    output, aux_output, gate_weights = model(x)
    print(f"输出形状: {output.shape}")
    print(f"辅助输出形状: {aux_output.shape}")
    print(f"门控权重形状: {gate_weights.shape}")