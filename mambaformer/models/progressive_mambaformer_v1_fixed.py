"""
Progressive MAMBAFORMER V1-Fixed - 修复版本
简化架构，确保基础功能正常
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import logging


class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class SimplifiedMultiModalExtractor(nn.Module):
    """简化的多模态特征提取器 - 确保稳定训练"""
    def __init__(self, input_channels=3, d_model=512, dropout=0.15):
        super().__init__()
        
        # 统一的CNN特征提取（保持与baseline相似）
        self.shared_cnn = nn.Sequential(
            # 第一层
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            # 第三层
            nn.Conv1d(128, 256, kernel_size=4, stride=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3),
            
            # 第四层 - 映射到d_model
            nn.Conv1d(256, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU()
        )
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 简单的模态特定投影（可选）
        self.modal_projections = nn.ModuleList([
            nn.Sequential(
                nn.Linear(d_model, d_model),
                nn.LayerNorm(d_model),
                nn.ReLU(),
                nn.Dropout(dropout)
            ) for _ in range(3)
        ])
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Linear(d_model * 3, d_model * 2),
            nn.LayerNorm(d_model * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 2, d_model),
            nn.LayerNorm(d_model)
        )
        
    def forward(self, x):
        """
        Args:
            x: (batch, channels=3, time_steps)
        Returns:
            features: (batch, d_model)
        """
        batch_size = x.size(0)
        
        # 整体提取特征（所有通道一起）
        shared_features = self.shared_cnn(x)  # (batch, d_model, time')
        shared_features = self.global_pool(shared_features)  # (batch, d_model, 1)
        shared_features = shared_features.squeeze(-1)  # (batch, d_model)
        
        # 分离每个模态并应用特定投影
        modal_features = []
        
        # 为每个模态单独处理
        for i in range(3):
            modal_x = x[:, i:i+1, :]  # (batch, 1, time_steps)
            # 扩展到3通道以复用CNN
            modal_x = modal_x.repeat(1, 3, 1) if modal_x.size(1) == 1 else modal_x
            
            # 提取特征
            modal_feat = self.shared_cnn(modal_x)
            modal_feat = self.global_pool(modal_feat).squeeze(-1)
            
            # 模态特定投影
            modal_feat = self.modal_projections[i](modal_feat)
            modal_features.append(modal_feat)
        
        # 融合所有模态特征
        concat_features = torch.cat(modal_features, dim=-1)  # (batch, d_model * 3)
        fused_features = self.fusion(concat_features)  # (batch, d_model)
        
        return fused_features


class ProgressiveMAMBAFORMER_V1_Fixed(nn.Module):
    """
    修复版本的渐进式MAMBAFORMER V1
    简化架构，确保稳定训练
    """
    def __init__(self, input_channels=3, n_classes=5, d_model=512, 
                 n_heads=32, n_layers=12, dropout=0.15, seq_len=7):
        super().__init__()
        
        self.input_channels = input_channels
        self.n_classes = n_classes
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 简化的多模态特征提取器
        self.feature_extractor = SimplifiedMultiModalExtractor(
            input_channels, d_model, dropout
        )
        
        # 时序位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # Transformer编码器（与baseline保持一致）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=n_layers
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 辅助分类器
        self.auxiliary_head = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 4, 2)  # REM/SWS vs 其他
        )
        
        # 权重初始化
        self._init_weights()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建ProgressiveMAMBAFORMER_V1_Fixed: 参数量={total_params:,}, "
                    f"d_model={d_model}, n_heads={n_heads}, n_layers={n_layers}")
        logging.info("修复版本：简化架构，稳定训练")
    
    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, (nn.BatchNorm1d, nn.LayerNorm)):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, channels, time_steps)
        Returns:
            main_output: (batch, seq_len, n_classes)
            aux_output: (batch, seq_len, 2)
        """
        batch_size, seq_len, channels, time_steps = x.shape
        
        # 重塑为 (batch*seq_len, channels, time_steps)
        x_reshaped = x.view(batch_size * seq_len, channels, time_steps)
        
        # 提取特征
        features = self.feature_extractor(x_reshaped)  # (batch*seq_len, d_model)
        
        # 重塑回序列形式
        features = features.view(batch_size, seq_len, self.d_model)
        
        # 添加位置编码
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # (batch, seq_len, d_model)
        
        # Transformer编码
        encoded_features = self.transformer_encoder(features)
        
        # 分类预测
        main_output = self.classifier(encoded_features)
        aux_output = self.auxiliary_head(encoded_features)
        
        return main_output, aux_output


class SequentialFocalLoss(nn.Module):
    """序列Focal损失"""
    def __init__(self, alpha=1, gamma=2):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        
    def forward(self, inputs, targets):
        inputs = inputs.view(-1, inputs.size(-1))
        targets = targets.view(-1)
        
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if isinstance(self.alpha, torch.Tensor):
            alpha_t = self.alpha.gather(0, targets)
            focal_loss = alpha_t * focal_loss
        else:
            focal_loss = self.alpha * focal_loss
        
        return focal_loss.mean()


class TemporalConsistencyLoss(nn.Module):
    """时序一致性损失"""
    def __init__(self, weight=0.1):
        super().__init__()
        self.weight = weight
    
    def forward(self, predictions):
        if predictions.size(1) <= 1:
            return torch.tensor(0.0, device=predictions.device)
        
        diff = predictions[:, 1:] - predictions[:, :-1]
        consistency_loss = torch.mean(torch.sum(diff ** 2, dim=-1))
        
        return self.weight * consistency_loss