#!/usr/bin/env python3
"""
🎯 第5阶段：Progressive MAMBAFORMER with Progressive Classification
渐进式分类策略，从粗分类到细分类
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from einops import rearrange, repeat
from typing import Optional, Tuple, Dict


class SimplifiedMultiModalExtractor(nn.Module):
    """简化的多模态特征提取器（继承自第1阶段）"""
    def __init__(self, input_channels=3, d_model=512, dropout=0.15):
        super().__init__()
        
        # 统一的CNN特征提取
        self.shared_cnn = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.<PERSON><PERSON><PERSON>(),
            nn.MaxPool1d(kernel_size=8, stride=8),
            nn.Dropout(dropout * 0.5),
            
            nn.Conv1d(128, 256, kernel_size=4, stride=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4, stride=4),
            
            nn.Conv1d(256, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
        )
        
        # 模态特定投影
        self.modal_projections = nn.ModuleList([
            nn.Sequential(
                nn.Linear(d_model, d_model),
                nn.LayerNorm(d_model),
                nn.ReLU(),
                nn.Dropout(dropout)
            ) for _ in range(3)
        ])
        
        # 特征融合
        self.fusion = nn.Sequential(
            nn.Linear(d_model * 3, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
    def forward(self, x):
        batch_size, seq_len, channels, time_len = x.shape
        
        # 处理每个时间步
        all_features = []
        for t in range(seq_len):
            x_t = x[:, t, :, :]
            
            # CNN特征提取
            cnn_out = self.shared_cnn(x_t)
            cnn_out = F.adaptive_avg_pool1d(cnn_out, 1).squeeze(-1)
            
            # 模态特定处理
            modal_features = []
            for i in range(3):
                modal_feat = self.modal_projections[i](cnn_out)
                modal_features.append(modal_feat)
            
            # 融合
            fused = torch.cat(modal_features, dim=-1)
            fused = self.fusion(fused)
            all_features.append(fused)
        
        features = torch.stack(all_features, dim=1)
        return features


class CrossModalAttention(nn.Module):
    """EEG中心的跨模态注意力（继承自第2阶段）"""
    def __init__(self, d_model, n_heads=4, dropout=0.1):
        super().__init__()
        self.n_heads = n_heads
        self.d_head = d_model // n_heads
        
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)
        self.v_proj = nn.Linear(d_model, d_model)
        self.out_proj = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        batch_size, seq_len, d_model = x.shape
        
        q = self.q_proj(x)
        k = self.k_proj(x)
        v = self.v_proj(x)
        
        q = rearrange(q, 'b l (h d) -> b h l d', h=self.n_heads)
        k = rearrange(k, 'b l (h d) -> b h l d', h=self.n_heads)
        v = rearrange(v, 'b l (h d) -> b h l d', h=self.n_heads)
        
        scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(self.d_head)
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        out = torch.matmul(attn_weights, v)
        out = rearrange(out, 'b h l d -> b l (h d)')
        out = self.out_proj(out)
        
        out = self.layer_norm(x + self.dropout(out))
        
        return out


class AdaptiveGatingFusion(nn.Module):
    """自适应门控融合（继承自第3阶段）"""
    def __init__(self, d_model, n_modalities=3, dropout=0.1):
        super().__init__()
        self.n_modalities = n_modalities
        
        self.gate_network = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, n_modalities),
            nn.Softmax(dim=-1)
        )
        
        self.modal_transforms = nn.ModuleList([
            nn.Sequential(
                nn.Linear(d_model, d_model),
                nn.LayerNorm(d_model),
                nn.ReLU(),
                nn.Dropout(dropout)
            ) for _ in range(n_modalities)
        ])
        
        self.output_proj = nn.Linear(d_model, d_model)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        batch_size, seq_len, d_model = x.shape
        
        gate_weights = self.gate_network(x)
        
        modal_outputs = []
        for i in range(self.n_modalities):
            modal_out = self.modal_transforms[i](x)
            modal_outputs.append(modal_out)
        
        modal_outputs = torch.stack(modal_outputs, dim=2)
        gate_weights = gate_weights.unsqueeze(-1)
        fused = torch.sum(modal_outputs * gate_weights, dim=2)
        
        out = self.output_proj(fused)
        out = self.layer_norm(x + out)
        
        return out, gate_weights.squeeze(-1)


class SimplifiedMambaBlock(nn.Module):
    """简化的Mamba块实现（继承自第4阶段）"""
    def __init__(self, d_model, d_state=16, d_conv=4, expand=2, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand = expand
        d_inner = int(expand * d_model)
        
        self.in_proj = nn.Linear(d_model, d_inner * 2)
        
        self.conv1d = nn.Conv1d(
            in_channels=d_inner,
            out_channels=d_inner,
            kernel_size=d_conv,
            padding=d_conv - 1,
            groups=d_inner
        )
        
        self.x_proj = nn.Linear(d_inner, d_state + d_state + 1)
        self.dt_proj = nn.Linear(d_state, d_inner)
        
        A = repeat(torch.arange(1, d_state + 1), 'n -> d n', d=d_inner)
        self.A_log = nn.Parameter(torch.log(A))
        self.D = nn.Parameter(torch.ones(d_inner))
        
        self.out_proj = nn.Linear(d_inner, d_model)
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        batch_size, seq_len, d_model = x.shape
        
        x_and_res = self.in_proj(x)
        x_inner, res = x_and_res.chunk(2, dim=-1)
        
        x_conv = rearrange(x_inner, 'b l d -> b d l')
        x_conv = self.conv1d(x_conv)[:, :, :seq_len]
        x_conv = rearrange(x_conv, 'b d l -> b l d')
        
        x_conv = F.silu(x_conv)
        y = x_conv * F.silu(res)
        
        output = self.out_proj(y)
        output = self.dropout(output)
        output = self.layer_norm(x + output)
        
        return output


class MAMBAFORMERBlock(nn.Module):
    """MAMBAFORMER块（继承自第4阶段）"""
    def __init__(self, d_model=512, n_heads=8, dropout=0.1, use_mamba=True):
        super().__init__()
        self.use_mamba = use_mamba
        
        self.self_attn = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=n_heads,
            dropout=dropout,
            batch_first=True
        )
        
        if use_mamba:
            self.mamba = SimplifiedMambaBlock(d_model, dropout=dropout)
        
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model * 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 4, d_model),
            nn.Dropout(dropout)
        )
        
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model) if use_mamba else None
        
    def forward(self, x):
        residual = x
        x = self.norm1(x)
        x_attn, _ = self.self_attn(x, x, x)
        x = residual + x_attn
        
        if self.use_mamba:
            residual = x
            x = self.norm3(x)
            x = self.mamba(x)
            x = residual + x
        
        residual = x
        x = self.norm2(x)
        x = self.ffn(x)
        x = residual + x
        
        return x


class ProgressiveClassificationHead(nn.Module):
    """渐进式分类头：从粗分类到细分类"""
    def __init__(self, d_model, n_classes=5, dropout=0.1):
        super().__init__()
        
        # 第1级：粗分类（清醒 vs 睡眠）
        self.coarse_classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, 2)  # Wake vs Sleep
        )
        
        # 第2级：中等分类（清醒 vs 浅睡 vs 深睡 vs REM）
        self.medium_classifier = nn.Sequential(
            nn.Linear(d_model + 2, d_model // 2),  # 输入包含粗分类结果
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 4)  # W, Light(N1+N2), Deep(N3), REM
        )
        
        # 第3级：细分类（最终5类）
        self.fine_classifier = nn.Sequential(
            nn.Linear(d_model + 2 + 4, d_model // 2),  # 输入包含前两级结果
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 不确定性估计
        self.uncertainty_estimator = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, 1),
            nn.Sigmoid()
        )
        
    def forward(self, features):
        batch_size, seq_len, d_model = features.shape
        
        # 第1级：粗分类
        coarse_logits = self.coarse_classifier(features)
        coarse_probs = F.softmax(coarse_logits, dim=-1)
        
        # 第2级：中等分类
        medium_input = torch.cat([features, coarse_probs], dim=-1)
        medium_logits = self.medium_classifier(medium_input)
        medium_probs = F.softmax(medium_logits, dim=-1)
        
        # 第3级：细分类
        fine_input = torch.cat([features, coarse_probs, medium_probs], dim=-1)
        fine_logits = self.fine_classifier(fine_input)
        
        # 不确定性估计
        uncertainty = self.uncertainty_estimator(features)
        
        return {
            'coarse': coarse_logits,
            'medium': medium_logits,
            'fine': fine_logits,
            'uncertainty': uncertainty
        }


class ProgressiveMAMBAFORMER_V5(nn.Module):
    """第5阶段：集成渐进式分类策略的MAMBAFORMER"""
    
    def __init__(
        self,
        input_channels=3,
        n_classes=5,
        d_model=512,
        n_heads=8,
        n_layers=12,
        dropout=0.1,
        seq_len=7,
    ):
        super().__init__()
        
        self.d_model = d_model
        self.seq_len = seq_len
        self.n_classes = n_classes
        
        # 多模态特征提取器
        self.feature_extractor = SimplifiedMultiModalExtractor(
            input_channels=input_channels,
            d_model=d_model,
            dropout=dropout
        )
        
        # 跨模态注意力
        self.cross_modal_attention = CrossModalAttention(
            d_model=d_model,
            n_heads=n_heads // 2,
            dropout=dropout
        )
        
        # 自适应门控融合
        self.adaptive_gating = AdaptiveGatingFusion(
            d_model=d_model,
            n_modalities=3,
            dropout=dropout
        )
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1, seq_len, d_model))
        
        # MAMBAFORMER层
        self.layers = nn.ModuleList([
            MAMBAFORMERBlock(
                d_model=d_model,
                n_heads=n_heads,
                dropout=dropout,
                use_mamba=(i % 2 == 1)
            ) for i in range(n_layers)
        ])
        
        # 渐进式分类头
        self.progressive_classifier = ProgressiveClassificationHead(
            d_model=d_model,
            n_classes=n_classes,
            dropout=dropout
        )
        
        # 标准分类头（用于比较）
        self.standard_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, n_classes)
        )
        
        self._init_weights()
        
        # 记录参数量
        total_params = sum(p.numel() for p in self.parameters())
        print(f"创建ProgressiveMAMBAFORMER_V5: 参数量={total_params:,}, "
              f"d_model={d_model}, n_heads={n_heads}, n_layers={n_layers}")
        print("第5阶段：渐进式分类策略")
    
    def _init_weights(self):
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(self, x, return_all_stages=False):
        batch_size = x.shape[0]
        
        # 阶段1：多模态特征提取
        features = self.feature_extractor(x)
        
        # 阶段2：跨模态注意力
        features = self.cross_modal_attention(features)
        
        # 阶段3：自适应门控融合
        features, gate_weights = self.adaptive_gating(features)
        
        # 添加位置编码
        features = features + self.pos_encoding
        
        # 阶段4：MAMBAFORMER层
        for layer in self.layers:
            features = layer(features)
        
        # 阶段5：渐进式分类
        progressive_outputs = self.progressive_classifier(features)
        
        # 标准分类（用于比较）
        standard_output = self.standard_classifier(features)
        
        if return_all_stages:
            return progressive_outputs, standard_output, gate_weights
        else:
            # 默认返回细分类结果
            return progressive_outputs['fine'], standard_output, gate_weights


class ProgressiveLoss(nn.Module):
    """渐进式分类损失函数"""
    def __init__(self, n_classes=5, alpha=None, gamma=2.0, device="cuda"):
        super().__init__()
        self.n_classes = n_classes
        self.device = device
        
        # Focal Loss参数
        self.gamma = gamma
        if alpha is not None:
            self.alpha = torch.tensor(alpha).to(device)
        else:
            self.alpha = None
        
        # 粗分类映射：0(W)->0, 1-4(N1-REM)->1
        self.coarse_mapping = torch.tensor([0, 1, 1, 1, 1]).to(device)
        
        # 中等分类映射：0(W)->0, 1-2(N1,N2)->1, 3(N3)->2, 4(REM)->3
        self.medium_mapping = torch.tensor([0, 1, 1, 2, 3]).to(device)
        
    def focal_loss(self, inputs, targets, alpha=None):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** self.gamma * ce_loss
        
        if alpha is not None:
            alpha_t = alpha[targets]
            focal_loss = alpha_t * focal_loss
        
        return focal_loss.mean()
    
    def forward(self, progressive_outputs, targets):
        if targets.dim() == 2:  # (batch, seq_len)
            batch_size, seq_len = targets.shape
            targets_flat = targets.reshape(-1)
        else:
            targets_flat = targets
        
        # 粗分类损失
        coarse_targets = self.coarse_mapping[targets_flat]
        coarse_logits = progressive_outputs['coarse'].reshape(-1, 2)
        coarse_loss = self.focal_loss(coarse_logits, coarse_targets)
        
        # 中等分类损失
        medium_targets = self.medium_mapping[targets_flat]
        medium_logits = progressive_outputs['medium'].reshape(-1, 4)
        medium_loss = self.focal_loss(medium_logits, medium_targets)
        
        # 细分类损失
        fine_logits = progressive_outputs['fine'].reshape(-1, self.n_classes)
        fine_loss = self.focal_loss(fine_logits, targets_flat, self.alpha)
        
        # 不确定性正则化（鼓励对错误预测有高不确定性）
        uncertainty = progressive_outputs['uncertainty'].reshape(-1)
        fine_probs = F.softmax(fine_logits, dim=-1)
        pred_confidence = fine_probs.max(dim=-1)[0]
        pred_correct = fine_probs.argmax(dim=-1) == targets_flat
        
        # 正确预测应该有低不确定性，错误预测应该有高不确定性
        uncertainty_loss = torch.mean(
            pred_correct.float() * uncertainty +
            (1 - pred_correct.float()) * (1 - uncertainty)
        )
        
        # 组合损失（渐进式权重）
        total_loss = (
            0.1 * coarse_loss +
            0.2 * medium_loss +
            0.6 * fine_loss +
            0.1 * uncertainty_loss
        )
        
        return total_loss, {
            'coarse': coarse_loss.item(),
            'medium': medium_loss.item(),
            'fine': fine_loss.item(),
            'uncertainty': uncertainty_loss.item()
        }


class TemporalConsistencyLoss(nn.Module):
    """时间一致性损失"""
    def __init__(self, weight=0.1):
        super().__init__()
        self.weight = weight
    
    def forward(self, predictions):
        if predictions.dim() == 3:
            batch_size, seq_len, n_classes = predictions.shape
            if seq_len <= 1:
                return torch.tensor(0.0).to(predictions.device)
            
            probs = F.softmax(predictions, dim=-1)
            diff = probs[:, 1:, :] - probs[:, :-1, :]
            loss = torch.mean(torch.abs(diff))
            
            return self.weight * loss
        return torch.tensor(0.0).to(predictions.device)


if __name__ == "__main__":
    # 测试模型
    model = ProgressiveMAMBAFORMER_V5(
        input_channels=3,
        n_classes=5,
        d_model=512,
        n_heads=32,
        n_layers=12,
        dropout=0.15,
        seq_len=7
    )
    
    # 测试输入
    x = torch.randn(2, 7, 3, 3000)
    progressive_outputs, standard_output, gate_weights = model(x, return_all_stages=True)
    
    print(f"粗分类输出形状: {progressive_outputs['coarse'].shape}")
    print(f"中等分类输出形状: {progressive_outputs['medium'].shape}")
    print(f"细分类输出形状: {progressive_outputs['fine'].shape}")
    print(f"不确定性输出形状: {progressive_outputs['uncertainty'].shape}")
    print(f"标准输出形状: {standard_output.shape}")
    print(f"门控权重形状: {gate_weights.shape}")
    
    # 测试损失函数
    targets = torch.randint(0, 5, (2, 7))
    loss_fn = ProgressiveLoss(n_classes=5)
    loss, loss_dict = loss_fn(progressive_outputs, targets)
    print(f"\n总损失: {loss.item():.4f}")
    print(f"损失分解: {loss_dict}")