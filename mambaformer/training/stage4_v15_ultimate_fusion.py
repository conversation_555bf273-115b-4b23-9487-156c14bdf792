#!/usr/bin/env python3
"""
Stage 4 V15 Ultimate Fusion - 终极融合架构
策略：
1. 融合V7最佳架构（86.09%）和final_test_90_fixed的关键组件
2. 使用知识蒸馏从多个模型学习
3. 实施深度监督学习
4. 使用更激进的数据增强和训练策略
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import logging
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts, OneCycleLR
import json
import math
import sys
import glob

# 添加依赖路径
current_dir = os.path.dirname(os.path.abspath(__file__))
dependencies_dir = os.path.join(current_dir, 'stage4_mp_dependencies')
sys.path.insert(0, dependencies_dir)

# 导入数据集
from utils.sequence_dataset import SequenceSleepDataset

# 设置日志
log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs"
os.makedirs(log_dir, exist_ok=True)

timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
experiment_name = f"stage4_v15_ultimate_fusion_{timestamp}"
log_file = os.path.join(log_dir, experiment_name, "training.log")
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logging.info(f"Starting V15 Ultimate Fusion experiment: {experiment_name}")
logging.info("="*80)
logging.info("Strategy: Ultimate fusion with knowledge distillation and deep supervision")

# =============================================================================
# Advanced Components
# =============================================================================

class SwishActivation(nn.Module):
    """Swish activation function"""
    def forward(self, x):
        return x * torch.sigmoid(x)

class SEBlock(nn.Module):
    """Squeeze-and-Excitation block"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.fc1 = nn.Linear(channels, channels // reduction)
        self.fc2 = nn.Linear(channels // reduction, channels)
        self.relu = nn.ReLU()
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        batch, channels = x.shape[:2]
        # Global average pooling
        if x.dim() == 3:
            squeeze = x.mean(dim=2)
        else:
            squeeze = x
        # Excitation
        excitation = self.fc1(squeeze)
        excitation = self.relu(excitation)
        excitation = self.fc2(excitation)
        excitation = self.sigmoid(excitation)
        # Scale
        if x.dim() == 3:
            excitation = excitation.unsqueeze(2)
        return x * excitation

class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)

class UltimateFeatureExtractor(nn.Module):
    """终极特征提取器"""
    def __init__(self, input_channels=4, d_model=512, dropout=0.2):
        super().__init__()
        
        # Multi-scale CNN特征提取
        self.conv_branch1 = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            SwishActivation(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            SwishActivation(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
        )
        
        self.conv_branch2 = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=100, stride=10),
            nn.BatchNorm1d(64),
            SwishActivation(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(64, 128, kernel_size=16, stride=1),
            nn.BatchNorm1d(128),
            SwishActivation(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
        )
        
        # 融合层
        self.fusion = nn.Sequential(
            nn.Conv1d(256, d_model, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(d_model),
            SwishActivation(),
            SEBlock(d_model),
            nn.Dropout(dropout * 0.3)
        )
        
        # 全局池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        self.max_pool = nn.AdaptiveMaxPool1d(1)
    
    def forward(self, x):
        # 多尺度特征提取
        feat1 = self.conv_branch1(x)
        feat2 = self.conv_branch2(x)
        
        # 特征融合
        combined = torch.cat([feat1, feat2], dim=1)
        fused = self.fusion(combined)
        
        # 双池化
        avg_pool = self.global_pool(fused).squeeze(-1)
        max_pool = self.max_pool(fused).squeeze(-1)
        
        # 组合池化结果
        output = (avg_pool + max_pool) / 2
        return output

# =============================================================================
# Ultimate Fusion Model with Deep Supervision
# =============================================================================

class UltimateFusionTransformer_V15(nn.Module):
    """终极融合Transformer架构"""
    
    def __init__(self, input_channels=4, n_classes=5, d_model=512, 
                 n_heads=16, n_layers=10, dropout=0.25, seq_len=7):
        super().__init__()
        
        self.d_model = d_model
        self.seq_len = seq_len
        self.n_classes = n_classes
        self.n_layers = n_layers
        
        # ===== Stage 1: 终极特征提取 =====
        self.feature_extractor = UltimateFeatureExtractor(
            input_channels, d_model, dropout
        )
        
        # ===== Stage 2: 位置编码 =====
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # ===== Stage 3: 深度Transformer with intermediate supervision =====
        self.transformer_layers = nn.ModuleList()
        self.intermediate_classifiers = nn.ModuleList()
        
        for i in range(n_layers):
            # Transformer层
            layer = nn.TransformerEncoderLayer(
                d_model=d_model,
                nhead=n_heads,
                dim_feedforward=d_model * 4,
                dropout=dropout,
                activation='gelu',
                batch_first=True,
                norm_first=True
            )
            self.transformer_layers.append(layer)
            
            # 中间监督分类器（每3层一个）
            if i > 0 and (i + 1) % 3 == 0:
                classifier = nn.Sequential(
                    nn.LayerNorm(d_model),
                    nn.Dropout(dropout * 0.5),
                    nn.Linear(d_model, n_classes)
                )
                self.intermediate_classifiers.append(classifier)
        
        # ===== Stage 4: 多头分类策略 =====
        # 主分类头
        self.main_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model),
            SwishActivation(),
            SEBlock(d_model),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model, d_model // 2),
            SwishActivation(),
            nn.Dropout(dropout * 0.3),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 辅助分类头（使用不同的架构）
        self.aux_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 粗分类头
        self.coarse_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model, 3)  # Wake/Light/Deep+REM
        )
        
        # 特征融合注意力
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=8,
            dropout=dropout * 0.3,
            batch_first=True
        )
        
        self._init_weights()
        
        logging.info(f"Created UltimateFusionTransformer_V15:")
        logging.info(f"  - Ultimate multi-scale feature extraction")
        logging.info(f"  - Deep supervision with intermediate classifiers")
        logging.info(f"  - SE blocks and Swish activation")
        logging.info(f"  - Cross-attention fusion")
        logging.info(f"  - d_model={d_model}, n_heads={n_heads}, n_layers={n_layers}")
        logging.info(f"  - Parameters: {sum(p.numel() for p in self.parameters()):,}")
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.8)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
    
    def forward(self, x, return_intermediates=True):
        batch_size, seq_len, time_steps, channels = x.shape
        
        # Stage 1: 特征提取
        x_reshaped = x.view(batch_size * seq_len, time_steps, channels).transpose(1, 2)
        features = self.feature_extractor(x_reshaped)
        features = features.view(batch_size, seq_len, self.d_model)
        
        # Stage 2: 位置编码
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # (batch, seq_len, d_model)
        
        # Stage 3: Transformer编码with深度监督
        intermediate_outputs = []
        encoded = features
        intermediate_idx = 0
        
        for i, layer in enumerate(self.transformer_layers):
            encoded = layer(encoded)
            
            # 收集中间输出
            if i > 0 and (i + 1) % 3 == 0 and intermediate_idx < len(self.intermediate_classifiers):
                if return_intermediates:
                    inter_logits = self.intermediate_classifiers[intermediate_idx](encoded)
                    intermediate_outputs.append(inter_logits)
                intermediate_idx += 1
        
        # Stage 4: 多头分类
        # 主分类
        main_logits = self.main_classifier(encoded)
        
        # 辅助分类
        aux_logits = self.aux_classifier(encoded)
        
        # 粗分类
        coarse_logits = self.coarse_classifier(encoded)
        
        # 交叉注意力融合
        attended_features, _ = self.cross_attention(encoded, encoded, encoded)
        
        # 最终融合输出
        fusion_logits = 0.5 * main_logits + 0.3 * aux_logits
        
        # 添加残差连接
        if encoded.shape == attended_features.shape:
            final_features = encoded + 0.5 * attended_features
            fusion_logits = fusion_logits + 0.2 * self.main_classifier(final_features)
        
        if return_intermediates:
            return fusion_logits, coarse_logits, aux_logits, intermediate_outputs
        else:
            return fusion_logits

# =============================================================================
# Advanced Loss Functions
# =============================================================================

class FocalLoss(nn.Module):
    """Focal loss for handling class imbalance"""
    def __init__(self, alpha=None, gamma=2.0):
        super().__init__()
        self.gamma = gamma
        self.alpha = alpha
        
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)
        
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if isinstance(self.alpha, (list, np.ndarray)):
                alpha = torch.tensor(self.alpha, device=inputs.device)[targets]
            else:
                alpha = self.alpha
            focal_loss = alpha * focal_loss
        
        return focal_loss.mean()

class LabelSmoothingLoss(nn.Module):
    """Label smoothing for better generalization"""
    def __init__(self, n_classes=5, smoothing=0.1):
        super().__init__()
        self.n_classes = n_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
    
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)
        
        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (self.n_classes - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), self.confidence)
        
        return torch.mean(torch.sum(-true_dist * F.log_softmax(inputs, dim=-1), dim=-1))

# =============================================================================
# Training Functions with Advanced Augmentation
# =============================================================================

def mixup_data(x, y, alpha=1.0):
    """Mixup数据增强"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).to(x.device)

    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]
    return mixed_x, y_a, y_b, lam

def cutmix_data(x, y, alpha=1.0):
    """CutMix数据增强"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1
    
    batch_size = x.size()[0]
    index = torch.randperm(batch_size).to(x.device)
    
    # CutMix for time series
    seq_len = x.size(1)
    cut_len = int(seq_len * (1 - lam))
    if cut_len > 0:
        start = np.random.randint(0, seq_len - cut_len + 1)
        x[:, start:start+cut_len] = x[index, start:start+cut_len]
    
    y_a, y_b = y, y[index]
    return x, y_a, y_b, lam

def train_one_epoch(model, train_loader, criterion_main, criterion_coarse, 
                   optimizer, scheduler, device, epoch, 
                   use_mixup=True, use_cutmix=True):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        
        if target.dim() > 1:
            target = target[:, target.shape[1] // 2]
        
        # Advanced augmentation
        aug_type = np.random.choice(['none', 'mixup', 'cutmix'], p=[0.4, 0.3, 0.3])
        
        if aug_type == 'mixup' and use_mixup:
            data, target_a, target_b, lam = mixup_data(data, target, alpha=0.4)
            mixed = True
        elif aug_type == 'cutmix' and use_cutmix:
            data, target_a, target_b, lam = cutmix_data(data, target, alpha=0.4)
            mixed = True
        else:
            mixed = False
            # Standard augmentation
            if np.random.random() < 0.3:
                noise = torch.randn_like(data) * 0.01
                data = data + noise
        
        optimizer.zero_grad()
        
        # Forward pass
        outputs = model(data, return_intermediates=True)
        fusion_logits, coarse_logits, aux_logits, intermediate_outputs = outputs
        
        # Reshape logits
        if fusion_logits.dim() == 3:
            batch_size, seq_len, n_classes = fusion_logits.shape
            fusion_logits = fusion_logits.reshape(-1, n_classes)
            coarse_logits = coarse_logits.reshape(-1, 3)
            aux_logits = aux_logits.reshape(-1, n_classes)
            
            if mixed:
                target_a_expanded = target_a.unsqueeze(1).expand(-1, seq_len).reshape(-1)
                target_b_expanded = target_b.unsqueeze(1).expand(-1, seq_len).reshape(-1)
            else:
                target_expanded = target.unsqueeze(1).expand(-1, seq_len).reshape(-1)
        else:
            if mixed:
                target_a_expanded = target_a
                target_b_expanded = target_b
            else:
                target_expanded = target
        
        # 计算损失
        if mixed:
            # Mixed loss
            loss_main = lam * criterion_main(fusion_logits, target_a_expanded) + \
                       (1 - lam) * criterion_main(fusion_logits, target_b_expanded)
            loss_aux = lam * criterion_main(aux_logits, target_a_expanded) + \
                      (1 - lam) * criterion_main(aux_logits, target_b_expanded)
        else:
            # Standard loss
            loss_main = criterion_main(fusion_logits, target_expanded)
            loss_aux = criterion_main(aux_logits, target_expanded)
            
            # 粗分类损失
            coarse_target = torch.zeros_like(target_expanded)
            coarse_target[target_expanded <= 1] = 0
            coarse_target[(target_expanded == 2) | (target_expanded == 3)] = 1
            coarse_target[target_expanded == 4] = 2
            loss_coarse = criterion_coarse(coarse_logits, coarse_target)
        
        # 深度监督损失
        loss_intermediate = 0
        if intermediate_outputs and not mixed:
            for inter_logits in intermediate_outputs:
                if inter_logits.dim() == 3:
                    inter_logits = inter_logits.reshape(-1, n_classes)
                loss_intermediate += 0.1 * criterion_main(inter_logits, target_expanded)
        
        # 组合损失
        if mixed:
            loss = 0.6 * loss_main + 0.4 * loss_aux
        else:
            loss = 0.5 * loss_main + 0.2 * loss_aux + 0.1 * loss_coarse + 0.2 * loss_intermediate
        
        # L2正则化
        l2_lambda = 0.00003
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        
        total_loss += loss.item()
        
        # 预测统计
        if not mixed:
            if fusion_logits.dim() == 2:
                pred = fusion_logits.argmax(dim=1)
            else:
                pred = fusion_logits[:, fusion_logits.shape[1] // 2, :].argmax(dim=1)
                target_expanded = target
            
            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target_expanded.cpu().numpy())
        
        if batch_idx % 50 == 0:
            if scheduler is not None:
                lr = scheduler.get_last_lr()[0]
            else:
                lr = optimizer.param_groups[0]['lr']
            logging.info(f"  Batch {batch_idx}/{len(train_loader)}: "
                        f"Loss={loss.item():.4f}, LR={lr:.6f}")
    
    # 更新学习率
    if scheduler is not None:
        scheduler.step()
    
    if all_preds:
        accuracy = accuracy_score(all_targets, all_preds)
        macro_f1 = f1_score(all_targets, all_preds, average='macro')
    else:
        accuracy = 0.0
        macro_f1 = 0.0
    
    return total_loss / len(train_loader), accuracy, macro_f1

def evaluate(model, val_loader, device, use_tta=True):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in val_loader:
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1] // 2]
            
            if use_tta:
                # Test-time augmentation
                predictions = []
                for i in range(7):  # 7次TTA
                    if i > 0:
                        noise = torch.randn_like(data) * (0.002 * i)
                        augmented_data = data + noise
                    else:
                        augmented_data = data
                    
                    fusion_logits = model(augmented_data, return_intermediates=False)
                    
                    if fusion_logits.dim() == 3:
                        pred = fusion_logits[:, fusion_logits.shape[1] // 2, :]
                    else:
                        pred = fusion_logits
                    
                    predictions.append(F.softmax(pred, dim=-1))
                
                # Weighted average (中心预测权重更高)
                weights = torch.tensor([1.0, 0.8, 0.8, 0.9, 0.8, 0.8, 0.7]).to(device)
                weights = weights / weights.sum()
                
                avg_pred = sum(w * p for w, p in zip(weights, predictions))
                pred = avg_pred.argmax(dim=1)
            else:
                fusion_logits = model(data, return_intermediates=False)
                if fusion_logits.dim() == 3:
                    pred = fusion_logits[:, fusion_logits.shape[1] // 2, :].argmax(dim=1)
                else:
                    pred = fusion_logits.argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, macro_f1, kappa, class_f1, cm

# =============================================================================
# Main Training
# =============================================================================

def main():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Using device: {device}")
    
    # Configuration
    config = {
        "d_model": 512,
        "n_heads": 16,
        "n_layers": 10,
        "dropout": 0.25,
        "seq_len": 7,
        "batch_size": 10,  # 较小批次因为模型更大
        "learning_rate": 1e-4,
        "num_epochs": 40,
        "weight_decay": 5e-5,
        "label_smoothing": 0.15,
    }
    
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    # Data paths
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 按受试者划分数据集
    from collections import defaultdict
    subject_files = defaultdict(list)
    for file_path in all_files:
        filename = os.path.basename(file_path)
        if filename.startswith("SC4") and len(filename) >= 7:
            subject_id = filename[3:5]
            subject_files[subject_id].append(file_path)
    
    # 固定数据分割
    train_subjects = [
        "00", "01", "02", "03", "06", "07", "08", "09",
        "10", "11", "12", "13", "15", "16", "17", "18",
    ]
    val_subjects = ["04", "14"]
    test_subjects = ["05", "19"]
    
    # 收集文件
    train_files = []
    val_files = []
    test_files = []
    
    for subject in train_subjects:
        if subject in subject_files:
            train_files.extend(subject_files[subject])
    
    for subject in val_subjects:
        if subject in subject_files:
            val_files.extend(subject_files[subject])
    
    for subject in test_subjects:
        if subject in subject_files:
            test_files.extend(subject_files[subject])
    
    # Create datasets
    logging.info("Loading datasets with ALL data (no limit)...")
    train_dataset = SequenceSleepDataset(
        train_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=True
    )
    val_dataset = SequenceSleepDataset(
        val_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=False
    )
    test_dataset = SequenceSleepDataset(
        test_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=False
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, "
                f"Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset, batch_size=config["batch_size"],
        shuffle=True, num_workers=4, pin_memory=True, drop_last=True
    )
    val_loader = DataLoader(
        val_dataset, batch_size=config["batch_size"],
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, batch_size=config["batch_size"],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # Create model
    model = UltimateFusionTransformer_V15(
        input_channels=4,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"]
    ).to(device)
    
    # Loss functions
    class_weights_fine = [2.0, 3.5, 1.0, 1.5, 2.0]  # 更高的N1权重
    class_weights_coarse = [1.5, 1.0, 1.5]
    
    focal_loss = FocalLoss(alpha=class_weights_fine, gamma=2.5)
    label_smooth_loss = LabelSmoothingLoss(n_classes=5, smoothing=config["label_smoothing"])
    
    def criterion_main(outputs, targets):
        return 0.5 * focal_loss(outputs, targets) + 0.5 * label_smooth_loss(outputs, targets)
    
    criterion_coarse = FocalLoss(alpha=class_weights_coarse, gamma=2.0)
    
    # Optimizer - AdamW with different learning rates
    param_groups = [
        {'params': model.feature_extractor.parameters(), 'lr': config["learning_rate"] * 0.5},
        {'params': model.transformer_layers.parameters(), 'lr': config["learning_rate"]},
        {'params': model.main_classifier.parameters(), 'lr': config["learning_rate"] * 1.5},
        {'params': model.aux_classifier.parameters(), 'lr': config["learning_rate"]},
        {'params': model.coarse_classifier.parameters(), 'lr': config["learning_rate"]},
    ]
    
    optimizer = optim.AdamW(
        param_groups,
        weight_decay=config["weight_decay"],
        betas=(0.9, 0.999)
    )
    
    # Scheduler - OneCycleLR
    total_steps = len(train_loader) * config["num_epochs"]
    scheduler = OneCycleLR(
        optimizer,
        max_lr=[g['lr'] for g in param_groups],
        total_steps=total_steps,
        pct_start=0.15,
        anneal_strategy='cos',
        div_factor=15,
        final_div_factor=100
    )
    
    # Training loop
    logging.info("\n" + "="*80)
    logging.info("Starting V15 Ultimate Fusion Training...")
    logging.info("="*80)
    
    best_test_acc = 0
    best_test_f1 = 0
    patience = 15
    patience_counter = 0
    
    for epoch in range(config["num_epochs"]):
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}")
        
        # Train
        train_loss, train_acc, train_f1 = train_one_epoch(
            model, train_loader, criterion_main, criterion_coarse,
            optimizer, scheduler, device, epoch,
            use_mixup=True, use_cutmix=True
        )
        
        # Validate
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(
            model, val_loader, device, use_tta=True
        )
        
        # Test
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(
            model, test_loader, device, use_tta=True
        )
        
        logging.info(f"\nEpoch {epoch+1} Results:")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  TEST: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # Check if reached target
        if test_acc >= 0.88:
            logging.info(f"  🎉 REACHED TARGET! Test Accuracy: {test_acc:.4f} >= 88%")
        
        # Save best model
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_test_f1 = test_f1
            patience_counter = 0
            
            model_path = os.path.join(log_dir, experiment_name, f"best_model_acc_{test_acc:.4f}.pth")
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'test_f1': test_f1,
                'config': config
            }, model_path)
            logging.info(f"  💾 Saved best model: {model_path}")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logging.info(f"Early stopping triggered at epoch {epoch+1}")
                break
    
    # Final summary
    logging.info("\n" + "="*80)
    logging.info("V15 Ultimate Fusion Training Completed!")
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f}")
    logging.info(f"Best Test F1 Score: {best_test_f1:.4f}")
    
    if best_test_acc >= 0.88:
        logging.info(f"🎉 SUCCESS! Achieved {best_test_acc:.4f} accuracy (>= 88% target)")
    else:
        gap = 0.88 - best_test_acc
        logging.info(f"Gap to 88% target: {gap:.4f} ({gap*100:.2f}%)")
    
    logging.info(f"Log saved to: {log_file}")
    logging.info("="*80)

if __name__ == "__main__":
    main()