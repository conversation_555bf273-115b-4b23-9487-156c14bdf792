#!/usr/bin/env python3
"""
持续监控Stage 1训练直到达到85%目标
"""
import time
import os
import re
import subprocess

def monitor_training():
    log_file = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_simple_fixed.log"
    target_accuracy = 85.0
    
    print("="*60)
    print("🎯 监控Stage 1训练进度 - 目标: 85%")
    print("="*60)
    
    last_epoch = 0
    best_test_acc = 0
    
    while True:
        try:
            # 读取最新日志
            with open(log_file, 'r') as f:
                lines = f.readlines()
            
            # 查找最新epoch和准确率
            current_epoch = 0
            latest_test_acc = 0
            
            for line in lines[-100:]:  # 只看最后100行
                # 查找当前epoch
                if "Epoch " in line and "/" in line:
                    match = re.search(r"Epoch (\d+)/", line)
                    if match:
                        current_epoch = max(current_epoch, int(match.group(1)))
                
                # 查找测试准确率
                if "测试 - Acc:" in line:
                    match = re.search(r"测试 - Acc: ([\d.]+)%", line)
                    if match:
                        latest_test_acc = float(match.group(1))
                        
                # 查找最佳模型
                if "保存最佳模型" in line:
                    match = re.search(r"Test: ([\d.]+)%", line)
                    if match:
                        best_test_acc = max(best_test_acc, float(match.group(1)))
                        
                # 检查是否达到目标
                if "达到目标" in line:
                    match = re.search(r"测试准确率: ([\d.]+)%", line)
                    if match:
                        final_acc = float(match.group(1))
                        print(f"\n🎉🎉🎉 成功达到目标！最终准确率: {final_acc}%")
                        return final_acc
            
            # 显示进度
            if current_epoch > last_epoch:
                print(f"\nEpoch {current_epoch}/20:")
                print(f"  最新测试准确率: {latest_test_acc:.2f}%")
                print(f"  最佳测试准确率: {best_test_acc:.2f}%")
                print(f"  距离目标: {target_accuracy - best_test_acc:.2f}%")
                
                # 进度条
                progress = min(100, (best_test_acc / target_accuracy) * 100)
                bar_length = 30
                filled = int(bar_length * progress / 100)
                bar = '█' * filled + '░' * (bar_length - filled)
                print(f"  进度: [{bar}] {progress:.1f}%")
                
                last_epoch = current_epoch
            
            # 检查进程是否还在运行
            result = subprocess.run(['pgrep', '-f', 'stage1_simple.py'], 
                                  capture_output=True, text=True)
            if not result.stdout.strip():
                print("\n⚠️ 训练进程已停止")
                print(f"最终最佳准确率: {best_test_acc:.2f}%")
                return best_test_acc
                
        except Exception as e:
            print(f"监控出错: {e}")
            
        time.sleep(60)  # 每分钟检查一次

if __name__ == "__main__":
    final_accuracy = monitor_training()
    
    if final_accuracy < 85.0:
        print(f"\n未达到85%目标 (最佳: {final_accuracy:.2f}%)")
        print("准备启动改进策略...")
        # 这里可以自动启动新的训练策略
    else:
        print(f"\n成功达到目标: {final_accuracy:.2f}%")
        print("准备启动Stage 2训练...")