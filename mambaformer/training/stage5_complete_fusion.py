#!/usr/bin/env python3
"""
🚀 第5阶段训练：完整渐进式融合模型
集成所有创新点：
1. 多模态特征细化
2. 跨模态注意力
3. 自适应门控融合
4. Mamba状态空间建模
5. 渐进式分类策略
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import (
    confusion_matrix,
    classification_report,
    accuracy_score,
    f1_score,
    cohen_kappa_score,
)
import warnings

warnings.filterwarnings("ignore")

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.progressive_mambaformer_v5_complete import CompleteFusionMAMBAFORMER
from utils.sequence_dataset import SequenceSleepDataset


class HybridLoss(nn.Module):
    """混合损失函数：Focal Loss + Label Smoothing"""
    def __init__(self, alpha=None, gamma=2.0, smoothing=0.1, device="cuda"):
        super().__init__()
        self.gamma = gamma
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
        self.device = device
        
        if alpha is not None:
            self.alpha = torch.tensor(alpha).to(device)
        else:
            self.alpha = None
    
    def forward(self, inputs, targets):
        n_classes = inputs.size(-1)
        
        # Label smoothing
        if self.smoothing > 0:
            with torch.no_grad():
                true_dist = torch.zeros_like(inputs)
                true_dist.fill_(self.smoothing / (n_classes - 1))
                if inputs.dim() == 3:
                    targets_flat = targets.reshape(-1)
                    true_dist = true_dist.reshape(-1, n_classes)
                    true_dist.scatter_(1, targets_flat.unsqueeze(1), self.confidence)
                    true_dist = true_dist.reshape(inputs.shape)
                else:
                    true_dist.scatter_(1, targets.unsqueeze(1), self.confidence)
        
        # Focal loss with label smoothing
        log_probs = torch.log_softmax(inputs, dim=-1)
        
        if self.smoothing > 0:
            if inputs.dim() == 3:
                batch_size, seq_len, _ = inputs.shape
                log_probs_flat = log_probs.reshape(-1, n_classes)
                true_dist_flat = true_dist.reshape(-1, n_classes)
                ce_loss = -torch.sum(true_dist_flat * log_probs_flat, dim=-1)
                ce_loss = ce_loss.reshape(batch_size, seq_len)
            else:
                ce_loss = -torch.sum(true_dist * log_probs, dim=-1)
        else:
            ce_loss = nn.functional.cross_entropy(inputs, targets, reduction='none')
        
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if inputs.dim() == 3:
                targets_for_alpha = targets.reshape(-1)
                alpha_t = self.alpha[targets_for_alpha]
                focal_loss_flat = focal_loss.reshape(-1)
                focal_loss = (alpha_t * focal_loss_flat).reshape(batch_size, seq_len)
            else:
                alpha_t = self.alpha[targets]
                focal_loss = alpha_t * focal_loss
        
        return focal_loss.mean()


def train_one_epoch(model, data_loader, criterion, optimizer, device, epoch, config):
    model.train()
    running_loss = 0.0
    running_coarse_loss = 0.0
    running_fine_loss = 0.0
    correct_predictions = 0
    total_predictions = 0
    
    progress_bar = tqdm(data_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']}")
    
    for batch_idx, (data, target) in enumerate(progress_bar):
        data, target = data.to(device), target.to(device)
        data = data.permute(0, 1, 3, 2)  # 转换格式
        
        optimizer.zero_grad()
        
        # 获取完整模型输出
        outputs = model(data)
        fine_output = outputs['fine_output']
        coarse_output = outputs.get('coarse_output', None)
        
        # 计算准确率（使用中心时间点）
        batch_size, seq_len, n_classes = fine_output.shape
        _, predicted = torch.max(fine_output[:, seq_len//2, :], dim=-1)
        true_labels = target[:, seq_len//2]
        correct_predictions += (predicted == true_labels).sum().item()
        total_predictions += true_labels.size(0)
        
        # 计算损失
        fine_loss = criterion(fine_output, target)
        total_loss = fine_loss
        
        # 如果有粗分类输出
        if coarse_output is not None:
            # 创建粗分类标签 (W, Light Sleep, Deep Sleep, REM)
            coarse_target = target.clone()
            coarse_target[target == 1] = 1  # N1 -> Light Sleep
            coarse_target[target == 2] = 1  # N2 -> Light Sleep
            coarse_target[target == 3] = 2  # N3 -> Deep Sleep
            coarse_target[target == 4] = 3  # REM stays REM
            
            coarse_loss = criterion(coarse_output, coarse_target)
            total_loss = 0.7 * fine_loss + 0.3 * coarse_loss
            running_coarse_loss += coarse_loss.item()
        
        running_fine_loss += fine_loss.item()
        
        # 反向传播
        total_loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
        optimizer.step()
        
        running_loss += total_loss.item()
        
        # 更新进度条
        current_acc = 100 * correct_predictions / total_predictions
        progress_bar.set_postfix({
            'loss': f'{running_loss/(batch_idx+1):.4f}',
            'acc': f'{current_acc:.2f}%',
            'lr': f'{optimizer.param_groups[0]["lr"]:.6f}'
        })
    
    epoch_loss = running_loss / len(data_loader)
    epoch_acc = 100 * correct_predictions / total_predictions
    
    return epoch_loss, epoch_acc


def evaluate(model, data_loader, device):
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc="Evaluating"):
            data = data.to(device)
            data = data.permute(0, 1, 3, 2)
            
            outputs = model(data)
            fine_output = outputs['fine_output']
            
            seq_len = fine_output.size(1)
            predictions = torch.argmax(fine_output[:, seq_len//2, :], dim=-1)
            targets = target[:, seq_len//2]
            
            all_predictions.extend(predictions.cpu().numpy())
            all_targets.extend(targets.numpy())
    
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    
    accuracy = accuracy_score(all_targets, all_predictions)
    f1_macro = f1_score(all_targets, all_predictions, average='macro')
    kappa = cohen_kappa_score(all_targets, all_predictions)
    
    report = classification_report(all_targets, all_predictions, 
                                 target_names=['W', 'N1', 'N2', 'N3', 'REM'],
                                 output_dict=True)
    
    cm = confusion_matrix(all_targets, all_predictions)
    
    return {
        'accuracy': accuracy,
        'f1_macro': f1_macro,
        'kappa': kappa,
        'classification_report': report,
        'confusion_matrix': cm
    }


def main():
    config = {
        "d_model": 512,
        "n_heads": 32,
        "n_layers": 14,  # 增加深度
        "dropout": 0.15,
        "seq_len": 7,
        "batch_size": 16,
        "learning_rate": 2e-5,  # 较低的学习率
        "num_epochs": 20,
        "gradient_clip": 0.5,
        "weight_decay": 0.01,
        "label_smoothing": 0.1,
        "warmup_epochs": 2,
    }

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage5_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(os.path.join(log_dir, "training.log")),
            logging.StreamHandler(),
        ],
    )

    logging.info("="*80)
    logging.info("🚀 第5阶段训练：完整渐进式融合模型")
    logging.info("="*80)
    logging.info(f"配置: {config}")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"Device: {device}")

    # 数据准备
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"

    train_files = [
        "SC4181E0.npz", "SC4182E0.npz", "SC4161E0.npz", "SC4162E0.npz",
        "SC4131E0.npz", "SC4101E0.npz", "SC4102E0.npz", "SC4111E0.npz",
        "SC4112E0.npz", "SC4041E0.npz", "SC4042E0.npz", "SC4191E0.npz",
        "SC4192E0.npz", "SC4061E0.npz", "SC4062E0.npz", "SC4091E0.npz",
        "SC4092E0.npz", "SC4121E0.npz", "SC4122E0.npz", "SC4141E0.npz",
        "SC4142E0.npz", "SC4051E0.npz", "SC4052E0.npz", "SC4081E0.npz",
        "SC4082E0.npz", "SC4151E0.npz", "SC4152E0.npz", "SC4171E0.npz",
        "SC4172E0.npz",
    ]
    val_files = [
        "SC4021E0.npz", "SC4022E0.npz", "SC4031E0.npz",
        "SC4032E0.npz", "SC4071E0.npz", "SC4072E0.npz",
    ]
    test_files = [
        "SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"
    ]

    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]

    logging.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=True,
    )

    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )

    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")

    # 创建完整融合模型
    model = CompleteFusionMAMBAFORMER(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)

    logging.info(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")

    # 尝试加载之前阶段的最佳模型
    stage4_model = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_*/best_model.pth"
    stage3_model = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage3_*/best_model.pth"
    
    import glob
    for pattern in [stage4_model, stage3_model]:
        matches = glob.glob(pattern)
        if matches:
            best_model_path = sorted(matches)[-1]
            if os.path.exists(best_model_path):
                logging.info(f"加载预训练模型: {best_model_path}")
                checkpoint = torch.load(best_model_path)
                # 尝试加载部分权重
                try:
                    model.load_state_dict(checkpoint['model_state_dict'], strict=False)
                    logging.info("✅ 成功加载预训练权重（部分）")
                except:
                    logging.info("⚠️ 无法加载预训练权重，从头开始训练")
                break

    # 类别权重（基于数据分布）
    class_weights = torch.tensor([1.2, 3.5, 0.8, 2.0, 1.5]).to(device)
    criterion = HybridLoss(
        alpha=class_weights,
        gamma=2.0,
        smoothing=config['label_smoothing'],
        device=device
    )

    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"]
    )

    # 学习率调度器
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=5,
        T_mult=2,
        eta_min=1e-7
    )

    best_val_acc = 0
    best_test_acc = 0
    best_epoch = -1
    patience = 10
    patience_counter = 0
    
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_acc': [],
        'test_acc': []
    }

    # 训练循环
    for epoch in range(config["num_epochs"]):
        logging.info(f"\n{'='*60}")
        logging.info(f"Epoch {epoch+1}/{config['num_epochs']}")
        
        # Warmup
        if epoch < config["warmup_epochs"]:
            warmup_factor = (epoch + 1) / config["warmup_epochs"]
            for param_group in optimizer.param_groups:
                param_group['lr'] = config["learning_rate"] * warmup_factor * 0.3
            logging.info(f"Warmup学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 训练
        train_loss, train_acc = train_one_epoch(
            model, train_loader, criterion, optimizer,
            device, epoch, config
        )
        
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        
        # 验证
        val_metrics = evaluate(model, val_loader, device)
        val_acc = val_metrics['accuracy'] * 100
        
        # 测试
        test_metrics = evaluate(model, test_loader, device)
        test_acc = test_metrics['accuracy'] * 100
        
        logging.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_metrics['f1_macro']:.4f}")
        logging.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_metrics['f1_macro']:.4f}")
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_acc'].append(val_acc)
        history['test_acc'].append(test_acc)
        
        # 检查是否达到目标
        if test_acc >= 85.0:
            logging.info(f"🎉🎉🎉 达到目标！测试准确率: {test_acc:.2f}% >= 85%")
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config,
                'history': history,
            }, os.path.join(log_dir, 'target_achieved_model.pth'))
            
            # 生成最终报告
            result_summary = {
                'stage': '第5阶段完整融合',
                'test_accuracy': test_acc,
                'test_f1_macro': test_metrics['f1_macro'],
                'test_kappa': test_metrics['kappa'],
                'best_epoch': epoch,
                'config': config,
                'timestamp': timestamp,
            }
            
            with open(os.path.join(log_dir, 'result_summary.json'), 'w') as f:
                json.dump(result_summary, f, indent=2)
            
            logging.info("✅ 第5阶段训练完成！")
            break
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_test_acc = test_acc
            best_epoch = epoch
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config,
                'history': history,
            }, os.path.join(log_dir, 'best_model.pth'))
            logging.info(f"✅ 保存最佳模型 (Val: {val_acc:.2f}%, Test: {test_acc:.2f}%)")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
        
        # 学习率调度
        if epoch >= config["warmup_epochs"]:
            scheduler.step()
        
        # 显示各类别性能
        logging.info("\n测试集各类别性能:")
        for class_name in ['W', 'N1', 'N2', 'N3', 'REM']:
            class_metrics = test_metrics['classification_report'][class_name]
            logging.info(f"{class_name:>4}: Precision={class_metrics['precision']:.3f}, "
                        f"Recall={class_metrics['recall']:.3f}, "
                        f"F1={class_metrics['f1-score']:.3f}")
    
    logging.info(f"\n{'='*60}")
    logging.info(f"训练完成! 最佳测试准确率: {best_test_acc:.2f}% (Epoch {best_epoch+1})")
    
    # 保存训练历史
    with open(os.path.join(log_dir, 'training_history.json'), 'w') as f:
        json.dump(history, f, indent=2)
    
    return best_test_acc


if __name__ == "__main__":
    final_acc = main()
    print(f"\n最终准确率: {final_acc:.2f}%")