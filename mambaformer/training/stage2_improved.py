#!/usr/bin/env python3
"""
Stage 2 改进版 - 使用全部4通道数据
- 2个EEG通道 + 2个EMG通道
- 改进的跨模态注意力机制
- 分模态处理后融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

import numpy as np
import os
import glob
import logging
from datetime import datetime
from pathlib import Path
import json
from tqdm import tqdm
import math

# 导入必要的模块
import sys
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
from mambaformer.utils.sequence_dataset import SequenceSleepDataset
from mambaformer.utils.train_utils import EarlyStopping
from mambaformer.models.progressive_mambaformer_v1_fixed import ProgressiveMAMBAFORMER_V1_Fixed

# ===================== 改进的跨模态模型 =====================

class MultiModalFeatureExtractor(nn.Module):
    """多模态特征提取器 - 分别处理EEG和EMG"""
    def __init__(self, eeg_channels=2, emg_channels=2, d_model=512, dropout=0.15):
        super().__init__()
        
        # EEG特征提取器（针对低频信号优化）
        self.eeg_extractor = nn.Sequential(
            # 更大的kernel捕捉低频模式
            nn.Conv1d(eeg_channels, 64, kernel_size=100, stride=10, padding=45),
            nn.BatchNorm1d(64),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Conv1d(64, 128, kernel_size=50, stride=5, padding=22),
            nn.BatchNorm1d(128),
            nn.GELU(),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(128, 256, kernel_size=10, stride=1, padding=4),
            nn.BatchNorm1d(256),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Conv1d(256, d_model//2, kernel_size=5, stride=1, padding=2),
            nn.BatchNorm1d(d_model//2),
            nn.GELU()
        )
        
        # EMG特征提取器（针对高频信号优化）
        self.emg_extractor = nn.Sequential(
            # 较小的kernel捕捉高频模式
            nn.Conv1d(emg_channels, 32, kernel_size=25, stride=5, padding=10),
            nn.BatchNorm1d(32),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Conv1d(32, 64, kernel_size=15, stride=3, padding=6),
            nn.BatchNorm1d(64),
            nn.GELU(),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(64, 128, kernel_size=5, stride=1, padding=2),
            nn.BatchNorm1d(128),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Conv1d(128, d_model//2, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm1d(d_model//2),
            nn.GELU()
        )
        
        # 全局池化
        self.eeg_pool = nn.AdaptiveAvgPool1d(1)
        self.emg_pool = nn.AdaptiveMaxPool1d(1)  # EMG用最大池化捕捉峰值
        
        # 特征投影
        self.eeg_proj = nn.Sequential(
            nn.Linear(d_model//2, d_model),
            nn.LayerNorm(d_model),
            nn.Dropout(dropout)
        )
        
        self.emg_proj = nn.Sequential(
            nn.Linear(d_model//2, d_model),
            nn.LayerNorm(d_model),
            nn.Dropout(dropout)
        )
        
    def forward(self, x):
        """
        Args:
            x: (batch * seq_len, 4, 3000)
        Returns:
            eeg_feat: (batch * seq_len, d_model)
            emg_feat: (batch * seq_len, d_model)
        """
        # 分离EEG和EMG通道
        eeg = x[:, :2, :]  # 前2个通道是EEG
        emg = x[:, 2:, :]  # 后2个通道是EMG
        
        # 分别提取特征
        eeg_feat = self.eeg_extractor(eeg)  # (batch, d_model//2, T1)
        emg_feat = self.emg_extractor(emg)  # (batch, d_model//2, T2)
        
        # 全局池化
        eeg_feat = self.eeg_pool(eeg_feat).squeeze(-1)  # (batch, d_model//2)
        emg_feat = self.emg_pool(emg_feat).squeeze(-1)  # (batch, d_model//2)
        
        # 投影到相同维度
        eeg_feat = self.eeg_proj(eeg_feat)  # (batch, d_model)
        emg_feat = self.emg_proj(emg_feat)  # (batch, d_model)
        
        return eeg_feat, emg_feat


class CrossModalAttentionFusion(nn.Module):
    """改进的跨模态注意力融合模块"""
    def __init__(self, d_model=512, n_heads=8, dropout=0.1):
        super().__init__()
        
        # 跨模态注意力（EEG关注EMG）
        self.eeg_to_emg_attn = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        
        # 跨模态注意力（EMG关注EEG）
        self.emg_to_eeg_attn = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        
        # 自注意力
        self.eeg_self_attn = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        
        self.emg_self_attn = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        
        # 层归一化
        self.eeg_norm1 = nn.LayerNorm(d_model)
        self.eeg_norm2 = nn.LayerNorm(d_model)
        self.emg_norm1 = nn.LayerNorm(d_model)
        self.emg_norm2 = nn.LayerNorm(d_model)
        
        # 融合门控机制
        self.fusion_gate = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.Sigmoid()
        )
        
        # 最终融合
        self.fusion_mlp = nn.Sequential(
            nn.Linear(d_model * 2, d_model * 2),
            nn.LayerNorm(d_model * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 2, d_model),
            nn.LayerNorm(d_model)
        )
        
        # 可学习的模态权重
        self.modal_weights = nn.Parameter(torch.ones(2) * 0.5)
        
    def forward(self, eeg_feat, emg_feat):
        """
        Args:
            eeg_feat: (batch, seq_len, d_model)
            emg_feat: (batch, seq_len, d_model)
        Returns:
            fused: (batch, seq_len, d_model)
        """
        # 跨模态注意力
        eeg_cross, _ = self.eeg_to_emg_attn(eeg_feat, emg_feat, emg_feat)
        emg_cross, _ = self.emg_to_eeg_attn(emg_feat, eeg_feat, eeg_feat)
        
        # 残差连接
        eeg_feat = self.eeg_norm1(eeg_feat + eeg_cross)
        emg_feat = self.emg_norm1(emg_feat + emg_cross)
        
        # 自注意力
        eeg_self, _ = self.eeg_self_attn(eeg_feat, eeg_feat, eeg_feat)
        emg_self, _ = self.emg_self_attn(emg_feat, emg_feat, emg_feat)
        
        # 残差连接
        eeg_feat = self.eeg_norm2(eeg_feat + eeg_self)
        emg_feat = self.emg_norm2(emg_feat + emg_self)
        
        # 门控融合
        concat_feat = torch.cat([eeg_feat, emg_feat], dim=-1)
        gate = self.fusion_gate(concat_feat)
        
        # 加权融合
        weights = F.softmax(self.modal_weights, dim=0)
        weighted_feat = weights[0] * eeg_feat + weights[1] * emg_feat
        gated_feat = gate * weighted_feat + (1 - gate) * eeg_feat
        
        # 最终融合
        fused = self.fusion_mlp(torch.cat([gated_feat, weighted_feat], dim=-1))
        
        return fused


class ImprovedStage2Model(nn.Module):
    """改进的Stage 2模型 - 使用全部4通道"""
    def __init__(self, config):
        super().__init__()
        
        # 多模态特征提取
        self.feature_extractor = MultiModalFeatureExtractor(
            eeg_channels=2,
            emg_channels=2,
            d_model=config["d_model"],
            dropout=config["dropout"]
        )
        
        # 基础Transformer模型（用于时序建模）
        self.base_model = ProgressiveMAMBAFORMER_V1_Fixed(
            d_model=config["d_model"],
            n_heads=config["n_heads"],
            n_layers=config["n_layers"],
            dropout=config["dropout"],
            input_channels=4,  # 使用全部4个通道
            n_classes=5
        )
        
        # 跨模态注意力融合
        self.cross_modal_fusion = CrossModalAttentionFusion(
            d_model=config["d_model"],
            n_heads=config["attention_heads"],
            dropout=config["dropout"]
        )
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.Linear(config["d_model"], config["d_model"] // 2),
            nn.LayerNorm(config["d_model"] // 2),
            nn.GELU(),
            nn.Dropout(config["dropout"]),
            nn.Linear(config["d_model"] // 2, 5)
        )
        
        # 辅助分类器（用于多任务学习）
        self.eeg_classifier = nn.Linear(config["d_model"], 5)
        self.emg_classifier = nn.Linear(config["d_model"], 5)
        
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, 4, 3000)
        Returns:
            main_output: (batch, seq_len, 5)
            aux_outputs: dict with eeg_output and emg_output
        """
        batch_size, seq_len, channels, time_steps = x.shape
        
        # Reshape for processing
        x = x.view(batch_size * seq_len, channels, time_steps)
        
        # 提取多模态特征
        eeg_feat, emg_feat = self.feature_extractor(x)
        
        # Reshape back to sequence
        eeg_feat = eeg_feat.view(batch_size, seq_len, -1)
        emg_feat = emg_feat.view(batch_size, seq_len, -1)
        
        # 跨模态融合
        fused_feat = self.cross_modal_fusion(eeg_feat, emg_feat)
        
        # 获取基础模型的特征（用于残差连接）
        x_reshaped = x.view(batch_size, seq_len, channels, time_steps)
        base_output = self.base_model(x_reshaped)
        
        # 主分类输出
        main_output = self.classifier(fused_feat)
        
        # 辅助分类输出（用于多任务学习）
        eeg_output = self.eeg_classifier(eeg_feat)
        emg_output = self.emg_classifier(emg_feat)
        
        return main_output, {
            'eeg': eeg_output,
            'emg': emg_output,
            'base': base_output,
            'modal_weights': F.softmax(self.cross_modal_fusion.modal_weights, dim=0)
        }


# ===================== 损失函数 =====================

class MultiTaskLoss(nn.Module):
    """多任务损失函数"""
    def __init__(self, alpha=1.0, gamma=2.0):
        super().__init__()
        self.focal_loss = SequentialFocalLoss(alpha=alpha, gamma=gamma)
        
    def forward(self, outputs, labels):
        """
        Args:
            outputs: tuple of (main_output, aux_dict)
            labels: (batch, seq_len)
        """
        main_output, aux_dict = outputs
        
        # 主任务损失
        main_loss = self.focal_loss(main_output, labels)
        
        # 辅助任务损失
        eeg_loss = self.focal_loss(aux_dict['eeg'], labels) * 0.3
        emg_loss = self.focal_loss(aux_dict['emg'], labels) * 0.2
        base_loss = self.focal_loss(aux_dict['base'], labels) * 0.5
        
        # 总损失
        total_loss = main_loss + eeg_loss + emg_loss + base_loss
        
        return total_loss, {
            'main': main_loss.item(),
            'eeg': eeg_loss.item(),
            'emg': emg_loss.item(),
            'base': base_loss.item(),
            'total': total_loss.item()
        }


class SequentialFocalLoss(nn.Module):
    """序列Focal Loss"""
    def __init__(self, alpha=1, gamma=2, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        """
        Args:
            inputs: (batch, seq_len, n_classes)
            targets: (batch, seq_len)
        """
        batch_size, seq_len, n_classes = inputs.shape
        
        # Reshape for loss calculation
        inputs = inputs.reshape(-1, n_classes)
        targets = targets.reshape(-1)
        
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        p_t = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - p_t) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss.reshape(batch_size, seq_len)