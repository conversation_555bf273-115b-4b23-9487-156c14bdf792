2025-08-18 02:40:55,353 - INFO - ================================================================================
2025-08-18 02:40:55,353 - INFO - 🎯 Stage 4: Mamba-Transformer混合架构 + 渐进式分类策略
2025-08-18 02:40:55,353 - INFO - 核心创新：
2025-08-18 02:40:55,353 - INFO - 1. Mamba组件用于长程依赖建模
2025-08-18 02:40:55,353 - INFO - 2. 模态内特征精炼（EEG局部注意力，EOG/EMG轻量级Mamba）
2025-08-18 02:40:55,353 - INFO - 3. 渐进式分类策略（粗分类→细分类）
2025-08-18 02:40:55,353 - INFO - 4. 不确定性估计和置信度决策
2025-08-18 02:40:55,353 - INFO - 目标: ≥85% accuracy
2025-08-18 02:40:55,353 - INFO - ================================================================================
2025-08-18 02:40:55,353 - INFO - 配置: {'n_classes': 5, 'd_model': 160, 'n_heads': 8, 'n_mamba_layers': 2, 'n_transformer_layers': 1, 'dropout': 0.15, 'seq_len': 9, 'batch_size': 48, 'learning_rate': 0.0003, 'num_epochs': 15, 'weight_decay': 0.0002, 'patience': 10, 'coarse_weight': 0.3, 'uncertainty_weight': 0.1, 'use_improved_classifier': False, 'use_focal_loss': True}
2025-08-18 02:40:55,385 - INFO - 设备: cuda
2025-08-18 02:40:55,385 - INFO - 训练集受试者: ['00', '01', '02', '03', '06', '07', '08', '09', '10', '11', '12', '13', '15', '16', '17', '18']
2025-08-18 02:40:55,385 - INFO - 验证集受试者: ['04', '14']
2025-08-18 02:40:55,385 - INFO - 测试集受试者: ['05', '19']
2025-08-18 02:40:55,386 - INFO - 加载数据集...
2025-08-18 02:40:57,204 - INFO - 从 31 个文件加载了 33190 个epochs, 创建了 32942 个序列
2025-08-18 02:40:57,204 - INFO - 创建序列数据集: 32942个序列, 序列长度=9, 通道数=4, 总epochs=33190
2025-08-18 02:40:57,479 - INFO - 从 4 个文件加载了 4391 个epochs, 创建了 4359 个序列
2025-08-18 02:40:57,479 - INFO - 创建序列数据集: 4359个序列, 序列长度=9, 通道数=4, 总epochs=4391
2025-08-18 02:40:57,853 - INFO - 从 4 个文件加载了 4727 个epochs, 创建了 4695 个序列
2025-08-18 02:40:57,853 - INFO - 创建序列数据集: 4695个序列, 序列长度=9, 通道数=4, 总epochs=4727
2025-08-18 02:40:58,577 - INFO - 类别分布: [6290, 2097, 13775, 4841, 5939]
2025-08-18 02:40:58,578 - INFO - 类别权重: [0.7337223887443542, 2.200817108154297, 0.33503544330596924, 0.9533388614654541, 0.7770859599113464]
2025-08-18 02:40:58,579 - INFO - 训练集: 32942 sequences
2025-08-18 02:40:58,579 - INFO - 验证集: 4359 sequences
2025-08-18 02:40:58,579 - INFO - 测试集: 4695 sequences
2025-08-18 02:40:58,605 - INFO - 使用原始渐进式分类器（W/NREM/REM）
2025-08-18 02:40:58,646 - INFO - 创建Stage4MambaProgressiveModel: 参数量=1,625,729
2025-08-18 02:40:58,930 - INFO - 使用原始损失函数（含Focal Loss）
/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py:1562: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler() if device.type == 'cuda' else None
2025-08-18 02:41:00,324 - INFO - 混合精度训练: 启用
2025-08-18 02:41:00,324 - INFO - 
============================================================
2025-08-18 02:41:00,324 - INFO - Epoch 1/15

Training:   0%|          | 0/687 [00:00<?, ?it/s]/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py:1175: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast(enabled=use_amp):

Training:   0%|          | 0/687 [00:02<?, ?it/s, loss=3.1169, acc=47.22%, coarse=7.41%]
Training:   0%|          | 1/687 [00:02<25:32,  2.23s/it, loss=3.1169, acc=47.22%, coarse=7.41%]
Training:   0%|          | 1/687 [00:02<25:32,  2.23s/it, loss=3.3920, acc=38.19%, coarse=6.94%]
Training:   0%|          | 2/687 [00:02<13:38,  1.19s/it, loss=3.3920, acc=38.19%, coarse=6.94%]
Training:   0%|          | 2/687 [00:03<13:38,  1.19s/it, loss=2.1491, acc=41.82%, coarse=7.41%]
Training:   0%|          | 3/687 [00:03<09:28,  1.20it/s, loss=2.1491, acc=41.82%, coarse=7.41%]
Training:   0%|          | 3/687 [00:03<09:28,  1.20it/s, loss=1.4192, acc=39.12%, coarse=7.87%]
Training:   1%|          | 4/687 [00:03<07:18,  1.56it/s, loss=1.4192, acc=39.12%, coarse=7.87%]
Training:   1%|          | 4/687 [00:03<07:18,  1.56it/s, loss=1.3422, acc=39.54%, coarse=7.96%]
Training:   1%|          | 5/687 [00:03<06:14,  1.82it/s, loss=1.3422, acc=39.54%, coarse=7.96%]
Training:   1%|          | 5/687 [00:04<06:14,  1.82it/s, loss=1.4724, acc=37.65%, coarse=7.95%]
Training:   1%|          | 6/687 [00:04<05:42,  1.99it/s, loss=1.4724, acc=37.65%, coarse=7.95%]
Training:   1%|          | 6/687 [00:04<05:42,  1.99it/s, loss=1.4582, acc=39.12%, coarse=7.87%]
Training:   1%|          | 7/687 [00:04<05:18,  2.13it/s, loss=1.4582, acc=39.12%, coarse=7.87%]
Training:   1%|          | 7/687 [00:05<05:18,  2.13it/s, loss=1.6232, acc=39.29%, coarse=7.90%]
Training:   1%|          | 8/687 [00:05<05:00,  2.26it/s, loss=1.6232, acc=39.29%, coarse=7.90%]
Training:   1%|          | 8/687 [00:05<05:00,  2.26it/s, loss=1.2454, acc=41.13%, coarse=8.08%]
Training:   1%|▏         | 9/687 [00:05<05:07,  2.21it/s, loss=1.2454, acc=41.13%, coarse=8.08%]
Training:   1%|▏         | 9/687 [00:05<05:07,  2.21it/s, loss=1.2770, acc=41.71%, coarse=7.96%]
Training:   1%|▏         | 10/687 [00:05<05:14,  2.16it/s, loss=1.2770, acc=41.71%, coarse=7.96%]
Training:   1%|▏         | 10/687 [00:06<05:14,  2.16it/s, loss=1.1961, acc=42.66%, coarse=8.10%]
Training:   2%|▏         | 11/687 [00:06<05:18,  2.12it/s, loss=1.1961, acc=42.66%, coarse=8.10%]
Training:   2%|▏         | 11/687 [00:06<05:18,  2.12it/s, loss=1.2227, acc=44.00%, coarse=8.20%]
Training:   2%|▏         | 12/687 [00:06<05:10,  2.17it/s, loss=1.2227, acc=44.00%, coarse=8.20%]
Training:   2%|▏         | 12/687 [00:07<05:10,  2.17it/s, loss=1.2838, acc=45.66%, coarse=8.24%]
Training:   2%|▏         | 13/687 [00:07<05:17,  2.13it/s, loss=1.2838, acc=45.66%, coarse=8.24%]
Training:   2%|▏         | 13/687 [00:07<05:17,  2.13it/s, loss=1.1814, acc=46.38%, coarse=8.23%]
Training:   2%|▏         | 14/687 [00:07<04:59,  2.25it/s, loss=1.1814, acc=46.38%, coarse=8.23%]
Training:   2%|▏         | 14/687 [00:08<04:59,  2.25it/s, loss=1.0326, acc=47.59%, coarse=8.24%]
Training:   2%|▏         | 15/687 [00:08<05:06,  2.19it/s, loss=1.0326, acc=47.59%, coarse=8.24%]
Training:   2%|▏         | 15/687 [00:08<05:06,  2.19it/s, loss=1.1652, acc=48.39%, coarse=8.29%]
Training:   2%|▏         | 16/687 [00:08<04:49,  2.32it/s, loss=1.1652, acc=48.39%, coarse=8.29%]
Training:   2%|▏         | 16/687 [00:09<04:49,  2.32it/s, loss=1.0407, acc=49.06%, coarse=8.35%]
Training:   2%|▏         | 17/687 [00:09<04:33,  2.45it/s, loss=1.0407, acc=49.06%, coarse=8.35%]
Training:   2%|▏         | 17/687 [00:09<04:33,  2.45it/s, loss=1.4523, acc=49.86%, coarse=8.40%]
Training:   3%|▎         | 18/687 [00:09<04:21,  2.56it/s, loss=1.4523, acc=49.86%, coarse=8.40%]
Training:   3%|▎         | 18/687 [00:09<04:21,  2.56it/s, loss=1.2711, acc=50.48%, coarse=8.39%]
Training:   3%|▎         | 19/687 [00:09<04:14,  2.63it/s, loss=1.2711, acc=50.48%, coarse=8.39%]
Training:   3%|▎         | 19/687 [00:10<04:14,  2.63it/s, loss=1.1044, acc=50.64%, coarse=8.40%]
Training:   3%|▎         | 20/687 [00:10<04:09,  2.67it/s, loss=1.1044, acc=50.64%, coarse=8.40%]
Training:   3%|▎         | 20/687 [00:10<04:09,  2.67it/s, loss=0.9492, acc=51.28%, coarse=8.45%]
Training:   3%|▎         | 21/687 [00:10<04:21,  2.54it/s, loss=0.9492, acc=51.28%, coarse=8.45%]
Training:   3%|▎         | 21/687 [00:10<04:21,  2.54it/s, loss=1.0553, acc=51.57%, coarse=8.46%]
Training:   3%|▎         | 22/687 [00:10<04:18,  2.57it/s, loss=1.0553, acc=51.57%, coarse=8.46%]
Training:   3%|▎         | 22/687 [00:11<04:18,  2.57it/s, loss=1.1011, acc=52.22%, coarse=8.49%]
Training:   3%|▎         | 23/687 [00:11<04:24,  2.51it/s, loss=1.1011, acc=52.22%, coarse=8.49%]
Training:   3%|▎         | 23/687 [00:11<04:24,  2.51it/s, loss=0.9305, acc=53.02%, coarse=8.54%]
Training:   3%|▎         | 24/687 [00:11<04:16,  2.59it/s, loss=0.9305, acc=53.02%, coarse=8.54%]
Training:   3%|▎         | 24/687 [00:12<04:16,  2.59it/s, loss=1.3163, acc=53.22%, coarse=8.55%]
Training:   4%|▎         | 25/687 [00:12<04:11,  2.63it/s, loss=1.3163, acc=53.22%, coarse=8.55%]
Training:   4%|▎         | 25/687 [00:12<04:11,  2.63it/s, loss=0.7358, acc=54.21%, coarse=8.58%]
Training:   4%|▍         | 26/687 [00:12<04:08,  2.66it/s, loss=0.7358, acc=54.21%, coarse=8.58%]
Training:   4%|▍         | 26/687 [00:12<04:08,  2.66it/s, loss=1.0776, acc=54.75%, coarse=8.61%]
Training:   4%|▍         | 27/687 [00:12<04:07,  2.66it/s, loss=1.0776, acc=54.75%, coarse=8.61%]
Training:   4%|▍         | 27/687 [00:13<04:07,  2.66it/s, loss=1.3877, acc=54.48%, coarse=8.59%]
Training:   4%|▍         | 28/687 [00:13<04:21,  2.52it/s, loss=1.3877, acc=54.48%, coarse=8.59%]
Training:   4%|▍         | 28/687 [00:13<04:21,  2.52it/s, loss=1.2372, acc=54.47%, coarse=8.60%]
Training:   4%|▍         | 29/687 [00:13<04:34,  2.39it/s, loss=1.2372, acc=54.47%, coarse=8.60%]
Training:   4%|▍         | 29/687 [00:14<04:34,  2.39it/s, loss=0.7616, acc=55.10%, coarse=8.65%]
Training:   4%|▍         | 30/687 [00:14<04:45,  2.30it/s, loss=0.7616, acc=55.10%, coarse=8.65%]
Training:   4%|▍         | 30/687 [00:14<04:45,  2.30it/s, loss=1.1110, acc=55.27%, coarse=8.65%]
Training:   5%|▍         | 31/687 [00:14<04:30,  2.43it/s, loss=1.1110, acc=55.27%, coarse=8.65%]
Training:   5%|▍         | 31/687 [00:14<04:30,  2.43it/s, loss=0.9602, acc=55.51%, coarse=8.69%]
Training:   5%|▍         | 32/687 [00:14<04:18,  2.54it/s, loss=0.9602, acc=55.51%, coarse=8.69%]
Training:   5%|▍         | 32/687 [00:15<04:18,  2.54it/s, loss=0.9139, acc=55.84%, coarse=8.71%]
Training:   5%|▍         | 33/687 [00:15<04:10,  2.61it/s, loss=0.9139, acc=55.84%, coarse=8.71%]
Training:   5%|▍         | 33/687 [00:15<04:10,  2.61it/s, loss=1.2574, acc=56.32%, coarse=8.72%]
Training:   5%|▍         | 34/687 [00:15<04:04,  2.67it/s, loss=1.2574, acc=56.32%, coarse=8.72%]
Training:   5%|▍         | 34/687 [00:15<04:04,  2.67it/s, loss=1.0291, acc=56.71%, coarse=8.75%]
Training:   5%|▌         | 35/687 [00:15<04:09,  2.62it/s, loss=1.0291, acc=56.71%, coarse=8.75%]
Training:   5%|▌         | 35/687 [00:16<04:09,  2.62it/s, loss=1.2971, acc=56.64%, coarse=8.74%]
Training:   5%|▌         | 36/687 [00:16<04:22,  2.48it/s, loss=1.2971, acc=56.64%, coarse=8.74%]
Training:   5%|▌         | 36/687 [00:16<04:22,  2.48it/s, loss=0.9957, acc=57.09%, coarse=8.74%]
Training:   5%|▌         | 37/687 [00:16<04:17,  2.53it/s, loss=0.9957, acc=57.09%, coarse=8.74%]
Training:   5%|▌         | 37/687 [00:17<04:17,  2.53it/s, loss=0.9631, acc=57.55%, coarse=8.74%]
Training:   6%|▌         | 38/687 [00:17<04:25,  2.44it/s, loss=0.9631, acc=57.55%, coarse=8.74%]
Training:   6%|▌         | 38/687 [00:17<04:25,  2.44it/s, loss=0.7276, acc=58.06%, coarse=8.78%]
Training:   6%|▌         | 39/687 [00:17<04:16,  2.53it/s, loss=0.7276, acc=58.06%, coarse=8.78%]
Training:   6%|▌         | 39/687 [00:18<04:16,  2.53it/s, loss=0.8108, acc=58.57%, coarse=8.83%]
Training:   6%|▌         | 40/687 [00:18<04:27,  2.42it/s, loss=0.8108, acc=58.57%, coarse=8.83%]
Training:   6%|▌         | 40/687 [00:18<04:27,  2.42it/s, loss=1.2002, acc=58.69%, coarse=8.82%]
Training:   6%|▌         | 41/687 [00:18<04:17,  2.50it/s, loss=1.2002, acc=58.69%, coarse=8.82%]
Training:   6%|▌         | 41/687 [00:18<04:17,  2.50it/s, loss=0.7006, acc=59.20%, coarse=8.84%]
Training:   6%|▌         | 42/687 [00:18<04:22,  2.46it/s, loss=0.7006, acc=59.20%, coarse=8.84%]
Training:   6%|▌         | 42/687 [00:19<04:22,  2.46it/s, loss=0.7879, acc=59.37%, coarse=8.86%]
Training:   6%|▋         | 43/687 [00:19<04:13,  2.54it/s, loss=0.7879, acc=59.37%, coarse=8.86%]
Training:   6%|▋         | 43/687 [00:19<04:13,  2.54it/s, loss=1.1666, acc=59.32%, coarse=8.84%]
Training:   6%|▋         | 44/687 [00:19<04:07,  2.60it/s, loss=1.1666, acc=59.32%, coarse=8.84%]
Training:   6%|▋         | 44/687 [00:20<04:07,  2.60it/s, loss=0.9863, acc=59.43%, coarse=8.83%]
Training:   7%|▋         | 45/687 [00:20<04:14,  2.52it/s, loss=0.9863, acc=59.43%, coarse=8.83%]
Training:   7%|▋         | 45/687 [00:20<04:14,  2.52it/s, loss=0.9266, acc=59.27%, coarse=8.84%]
Training:   7%|▋         | 46/687 [00:20<04:07,  2.59it/s, loss=0.9266, acc=59.27%, coarse=8.84%]
Training:   7%|▋         | 46/687 [00:20<04:07,  2.59it/s, loss=1.3042, acc=59.47%, coarse=8.84%]
Training:   7%|▋         | 47/687 [00:20<04:13,  2.53it/s, loss=1.3042, acc=59.47%, coarse=8.84%]
Training:   7%|▋         | 47/687 [00:21<04:13,  2.53it/s, loss=0.8165, acc=59.92%, coarse=8.85%]
Training:   7%|▋         | 48/687 [00:21<04:06,  2.59it/s, loss=0.8165, acc=59.92%, coarse=8.85%]
Training:   7%|▋         | 48/687 [00:21<04:06,  2.59it/s, loss=0.8994, acc=60.27%, coarse=8.87%]
Training:   7%|▋         | 49/687 [00:21<04:12,  2.52it/s, loss=0.8994, acc=60.27%, coarse=8.87%]
Training:   7%|▋         | 49/687 [00:21<04:12,  2.52it/s, loss=0.8217, acc=60.43%, coarse=8.87%]
Training:   7%|▋         | 50/687 [00:21<04:04,  2.61it/s, loss=0.8217, acc=60.43%, coarse=8.87%]
Training:   7%|▋         | 50/687 [00:22<04:04,  2.61it/s, loss=1.1352, acc=60.35%, coarse=8.87%]
Training:   7%|▋         | 51/687 [00:22<04:06,  2.58it/s, loss=1.1352, acc=60.35%, coarse=8.87%]
Training:   7%|▋         | 51/687 [00:22<04:06,  2.58it/s, loss=0.7329, acc=60.73%, coarse=8.89%]
Training:   8%|▊         | 52/687 [00:22<03:58,  2.66it/s, loss=0.7329, acc=60.73%, coarse=8.89%]
Training:   8%|▊         | 52/687 [00:23<03:58,  2.66it/s, loss=0.8347, acc=60.96%, coarse=8.90%]
Training:   8%|▊         | 53/687 [00:23<04:08,  2.55it/s, loss=0.8347, acc=60.96%, coarse=8.90%]
Training:   8%|▊         | 53/687 [00:23<04:08,  2.55it/s, loss=1.0326, acc=61.26%, coarse=8.92%]
Training:   8%|▊         | 54/687 [00:23<04:04,  2.59it/s, loss=1.0326, acc=61.26%, coarse=8.92%]
Training:   8%|▊         | 54/687 [00:23<04:04,  2.59it/s, loss=0.9421, acc=61.52%, coarse=8.94%]
Training:   8%|▊         | 55/687 [00:23<04:08,  2.54it/s, loss=0.9421, acc=61.52%, coarse=8.94%]
Training:   8%|▊         | 55/687 [00:24<04:08,  2.54it/s, loss=0.9819, acc=61.72%, coarse=8.95%]
Training:   8%|▊         | 56/687 [00:24<04:03,  2.59it/s, loss=0.9819, acc=61.72%, coarse=8.95%]
Training:   8%|▊         | 56/687 [00:24<04:03,  2.59it/s, loss=1.0091, acc=61.88%, coarse=8.97%]
Training:   8%|▊         | 57/687 [00:24<03:59,  2.63it/s, loss=1.0091, acc=61.88%, coarse=8.97%]
Training:   8%|▊         | 57/687 [00:25<03:59,  2.63it/s, loss=1.0873, acc=61.91%, coarse=8.96%]
Training:   8%|▊         | 58/687 [00:25<03:58,  2.64it/s, loss=1.0873, acc=61.91%, coarse=8.96%]
Training:   8%|▊         | 58/687 [00:25<03:58,  2.64it/s, loss=1.1137, acc=61.95%, coarse=8.97%]
Training:   9%|▊         | 59/687 [00:25<03:54,  2.68it/s, loss=1.1137, acc=61.95%, coarse=8.97%]
Training:   9%|▊         | 59/687 [00:25<03:54,  2.68it/s, loss=0.8263, acc=62.07%, coarse=8.99%]
Training:   9%|▊         | 60/687 [00:25<03:52,  2.69it/s, loss=0.8263, acc=62.07%, coarse=8.99%]
Training:   9%|▊         | 60/687 [00:26<03:52,  2.69it/s, loss=1.0811, acc=62.03%, coarse=9.00%]
Training:   9%|▉         | 61/687 [00:26<03:48,  2.74it/s, loss=1.0811, acc=62.03%, coarse=9.00%]
Training:   9%|▉         | 61/687 [00:26<03:48,  2.74it/s, loss=1.1402, acc=62.06%, coarse=9.01%]
Training:   9%|▉         | 62/687 [00:26<03:45,  2.77it/s, loss=1.1402, acc=62.06%, coarse=9.01%]
Training:   9%|▉         | 62/687 [00:26<03:45,  2.77it/s, loss=0.8507, acc=62.08%, coarse=9.01%]
Training:   9%|▉         | 63/687 [00:26<03:43,  2.79it/s, loss=0.8507, acc=62.08%, coarse=9.01%]
Training:   9%|▉         | 63/687 [00:27<03:43,  2.79it/s, loss=0.7060, acc=62.26%, coarse=9.03%]
Training:   9%|▉         | 64/687 [00:27<03:44,  2.78it/s, loss=0.7060, acc=62.26%, coarse=9.03%]
Training:   9%|▉         | 64/687 [00:27<03:44,  2.78it/s, loss=0.9342, acc=62.41%, coarse=9.03%]
Training:   9%|▉         | 65/687 [00:27<03:43,  2.78it/s, loss=0.9342, acc=62.41%, coarse=9.03%]
Training:   9%|▉         | 65/687 [00:27<03:43,  2.78it/s, loss=0.9573, acc=62.54%, coarse=9.04%]
Training:  10%|▉         | 66/687 [00:27<03:44,  2.77it/s, loss=0.9573, acc=62.54%, coarse=9.04%]
Training:  10%|▉         | 66/687 [00:28<03:44,  2.77it/s, loss=0.7214, acc=62.71%, coarse=9.05%]
Training:  10%|▉         | 67/687 [00:28<03:48,  2.72it/s, loss=0.7214, acc=62.71%, coarse=9.05%]
Training:  10%|▉         | 67/687 [00:28<03:48,  2.72it/s, loss=0.9736, acc=62.69%, coarse=9.07%]
Training:  10%|▉         | 68/687 [00:28<03:53,  2.65it/s, loss=0.9736, acc=62.69%, coarse=9.07%]
Training:  10%|▉         | 68/687 [00:29<03:53,  2.65it/s, loss=0.8855, acc=62.69%, coarse=9.08%]
Training:  10%|█         | 69/687 [00:29<03:49,  2.69it/s, loss=0.8855, acc=62.69%, coarse=9.08%]
Training:  10%|█         | 69/687 [00:29<03:49,  2.69it/s, loss=0.7047, acc=62.90%, coarse=9.10%]
Training:  10%|█         | 70/687 [00:29<03:46,  2.73it/s, loss=0.7047, acc=62.90%, coarse=9.10%]
Training:  10%|█         | 70/687 [00:29<03:46,  2.73it/s, loss=1.1015, acc=62.93%, coarse=9.11%]
Training:  10%|█         | 71/687 [00:29<03:44,  2.75it/s, loss=1.1015, acc=62.93%, coarse=9.11%]
Training:  10%|█         | 71/687 [00:30<03:44,  2.75it/s, loss=0.6614, acc=63.08%, coarse=9.12%]
Training:  10%|█         | 72/687 [00:30<03:43,  2.76it/s, loss=0.6614, acc=63.08%, coarse=9.12%]
Training:  10%|█         | 72/687 [00:30<03:43,  2.76it/s, loss=0.7618, acc=63.19%, coarse=9.14%]
Training:  11%|█         | 73/687 [00:30<03:41,  2.77it/s, loss=0.7618, acc=63.19%, coarse=9.14%]
Training:  11%|█         | 73/687 [00:30<03:41,  2.77it/s, loss=0.6590, acc=63.44%, coarse=9.15%]
Training:  11%|█         | 74/687 [00:30<03:40,  2.77it/s, loss=0.6590, acc=63.44%, coarse=9.15%]
Training:  11%|█         | 74/687 [00:31<03:40,  2.77it/s, loss=0.6053, acc=63.72%, coarse=9.18%]
Training:  11%|█         | 75/687 [00:31<03:40,  2.78it/s, loss=0.6053, acc=63.72%, coarse=9.18%]
Training:  11%|█         | 75/687 [00:31<03:40,  2.78it/s, loss=1.2752, acc=63.89%, coarse=9.17%]
Training:  11%|█         | 76/687 [00:31<03:45,  2.71it/s, loss=1.2752, acc=63.89%, coarse=9.17%]
Training:  11%|█         | 76/687 [00:31<03:45,  2.71it/s, loss=0.9709, acc=64.02%, coarse=9.18%]
Training:  11%|█         | 77/687 [00:31<03:42,  2.74it/s, loss=0.9709, acc=64.02%, coarse=9.18%]
Training:  11%|█         | 77/687 [00:32<03:42,  2.74it/s, loss=1.1103, acc=64.01%, coarse=9.17%]
Training:  11%|█▏        | 78/687 [00:32<03:50,  2.64it/s, loss=1.1103, acc=64.01%, coarse=9.17%]
Training:  11%|█▏        | 78/687 [00:32<03:50,  2.64it/s, loss=0.8919, acc=64.16%, coarse=9.18%]
Training:  11%|█▏        | 79/687 [00:32<03:49,  2.65it/s, loss=0.8919, acc=64.16%, coarse=9.18%]
Training:  11%|█▏        | 79/687 [00:33<03:49,  2.65it/s, loss=1.1085, acc=64.28%, coarse=9.18%]
Training:  12%|█▏        | 80/687 [00:33<03:55,  2.58it/s, loss=1.1085, acc=64.28%, coarse=9.18%]
Training:  12%|█▏        | 80/687 [00:33<03:55,  2.58it/s, loss=0.8485, acc=64.44%, coarse=9.18%]
Training:  12%|█▏        | 81/687 [00:33<04:00,  2.52it/s, loss=0.8485, acc=64.44%, coarse=9.18%]
Training:  12%|█▏        | 81/687 [00:33<04:00,  2.52it/s, loss=0.9474, acc=64.60%, coarse=9.20%]
Training:  12%|█▏        | 82/687 [00:33<04:02,  2.50it/s, loss=0.9474, acc=64.60%, coarse=9.20%]
Training:  12%|█▏        | 82/687 [00:34<04:02,  2.50it/s, loss=0.8144, acc=64.69%, coarse=9.21%]
Training:  12%|█▏        | 83/687 [00:34<04:03,  2.48it/s, loss=0.8144, acc=64.69%, coarse=9.21%]
Training:  12%|█▏        | 83/687 [00:34<04:03,  2.48it/s, loss=0.7181, acc=64.83%, coarse=9.22%]
Training:  12%|█▏        | 84/687 [00:34<04:02,  2.49it/s, loss=0.7181, acc=64.83%, coarse=9.22%]
Training:  12%|█▏        | 84/687 [00:35<04:02,  2.49it/s, loss=0.8665, acc=64.95%, coarse=9.22%]
Training:  12%|█▏        | 85/687 [00:35<04:02,  2.49it/s, loss=0.8665, acc=64.95%, coarse=9.22%]
Training:  12%|█▏        | 85/687 [00:35<04:02,  2.49it/s, loss=0.9044, acc=64.95%, coarse=9.23%]
Training:  13%|█▎        | 86/687 [00:35<04:00,  2.49it/s, loss=0.9044, acc=64.95%, coarse=9.23%]
Training:  13%|█▎        | 86/687 [00:35<04:00,  2.49it/s, loss=0.7601, acc=65.14%, coarse=9.25%]
Training:  13%|█▎        | 87/687 [00:35<04:00,  2.50it/s, loss=0.7601, acc=65.14%, coarse=9.25%]
Training:  13%|█▎        | 87/687 [00:36<04:00,  2.50it/s, loss=0.7609, acc=65.31%, coarse=9.26%]
Training:  13%|█▎        | 88/687 [00:36<03:52,  2.57it/s, loss=0.7609, acc=65.31%, coarse=9.26%]
Training:  13%|█▎        | 88/687 [00:36<03:52,  2.57it/s, loss=0.7949, acc=65.48%, coarse=9.26%]
Training:  13%|█▎        | 89/687 [00:36<03:53,  2.56it/s, loss=0.7949, acc=65.48%, coarse=9.26%]
Training:  13%|█▎        | 89/687 [00:37<03:53,  2.56it/s, loss=0.7972, acc=65.70%, coarse=9.27%]
Training:  13%|█▎        | 90/687 [00:37<04:02,  2.46it/s, loss=0.7972, acc=65.70%, coarse=9.27%]
Training:  13%|█▎        | 90/687 [00:37<04:02,  2.46it/s, loss=0.9752, acc=65.80%, coarse=9.29%]
Training:  13%|█▎        | 91/687 [00:37<04:11,  2.37it/s, loss=0.9752, acc=65.80%, coarse=9.29%]
Training:  13%|█▎        | 91/687 [00:38<04:11,  2.37it/s, loss=0.9120, acc=65.92%, coarse=9.30%]
Training:  13%|█▎        | 92/687 [00:38<04:13,  2.35it/s, loss=0.9120, acc=65.92%, coarse=9.30%]
Training:  13%|█▎        | 92/687 [00:38<04:13,  2.35it/s, loss=0.6953, acc=66.07%, coarse=9.31%]
Training:  14%|█▎        | 93/687 [00:38<04:10,  2.37it/s, loss=0.6953, acc=66.07%, coarse=9.31%]
Training:  14%|█▎        | 93/687 [00:38<04:10,  2.37it/s, loss=0.6035, acc=66.23%, coarse=9.32%]
Training:  14%|█▎        | 94/687 [00:38<04:08,  2.39it/s, loss=0.6035, acc=66.23%, coarse=9.32%]
Training:  14%|█▎        | 94/687 [00:39<04:08,  2.39it/s, loss=0.8406, acc=66.26%, coarse=9.32%]
Training:  14%|█▍        | 95/687 [00:39<04:07,  2.40it/s, loss=0.8406, acc=66.26%, coarse=9.32%]
Training:  14%|█▍        | 95/687 [00:39<04:07,  2.40it/s, loss=0.7124, acc=66.39%, coarse=9.34%]
Training:  14%|█▍        | 96/687 [00:39<04:06,  2.40it/s, loss=0.7124, acc=66.39%, coarse=9.34%]
Training:  14%|█▍        | 96/687 [00:40<04:06,  2.40it/s, loss=0.7566, acc=66.48%, coarse=9.34%]
Training:  14%|█▍        | 97/687 [00:40<04:07,  2.38it/s, loss=0.7566, acc=66.48%, coarse=9.34%]
Training:  14%|█▍        | 97/687 [00:40<04:07,  2.38it/s, loss=0.6281, acc=66.64%, coarse=9.35%]
Training:  14%|█▍        | 98/687 [00:40<04:05,  2.40it/s, loss=0.6281, acc=66.64%, coarse=9.35%]
Training:  14%|█▍        | 98/687 [00:40<04:05,  2.40it/s, loss=0.7206, acc=66.71%, coarse=9.36%]
Training:  14%|█▍        | 99/687 [00:40<04:02,  2.42it/s, loss=0.7206, acc=66.71%, coarse=9.36%]
Training:  14%|█▍        | 99/687 [00:41<04:02,  2.42it/s, loss=0.7122, acc=66.84%, coarse=9.37%]
Training:  15%|█▍        | 100/687 [00:41<04:02,  2.42it/s, loss=0.7122, acc=66.84%, coarse=9.37%]
Training:  15%|█▍        | 100/687 [00:41<04:02,  2.42it/s, loss=1.0578, acc=66.88%, coarse=9.38%]
Training:  15%|█▍        | 101/687 [00:41<03:53,  2.51it/s, loss=1.0578, acc=66.88%, coarse=9.38%]
Training:  15%|█▍        | 101/687 [00:42<03:53,  2.51it/s, loss=0.9035, acc=66.83%, coarse=9.38%]
Training:  15%|█▍        | 102/687 [00:42<03:55,  2.49it/s, loss=0.9035, acc=66.83%, coarse=9.38%]
Training:  15%|█▍        | 102/687 [00:42<03:55,  2.49it/s, loss=1.0140, acc=66.75%, coarse=9.39%]
Training:  15%|█▍        | 103/687 [00:42<03:57,  2.46it/s, loss=1.0140, acc=66.75%, coarse=9.39%]
Training:  15%|█▍        | 103/687 [00:42<03:57,  2.46it/s, loss=0.7676, acc=66.89%, coarse=9.39%]
Training:  15%|█▌        | 104/687 [00:42<03:48,  2.55it/s, loss=0.7676, acc=66.89%, coarse=9.39%]
Training:  15%|█▌        | 104/687 [00:43<03:48,  2.55it/s, loss=0.5430, acc=67.05%, coarse=9.40%]
Training:  15%|█▌        | 105/687 [00:43<03:48,  2.55it/s, loss=0.5430, acc=67.05%, coarse=9.40%]
Training:  15%|█▌        | 105/687 [00:43<03:48,  2.55it/s, loss=0.7586, acc=67.19%, coarse=9.40%]
Training:  15%|█▌        | 106/687 [00:43<03:49,  2.54it/s, loss=0.7586, acc=67.19%, coarse=9.40%]
Training:  15%|█▌        | 106/687 [00:44<03:49,  2.54it/s, loss=0.7715, acc=67.24%, coarse=9.41%]
Training:  16%|█▌        | 107/687 [00:44<03:42,  2.61it/s, loss=0.7715, acc=67.24%, coarse=9.41%]
Training:  16%|█▌        | 107/687 [00:44<03:42,  2.61it/s, loss=0.9459, acc=67.21%, coarse=9.41%]
Training:  16%|█▌        | 108/687 [00:44<03:38,  2.65it/s, loss=0.9459, acc=67.21%, coarse=9.41%]
Training:  16%|█▌        | 108/687 [00:44<03:38,  2.65it/s, loss=0.8144, acc=67.36%, coarse=9.42%]
Training:  16%|█▌        | 109/687 [00:44<03:34,  2.69it/s, loss=0.8144, acc=67.36%, coarse=9.42%]
Training:  16%|█▌        | 109/687 [00:45<03:34,  2.69it/s, loss=0.9034, acc=67.43%, coarse=9.43%]
Training:  16%|█▌        | 110/687 [00:45<03:39,  2.63it/s, loss=0.9034, acc=67.43%, coarse=9.43%]
Training:  16%|█▌        | 110/687 [00:45<03:39,  2.63it/s, loss=0.9971, acc=67.52%, coarse=9.42%]
Training:  16%|█▌        | 111/687 [00:45<03:38,  2.64it/s, loss=0.9971, acc=67.52%, coarse=9.42%]
Training:  16%|█▌        | 111/687 [00:45<03:38,  2.64it/s, loss=1.0047, acc=67.58%, coarse=9.43%]
Training:  16%|█▋        | 112/687 [00:45<03:37,  2.64it/s, loss=1.0047, acc=67.58%, coarse=9.43%]
Training:  16%|█▋        | 112/687 [00:46<03:37,  2.64it/s, loss=1.2360, acc=67.58%, coarse=9.42%]
Training:  16%|█▋        | 113/687 [00:46<03:35,  2.66it/s, loss=1.2360, acc=67.58%, coarse=9.42%]
Training:  16%|█▋        | 113/687 [00:46<03:35,  2.66it/s, loss=0.8700, acc=67.52%, coarse=9.43%]
Training:  17%|█▋        | 114/687 [00:46<03:37,  2.63it/s, loss=0.8700, acc=67.52%, coarse=9.43%]
Training:  17%|█▋        | 114/687 [00:47<03:37,  2.63it/s, loss=0.8428, acc=67.51%, coarse=9.43%]
Training:  17%|█▋        | 115/687 [00:47<03:53,  2.45it/s, loss=0.8428, acc=67.51%, coarse=9.43%]
Training:  17%|█▋        | 115/687 [00:47<03:53,  2.45it/s, loss=0.8146, acc=67.54%, coarse=9.44%]
Training:  17%|█▋        | 116/687 [00:47<03:50,  2.47it/s, loss=0.8146, acc=67.54%, coarse=9.44%]
Training:  17%|█▋        | 116/687 [00:47<03:50,  2.47it/s, loss=0.8240, acc=67.59%, coarse=9.44%]
Training:  17%|█▋        | 117/687 [00:47<03:46,  2.52it/s, loss=0.8240, acc=67.59%, coarse=9.44%]
Training:  17%|█▋        | 117/687 [00:48<03:46,  2.52it/s, loss=0.8761, acc=67.61%, coarse=9.44%]
Training:  17%|█▋        | 118/687 [00:48<03:42,  2.56it/s, loss=0.8761, acc=67.61%, coarse=9.44%]
Training:  17%|█▋        | 118/687 [00:48<03:42,  2.56it/s, loss=0.9477, acc=67.66%, coarse=9.44%]
Training:  17%|█▋        | 119/687 [00:48<03:38,  2.60it/s, loss=0.9477, acc=67.66%, coarse=9.44%]
Training:  17%|█▋        | 119/687 [00:49<03:38,  2.60it/s, loss=0.7716, acc=67.67%, coarse=9.45%]
Training:  17%|█▋        | 120/687 [00:49<03:37,  2.61it/s, loss=0.7716, acc=67.67%, coarse=9.45%]
Training:  17%|█▋        | 120/687 [00:49<03:37,  2.61it/s, loss=0.6917, acc=67.76%, coarse=9.45%]
Training:  18%|█▊        | 121/687 [00:49<03:40,  2.57it/s, loss=0.6917, acc=67.76%, coarse=9.45%]
Training:  18%|█▊        | 121/687 [00:49<03:40,  2.57it/s, loss=0.6597, acc=67.91%, coarse=9.46%]
Training:  18%|█▊        | 122/687 [00:49<03:42,  2.54it/s, loss=0.6597, acc=67.91%, coarse=9.46%]
Training:  18%|█▊        | 122/687 [00:50<03:42,  2.54it/s, loss=1.1858, acc=67.93%, coarse=9.46%]
Training:  18%|█▊        | 123/687 [00:50<03:53,  2.42it/s, loss=1.1858, acc=67.93%, coarse=9.46%]
Training:  18%|█▊        | 123/687 [00:50<03:53,  2.42it/s, loss=0.6624, acc=68.06%, coarse=9.47%]
Training:  18%|█▊        | 124/687 [00:50<03:48,  2.47it/s, loss=0.6624, acc=68.06%, coarse=9.47%]
Training:  18%|█▊        | 124/687 [00:51<03:48,  2.47it/s, loss=0.9039, acc=68.14%, coarse=9.47%]
Training:  18%|█▊        | 125/687 [00:51<03:42,  2.52it/s, loss=0.9039, acc=68.14%, coarse=9.47%]
Training:  18%|█▊        | 125/687 [00:51<03:42,  2.52it/s, loss=0.9838, acc=68.24%, coarse=9.47%]
Training:  18%|█▊        | 126/687 [00:51<03:43,  2.51it/s, loss=0.9838, acc=68.24%, coarse=9.47%]
Training:  18%|█▊        | 126/687 [00:51<03:43,  2.51it/s, loss=0.7569, acc=68.27%, coarse=9.48%]
Training:  18%|█▊        | 127/687 [00:51<03:44,  2.50it/s, loss=0.7569, acc=68.27%, coarse=9.48%]
Training:  18%|█▊        | 127/687 [00:52<03:44,  2.50it/s, loss=0.7780, acc=68.35%, coarse=9.48%]
Training:  19%|█▊        | 128/687 [00:52<03:48,  2.45it/s, loss=0.7780, acc=68.35%, coarse=9.48%]
Training:  19%|█▊        | 128/687 [00:52<03:48,  2.45it/s, loss=0.6401, acc=68.39%, coarse=9.49%]
Training:  19%|█▉        | 129/687 [00:52<03:39,  2.54it/s, loss=0.6401, acc=68.39%, coarse=9.49%]
Training:  19%|█▉        | 129/687 [00:53<03:39,  2.54it/s, loss=0.6897, acc=68.47%, coarse=9.50%]
Training:  19%|█▉        | 130/687 [00:53<03:36,  2.58it/s, loss=0.6897, acc=68.47%, coarse=9.50%]
Training:  19%|█▉        | 130/687 [00:53<03:36,  2.58it/s, loss=0.6892, acc=68.56%, coarse=9.50%]
Training:  19%|█▉        | 131/687 [00:53<03:31,  2.63it/s, loss=0.6892, acc=68.56%, coarse=9.50%]
Training:  19%|█▉        | 131/687 [00:53<03:31,  2.63it/s, loss=0.8038, acc=68.57%, coarse=9.51%]
Training:  19%|█▉        | 132/687 [00:53<03:28,  2.66it/s, loss=0.8038, acc=68.57%, coarse=9.51%]
Training:  19%|█▉        | 132/687 [00:54<03:28,  2.66it/s, loss=0.9163, acc=68.53%, coarse=9.51%]
Training:  19%|█▉        | 133/687 [00:54<03:27,  2.67it/s, loss=0.9163, acc=68.53%, coarse=9.51%]
Training:  19%|█▉        | 133/687 [00:54<03:27,  2.67it/s, loss=0.8171, acc=68.53%, coarse=9.51%]
Training:  20%|█▉        | 134/687 [00:54<03:23,  2.71it/s, loss=0.8171, acc=68.53%, coarse=9.51%]
Training:  20%|█▉        | 134/687 [00:54<03:23,  2.71it/s, loss=1.0159, acc=68.45%, coarse=9.51%]
Training:  20%|█▉        | 135/687 [00:54<03:20,  2.76it/s, loss=1.0159, acc=68.45%, coarse=9.51%]
Training:  20%|█▉        | 135/687 [00:55<03:20,  2.76it/s, loss=0.9214, acc=68.46%, coarse=9.51%]
Training:  20%|█▉        | 136/687 [00:55<03:17,  2.79it/s, loss=0.9214, acc=68.46%, coarse=9.51%]
Training:  20%|█▉        | 136/687 [00:55<03:17,  2.79it/s, loss=0.8526, acc=68.48%, coarse=9.50%]
Training:  20%|█▉        | 137/687 [00:55<03:14,  2.82it/s, loss=0.8526, acc=68.48%, coarse=9.50%]
Training:  20%|█▉        | 137/687 [00:55<03:14,  2.82it/s, loss=0.6829, acc=68.54%, coarse=9.51%]
Training:  20%|██        | 138/687 [00:55<03:12,  2.85it/s, loss=0.6829, acc=68.54%, coarse=9.51%]
Training:  20%|██        | 138/687 [00:56<03:12,  2.85it/s, loss=0.8485, acc=68.60%, coarse=9.51%]
Training:  20%|██        | 139/687 [00:56<03:11,  2.86it/s, loss=0.8485, acc=68.60%, coarse=9.51%]
Training:  20%|██        | 139/687 [00:56<03:11,  2.86it/s, loss=0.7118, acc=68.69%, coarse=9.51%]
Training:  20%|██        | 140/687 [00:56<03:11,  2.86it/s, loss=0.7118, acc=68.69%, coarse=9.51%]
Training:  20%|██        | 140/687 [00:56<03:11,  2.86it/s, loss=0.7681, acc=68.70%, coarse=9.52%]
Training:  21%|██        | 141/687 [00:56<03:09,  2.88it/s, loss=0.7681, acc=68.70%, coarse=9.52%]
Training:  21%|██        | 141/687 [00:57<03:09,  2.88it/s, loss=0.8682, acc=68.76%, coarse=9.52%]
Training:  21%|██        | 142/687 [00:57<03:08,  2.89it/s, loss=0.8682, acc=68.76%, coarse=9.52%]
Training:  21%|██        | 142/687 [00:57<03:08,  2.89it/s, loss=0.6740, acc=68.88%, coarse=9.53%]
Training:  21%|██        | 143/687 [00:57<03:08,  2.88it/s, loss=0.6740, acc=68.88%, coarse=9.53%]
Training:  21%|██        | 143/687 [00:57<03:08,  2.88it/s, loss=0.7578, acc=68.92%, coarse=9.53%]
Training:  21%|██        | 144/687 [00:57<03:09,  2.87it/s, loss=0.7578, acc=68.92%, coarse=9.53%]
Training:  21%|██        | 144/687 [00:58<03:09,  2.87it/s, loss=1.0822, acc=68.92%, coarse=9.53%]
Training:  21%|██        | 145/687 [00:58<03:09,  2.87it/s, loss=1.0822, acc=68.92%, coarse=9.53%]
Training:  21%|██        | 145/687 [00:58<03:09,  2.87it/s, loss=1.0258, acc=68.90%, coarse=9.53%]
Training:  21%|██▏       | 146/687 [00:58<03:09,  2.85it/s, loss=1.0258, acc=68.90%, coarse=9.53%]
Training:  21%|██▏       | 146/687 [00:59<03:09,  2.85it/s, loss=0.7532, acc=68.91%, coarse=9.53%]
Training:  21%|██▏       | 147/687 [00:59<03:08,  2.87it/s, loss=0.7532, acc=68.91%, coarse=9.53%]
Training:  21%|██▏       | 147/687 [00:59<03:08,  2.87it/s, loss=0.7796, acc=68.96%, coarse=9.53%]
Training:  22%|██▏       | 148/687 [00:59<03:08,  2.86it/s, loss=0.7796, acc=68.96%, coarse=9.53%]
Training:  22%|██▏       | 148/687 [00:59<03:08,  2.86it/s, loss=0.5933, acc=69.03%, coarse=9.54%]
Training:  22%|██▏       | 149/687 [00:59<03:06,  2.88it/s, loss=0.5933, acc=69.03%, coarse=9.54%]
Training:  22%|██▏       | 149/687 [01:00<03:06,  2.88it/s, loss=0.9337, acc=68.96%, coarse=9.54%]
Training:  22%|██▏       | 150/687 [01:00<03:15,  2.74it/s, loss=0.9337, acc=68.96%, coarse=9.54%]
Training:  22%|██▏       | 150/687 [01:00<03:15,  2.74it/s, loss=0.8583, acc=69.00%, coarse=9.54%]
Training:  22%|██▏       | 151/687 [01:00<03:12,  2.79it/s, loss=0.8583, acc=69.00%, coarse=9.54%]
Training:  22%|██▏       | 151/687 [01:00<03:12,  2.79it/s, loss=0.6330, acc=69.07%, coarse=9.55%]
Training:  22%|██▏       | 152/687 [01:00<03:11,  2.80it/s, loss=0.6330, acc=69.07%, coarse=9.55%]
Training:  22%|██▏       | 152/687 [01:01<03:11,  2.80it/s, loss=0.8486, acc=69.14%, coarse=9.55%]
Training:  22%|██▏       | 153/687 [01:01<03:10,  2.80it/s, loss=0.8486, acc=69.14%, coarse=9.55%]
Training:  22%|██▏       | 153/687 [01:01<03:10,  2.80it/s, loss=0.6497, acc=69.20%, coarse=9.55%]
Training:  22%|██▏       | 154/687 [01:01<03:11,  2.79it/s, loss=0.6497, acc=69.20%, coarse=9.55%]
Training:  22%|██▏       | 154/687 [01:01<03:11,  2.79it/s, loss=0.9304, acc=69.20%, coarse=9.55%]
Training:  23%|██▎       | 155/687 [01:01<03:11,  2.78it/s, loss=0.9304, acc=69.20%, coarse=9.55%]
Training:  23%|██▎       | 155/687 [01:02<03:11,  2.78it/s, loss=0.6981, acc=69.23%, coarse=9.56%]
Training:  23%|██▎       | 156/687 [01:02<03:11,  2.77it/s, loss=0.6981, acc=69.23%, coarse=9.56%]
Training:  23%|██▎       | 156/687 [01:02<03:11,  2.77it/s, loss=0.7735, acc=69.23%, coarse=9.56%]
Training:  23%|██▎       | 157/687 [01:02<03:11,  2.77it/s, loss=0.7735, acc=69.23%, coarse=9.56%]
Training:  23%|██▎       | 157/687 [01:02<03:11,  2.77it/s, loss=0.7724, acc=69.27%, coarse=9.56%]
Training:  23%|██▎       | 158/687 [01:02<03:11,  2.77it/s, loss=0.7724, acc=69.27%, coarse=9.56%]
Training:  23%|██▎       | 158/687 [01:03<03:11,  2.77it/s, loss=0.6347, acc=69.36%, coarse=9.56%]
Training:  23%|██▎       | 159/687 [01:03<03:17,  2.67it/s, loss=0.6347, acc=69.36%, coarse=9.56%]
Training:  23%|██▎       | 159/687 [01:03<03:17,  2.67it/s, loss=0.5789, acc=69.39%, coarse=9.57%]
Training:  23%|██▎       | 160/687 [01:03<03:23,  2.59it/s, loss=0.5789, acc=69.39%, coarse=9.57%]
Training:  23%|██▎       | 160/687 [01:04<03:23,  2.59it/s, loss=0.7670, acc=69.39%, coarse=9.57%]
Training:  23%|██▎       | 161/687 [01:04<03:17,  2.67it/s, loss=0.7670, acc=69.39%, coarse=9.57%]
Training:  23%|██▎       | 161/687 [01:04<03:17,  2.67it/s, loss=1.0603, acc=69.42%, coarse=9.57%]
Training:  24%|██▎       | 162/687 [01:04<03:11,  2.75it/s, loss=1.0603, acc=69.42%, coarse=9.57%]
Training:  24%|██▎       | 162/687 [01:04<03:11,  2.75it/s, loss=0.5503, acc=69.46%, coarse=9.58%]
Training:  24%|██▎       | 163/687 [01:04<03:07,  2.79it/s, loss=0.5503, acc=69.46%, coarse=9.58%]
Training:  24%|██▎       | 163/687 [01:05<03:07,  2.79it/s, loss=0.7442, acc=69.53%, coarse=9.58%]
Training:  24%|██▍       | 164/687 [01:05<03:05,  2.82it/s, loss=0.7442, acc=69.53%, coarse=9.58%]
Training:  24%|██▍       | 164/687 [01:05<03:05,  2.82it/s, loss=0.8720, acc=69.55%, coarse=9.58%]
Training:  24%|██▍       | 165/687 [01:05<03:04,  2.83it/s, loss=0.8720, acc=69.55%, coarse=9.58%]
Training:  24%|██▍       | 165/687 [01:05<03:04,  2.83it/s, loss=0.7787, acc=69.60%, coarse=9.59%]
Training:  24%|██▍       | 166/687 [01:05<03:02,  2.85it/s, loss=0.7787, acc=69.60%, coarse=9.59%]
Training:  24%|██▍       | 166/687 [01:06<03:02,  2.85it/s, loss=0.9189, acc=69.60%, coarse=9.59%]
Training:  24%|██▍       | 167/687 [01:06<03:01,  2.87it/s, loss=0.9189, acc=69.60%, coarse=9.59%]
Training:  24%|██▍       | 167/687 [01:06<03:01,  2.87it/s, loss=0.7103, acc=69.67%, coarse=9.59%]
Training:  24%|██▍       | 168/687 [01:06<03:08,  2.75it/s, loss=0.7103, acc=69.67%, coarse=9.59%]
Training:  24%|██▍       | 168/687 [01:06<03:08,  2.75it/s, loss=0.6416, acc=69.70%, coarse=9.59%]
Training:  25%|██▍       | 169/687 [01:06<03:05,  2.79it/s, loss=0.6416, acc=69.70%, coarse=9.59%]
Training:  25%|██▍       | 169/687 [01:07<03:05,  2.79it/s, loss=0.6461, acc=69.74%, coarse=9.60%]
Training:  25%|██▍       | 170/687 [01:07<03:03,  2.82it/s, loss=0.6461, acc=69.74%, coarse=9.60%]
Training:  25%|██▍       | 170/687 [01:07<03:03,  2.82it/s, loss=0.7067, acc=69.78%, coarse=9.60%]
Training:  25%|██▍       | 171/687 [01:07<03:01,  2.84it/s, loss=0.7067, acc=69.78%, coarse=9.60%]
Training:  25%|██▍       | 171/687 [01:08<03:01,  2.84it/s, loss=0.7907, acc=69.85%, coarse=9.60%]
Training:  25%|██▌       | 172/687 [01:08<02:59,  2.86it/s, loss=0.7907, acc=69.85%, coarse=9.60%]
Training:  25%|██▌       | 172/687 [01:08<02:59,  2.86it/s, loss=0.5753, acc=69.92%, coarse=9.60%]
Training:  25%|██▌       | 173/687 [01:08<02:58,  2.87it/s, loss=0.5753, acc=69.92%, coarse=9.60%]
Training:  25%|██▌       | 173/687 [01:08<02:58,  2.87it/s, loss=0.7856, acc=69.94%, coarse=9.61%]
Training:  25%|██▌       | 174/687 [01:08<02:58,  2.87it/s, loss=0.7856, acc=69.94%, coarse=9.61%]
Training:  25%|██▌       | 174/687 [01:09<02:58,  2.87it/s, loss=0.9092, acc=69.94%, coarse=9.61%]
Training:  25%|██▌       | 175/687 [01:09<02:58,  2.87it/s, loss=0.9092, acc=69.94%, coarse=9.61%]
Training:  25%|██▌       | 175/687 [01:09<02:58,  2.87it/s, loss=0.6232, acc=69.97%, coarse=9.61%]
Training:  26%|██▌       | 176/687 [01:09<02:57,  2.88it/s, loss=0.6232, acc=69.97%, coarse=9.61%]
Training:  26%|██▌       | 176/687 [01:09<02:57,  2.88it/s, loss=0.7153, acc=69.98%, coarse=9.61%]
Training:  26%|██▌       | 177/687 [01:09<02:57,  2.88it/s, loss=0.7153, acc=69.98%, coarse=9.61%]
Training:  26%|██▌       | 177/687 [01:10<02:57,  2.88it/s, loss=0.9012, acc=69.99%, coarse=9.62%]
Training:  26%|██▌       | 178/687 [01:10<02:57,  2.87it/s, loss=0.9012, acc=69.99%, coarse=9.62%]
Training:  26%|██▌       | 178/687 [01:10<02:57,  2.87it/s, loss=0.8811, acc=70.01%, coarse=9.61%]
Training:  26%|██▌       | 179/687 [01:10<02:55,  2.89it/s, loss=0.8811, acc=70.01%, coarse=9.61%]
Training:  26%|██▌       | 179/687 [01:10<02:55,  2.89it/s, loss=0.7496, acc=70.05%, coarse=9.62%]
Training:  26%|██▌       | 180/687 [01:10<02:55,  2.89it/s, loss=0.7496, acc=70.05%, coarse=9.62%]
Training:  26%|██▌       | 180/687 [01:11<02:55,  2.89it/s, loss=0.7613, acc=70.10%, coarse=9.62%]
Training:  26%|██▋       | 181/687 [01:11<02:54,  2.89it/s, loss=0.7613, acc=70.10%, coarse=9.62%]
Training:  26%|██▋       | 181/687 [01:11<02:54,  2.89it/s, loss=0.7213, acc=70.15%, coarse=9.63%]
Training:  26%|██▋       | 182/687 [01:11<02:55,  2.88it/s, loss=0.7213, acc=70.15%, coarse=9.63%]
Training:  26%|██▋       | 182/687 [01:11<02:55,  2.88it/s, loss=0.5709, acc=70.18%, coarse=9.63%]
Training:  27%|██▋       | 183/687 [01:11<02:55,  2.88it/s, loss=0.5709, acc=70.18%, coarse=9.63%]
Training:  27%|██▋       | 183/687 [01:12<02:55,  2.88it/s, loss=0.9597, acc=70.15%, coarse=9.63%]
Training:  27%|██▋       | 184/687 [01:12<02:55,  2.87it/s, loss=0.9597, acc=70.15%, coarse=9.63%]
Training:  27%|██▋       | 184/687 [01:12<02:55,  2.87it/s, loss=0.7518, acc=70.15%, coarse=9.63%]
Training:  27%|██▋       | 185/687 [01:12<02:57,  2.82it/s, loss=0.7518, acc=70.15%, coarse=9.63%]
Training:  27%|██▋       | 185/687 [01:12<02:57,  2.82it/s, loss=0.7205, acc=70.13%, coarse=9.63%]
Training:  27%|██▋       | 186/687 [01:12<02:56,  2.84it/s, loss=0.7205, acc=70.13%, coarse=9.63%]
Training:  27%|██▋       | 186/687 [01:13<02:56,  2.84it/s, loss=0.6222, acc=70.20%, coarse=9.63%]
Training:  27%|██▋       | 187/687 [01:13<02:54,  2.86it/s, loss=0.6222, acc=70.20%, coarse=9.63%]
Training:  27%|██▋       | 187/687 [01:13<02:54,  2.86it/s, loss=0.7078, acc=70.21%, coarse=9.63%]
Training:  27%|██▋       | 188/687 [01:13<02:53,  2.88it/s, loss=0.7078, acc=70.21%, coarse=9.63%]
Training:  27%|██▋       | 188/687 [01:13<02:53,  2.88it/s, loss=0.6207, acc=70.25%, coarse=9.64%]
Training:  28%|██▊       | 189/687 [01:13<02:53,  2.88it/s, loss=0.6207, acc=70.25%, coarse=9.64%]
Training:  28%|██▊       | 189/687 [01:14<02:53,  2.88it/s, loss=0.7543, acc=70.29%, coarse=9.64%]
Training:  28%|██▊       | 190/687 [01:14<02:53,  2.87it/s, loss=0.7543, acc=70.29%, coarse=9.64%]
Training:  28%|██▊       | 190/687 [01:14<02:53,  2.87it/s, loss=0.8293, acc=70.35%, coarse=9.64%]
Training:  28%|██▊       | 191/687 [01:14<02:53,  2.86it/s, loss=0.8293, acc=70.35%, coarse=9.64%]
Training:  28%|██▊       | 191/687 [01:15<02:53,  2.86it/s, loss=0.7321, acc=70.37%, coarse=9.64%]
Training:  28%|██▊       | 192/687 [01:15<02:58,  2.78it/s, loss=0.7321, acc=70.37%, coarse=9.64%]
Training:  28%|██▊       | 192/687 [01:15<02:58,  2.78it/s, loss=0.5601, acc=70.46%, coarse=9.65%]
Training:  28%|██▊       | 193/687 [01:15<03:04,  2.68it/s, loss=0.5601, acc=70.46%, coarse=9.65%]
Training:  28%|██▊       | 193/687 [01:15<03:04,  2.68it/s, loss=0.8565, acc=70.53%, coarse=9.65%]
Training:  28%|██▊       | 194/687 [01:15<03:09,  2.60it/s, loss=0.8565, acc=70.53%, coarse=9.65%]
Training:  28%|██▊       | 194/687 [01:16<03:09,  2.60it/s, loss=0.7713, acc=70.54%, coarse=9.65%]
Training:  28%|██▊       | 195/687 [01:16<03:02,  2.69it/s, loss=0.7713, acc=70.54%, coarse=9.65%]
Training:  28%|██▊       | 195/687 [01:16<03:02,  2.69it/s, loss=0.8919, acc=70.56%, coarse=9.65%]
Training:  29%|██▊       | 196/687 [01:16<03:00,  2.71it/s, loss=0.8919, acc=70.56%, coarse=9.65%]
Training:  29%|██▊       | 196/687 [01:16<03:00,  2.71it/s, loss=0.5361, acc=70.62%, coarse=9.66%]
Training:  29%|██▊       | 197/687 [01:16<02:57,  2.76it/s, loss=0.5361, acc=70.62%, coarse=9.66%]
Training:  29%|██▊       | 197/687 [01:17<02:57,  2.76it/s, loss=0.7589, acc=70.64%, coarse=9.66%]
Training:  29%|██▉       | 198/687 [01:17<02:54,  2.80it/s, loss=0.7589, acc=70.64%, coarse=9.66%]
Training:  29%|██▉       | 198/687 [01:17<02:54,  2.80it/s, loss=0.8988, acc=70.63%, coarse=9.67%]
Training:  29%|██▉       | 199/687 [01:17<02:51,  2.84it/s, loss=0.8988, acc=70.63%, coarse=9.67%]
Training:  29%|██▉       | 199/687 [01:17<02:51,  2.84it/s, loss=0.6778, acc=70.66%, coarse=9.67%]
Training:  29%|██▉       | 200/687 [01:17<02:50,  2.86it/s, loss=0.6778, acc=70.66%, coarse=9.67%]
Training:  29%|██▉       | 200/687 [01:18<02:50,  2.86it/s, loss=0.8663, acc=70.64%, coarse=9.67%]
Training:  29%|██▉       | 201/687 [01:18<02:51,  2.83it/s, loss=0.8663, acc=70.64%, coarse=9.67%]
Training:  29%|██▉       | 201/687 [01:18<02:51,  2.83it/s, loss=0.8186, acc=70.61%, coarse=9.66%]
Training:  29%|██▉       | 202/687 [01:18<02:52,  2.80it/s, loss=0.8186, acc=70.61%, coarse=9.66%]
Training:  29%|██▉       | 202/687 [01:18<02:52,  2.80it/s, loss=0.7311, acc=70.63%, coarse=9.67%]
Training:  30%|██▉       | 203/687 [01:18<02:52,  2.81it/s, loss=0.7311, acc=70.63%, coarse=9.67%]
Training:  30%|██▉       | 203/687 [01:19<02:52,  2.81it/s, loss=0.6945, acc=70.64%, coarse=9.67%]
Training:  30%|██▉       | 204/687 [01:19<02:54,  2.77it/s, loss=0.6945, acc=70.64%, coarse=9.67%]
Training:  30%|██▉       | 204/687 [01:19<02:54,  2.77it/s, loss=0.6934, acc=70.66%, coarse=9.67%]
Training:  30%|██▉       | 205/687 [01:19<03:06,  2.58it/s, loss=0.6934, acc=70.66%, coarse=9.67%]
Training:  30%|██▉       | 205/687 [01:20<03:06,  2.58it/s, loss=1.0989, acc=70.64%, coarse=9.67%]
Training:  30%|██▉       | 206/687 [01:20<03:05,  2.59it/s, loss=1.0989, acc=70.64%, coarse=9.67%]
Training:  30%|██▉       | 206/687 [01:20<03:05,  2.59it/s, loss=1.0683, acc=70.64%, coarse=9.67%]
Training:  30%|███       | 207/687 [01:20<03:04,  2.61it/s, loss=1.0683, acc=70.64%, coarse=9.67%]
Training:  30%|███       | 207/687 [01:20<03:04,  2.61it/s, loss=0.8189, acc=70.66%, coarse=9.66%]
Training:  30%|███       | 208/687 [01:20<03:00,  2.66it/s, loss=0.8189, acc=70.66%, coarse=9.66%]
Training:  30%|███       | 208/687 [01:21<03:00,  2.66it/s, loss=0.5725, acc=70.73%, coarse=9.66%]
Training:  30%|███       | 209/687 [01:21<02:55,  2.72it/s, loss=0.5725, acc=70.73%, coarse=9.66%]
Training:  30%|███       | 209/687 [01:21<02:55,  2.72it/s, loss=0.6604, acc=70.79%, coarse=9.67%]
Training:  31%|███       | 210/687 [01:21<02:51,  2.78it/s, loss=0.6604, acc=70.79%, coarse=9.67%]
Training:  31%|███       | 210/687 [01:21<02:51,  2.78it/s, loss=0.6476, acc=70.83%, coarse=9.67%]
Training:  31%|███       | 211/687 [01:21<02:51,  2.77it/s, loss=0.6476, acc=70.83%, coarse=9.67%]
Training:  31%|███       | 211/687 [01:22<02:51,  2.77it/s, loss=0.7753, acc=70.87%, coarse=9.66%]
Training:  31%|███       | 212/687 [01:22<02:49,  2.80it/s, loss=0.7753, acc=70.87%, coarse=9.66%]
Training:  31%|███       | 212/687 [01:22<02:49,  2.80it/s, loss=0.5997, acc=70.92%, coarse=9.67%]
Training:  31%|███       | 213/687 [01:22<02:49,  2.80it/s, loss=0.5997, acc=70.92%, coarse=9.67%]
Training:  31%|███       | 213/687 [01:23<02:49,  2.80it/s, loss=0.6494, acc=70.96%, coarse=9.67%]
Training:  31%|███       | 214/687 [01:23<02:47,  2.82it/s, loss=0.6494, acc=70.96%, coarse=9.67%]
Training:  31%|███       | 214/687 [01:23<02:47,  2.82it/s, loss=0.7586, acc=70.99%, coarse=9.67%]
Training:  31%|███▏      | 215/687 [01:23<02:49,  2.78it/s, loss=0.7586, acc=70.99%, coarse=9.67%]
Training:  31%|███▏      | 215/687 [01:23<02:49,  2.78it/s, loss=0.5701, acc=71.04%, coarse=9.68%]
Training:  31%|███▏      | 216/687 [01:23<02:49,  2.78it/s, loss=0.5701, acc=71.04%, coarse=9.68%]
Training:  31%|███▏      | 216/687 [01:24<02:49,  2.78it/s, loss=1.0271, acc=71.05%, coarse=9.68%]
Training:  32%|███▏      | 217/687 [01:24<02:47,  2.80it/s, loss=1.0271, acc=71.05%, coarse=9.68%]
Training:  32%|███▏      | 217/687 [01:24<02:47,  2.80it/s, loss=0.8637, acc=71.04%, coarse=9.67%]
Training:  32%|███▏      | 218/687 [01:24<02:47,  2.80it/s, loss=0.8637, acc=71.04%, coarse=9.67%]
Training:  32%|███▏      | 218/687 [01:24<02:47,  2.80it/s, loss=0.7153, acc=71.10%, coarse=9.68%]
Training:  32%|███▏      | 219/687 [01:24<02:47,  2.79it/s, loss=0.7153, acc=71.10%, coarse=9.68%]
Training:  32%|███▏      | 219/687 [01:25<02:47,  2.79it/s, loss=0.7233, acc=71.14%, coarse=9.68%]
Training:  32%|███▏      | 220/687 [01:25<02:46,  2.81it/s, loss=0.7233, acc=71.14%, coarse=9.68%]
Training:  32%|███▏      | 220/687 [01:25<02:46,  2.81it/s, loss=0.7081, acc=71.17%, coarse=9.68%]
Training:  32%|███▏      | 221/687 [01:25<02:45,  2.82it/s, loss=0.7081, acc=71.17%, coarse=9.68%]
Training:  32%|███▏      | 221/687 [01:25<02:45,  2.82it/s, loss=0.5583, acc=71.24%, coarse=9.68%]
Training:  32%|███▏      | 222/687 [01:25<02:44,  2.83it/s, loss=0.5583, acc=71.24%, coarse=9.68%]
Training:  32%|███▏      | 222/687 [01:26<02:44,  2.83it/s, loss=0.5481, acc=71.24%, coarse=9.69%]
Training:  32%|███▏      | 223/687 [01:26<02:44,  2.82it/s, loss=0.5481, acc=71.24%, coarse=9.69%]
Training:  32%|███▏      | 223/687 [01:26<02:44,  2.82it/s, loss=0.5764, acc=71.26%, coarse=9.69%]
Training:  33%|███▎      | 224/687 [01:26<02:43,  2.83it/s, loss=0.5764, acc=71.26%, coarse=9.69%]
Training:  33%|███▎      | 224/687 [01:26<02:43,  2.83it/s, loss=0.5953, acc=71.27%, coarse=9.70%]
Training:  33%|███▎      | 225/687 [01:26<02:42,  2.84it/s, loss=0.5953, acc=71.27%, coarse=9.70%]
Training:  33%|███▎      | 225/687 [01:27<02:42,  2.84it/s, loss=0.6764, acc=71.26%, coarse=9.70%]
Training:  33%|███▎      | 226/687 [01:27<02:41,  2.85it/s, loss=0.6764, acc=71.26%, coarse=9.70%]
Training:  33%|███▎      | 226/687 [01:27<02:41,  2.85it/s, loss=0.5905, acc=71.33%, coarse=9.70%]
Training:  33%|███▎      | 227/687 [01:27<02:47,  2.75it/s, loss=0.5905, acc=71.33%, coarse=9.70%]
Training:  33%|███▎      | 227/687 [01:28<02:47,  2.75it/s, loss=0.6119, acc=71.35%, coarse=9.70%]
Training:  33%|███▎      | 228/687 [01:28<02:45,  2.77it/s, loss=0.6119, acc=71.35%, coarse=9.70%]
Training:  33%|███▎      | 228/687 [01:28<02:45,  2.77it/s, loss=0.7869, acc=71.38%, coarse=9.71%]
Training:  33%|███▎      | 229/687 [01:28<02:52,  2.66it/s, loss=0.7869, acc=71.38%, coarse=9.71%]
Training:  33%|███▎      | 229/687 [01:28<02:52,  2.66it/s, loss=0.8268, acc=71.39%, coarse=9.70%]
Training:  33%|███▎      | 230/687 [01:28<02:56,  2.59it/s, loss=0.8268, acc=71.39%, coarse=9.70%]
Training:  33%|███▎      | 230/687 [01:29<02:56,  2.59it/s, loss=0.8098, acc=71.42%, coarse=9.70%]
Training:  34%|███▎      | 231/687 [01:29<02:51,  2.67it/s, loss=0.8098, acc=71.42%, coarse=9.70%]
Training:  34%|███▎      | 231/687 [01:29<02:51,  2.67it/s, loss=0.6931, acc=71.44%, coarse=9.70%]
Training:  34%|███▍      | 232/687 [01:29<02:46,  2.73it/s, loss=0.6931, acc=71.44%, coarse=9.70%]
Training:  34%|███▍      | 232/687 [01:29<02:46,  2.73it/s, loss=0.8485, acc=71.45%, coarse=9.70%]
Training:  34%|███▍      | 233/687 [01:29<02:44,  2.77it/s, loss=0.8485, acc=71.45%, coarse=9.70%]
Training:  34%|███▍      | 233/687 [01:30<02:44,  2.77it/s, loss=0.8158, acc=71.41%, coarse=9.70%]
Training:  34%|███▍      | 234/687 [01:30<02:42,  2.79it/s, loss=0.8158, acc=71.41%, coarse=9.70%]
Training:  34%|███▍      | 234/687 [01:30<02:42,  2.79it/s, loss=0.4038, acc=71.50%, coarse=9.71%]
Training:  34%|███▍      | 235/687 [01:30<02:40,  2.81it/s, loss=0.4038, acc=71.50%, coarse=9.71%]
Training:  34%|███▍      | 235/687 [01:30<02:40,  2.81it/s, loss=0.4978, acc=71.55%, coarse=9.71%]
Training:  34%|███▍      | 236/687 [01:30<02:40,  2.82it/s, loss=0.4978, acc=71.55%, coarse=9.71%]
Training:  34%|███▍      | 236/687 [01:31<02:40,  2.82it/s, loss=0.8460, acc=71.59%, coarse=9.71%]
Training:  34%|███▍      | 237/687 [01:31<02:44,  2.73it/s, loss=0.8460, acc=71.59%, coarse=9.71%]
Training:  34%|███▍      | 237/687 [01:31<02:44,  2.73it/s, loss=1.2527, acc=71.60%, coarse=9.71%]
Training:  35%|███▍      | 238/687 [01:31<02:43,  2.75it/s, loss=1.2527, acc=71.60%, coarse=9.71%]
Training:  35%|███▍      | 238/687 [01:32<02:43,  2.75it/s, loss=0.8216, acc=71.60%, coarse=9.71%]
Training:  35%|███▍      | 239/687 [01:32<02:42,  2.76it/s, loss=0.8216, acc=71.60%, coarse=9.71%]
Training:  35%|███▍      | 239/687 [01:32<02:42,  2.76it/s, loss=0.9456, acc=71.53%, coarse=9.71%]
Training:  35%|███▍      | 240/687 [01:32<02:40,  2.79it/s, loss=0.9456, acc=71.53%, coarse=9.71%]
Training:  35%|███▍      | 240/687 [01:32<02:40,  2.79it/s, loss=0.6778, acc=71.49%, coarse=9.71%]
Training:  35%|███▌      | 241/687 [01:32<02:43,  2.72it/s, loss=0.6778, acc=71.49%, coarse=9.71%]
Training:  35%|███▌      | 241/687 [01:33<02:43,  2.72it/s, loss=0.5041, acc=71.54%, coarse=9.71%]
Training:  35%|███▌      | 242/687 [01:33<02:41,  2.75it/s, loss=0.5041, acc=71.54%, coarse=9.71%]
Training:  35%|███▌      | 242/687 [01:33<02:41,  2.75it/s, loss=0.6323, acc=71.56%, coarse=9.72%]
Training:  35%|███▌      | 243/687 [01:33<02:40,  2.77it/s, loss=0.6323, acc=71.56%, coarse=9.72%]
Training:  35%|███▌      | 243/687 [01:33<02:40,  2.77it/s, loss=0.7675, acc=71.57%, coarse=9.72%]
Training:  36%|███▌      | 244/687 [01:33<02:38,  2.80it/s, loss=0.7675, acc=71.57%, coarse=9.72%]
Training:  36%|███▌      | 244/687 [01:34<02:38,  2.80it/s, loss=0.6316, acc=71.63%, coarse=9.72%]
Training:  36%|███▌      | 245/687 [01:34<02:37,  2.81it/s, loss=0.6316, acc=71.63%, coarse=9.72%]
Training:  36%|███▌      | 245/687 [01:34<02:37,  2.81it/s, loss=0.7679, acc=71.67%, coarse=9.72%]
Training:  36%|███▌      | 246/687 [01:34<02:37,  2.81it/s, loss=0.7679, acc=71.67%, coarse=9.72%]
Training:  36%|███▌      | 246/687 [01:34<02:37,  2.81it/s, loss=0.5882, acc=71.70%, coarse=9.73%]
Training:  36%|███▌      | 247/687 [01:34<02:35,  2.82it/s, loss=0.5882, acc=71.70%, coarse=9.73%]
Training:  36%|███▌      | 247/687 [01:35<02:35,  2.82it/s, loss=0.7587, acc=71.74%, coarse=9.73%]
Training:  36%|███▌      | 248/687 [01:35<02:34,  2.84it/s, loss=0.7587, acc=71.74%, coarse=9.73%]
Training:  36%|███▌      | 248/687 [01:35<02:34,  2.84it/s, loss=0.7631, acc=71.80%, coarse=9.73%]
Training:  36%|███▌      | 249/687 [01:35<02:34,  2.84it/s, loss=0.7631, acc=71.80%, coarse=9.73%]
Training:  36%|███▌      | 249/687 [01:35<02:34,  2.84it/s, loss=0.7068, acc=71.83%, coarse=9.72%]
Training:  36%|███▋      | 250/687 [01:35<02:34,  2.84it/s, loss=0.7068, acc=71.83%, coarse=9.72%]
Training:  36%|███▋      | 250/687 [01:36<02:34,  2.84it/s, loss=0.8074, acc=71.87%, coarse=9.73%]
Training:  37%|███▋      | 251/687 [01:36<02:33,  2.83it/s, loss=0.8074, acc=71.87%, coarse=9.73%]
Training:  37%|███▋      | 251/687 [01:36<02:33,  2.83it/s, loss=0.5667, acc=71.90%, coarse=9.73%]
Training:  37%|███▋      | 252/687 [01:36<02:33,  2.83it/s, loss=0.5667, acc=71.90%, coarse=9.73%]
Training:  37%|███▋      | 252/687 [01:37<02:33,  2.83it/s, loss=0.6620, acc=71.90%, coarse=9.73%]
Training:  37%|███▋      | 253/687 [01:37<02:33,  2.82it/s, loss=0.6620, acc=71.90%, coarse=9.73%]
Training:  37%|███▋      | 253/687 [01:37<02:33,  2.82it/s, loss=0.9494, acc=71.94%, coarse=9.73%]
Training:  37%|███▋      | 254/687 [01:37<02:34,  2.81it/s, loss=0.9494, acc=71.94%, coarse=9.73%]
Training:  37%|███▋      | 254/687 [01:37<02:34,  2.81it/s, loss=0.5958, acc=71.98%, coarse=9.73%]
Training:  37%|███▋      | 255/687 [01:37<02:32,  2.83it/s, loss=0.5958, acc=71.98%, coarse=9.73%]
Training:  37%|███▋      | 255/687 [01:38<02:32,  2.83it/s, loss=0.9859, acc=71.99%, coarse=9.73%]
Training:  37%|███▋      | 256/687 [01:38<02:31,  2.85it/s, loss=0.9859, acc=71.99%, coarse=9.73%]
Training:  37%|███▋      | 256/687 [01:38<02:31,  2.85it/s, loss=0.6112, acc=72.02%, coarse=9.73%]
Training:  37%|███▋      | 257/687 [01:38<02:30,  2.86it/s, loss=0.6112, acc=72.02%, coarse=9.73%]
Training:  37%|███▋      | 257/687 [01:38<02:30,  2.86it/s, loss=0.4529, acc=72.07%, coarse=9.73%]
Training:  38%|███▊      | 258/687 [01:38<02:29,  2.86it/s, loss=0.4529, acc=72.07%, coarse=9.73%]
Training:  38%|███▊      | 258/687 [01:39<02:29,  2.86it/s, loss=0.6509, acc=72.09%, coarse=9.74%]
Training:  38%|███▊      | 259/687 [01:39<02:29,  2.86it/s, loss=0.6509, acc=72.09%, coarse=9.74%]
Training:  38%|███▊      | 259/687 [01:39<02:29,  2.86it/s, loss=0.7566, acc=72.10%, coarse=9.74%]
Training:  38%|███▊      | 260/687 [01:39<02:31,  2.82it/s, loss=0.7566, acc=72.10%, coarse=9.74%]
Training:  38%|███▊      | 260/687 [01:39<02:31,  2.82it/s, loss=0.7129, acc=72.15%, coarse=9.74%]
Training:  38%|███▊      | 261/687 [01:39<02:34,  2.76it/s, loss=0.7129, acc=72.15%, coarse=9.74%]
Training:  38%|███▊      | 261/687 [01:40<02:34,  2.76it/s, loss=0.7083, acc=72.17%, coarse=9.74%]
Training:  38%|███▊      | 262/687 [01:40<02:37,  2.70it/s, loss=0.7083, acc=72.17%, coarse=9.74%]
Training:  38%|███▊      | 262/687 [01:40<02:37,  2.70it/s, loss=0.7900, acc=72.20%, coarse=9.75%]
Training:  38%|███▊      | 263/687 [01:40<02:35,  2.73it/s, loss=0.7900, acc=72.20%, coarse=9.75%]
Training:  38%|███▊      | 263/687 [01:41<02:35,  2.73it/s, loss=0.7093, acc=72.21%, coarse=9.75%]
Training:  38%|███▊      | 264/687 [01:41<02:37,  2.68it/s, loss=0.7093, acc=72.21%, coarse=9.75%]
Training:  38%|███▊      | 264/687 [01:41<02:37,  2.68it/s, loss=0.6598, acc=72.23%, coarse=9.75%]
Training:  39%|███▊      | 265/687 [01:41<02:40,  2.64it/s, loss=0.6598, acc=72.23%, coarse=9.75%]
Training:  39%|███▊      | 265/687 [01:41<02:40,  2.64it/s, loss=0.8443, acc=72.25%, coarse=9.75%]
Training:  39%|███▊      | 266/687 [01:41<02:38,  2.66it/s, loss=0.8443, acc=72.25%, coarse=9.75%]
Training:  39%|███▊      | 266/687 [01:42<02:38,  2.66it/s, loss=0.7122, acc=72.23%, coarse=9.75%]
Training:  39%|███▉      | 267/687 [01:42<02:35,  2.70it/s, loss=0.7122, acc=72.23%, coarse=9.75%]
Training:  39%|███▉      | 267/687 [01:42<02:35,  2.70it/s, loss=0.9912, acc=72.22%, coarse=9.75%]
Training:  39%|███▉      | 268/687 [01:42<02:32,  2.74it/s, loss=0.9912, acc=72.22%, coarse=9.75%]
Training:  39%|███▉      | 268/687 [01:42<02:32,  2.74it/s, loss=0.7242, acc=72.24%, coarse=9.75%]
Training:  39%|███▉      | 269/687 [01:42<02:31,  2.76it/s, loss=0.7242, acc=72.24%, coarse=9.75%]
Training:  39%|███▉      | 269/687 [01:43<02:31,  2.76it/s, loss=0.6490, acc=72.24%, coarse=9.75%]
Training:  39%|███▉      | 270/687 [01:43<02:28,  2.80it/s, loss=0.6490, acc=72.24%, coarse=9.75%]
Training:  39%|███▉      | 270/687 [01:43<02:28,  2.80it/s, loss=0.5893, acc=72.29%, coarse=9.75%]
Training:  39%|███▉      | 271/687 [01:43<02:30,  2.76it/s, loss=0.5893, acc=72.29%, coarse=9.75%]
Training:  39%|███▉      | 271/687 [01:44<02:30,  2.76it/s, loss=0.6712, acc=72.29%, coarse=9.75%]
Training:  40%|███▉      | 272/687 [01:44<02:41,  2.58it/s, loss=0.6712, acc=72.29%, coarse=9.75%]
Training:  40%|███▉      | 272/687 [01:44<02:41,  2.58it/s, loss=0.5910, acc=72.33%, coarse=9.76%]
Training:  40%|███▉      | 273/687 [01:44<02:36,  2.64it/s, loss=0.5910, acc=72.33%, coarse=9.76%]
Training:  40%|███▉      | 273/687 [01:44<02:36,  2.64it/s, loss=0.5408, acc=72.38%, coarse=9.76%]
Training:  40%|███▉      | 274/687 [01:44<02:33,  2.69it/s, loss=0.5408, acc=72.38%, coarse=9.76%]
Training:  40%|███▉      | 274/687 [01:45<02:33,  2.69it/s, loss=0.6482, acc=72.37%, coarse=9.76%]
Training:  40%|████      | 275/687 [01:45<02:30,  2.74it/s, loss=0.6482, acc=72.37%, coarse=9.76%]
Training:  40%|████      | 275/687 [01:45<02:30,  2.74it/s, loss=0.9130, acc=72.37%, coarse=9.76%]
Training:  40%|████      | 276/687 [01:45<02:28,  2.77it/s, loss=0.9130, acc=72.37%, coarse=9.76%]
Training:  40%|████      | 276/687 [01:45<02:28,  2.77it/s, loss=0.8246, acc=72.37%, coarse=9.76%]
Training:  40%|████      | 277/687 [01:45<02:28,  2.76it/s, loss=0.8246, acc=72.37%, coarse=9.76%]
Training:  40%|████      | 277/687 [01:46<02:28,  2.76it/s, loss=0.8724, acc=72.39%, coarse=9.76%]
Training:  40%|████      | 278/687 [01:46<02:28,  2.75it/s, loss=0.8724, acc=72.39%, coarse=9.76%]
Training:  40%|████      | 278/687 [01:46<02:28,  2.75it/s, loss=0.8902, acc=72.41%, coarse=9.77%]
Training:  41%|████      | 279/687 [01:46<02:28,  2.75it/s, loss=0.8902, acc=72.41%, coarse=9.77%]
Training:  41%|████      | 279/687 [01:46<02:28,  2.75it/s, loss=0.6671, acc=72.43%, coarse=9.77%]
Training:  41%|████      | 280/687 [01:46<02:27,  2.76it/s, loss=0.6671, acc=72.43%, coarse=9.77%]
Training:  41%|████      | 280/687 [01:47<02:27,  2.76it/s, loss=0.7506, acc=72.44%, coarse=9.77%]
Training:  41%|████      | 281/687 [01:47<02:26,  2.77it/s, loss=0.7506, acc=72.44%, coarse=9.77%]
Training:  41%|████      | 281/687 [01:47<02:26,  2.77it/s, loss=0.8769, acc=72.45%, coarse=9.77%]
Training:  41%|████      | 282/687 [01:47<02:26,  2.76it/s, loss=0.8769, acc=72.45%, coarse=9.77%]
Training:  41%|████      | 282/687 [01:47<02:26,  2.76it/s, loss=0.9462, acc=72.44%, coarse=9.77%]
Training:  41%|████      | 283/687 [01:47<02:28,  2.72it/s, loss=0.9462, acc=72.44%, coarse=9.77%]
Training:  41%|████      | 283/687 [01:48<02:28,  2.72it/s, loss=0.4898, acc=72.50%, coarse=9.77%]
Training:  41%|████▏     | 284/687 [01:48<02:27,  2.73it/s, loss=0.4898, acc=72.50%, coarse=9.77%]
Training:  41%|████▏     | 284/687 [01:48<02:27,  2.73it/s, loss=0.8088, acc=72.53%, coarse=9.77%]
Training:  41%|████▏     | 285/687 [01:48<02:25,  2.77it/s, loss=0.8088, acc=72.53%, coarse=9.77%]
Training:  41%|████▏     | 285/687 [01:49<02:25,  2.77it/s, loss=0.6036, acc=72.55%, coarse=9.77%]
Training:  42%|████▏     | 286/687 [01:49<02:22,  2.82it/s, loss=0.6036, acc=72.55%, coarse=9.77%]
Training:  42%|████▏     | 286/687 [01:49<02:22,  2.82it/s, loss=0.7554, acc=72.59%, coarse=9.77%]
Training:  42%|████▏     | 287/687 [01:49<02:20,  2.85it/s, loss=0.7554, acc=72.59%, coarse=9.77%]
Training:  42%|████▏     | 287/687 [01:49<02:20,  2.85it/s, loss=0.7575, acc=72.59%, coarse=9.77%]
Training:  42%|████▏     | 288/687 [01:49<02:20,  2.85it/s, loss=0.7575, acc=72.59%, coarse=9.77%]
Training:  42%|████▏     | 288/687 [01:50<02:20,  2.85it/s, loss=0.7378, acc=72.62%, coarse=9.77%]
Training:  42%|████▏     | 289/687 [01:50<02:20,  2.83it/s, loss=0.7378, acc=72.62%, coarse=9.77%]
Training:  42%|████▏     | 289/687 [01:50<02:20,  2.83it/s, loss=0.6635, acc=72.64%, coarse=9.78%]
Training:  42%|████▏     | 290/687 [01:50<02:21,  2.81it/s, loss=0.6635, acc=72.64%, coarse=9.78%]
Training:  42%|████▏     | 290/687 [01:50<02:21,  2.81it/s, loss=0.6176, acc=72.66%, coarse=9.78%]
Training:  42%|████▏     | 291/687 [01:50<02:21,  2.80it/s, loss=0.6176, acc=72.66%, coarse=9.78%]
Training:  42%|████▏     | 291/687 [01:51<02:21,  2.80it/s, loss=0.7712, acc=72.68%, coarse=9.78%]
Training:  43%|████▎     | 292/687 [01:51<02:25,  2.71it/s, loss=0.7712, acc=72.68%, coarse=9.78%]
Training:  43%|████▎     | 292/687 [01:51<02:25,  2.71it/s, loss=0.7567, acc=72.70%, coarse=9.78%]
Training:  43%|████▎     | 293/687 [01:51<02:30,  2.61it/s, loss=0.7567, acc=72.70%, coarse=9.78%]
Training:  43%|████▎     | 293/687 [01:52<02:30,  2.61it/s, loss=0.6370, acc=72.70%, coarse=9.78%]
Training:  43%|████▎     | 294/687 [01:52<02:29,  2.62it/s, loss=0.6370, acc=72.70%, coarse=9.78%]
Training:  43%|████▎     | 294/687 [01:52<02:29,  2.62it/s, loss=0.6521, acc=72.72%, coarse=9.78%]
Training:  43%|████▎     | 295/687 [01:52<02:34,  2.54it/s, loss=0.6521, acc=72.72%, coarse=9.78%]
Training:  43%|████▎     | 295/687 [01:52<02:34,  2.54it/s, loss=0.5111, acc=72.75%, coarse=9.79%]
Training:  43%|████▎     | 296/687 [01:52<02:41,  2.42it/s, loss=0.5111, acc=72.75%, coarse=9.79%]
Training:  43%|████▎     | 296/687 [01:53<02:41,  2.42it/s, loss=0.5079, acc=72.78%, coarse=9.79%]
Training:  43%|████▎     | 297/687 [01:53<02:40,  2.43it/s, loss=0.5079, acc=72.78%, coarse=9.79%]
Training:  43%|████▎     | 297/687 [01:53<02:40,  2.43it/s, loss=0.8892, acc=72.78%, coarse=9.79%]
Training:  43%|████▎     | 298/687 [01:53<02:38,  2.45it/s, loss=0.8892, acc=72.78%, coarse=9.79%]
Training:  43%|████▎     | 298/687 [01:54<02:38,  2.45it/s, loss=0.7149, acc=72.79%, coarse=9.79%]
Training:  44%|████▎     | 299/687 [01:54<02:37,  2.46it/s, loss=0.7149, acc=72.79%, coarse=9.79%]
Training:  44%|████▎     | 299/687 [01:54<02:37,  2.46it/s, loss=0.6274, acc=72.83%, coarse=9.79%]
Training:  44%|████▎     | 300/687 [01:54<02:36,  2.48it/s, loss=0.6274, acc=72.83%, coarse=9.79%]
Training:  44%|████▎     | 300/687 [01:54<02:36,  2.48it/s, loss=0.7955, acc=72.81%, coarse=9.79%]
Training:  44%|████▍     | 301/687 [01:54<02:38,  2.43it/s, loss=0.7955, acc=72.81%, coarse=9.79%]
Training:  44%|████▍     | 301/687 [01:55<02:38,  2.43it/s, loss=0.6430, acc=72.84%, coarse=9.79%]
Training:  44%|████▍     | 302/687 [01:55<02:40,  2.40it/s, loss=0.6430, acc=72.84%, coarse=9.79%]
Training:  44%|████▍     | 302/687 [01:55<02:40,  2.40it/s, loss=0.6968, acc=72.87%, coarse=9.80%]
Training:  44%|████▍     | 303/687 [01:55<02:43,  2.35it/s, loss=0.6968, acc=72.87%, coarse=9.80%]
Training:  44%|████▍     | 303/687 [01:56<02:43,  2.35it/s, loss=0.4565, acc=72.91%, coarse=9.80%]
Training:  44%|████▍     | 304/687 [01:56<02:39,  2.39it/s, loss=0.4565, acc=72.91%, coarse=9.80%]
Training:  44%|████▍     | 304/687 [01:56<02:39,  2.39it/s, loss=0.5093, acc=72.95%, coarse=9.80%]
Training:  44%|████▍     | 305/687 [01:56<02:33,  2.49it/s, loss=0.5093, acc=72.95%, coarse=9.80%]
Training:  44%|████▍     | 305/687 [01:56<02:33,  2.49it/s, loss=0.7743, acc=72.98%, coarse=9.80%]
Training:  45%|████▍     | 306/687 [01:56<02:27,  2.59it/s, loss=0.7743, acc=72.98%, coarse=9.80%]
Training:  45%|████▍     | 306/687 [01:57<02:27,  2.59it/s, loss=1.0909, acc=72.99%, coarse=9.80%]
Training:  45%|████▍     | 307/687 [01:57<02:22,  2.67it/s, loss=1.0909, acc=72.99%, coarse=9.80%]
Training:  45%|████▍     | 307/687 [01:57<02:22,  2.67it/s, loss=0.6622, acc=73.03%, coarse=9.80%]
Training:  45%|████▍     | 308/687 [01:57<02:18,  2.74it/s, loss=0.6622, acc=73.03%, coarse=9.80%]
Training:  45%|████▍     | 308/687 [01:57<02:18,  2.74it/s, loss=0.6068, acc=73.06%, coarse=9.80%]
Training:  45%|████▍     | 309/687 [01:57<02:17,  2.75it/s, loss=0.6068, acc=73.06%, coarse=9.80%]
Training:  45%|████▍     | 309/687 [01:58<02:17,  2.75it/s, loss=0.6505, acc=73.09%, coarse=9.80%]
Training:  45%|████▌     | 310/687 [01:58<02:15,  2.78it/s, loss=0.6505, acc=73.09%, coarse=9.80%]
Training:  45%|████▌     | 310/687 [01:58<02:15,  2.78it/s, loss=0.6929, acc=73.10%, coarse=9.80%]
Training:  45%|████▌     | 311/687 [01:58<02:20,  2.68it/s, loss=0.6929, acc=73.10%, coarse=9.80%]
Training:  45%|████▌     | 311/687 [01:59<02:20,  2.68it/s, loss=0.8456, acc=73.10%, coarse=9.80%]
Training:  45%|████▌     | 312/687 [01:59<02:18,  2.72it/s, loss=0.8456, acc=73.10%, coarse=9.80%]
Training:  45%|████▌     | 312/687 [01:59<02:18,  2.72it/s, loss=0.7074, acc=73.12%, coarse=9.80%]
Training:  46%|████▌     | 313/687 [01:59<02:19,  2.68it/s, loss=0.7074, acc=73.12%, coarse=9.80%]
Training:  46%|████▌     | 313/687 [01:59<02:19,  2.68it/s, loss=0.6289, acc=73.12%, coarse=9.80%]
Training:  46%|████▌     | 314/687 [01:59<02:16,  2.74it/s, loss=0.6289, acc=73.12%, coarse=9.80%]
Training:  46%|████▌     | 314/687 [02:00<02:16,  2.74it/s, loss=0.7263, acc=73.13%, coarse=9.81%]
Training:  46%|████▌     | 315/687 [02:00<02:13,  2.79it/s, loss=0.7263, acc=73.13%, coarse=9.81%]
Training:  46%|████▌     | 315/687 [02:00<02:13,  2.79it/s, loss=0.6995, acc=73.10%, coarse=9.81%]
Training:  46%|████▌     | 316/687 [02:00<02:10,  2.84it/s, loss=0.6995, acc=73.10%, coarse=9.81%]
Training:  46%|████▌     | 316/687 [02:00<02:10,  2.84it/s, loss=0.7496, acc=73.11%, coarse=9.81%]
Training:  46%|████▌     | 317/687 [02:00<02:09,  2.85it/s, loss=0.7496, acc=73.11%, coarse=9.81%]
Training:  46%|████▌     | 317/687 [02:01<02:09,  2.85it/s, loss=0.8506, acc=73.14%, coarse=9.81%]
Training:  46%|████▋     | 318/687 [02:01<02:08,  2.87it/s, loss=0.8506, acc=73.14%, coarse=9.81%]
Training:  46%|████▋     | 318/687 [02:01<02:08,  2.87it/s, loss=0.5864, acc=73.15%, coarse=9.81%]
Training:  46%|████▋     | 319/687 [02:01<02:08,  2.86it/s, loss=0.5864, acc=73.15%, coarse=9.81%]
Training:  46%|████▋     | 319/687 [02:01<02:08,  2.86it/s, loss=0.9918, acc=73.13%, coarse=9.81%]
Training:  47%|████▋     | 320/687 [02:01<02:08,  2.87it/s, loss=0.9918, acc=73.13%, coarse=9.81%]
Training:  47%|████▋     | 320/687 [02:02<02:08,  2.87it/s, loss=0.7344, acc=73.15%, coarse=9.82%]
Training:  47%|████▋     | 321/687 [02:02<02:07,  2.87it/s, loss=0.7344, acc=73.15%, coarse=9.82%]
Training:  47%|████▋     | 321/687 [02:02<02:07,  2.87it/s, loss=0.6593, acc=73.16%, coarse=9.82%]
Training:  47%|████▋     | 322/687 [02:02<02:07,  2.87it/s, loss=0.6593, acc=73.16%, coarse=9.82%]
Training:  47%|████▋     | 322/687 [02:02<02:07,  2.87it/s, loss=0.7036, acc=73.17%, coarse=9.82%]
Training:  47%|████▋     | 323/687 [02:02<02:06,  2.87it/s, loss=0.7036, acc=73.17%, coarse=9.82%]
Training:  47%|████▋     | 323/687 [02:03<02:06,  2.87it/s, loss=0.9475, acc=73.17%, coarse=9.82%]
Training:  47%|████▋     | 324/687 [02:03<02:05,  2.88it/s, loss=0.9475, acc=73.17%, coarse=9.82%]
Training:  47%|████▋     | 324/687 [02:03<02:05,  2.88it/s, loss=1.0289, acc=73.17%, coarse=9.82%]
Training:  47%|████▋     | 325/687 [02:03<02:06,  2.87it/s, loss=1.0289, acc=73.17%, coarse=9.82%]
Training:  47%|████▋     | 325/687 [02:03<02:06,  2.87it/s, loss=0.5338, acc=73.21%, coarse=9.82%]
Training:  47%|████▋     | 326/687 [02:03<02:05,  2.87it/s, loss=0.5338, acc=73.21%, coarse=9.82%]
Training:  47%|████▋     | 326/687 [02:04<02:05,  2.87it/s, loss=0.7182, acc=73.24%, coarse=9.82%]
Training:  48%|████▊     | 327/687 [02:04<02:05,  2.86it/s, loss=0.7182, acc=73.24%, coarse=9.82%]
Training:  48%|████▊     | 327/687 [02:04<02:05,  2.86it/s, loss=0.4958, acc=73.28%, coarse=9.82%]
Training:  48%|████▊     | 328/687 [02:04<02:05,  2.86it/s, loss=0.4958, acc=73.28%, coarse=9.82%]
Training:  48%|████▊     | 328/687 [02:05<02:05,  2.86it/s, loss=0.6824, acc=73.30%, coarse=9.82%]
Training:  48%|████▊     | 329/687 [02:05<02:05,  2.86it/s, loss=0.6824, acc=73.30%, coarse=9.82%]
Training:  48%|████▊     | 329/687 [02:05<02:05,  2.86it/s, loss=0.5486, acc=73.34%, coarse=9.83%]
Training:  48%|████▊     | 330/687 [02:05<02:04,  2.88it/s, loss=0.5486, acc=73.34%, coarse=9.83%]
Training:  48%|████▊     | 330/687 [02:05<02:04,  2.88it/s, loss=0.6858, acc=73.34%, coarse=9.83%]
Training:  48%|████▊     | 331/687 [02:05<02:03,  2.89it/s, loss=0.6858, acc=73.34%, coarse=9.83%]
Training:  48%|████▊     | 331/687 [02:06<02:03,  2.89it/s, loss=0.7860, acc=73.34%, coarse=9.83%]
Training:  48%|████▊     | 332/687 [02:06<02:03,  2.89it/s, loss=0.7860, acc=73.34%, coarse=9.83%]
Training:  48%|████▊     | 332/687 [02:06<02:03,  2.89it/s, loss=0.8548, acc=73.31%, coarse=9.83%]
Training:  48%|████▊     | 333/687 [02:06<02:02,  2.88it/s, loss=0.8548, acc=73.31%, coarse=9.83%]
Training:  48%|████▊     | 333/687 [02:06<02:02,  2.88it/s, loss=0.7372, acc=73.32%, coarse=9.83%]
Training:  49%|████▊     | 334/687 [02:06<02:02,  2.87it/s, loss=0.7372, acc=73.32%, coarse=9.83%]
Training:  49%|████▊     | 334/687 [02:07<02:02,  2.87it/s, loss=0.7118, acc=73.31%, coarse=9.83%]
Training:  49%|████▉     | 335/687 [02:07<02:02,  2.87it/s, loss=0.7118, acc=73.31%, coarse=9.83%]
Training:  49%|████▉     | 335/687 [02:07<02:02,  2.87it/s, loss=0.7827, acc=73.33%, coarse=9.83%]
Training:  49%|████▉     | 336/687 [02:07<02:03,  2.84it/s, loss=0.7827, acc=73.33%, coarse=9.83%]
Training:  49%|████▉     | 336/687 [02:07<02:03,  2.84it/s, loss=0.7279, acc=73.33%, coarse=9.83%]
Training:  49%|████▉     | 337/687 [02:07<02:03,  2.84it/s, loss=0.7279, acc=73.33%, coarse=9.83%]
Training:  49%|████▉     | 337/687 [02:08<02:03,  2.84it/s, loss=0.6972, acc=73.33%, coarse=9.83%]
Training:  49%|████▉     | 338/687 [02:08<02:03,  2.84it/s, loss=0.6972, acc=73.33%, coarse=9.83%]
Training:  49%|████▉     | 338/687 [02:08<02:03,  2.84it/s, loss=0.8286, acc=73.33%, coarse=9.83%]
Training:  49%|████▉     | 339/687 [02:08<02:02,  2.85it/s, loss=0.8286, acc=73.33%, coarse=9.83%]
Training:  49%|████▉     | 339/687 [02:08<02:02,  2.85it/s, loss=0.9704, acc=73.33%, coarse=9.83%]
Training:  49%|████▉     | 340/687 [02:08<02:01,  2.85it/s, loss=0.9704, acc=73.33%, coarse=9.83%]
Training:  49%|████▉     | 340/687 [02:09<02:01,  2.85it/s, loss=0.6055, acc=73.37%, coarse=9.83%]
Training:  50%|████▉     | 341/687 [02:09<02:01,  2.86it/s, loss=0.6055, acc=73.37%, coarse=9.83%]
Training:  50%|████▉     | 341/687 [02:09<02:01,  2.86it/s, loss=0.8183, acc=73.38%, coarse=9.83%]
Training:  50%|████▉     | 342/687 [02:09<02:00,  2.86it/s, loss=0.8183, acc=73.38%, coarse=9.83%]
Training:  50%|████▉     | 342/687 [02:09<02:00,  2.86it/s, loss=0.7805, acc=73.40%, coarse=9.83%]
Training:  50%|████▉     | 343/687 [02:09<02:00,  2.86it/s, loss=0.7805, acc=73.40%, coarse=9.83%]
Training:  50%|████▉     | 343/687 [02:10<02:00,  2.86it/s, loss=0.5978, acc=73.42%, coarse=9.83%]
Training:  50%|█████     | 344/687 [02:10<02:00,  2.85it/s, loss=0.5978, acc=73.42%, coarse=9.83%]
Training:  50%|█████     | 344/687 [02:10<02:00,  2.85it/s, loss=0.5908, acc=73.46%, coarse=9.83%]
Training:  50%|█████     | 345/687 [02:10<01:59,  2.86it/s, loss=0.5908, acc=73.46%, coarse=9.83%]
Training:  50%|█████     | 345/687 [02:10<01:59,  2.86it/s, loss=0.5459, acc=73.48%, coarse=9.83%]
Training:  50%|█████     | 346/687 [02:10<01:58,  2.88it/s, loss=0.5459, acc=73.48%, coarse=9.83%]
Training:  50%|█████     | 346/687 [02:11<01:58,  2.88it/s, loss=0.5729, acc=73.48%, coarse=9.83%]
Training:  51%|█████     | 347/687 [02:11<01:57,  2.89it/s, loss=0.5729, acc=73.48%, coarse=9.83%]
Training:  51%|█████     | 347/687 [02:11<01:57,  2.89it/s, loss=0.7698, acc=73.45%, coarse=9.83%]
Training:  51%|█████     | 348/687 [02:11<01:56,  2.90it/s, loss=0.7698, acc=73.45%, coarse=9.83%]
Training:  51%|█████     | 348/687 [02:12<01:56,  2.90it/s, loss=0.7505, acc=73.46%, coarse=9.83%]
Training:  51%|█████     | 349/687 [02:12<02:03,  2.74it/s, loss=0.7505, acc=73.46%, coarse=9.83%]
Training:  51%|█████     | 349/687 [02:12<02:03,  2.74it/s, loss=0.5902, acc=73.47%, coarse=9.84%]
Training:  51%|█████     | 350/687 [02:12<02:10,  2.59it/s, loss=0.5902, acc=73.47%, coarse=9.84%]
Training:  51%|█████     | 350/687 [02:12<02:10,  2.59it/s, loss=0.5596, acc=73.50%, coarse=9.84%]
Training:  51%|█████     | 351/687 [02:12<02:06,  2.65it/s, loss=0.5596, acc=73.50%, coarse=9.84%]
Training:  51%|█████     | 351/687 [02:13<02:06,  2.65it/s, loss=0.6748, acc=73.50%, coarse=9.84%]
Training:  51%|█████     | 352/687 [02:13<02:03,  2.70it/s, loss=0.6748, acc=73.50%, coarse=9.84%]
Training:  51%|█████     | 352/687 [02:13<02:03,  2.70it/s, loss=0.6973, acc=73.50%, coarse=9.84%]
Training:  51%|█████▏    | 353/687 [02:13<02:01,  2.76it/s, loss=0.6973, acc=73.50%, coarse=9.84%]
Training:  51%|█████▏    | 353/687 [02:13<02:01,  2.76it/s, loss=0.4725, acc=73.53%, coarse=9.84%]
Training:  52%|█████▏    | 354/687 [02:13<01:59,  2.80it/s, loss=0.4725, acc=73.53%, coarse=9.84%]
Training:  52%|█████▏    | 354/687 [02:14<01:59,  2.80it/s, loss=0.5150, acc=73.56%, coarse=9.84%]
Training:  52%|█████▏    | 355/687 [02:14<01:58,  2.81it/s, loss=0.5150, acc=73.56%, coarse=9.84%]
Training:  52%|█████▏    | 355/687 [02:14<01:58,  2.81it/s, loss=0.7649, acc=73.57%, coarse=9.85%]
Training:  52%|█████▏    | 356/687 [02:14<01:58,  2.80it/s, loss=0.7649, acc=73.57%, coarse=9.85%]
Training:  52%|█████▏    | 356/687 [02:14<01:58,  2.80it/s, loss=0.7060, acc=73.59%, coarse=9.85%]
Training:  52%|█████▏    | 357/687 [02:14<01:57,  2.81it/s, loss=0.7060, acc=73.59%, coarse=9.85%]
Training:  52%|█████▏    | 357/687 [02:15<01:57,  2.81it/s, loss=0.6636, acc=73.59%, coarse=9.85%]
Training:  52%|█████▏    | 358/687 [02:15<01:56,  2.82it/s, loss=0.6636, acc=73.59%, coarse=9.85%]
Training:  52%|█████▏    | 358/687 [02:15<01:56,  2.82it/s, loss=0.5290, acc=73.63%, coarse=9.85%]
Training:  52%|█████▏    | 359/687 [02:15<01:56,  2.82it/s, loss=0.5290, acc=73.63%, coarse=9.85%]
Training:  52%|█████▏    | 359/687 [02:16<01:56,  2.82it/s, loss=0.8352, acc=73.65%, coarse=9.85%]
Training:  52%|█████▏    | 360/687 [02:16<02:02,  2.66it/s, loss=0.8352, acc=73.65%, coarse=9.85%]
Training:  52%|█████▏    | 360/687 [02:16<02:02,  2.66it/s, loss=0.7943, acc=73.63%, coarse=9.85%]
Training:  53%|█████▎    | 361/687 [02:16<01:59,  2.74it/s, loss=0.7943, acc=73.63%, coarse=9.85%]
Training:  53%|█████▎    | 361/687 [02:16<01:59,  2.74it/s, loss=0.8498, acc=73.65%, coarse=9.85%]
Training:  53%|█████▎    | 362/687 [02:16<01:56,  2.79it/s, loss=0.8498, acc=73.65%, coarse=9.85%]
Training:  53%|█████▎    | 362/687 [02:17<01:56,  2.79it/s, loss=0.6532, acc=73.66%, coarse=9.85%]
Training:  53%|█████▎    | 363/687 [02:17<01:59,  2.72it/s, loss=0.6532, acc=73.66%, coarse=9.85%]
Training:  53%|█████▎    | 363/687 [02:17<01:59,  2.72it/s, loss=0.6272, acc=73.68%, coarse=9.85%]
Training:  53%|█████▎    | 364/687 [02:17<01:56,  2.77it/s, loss=0.6272, acc=73.68%, coarse=9.85%]
Training:  53%|█████▎    | 364/687 [02:17<01:56,  2.77it/s, loss=0.6496, acc=73.69%, coarse=9.85%]
Training:  53%|█████▎    | 365/687 [02:17<01:55,  2.79it/s, loss=0.6496, acc=73.69%, coarse=9.85%]
Training:  53%|█████▎    | 365/687 [02:18<01:55,  2.79it/s, loss=0.4840, acc=73.70%, coarse=9.85%]
Training:  53%|█████▎    | 366/687 [02:18<01:54,  2.81it/s, loss=0.4840, acc=73.70%, coarse=9.85%]
Training:  53%|█████▎    | 366/687 [02:18<01:54,  2.81it/s, loss=0.5769, acc=73.72%, coarse=9.86%]
Training:  53%|█████▎    | 367/687 [02:18<01:53,  2.83it/s, loss=0.5769, acc=73.72%, coarse=9.86%]
Training:  53%|█████▎    | 367/687 [02:18<01:53,  2.83it/s, loss=0.8001, acc=73.72%, coarse=9.86%]
Training:  54%|█████▎    | 368/687 [02:18<01:52,  2.83it/s, loss=0.8001, acc=73.72%, coarse=9.86%]
Training:  54%|█████▎    | 368/687 [02:19<01:52,  2.83it/s, loss=0.6528, acc=73.74%, coarse=9.86%]
Training:  54%|█████▎    | 369/687 [02:19<01:54,  2.78it/s, loss=0.6528, acc=73.74%, coarse=9.86%]
Training:  54%|█████▎    | 369/687 [02:19<01:54,  2.78it/s, loss=0.7389, acc=73.74%, coarse=9.86%]
Training:  54%|█████▍    | 370/687 [02:19<02:00,  2.62it/s, loss=0.7389, acc=73.74%, coarse=9.86%]
Training:  54%|█████▍    | 370/687 [02:20<02:00,  2.62it/s, loss=0.7888, acc=73.74%, coarse=9.86%]
Training:  54%|█████▍    | 371/687 [02:20<02:04,  2.53it/s, loss=0.7888, acc=73.74%, coarse=9.86%]
Training:  54%|█████▍    | 371/687 [02:20<02:04,  2.53it/s, loss=0.5907, acc=73.76%, coarse=9.86%]
Training:  54%|█████▍    | 372/687 [02:20<02:07,  2.47it/s, loss=0.5907, acc=73.76%, coarse=9.86%]
Training:  54%|█████▍    | 372/687 [02:20<02:07,  2.47it/s, loss=0.9831, acc=73.76%, coarse=9.86%]
Training:  54%|█████▍    | 373/687 [02:20<02:02,  2.56it/s, loss=0.9831, acc=73.76%, coarse=9.86%]
Training:  54%|█████▍    | 373/687 [02:21<02:02,  2.56it/s, loss=0.7422, acc=73.77%, coarse=9.86%]
Training:  54%|█████▍    | 374/687 [02:21<02:07,  2.46it/s, loss=0.7422, acc=73.77%, coarse=9.86%]
Training:  54%|█████▍    | 374/687 [02:21<02:07,  2.46it/s, loss=0.6754, acc=73.79%, coarse=9.86%]
Training:  55%|█████▍    | 375/687 [02:21<02:02,  2.55it/s, loss=0.6754, acc=73.79%, coarse=9.86%]
Training:  55%|█████▍    | 375/687 [02:22<02:02,  2.55it/s, loss=0.7431, acc=73.81%, coarse=9.86%]
Training:  55%|█████▍    | 376/687 [02:22<02:02,  2.54it/s, loss=0.7431, acc=73.81%, coarse=9.86%]
Training:  55%|█████▍    | 376/687 [02:22<02:02,  2.54it/s, loss=0.5633, acc=73.81%, coarse=9.86%]
Training:  55%|█████▍    | 377/687 [02:22<01:58,  2.62it/s, loss=0.5633, acc=73.81%, coarse=9.86%]
Training:  55%|█████▍    | 377/687 [02:22<01:58,  2.62it/s, loss=0.7455, acc=73.81%, coarse=9.86%]
Training:  55%|█████▌    | 378/687 [02:22<02:00,  2.57it/s, loss=0.7455, acc=73.81%, coarse=9.86%]
Training:  55%|█████▌    | 378/687 [02:23<02:00,  2.57it/s, loss=0.9210, acc=73.82%, coarse=9.86%]
Training:  55%|█████▌    | 379/687 [02:23<01:59,  2.59it/s, loss=0.9210, acc=73.82%, coarse=9.86%]
Training:  55%|█████▌    | 379/687 [02:23<01:59,  2.59it/s, loss=0.6078, acc=73.84%, coarse=9.86%]
Training:  55%|█████▌    | 380/687 [02:23<01:59,  2.57it/s, loss=0.6078, acc=73.84%, coarse=9.86%]
Training:  55%|█████▌    | 380/687 [02:24<01:59,  2.57it/s, loss=0.5101, acc=73.87%, coarse=9.86%]
Training:  55%|█████▌    | 381/687 [02:24<01:55,  2.64it/s, loss=0.5101, acc=73.87%, coarse=9.86%]
Training:  55%|█████▌    | 381/687 [02:24<01:55,  2.64it/s, loss=0.7583, acc=73.88%, coarse=9.86%]
Training:  56%|█████▌    | 382/687 [02:24<01:57,  2.59it/s, loss=0.7583, acc=73.88%, coarse=9.86%]
Training:  56%|█████▌    | 382/687 [02:24<01:57,  2.59it/s, loss=0.5806, acc=73.88%, coarse=9.86%]
Training:  56%|█████▌    | 383/687 [02:24<01:53,  2.67it/s, loss=0.5806, acc=73.88%, coarse=9.86%]
Training:  56%|█████▌    | 383/687 [02:25<01:53,  2.67it/s, loss=0.6488, acc=73.89%, coarse=9.87%]
Training:  56%|█████▌    | 384/687 [02:25<01:55,  2.63it/s, loss=0.6488, acc=73.89%, coarse=9.87%]
Training:  56%|█████▌    | 384/687 [02:25<01:55,  2.63it/s, loss=0.4828, acc=73.91%, coarse=9.87%]
Training:  56%|█████▌    | 385/687 [02:25<01:55,  2.61it/s, loss=0.4828, acc=73.91%, coarse=9.87%]
Training:  56%|█████▌    | 385/687 [02:25<01:55,  2.61it/s, loss=0.5695, acc=73.93%, coarse=9.87%]
Training:  56%|█████▌    | 386/687 [02:25<01:52,  2.68it/s, loss=0.5695, acc=73.93%, coarse=9.87%]
Training:  56%|█████▌    | 386/687 [02:26<01:52,  2.68it/s, loss=0.7274, acc=73.93%, coarse=9.87%]
Training:  56%|█████▋    | 387/687 [02:26<01:49,  2.73it/s, loss=0.7274, acc=73.93%, coarse=9.87%]
Training:  56%|█████▋    | 387/687 [02:26<01:49,  2.73it/s, loss=0.7519, acc=73.94%, coarse=9.87%]
Training:  56%|█████▋    | 388/687 [02:26<01:48,  2.76it/s, loss=0.7519, acc=73.94%, coarse=9.87%]
Training:  56%|█████▋    | 388/687 [02:26<01:48,  2.76it/s, loss=0.5845, acc=73.95%, coarse=9.87%]
Training:  57%|█████▋    | 389/687 [02:26<01:46,  2.79it/s, loss=0.5845, acc=73.95%, coarse=9.87%]
Training:  57%|█████▋    | 389/687 [02:27<01:46,  2.79it/s, loss=0.7818, acc=73.97%, coarse=9.87%]
Training:  57%|█████▋    | 390/687 [02:27<01:45,  2.81it/s, loss=0.7818, acc=73.97%, coarse=9.87%]
Training:  57%|█████▋    | 390/687 [02:27<01:45,  2.81it/s, loss=0.4481, acc=74.00%, coarse=9.87%]
Training:  57%|█████▋    | 391/687 [02:27<01:45,  2.81it/s, loss=0.4481, acc=74.00%, coarse=9.87%]
Training:  57%|█████▋    | 391/687 [02:28<01:45,  2.81it/s, loss=0.6680, acc=74.03%, coarse=9.87%]
Training:  57%|█████▋    | 392/687 [02:28<01:46,  2.78it/s, loss=0.6680, acc=74.03%, coarse=9.87%]
Training:  57%|█████▋    | 392/687 [02:28<01:46,  2.78it/s, loss=0.6379, acc=74.04%, coarse=9.87%]
Training:  57%|█████▋    | 393/687 [02:28<01:52,  2.62it/s, loss=0.6379, acc=74.04%, coarse=9.87%]
Training:  57%|█████▋    | 393/687 [02:28<01:52,  2.62it/s, loss=0.7568, acc=74.05%, coarse=9.88%]
Training:  57%|█████▋    | 394/687 [02:28<01:52,  2.60it/s, loss=0.7568, acc=74.05%, coarse=9.88%]
Training:  57%|█████▋    | 394/687 [02:29<01:52,  2.60it/s, loss=0.5250, acc=74.09%, coarse=9.88%]
Training:  57%|█████▋    | 395/687 [02:29<01:53,  2.56it/s, loss=0.5250, acc=74.09%, coarse=9.88%]
Training:  57%|█████▋    | 395/687 [02:29<01:53,  2.56it/s, loss=0.8937, acc=74.08%, coarse=9.88%]
Training:  58%|█████▊    | 396/687 [02:29<01:50,  2.63it/s, loss=0.8937, acc=74.08%, coarse=9.88%]
Training:  58%|█████▊    | 396/687 [02:30<01:50,  2.63it/s, loss=0.5241, acc=74.11%, coarse=9.88%]
Training:  58%|█████▊    | 397/687 [02:30<01:54,  2.52it/s, loss=0.5241, acc=74.11%, coarse=9.88%]
Training:  58%|█████▊    | 397/687 [02:30<01:54,  2.52it/s, loss=0.7035, acc=74.12%, coarse=9.88%]
Training:  58%|█████▊    | 398/687 [02:30<01:51,  2.59it/s, loss=0.7035, acc=74.12%, coarse=9.88%]
Training:  58%|█████▊    | 398/687 [02:30<01:51,  2.59it/s, loss=0.8109, acc=74.13%, coarse=9.88%]
Training:  58%|█████▊    | 399/687 [02:30<01:53,  2.54it/s, loss=0.8109, acc=74.13%, coarse=9.88%]
Training:  58%|█████▊    | 399/687 [02:31<01:53,  2.54it/s, loss=0.8361, acc=74.14%, coarse=9.88%]
Training:  58%|█████▊    | 400/687 [02:31<01:50,  2.61it/s, loss=0.8361, acc=74.14%, coarse=9.88%]
Training:  58%|█████▊    | 400/687 [02:31<01:50,  2.61it/s, loss=0.5814, acc=74.15%, coarse=9.89%]
Training:  58%|█████▊    | 401/687 [02:31<01:51,  2.57it/s, loss=0.5814, acc=74.15%, coarse=9.89%]
Training:  58%|█████▊    | 401/687 [02:31<01:51,  2.57it/s, loss=0.7952, acc=74.16%, coarse=9.89%]
Training:  59%|█████▊    | 402/687 [02:31<01:48,  2.63it/s, loss=0.7952, acc=74.16%, coarse=9.89%]
Training:  59%|█████▊    | 402/687 [02:32<01:48,  2.63it/s, loss=0.5687, acc=74.17%, coarse=9.89%]
Training:  59%|█████▊    | 403/687 [02:32<01:51,  2.55it/s, loss=0.5687, acc=74.17%, coarse=9.89%]
Training:  59%|█████▊    | 403/687 [02:32<01:51,  2.55it/s, loss=0.7722, acc=74.16%, coarse=9.88%]
Training:  59%|█████▉    | 404/687 [02:32<01:47,  2.63it/s, loss=0.7722, acc=74.16%, coarse=9.88%]
Training:  59%|█████▉    | 404/687 [02:33<01:47,  2.63it/s, loss=0.7081, acc=74.16%, coarse=9.88%]
Training:  59%|█████▉    | 405/687 [02:33<01:49,  2.57it/s, loss=0.7081, acc=74.16%, coarse=9.88%]
Training:  59%|█████▉    | 405/687 [02:33<01:49,  2.57it/s, loss=0.8260, acc=74.16%, coarse=9.88%]
Training:  59%|█████▉    | 406/687 [02:33<01:46,  2.65it/s, loss=0.8260, acc=74.16%, coarse=9.88%]
Training:  59%|█████▉    | 406/687 [02:33<01:46,  2.65it/s, loss=0.5871, acc=74.17%, coarse=9.89%]
Training:  59%|█████▉    | 407/687 [02:33<01:47,  2.60it/s, loss=0.5871, acc=74.17%, coarse=9.89%]
Training:  59%|█████▉    | 407/687 [02:34<01:47,  2.60it/s, loss=0.6450, acc=74.18%, coarse=9.89%]
Training:  59%|█████▉    | 408/687 [02:34<01:44,  2.68it/s, loss=0.6450, acc=74.18%, coarse=9.89%]
Training:  59%|█████▉    | 408/687 [02:34<01:44,  2.68it/s, loss=0.5086, acc=74.20%, coarse=9.89%]
Training:  60%|█████▉    | 409/687 [02:34<01:43,  2.69it/s, loss=0.5086, acc=74.20%, coarse=9.89%]
Training:  60%|█████▉    | 409/687 [02:34<01:43,  2.69it/s, loss=0.7228, acc=74.22%, coarse=9.89%]
Training:  60%|█████▉    | 410/687 [02:34<01:41,  2.74it/s, loss=0.7228, acc=74.22%, coarse=9.89%]
Training:  60%|█████▉    | 410/687 [02:35<01:41,  2.74it/s, loss=0.5618, acc=74.22%, coarse=9.89%]
Training:  60%|█████▉    | 411/687 [02:35<01:39,  2.79it/s, loss=0.5618, acc=74.22%, coarse=9.89%]
Training:  60%|█████▉    | 411/687 [02:35<01:39,  2.79it/s, loss=0.9483, acc=74.22%, coarse=9.89%]
Training:  60%|█████▉    | 412/687 [02:35<01:37,  2.82it/s, loss=0.9483, acc=74.22%, coarse=9.89%]
Training:  60%|█████▉    | 412/687 [02:35<01:37,  2.82it/s, loss=0.5312, acc=74.23%, coarse=9.89%]
Training:  60%|██████    | 413/687 [02:35<01:36,  2.84it/s, loss=0.5312, acc=74.23%, coarse=9.89%]
Training:  60%|██████    | 413/687 [02:36<01:36,  2.84it/s, loss=0.5188, acc=74.24%, coarse=9.89%]
Training:  60%|██████    | 414/687 [02:36<01:35,  2.85it/s, loss=0.5188, acc=74.24%, coarse=9.89%]
Training:  60%|██████    | 414/687 [02:36<01:35,  2.85it/s, loss=0.5093, acc=74.26%, coarse=9.90%]
Training:  60%|██████    | 415/687 [02:36<01:34,  2.87it/s, loss=0.5093, acc=74.26%, coarse=9.90%]
Training:  60%|██████    | 415/687 [02:37<01:34,  2.87it/s, loss=0.7226, acc=74.26%, coarse=9.90%]
Training:  61%|██████    | 416/687 [02:37<01:34,  2.86it/s, loss=0.7226, acc=74.26%, coarse=9.90%]
Training:  61%|██████    | 416/687 [02:37<01:34,  2.86it/s, loss=0.8467, acc=74.26%, coarse=9.90%]
Training:  61%|██████    | 417/687 [02:37<01:34,  2.85it/s, loss=0.8467, acc=74.26%, coarse=9.90%]
Training:  61%|██████    | 417/687 [02:37<01:34,  2.85it/s, loss=1.0208, acc=74.27%, coarse=9.90%]
Training:  61%|██████    | 418/687 [02:37<01:34,  2.83it/s, loss=1.0208, acc=74.27%, coarse=9.90%]
Training:  61%|██████    | 418/687 [02:38<01:34,  2.83it/s, loss=0.6661, acc=74.29%, coarse=9.90%]
Training:  61%|██████    | 419/687 [02:38<01:35,  2.80it/s, loss=0.6661, acc=74.29%, coarse=9.90%]
Training:  61%|██████    | 419/687 [02:38<01:35,  2.80it/s, loss=0.5164, acc=74.29%, coarse=9.90%]
Training:  61%|██████    | 420/687 [02:38<01:34,  2.81it/s, loss=0.5164, acc=74.29%, coarse=9.90%]
Training:  61%|██████    | 420/687 [02:38<01:34,  2.81it/s, loss=0.4415, acc=74.31%, coarse=9.90%]
Training:  61%|██████▏   | 421/687 [02:38<01:34,  2.82it/s, loss=0.4415, acc=74.31%, coarse=9.90%]
Training:  61%|██████▏   | 421/687 [02:39<01:34,  2.82it/s, loss=0.4635, acc=74.35%, coarse=9.90%]
Training:  61%|██████▏   | 422/687 [02:39<01:34,  2.81it/s, loss=0.4635, acc=74.35%, coarse=9.90%]
Training:  61%|██████▏   | 422/687 [02:39<01:34,  2.81it/s, loss=0.7754, acc=74.35%, coarse=9.90%]
Training:  62%|██████▏   | 423/687 [02:39<01:34,  2.79it/s, loss=0.7754, acc=74.35%, coarse=9.90%]
Training:  62%|██████▏   | 423/687 [02:39<01:34,  2.79it/s, loss=0.7304, acc=74.36%, coarse=9.90%]
Training:  62%|██████▏   | 424/687 [02:39<01:38,  2.67it/s, loss=0.7304, acc=74.36%, coarse=9.90%]
Training:  62%|██████▏   | 424/687 [02:40<01:38,  2.67it/s, loss=0.6143, acc=74.39%, coarse=9.90%]
Training:  62%|██████▏   | 425/687 [02:40<01:36,  2.71it/s, loss=0.6143, acc=74.39%, coarse=9.90%]
Training:  62%|██████▏   | 425/687 [02:40<01:36,  2.71it/s, loss=0.6624, acc=74.41%, coarse=9.90%]
Training:  62%|██████▏   | 426/687 [02:40<01:40,  2.60it/s, loss=0.6624, acc=74.41%, coarse=9.90%]
Training:  62%|██████▏   | 426/687 [02:41<01:40,  2.60it/s, loss=0.9128, acc=74.39%, coarse=9.90%]
Training:  62%|██████▏   | 427/687 [02:41<01:37,  2.65it/s, loss=0.9128, acc=74.39%, coarse=9.90%]
Training:  62%|██████▏   | 427/687 [02:41<01:37,  2.65it/s, loss=0.9863, acc=74.39%, coarse=9.90%]
Training:  62%|██████▏   | 428/687 [02:41<01:35,  2.72it/s, loss=0.9863, acc=74.39%, coarse=9.90%]
Training:  62%|██████▏   | 428/687 [02:41<01:35,  2.72it/s, loss=0.8272, acc=74.41%, coarse=9.90%]
Training:  62%|██████▏   | 429/687 [02:41<01:33,  2.75it/s, loss=0.8272, acc=74.41%, coarse=9.90%]
Training:  62%|██████▏   | 429/687 [02:42<01:33,  2.75it/s, loss=0.9276, acc=74.42%, coarse=9.90%]
Training:  63%|██████▎   | 430/687 [02:42<01:32,  2.77it/s, loss=0.9276, acc=74.42%, coarse=9.90%]
Training:  63%|██████▎   | 430/687 [02:42<01:32,  2.77it/s, loss=0.4701, acc=74.41%, coarse=9.90%]
Training:  63%|██████▎   | 431/687 [02:42<01:36,  2.66it/s, loss=0.4701, acc=74.41%, coarse=9.90%]
Training:  63%|██████▎   | 431/687 [02:42<01:36,  2.66it/s, loss=0.7831, acc=74.40%, coarse=9.90%]
Training:  63%|██████▎   | 432/687 [02:42<01:34,  2.70it/s, loss=0.7831, acc=74.40%, coarse=9.90%]
Training:  63%|██████▎   | 432/687 [02:43<01:34,  2.70it/s, loss=0.7718, acc=74.40%, coarse=9.91%]
Training:  63%|██████▎   | 433/687 [02:43<01:37,  2.61it/s, loss=0.7718, acc=74.40%, coarse=9.91%]
Training:  63%|██████▎   | 433/687 [02:43<01:37,  2.61it/s, loss=0.5158, acc=74.42%, coarse=9.91%]
Training:  63%|██████▎   | 434/687 [02:43<01:34,  2.67it/s, loss=0.5158, acc=74.42%, coarse=9.91%]
Training:  63%|██████▎   | 434/687 [02:44<01:34,  2.67it/s, loss=0.5830, acc=74.43%, coarse=9.91%]
Training:  63%|██████▎   | 435/687 [02:44<01:35,  2.63it/s, loss=0.5830, acc=74.43%, coarse=9.91%]
Training:  63%|██████▎   | 435/687 [02:44<01:35,  2.63it/s, loss=0.5909, acc=74.45%, coarse=9.91%]
Training:  63%|██████▎   | 436/687 [02:44<01:33,  2.68it/s, loss=0.5909, acc=74.45%, coarse=9.91%]
Training:  63%|██████▎   | 436/687 [02:44<01:33,  2.68it/s, loss=0.7586, acc=74.45%, coarse=9.91%]
Training:  64%|██████▎   | 437/687 [02:44<01:35,  2.62it/s, loss=0.7586, acc=74.45%, coarse=9.91%]
Training:  64%|██████▎   | 437/687 [02:45<01:35,  2.62it/s, loss=0.5256, acc=74.48%, coarse=9.91%]
Training:  64%|██████▍   | 438/687 [02:45<01:32,  2.68it/s, loss=0.5256, acc=74.48%, coarse=9.91%]
Training:  64%|██████▍   | 438/687 [02:45<01:32,  2.68it/s, loss=0.8345, acc=74.49%, coarse=9.91%]
Training:  64%|██████▍   | 439/687 [02:45<01:34,  2.61it/s, loss=0.8345, acc=74.49%, coarse=9.91%]
Training:  64%|██████▍   | 439/687 [02:45<01:34,  2.61it/s, loss=0.7189, acc=74.49%, coarse=9.91%]
Training:  64%|██████▍   | 440/687 [02:45<01:32,  2.66it/s, loss=0.7189, acc=74.49%, coarse=9.91%]
Training:  64%|██████▍   | 440/687 [02:46<01:32,  2.66it/s, loss=0.6021, acc=74.50%, coarse=9.91%]
Training:  64%|██████▍   | 441/687 [02:46<01:35,  2.58it/s, loss=0.6021, acc=74.50%, coarse=9.91%]
Training:  64%|██████▍   | 441/687 [02:46<01:35,  2.58it/s, loss=0.9018, acc=74.51%, coarse=9.91%]
Training:  64%|██████▍   | 442/687 [02:46<01:33,  2.63it/s, loss=0.9018, acc=74.51%, coarse=9.91%]
Training:  64%|██████▍   | 442/687 [02:47<01:33,  2.63it/s, loss=0.6392, acc=74.53%, coarse=9.91%]
Training:  64%|██████▍   | 443/687 [02:47<01:34,  2.58it/s, loss=0.6392, acc=74.53%, coarse=9.91%]
Training:  64%|██████▍   | 443/687 [02:47<01:34,  2.58it/s, loss=0.6877, acc=74.55%, coarse=9.91%]
Training:  65%|██████▍   | 444/687 [02:47<01:32,  2.64it/s, loss=0.6877, acc=74.55%, coarse=9.91%]
Training:  65%|██████▍   | 444/687 [02:47<01:32,  2.64it/s, loss=0.9670, acc=74.55%, coarse=9.91%]
Training:  65%|██████▍   | 445/687 [02:47<01:29,  2.69it/s, loss=0.9670, acc=74.55%, coarse=9.91%]
Training:  65%|██████▍   | 445/687 [02:48<01:29,  2.69it/s, loss=0.9303, acc=74.53%, coarse=9.91%]
Training:  65%|██████▍   | 446/687 [02:48<01:28,  2.72it/s, loss=0.9303, acc=74.53%, coarse=9.91%]
Training:  65%|██████▍   | 446/687 [02:48<01:28,  2.72it/s, loss=0.6294, acc=74.52%, coarse=9.91%]
Training:  65%|██████▌   | 447/687 [02:48<01:30,  2.66it/s, loss=0.6294, acc=74.52%, coarse=9.91%]
Training:  65%|██████▌   | 447/687 [02:49<01:30,  2.66it/s, loss=0.8531, acc=74.51%, coarse=9.91%]
Training:  65%|██████▌   | 448/687 [02:49<01:33,  2.55it/s, loss=0.8531, acc=74.51%, coarse=9.91%]
Training:  65%|██████▌   | 448/687 [02:49<01:33,  2.55it/s, loss=0.9535, acc=74.51%, coarse=9.91%]
Training:  65%|██████▌   | 449/687 [02:49<01:29,  2.65it/s, loss=0.9535, acc=74.51%, coarse=9.91%]
Training:  65%|██████▌   | 449/687 [02:49<01:29,  2.65it/s, loss=0.6306, acc=74.51%, coarse=9.91%]
Training:  66%|██████▌   | 450/687 [02:49<01:27,  2.72it/s, loss=0.6306, acc=74.51%, coarse=9.91%]
Training:  66%|██████▌   | 450/687 [02:50<01:27,  2.72it/s, loss=0.6544, acc=74.51%, coarse=9.91%]
Training:  66%|██████▌   | 451/687 [02:50<01:25,  2.78it/s, loss=0.6544, acc=74.51%, coarse=9.91%]
Training:  66%|██████▌   | 451/687 [02:50<01:25,  2.78it/s, loss=0.9069, acc=74.49%, coarse=9.91%]
Training:  66%|██████▌   | 452/687 [02:50<01:24,  2.78it/s, loss=0.9069, acc=74.49%, coarse=9.91%]
Training:  66%|██████▌   | 452/687 [02:50<01:24,  2.78it/s, loss=0.5906, acc=74.50%, coarse=9.91%]
Training:  66%|██████▌   | 453/687 [02:50<01:28,  2.66it/s, loss=0.5906, acc=74.50%, coarse=9.91%]
Training:  66%|██████▌   | 453/687 [02:51<01:28,  2.66it/s, loss=0.8941, acc=74.49%, coarse=9.91%]
Training:  66%|██████▌   | 454/687 [02:51<01:26,  2.69it/s, loss=0.8941, acc=74.49%, coarse=9.91%]
Training:  66%|██████▌   | 454/687 [02:51<01:26,  2.69it/s, loss=0.5907, acc=74.49%, coarse=9.91%]
Training:  66%|██████▌   | 455/687 [02:51<01:28,  2.61it/s, loss=0.5907, acc=74.49%, coarse=9.91%]
Training:  66%|██████▌   | 455/687 [02:51<01:28,  2.61it/s, loss=0.7159, acc=74.50%, coarse=9.91%]
Training:  66%|██████▋   | 456/687 [02:51<01:26,  2.67it/s, loss=0.7159, acc=74.50%, coarse=9.91%]
Training:  66%|██████▋   | 456/687 [02:52<01:26,  2.67it/s, loss=0.6684, acc=74.51%, coarse=9.91%]
Training:  67%|██████▋   | 457/687 [02:52<01:35,  2.42it/s, loss=0.6684, acc=74.51%, coarse=9.91%]
Training:  67%|██████▋   | 457/687 [02:52<01:35,  2.42it/s, loss=0.5230, acc=74.51%, coarse=9.91%]
Training:  67%|██████▋   | 458/687 [02:52<01:36,  2.36it/s, loss=0.5230, acc=74.51%, coarse=9.91%]
Training:  67%|██████▋   | 458/687 [02:53<01:36,  2.36it/s, loss=0.4815, acc=74.54%, coarse=9.91%]
Training:  67%|██████▋   | 459/687 [02:53<01:39,  2.29it/s, loss=0.4815, acc=74.54%, coarse=9.91%]
Training:  67%|██████▋   | 459/687 [02:53<01:39,  2.29it/s, loss=0.9269, acc=74.53%, coarse=9.91%]
Training:  67%|██████▋   | 460/687 [02:53<01:41,  2.23it/s, loss=0.9269, acc=74.53%, coarse=9.91%]
Training:  67%|██████▋   | 460/687 [02:54<01:41,  2.23it/s, loss=0.6571, acc=74.54%, coarse=9.91%]
Training:  67%|██████▋   | 461/687 [02:54<01:41,  2.24it/s, loss=0.6571, acc=74.54%, coarse=9.91%]
Training:  67%|██████▋   | 461/687 [02:54<01:41,  2.24it/s, loss=0.5845, acc=74.57%, coarse=9.92%]
Training:  67%|██████▋   | 462/687 [02:54<01:46,  2.12it/s, loss=0.5845, acc=74.57%, coarse=9.92%]
Training:  67%|██████▋   | 462/687 [02:55<01:46,  2.12it/s, loss=0.6069, acc=74.57%, coarse=9.92%]
Training:  67%|██████▋   | 463/687 [02:55<01:41,  2.21it/s, loss=0.6069, acc=74.57%, coarse=9.92%]
Training:  67%|██████▋   | 463/687 [02:55<01:41,  2.21it/s, loss=0.5843, acc=74.58%, coarse=9.92%]
Training:  68%|██████▊   | 464/687 [02:55<01:36,  2.32it/s, loss=0.5843, acc=74.58%, coarse=9.92%]
Training:  68%|██████▊   | 464/687 [02:56<01:36,  2.32it/s, loss=0.9685, acc=74.57%, coarse=9.92%]
Training:  68%|██████▊   | 465/687 [02:56<01:34,  2.35it/s, loss=0.9685, acc=74.57%, coarse=9.92%]
Training:  68%|██████▊   | 465/687 [02:56<01:34,  2.35it/s, loss=0.5659, acc=74.59%, coarse=9.92%]
Training:  68%|██████▊   | 466/687 [02:56<01:30,  2.45it/s, loss=0.5659, acc=74.59%, coarse=9.92%]
Training:  68%|██████▊   | 466/687 [02:56<01:30,  2.45it/s, loss=0.6569, acc=74.60%, coarse=9.92%]
Training:  68%|██████▊   | 467/687 [02:56<01:27,  2.53it/s, loss=0.6569, acc=74.60%, coarse=9.92%]
Training:  68%|██████▊   | 467/687 [02:57<01:27,  2.53it/s, loss=0.6519, acc=74.61%, coarse=9.92%]
Training:  68%|██████▊   | 468/687 [02:57<01:23,  2.62it/s, loss=0.6519, acc=74.61%, coarse=9.92%]
Training:  68%|██████▊   | 468/687 [02:57<01:23,  2.62it/s, loss=0.7008, acc=74.62%, coarse=9.92%]
Training:  68%|██████▊   | 469/687 [02:57<01:21,  2.66it/s, loss=0.7008, acc=74.62%, coarse=9.92%]
Training:  68%|██████▊   | 469/687 [02:57<01:21,  2.66it/s, loss=0.5820, acc=74.62%, coarse=9.92%]
Training:  68%|██████▊   | 470/687 [02:57<01:21,  2.65it/s, loss=0.5820, acc=74.62%, coarse=9.92%]
Training:  68%|██████▊   | 470/687 [02:58<01:21,  2.65it/s, loss=0.8601, acc=74.61%, coarse=9.92%]
Training:  69%|██████▊   | 471/687 [02:58<01:23,  2.60it/s, loss=0.8601, acc=74.61%, coarse=9.92%]
Training:  69%|██████▊   | 471/687 [02:58<01:23,  2.60it/s, loss=0.7950, acc=74.62%, coarse=9.92%]
Training:  69%|██████▊   | 472/687 [02:58<01:21,  2.65it/s, loss=0.7950, acc=74.62%, coarse=9.92%]
Training:  69%|██████▊   | 472/687 [02:58<01:21,  2.65it/s, loss=0.5829, acc=74.64%, coarse=9.92%]
Training:  69%|██████▉   | 473/687 [02:58<01:19,  2.70it/s, loss=0.5829, acc=74.64%, coarse=9.92%]
Training:  69%|██████▉   | 473/687 [02:59<01:19,  2.70it/s, loss=1.0505, acc=74.64%, coarse=9.92%]
Training:  69%|██████▉   | 474/687 [02:59<01:18,  2.71it/s, loss=1.0505, acc=74.64%, coarse=9.92%]
Training:  69%|██████▉   | 474/687 [02:59<01:18,  2.71it/s, loss=0.7433, acc=74.64%, coarse=9.92%]
Training:  69%|██████▉   | 475/687 [02:59<01:18,  2.72it/s, loss=0.7433, acc=74.64%, coarse=9.92%]
Training:  69%|██████▉   | 475/687 [03:00<01:18,  2.72it/s, loss=0.6467, acc=74.65%, coarse=9.92%]
Training:  69%|██████▉   | 476/687 [03:00<01:17,  2.73it/s, loss=0.6467, acc=74.65%, coarse=9.92%]
Training:  69%|██████▉   | 476/687 [03:00<01:17,  2.73it/s, loss=0.5356, acc=74.66%, coarse=9.92%]
Training:  69%|██████▉   | 477/687 [03:00<01:15,  2.78it/s, loss=0.5356, acc=74.66%, coarse=9.92%]
Training:  69%|██████▉   | 477/687 [03:00<01:15,  2.78it/s, loss=0.7357, acc=74.66%, coarse=9.92%]
Training:  70%|██████▉   | 478/687 [03:00<01:14,  2.82it/s, loss=0.7357, acc=74.66%, coarse=9.92%]
Training:  70%|██████▉   | 478/687 [03:01<01:14,  2.82it/s, loss=0.4866, acc=74.67%, coarse=9.92%]
Training:  70%|██████▉   | 479/687 [03:01<01:13,  2.83it/s, loss=0.4866, acc=74.67%, coarse=9.92%]
Training:  70%|██████▉   | 479/687 [03:01<01:13,  2.83it/s, loss=0.7785, acc=74.67%, coarse=9.92%]
Training:  70%|██████▉   | 480/687 [03:01<01:12,  2.84it/s, loss=0.7785, acc=74.67%, coarse=9.92%]
Training:  70%|██████▉   | 480/687 [03:01<01:12,  2.84it/s, loss=0.5717, acc=74.67%, coarse=9.93%]
Training:  70%|███████   | 481/687 [03:01<01:12,  2.84it/s, loss=0.5717, acc=74.67%, coarse=9.93%]
Training:  70%|███████   | 481/687 [03:02<01:12,  2.84it/s, loss=0.5783, acc=74.70%, coarse=9.93%]
Training:  70%|███████   | 482/687 [03:02<01:11,  2.85it/s, loss=0.5783, acc=74.70%, coarse=9.93%]
Training:  70%|███████   | 482/687 [03:02<01:11,  2.85it/s, loss=0.7984, acc=74.68%, coarse=9.93%]
Training:  70%|███████   | 483/687 [03:02<01:11,  2.84it/s, loss=0.7984, acc=74.68%, coarse=9.93%]
Training:  70%|███████   | 483/687 [03:02<01:11,  2.84it/s, loss=0.8145, acc=74.68%, coarse=9.93%]
Training:  70%|███████   | 484/687 [03:02<01:11,  2.83it/s, loss=0.8145, acc=74.68%, coarse=9.93%]
Training:  70%|███████   | 484/687 [03:03<01:11,  2.83it/s, loss=0.7361, acc=74.68%, coarse=9.93%]
Training:  71%|███████   | 485/687 [03:03<01:11,  2.83it/s, loss=0.7361, acc=74.68%, coarse=9.93%]
Training:  71%|███████   | 485/687 [03:03<01:11,  2.83it/s, loss=0.7637, acc=74.68%, coarse=9.93%]
Training:  71%|███████   | 486/687 [03:03<01:11,  2.79it/s, loss=0.7637, acc=74.68%, coarse=9.93%]
Training:  71%|███████   | 486/687 [03:03<01:11,  2.79it/s, loss=0.7208, acc=74.70%, coarse=9.93%]
Training:  71%|███████   | 487/687 [03:03<01:12,  2.75it/s, loss=0.7208, acc=74.70%, coarse=9.93%]
Training:  71%|███████   | 487/687 [03:04<01:12,  2.75it/s, loss=0.6283, acc=74.69%, coarse=9.93%]
Training:  71%|███████   | 488/687 [03:04<01:15,  2.64it/s, loss=0.6283, acc=74.69%, coarse=9.93%]
Training:  71%|███████   | 488/687 [03:04<01:15,  2.64it/s, loss=0.9370, acc=74.66%, coarse=9.93%]
Training:  71%|███████   | 489/687 [03:04<01:13,  2.70it/s, loss=0.9370, acc=74.66%, coarse=9.93%]
Training:  71%|███████   | 489/687 [03:05<01:13,  2.70it/s, loss=0.5639, acc=74.68%, coarse=9.93%]
Training:  71%|███████▏  | 490/687 [03:05<01:14,  2.65it/s, loss=0.5639, acc=74.68%, coarse=9.93%]
Training:  71%|███████▏  | 490/687 [03:05<01:14,  2.65it/s, loss=0.7675, acc=74.69%, coarse=9.93%]
Training:  71%|███████▏  | 491/687 [03:05<01:12,  2.70it/s, loss=0.7675, acc=74.69%, coarse=9.93%]
Training:  71%|███████▏  | 491/687 [03:05<01:12,  2.70it/s, loss=0.6519, acc=74.71%, coarse=9.93%]
Training:  72%|███████▏  | 492/687 [03:05<01:14,  2.63it/s, loss=0.6519, acc=74.71%, coarse=9.93%]
Training:  72%|███████▏  | 492/687 [03:06<01:14,  2.63it/s, loss=0.9334, acc=74.70%, coarse=9.93%]
Training:  72%|███████▏  | 493/687 [03:06<01:12,  2.68it/s, loss=0.9334, acc=74.70%, coarse=9.93%]
Training:  72%|███████▏  | 493/687 [03:06<01:12,  2.68it/s, loss=0.5968, acc=74.71%, coarse=9.93%]
Training:  72%|███████▏  | 494/687 [03:06<01:13,  2.62it/s, loss=0.5968, acc=74.71%, coarse=9.93%]
Training:  72%|███████▏  | 494/687 [03:06<01:13,  2.62it/s, loss=0.7097, acc=74.72%, coarse=9.93%]
Training:  72%|███████▏  | 495/687 [03:06<01:12,  2.65it/s, loss=0.7097, acc=74.72%, coarse=9.93%]
Training:  72%|███████▏  | 495/687 [03:07<01:12,  2.65it/s, loss=0.4685, acc=74.73%, coarse=9.93%]
Training:  72%|███████▏  | 496/687 [03:07<01:13,  2.61it/s, loss=0.4685, acc=74.73%, coarse=9.93%]
Training:  72%|███████▏  | 496/687 [03:07<01:13,  2.61it/s, loss=0.8216, acc=74.73%, coarse=9.93%]
Training:  72%|███████▏  | 497/687 [03:07<01:11,  2.67it/s, loss=0.8216, acc=74.73%, coarse=9.93%]
Training:  72%|███████▏  | 497/687 [03:08<01:11,  2.67it/s, loss=0.9435, acc=74.73%, coarse=9.93%]
Training:  72%|███████▏  | 498/687 [03:08<01:12,  2.60it/s, loss=0.9435, acc=74.73%, coarse=9.93%]
Training:  72%|███████▏  | 498/687 [03:08<01:12,  2.60it/s, loss=0.6582, acc=74.74%, coarse=9.93%]
Training:  73%|███████▎  | 499/687 [03:08<01:10,  2.66it/s, loss=0.6582, acc=74.74%, coarse=9.93%]
Training:  73%|███████▎  | 499/687 [03:08<01:10,  2.66it/s, loss=0.5477, acc=74.74%, coarse=9.93%]
Training:  73%|███████▎  | 500/687 [03:08<01:12,  2.58it/s, loss=0.5477, acc=74.74%, coarse=9.93%]
Training:  73%|███████▎  | 500/687 [03:09<01:12,  2.58it/s, loss=0.6469, acc=74.76%, coarse=9.94%]
Training:  73%|███████▎  | 501/687 [03:09<01:10,  2.63it/s, loss=0.6469, acc=74.76%, coarse=9.94%]
Training:  73%|███████▎  | 501/687 [03:09<01:10,  2.63it/s, loss=0.7504, acc=74.76%, coarse=9.94%]
Training:  73%|███████▎  | 502/687 [03:09<01:12,  2.54it/s, loss=0.7504, acc=74.76%, coarse=9.94%]
Training:  73%|███████▎  | 502/687 [03:10<01:12,  2.54it/s, loss=0.6284, acc=74.78%, coarse=9.94%]
Training:  73%|███████▎  | 503/687 [03:10<01:09,  2.64it/s, loss=0.6284, acc=74.78%, coarse=9.94%]
Training:  73%|███████▎  | 503/687 [03:10<01:09,  2.64it/s, loss=0.4046, acc=74.79%, coarse=9.94%]
Training:  73%|███████▎  | 504/687 [03:10<01:07,  2.70it/s, loss=0.4046, acc=74.79%, coarse=9.94%]
Training:  73%|███████▎  | 504/687 [03:10<01:07,  2.70it/s, loss=0.6582, acc=74.79%, coarse=9.94%]
Training:  74%|███████▎  | 505/687 [03:10<01:06,  2.75it/s, loss=0.6582, acc=74.79%, coarse=9.94%]
Training:  74%|███████▎  | 505/687 [03:11<01:06,  2.75it/s, loss=0.7012, acc=74.79%, coarse=9.94%]
Training:  74%|███████▎  | 506/687 [03:11<01:05,  2.74it/s, loss=0.7012, acc=74.79%, coarse=9.94%]
Training:  74%|███████▎  | 506/687 [03:11<01:05,  2.74it/s, loss=0.7982, acc=74.79%, coarse=9.94%]
Training:  74%|███████▍  | 507/687 [03:11<01:04,  2.77it/s, loss=0.7982, acc=74.79%, coarse=9.94%]
Training:  74%|███████▍  | 507/687 [03:11<01:04,  2.77it/s, loss=0.5810, acc=74.79%, coarse=9.94%]
Training:  74%|███████▍  | 508/687 [03:11<01:03,  2.80it/s, loss=0.5810, acc=74.79%, coarse=9.94%]
Training:  74%|███████▍  | 508/687 [03:12<01:03,  2.80it/s, loss=0.5429, acc=74.79%, coarse=9.94%]
Training:  74%|███████▍  | 509/687 [03:12<01:02,  2.84it/s, loss=0.5429, acc=74.79%, coarse=9.94%]
Training:  74%|███████▍  | 509/687 [03:12<01:02,  2.84it/s, loss=0.6424, acc=74.81%, coarse=9.94%]
Training:  74%|███████▍  | 510/687 [03:12<01:03,  2.80it/s, loss=0.6424, acc=74.81%, coarse=9.94%]
Training:  74%|███████▍  | 510/687 [03:12<01:03,  2.80it/s, loss=0.6973, acc=74.81%, coarse=9.94%]
Training:  74%|███████▍  | 511/687 [03:12<01:05,  2.69it/s, loss=0.6973, acc=74.81%, coarse=9.94%]
Training:  74%|███████▍  | 511/687 [03:13<01:05,  2.69it/s, loss=0.6684, acc=74.80%, coarse=9.95%]
Training:  75%|███████▍  | 512/687 [03:13<01:07,  2.57it/s, loss=0.6684, acc=74.80%, coarse=9.95%]
Training:  75%|███████▍  | 512/687 [03:13<01:07,  2.57it/s, loss=0.8261, acc=74.80%, coarse=9.95%]
Training:  75%|███████▍  | 513/687 [03:13<01:07,  2.60it/s, loss=0.8261, acc=74.80%, coarse=9.95%]
Training:  75%|███████▍  | 513/687 [03:14<01:07,  2.60it/s, loss=0.5452, acc=74.81%, coarse=9.95%]
Training:  75%|███████▍  | 514/687 [03:14<01:07,  2.55it/s, loss=0.5452, acc=74.81%, coarse=9.95%]
Training:  75%|███████▍  | 514/687 [03:14<01:07,  2.55it/s, loss=0.5413, acc=74.82%, coarse=9.95%]
Training:  75%|███████▍  | 515/687 [03:14<01:07,  2.56it/s, loss=0.5413, acc=74.82%, coarse=9.95%]
Training:  75%|███████▍  | 515/687 [03:14<01:07,  2.56it/s, loss=0.5070, acc=74.83%, coarse=9.95%]
Training:  75%|███████▌  | 516/687 [03:14<01:07,  2.54it/s, loss=0.5070, acc=74.83%, coarse=9.95%]
Training:  75%|███████▌  | 516/687 [03:15<01:07,  2.54it/s, loss=0.7962, acc=74.84%, coarse=9.95%]
Training:  75%|███████▌  | 517/687 [03:15<01:05,  2.59it/s, loss=0.7962, acc=74.84%, coarse=9.95%]
Training:  75%|███████▌  | 517/687 [03:15<01:05,  2.59it/s, loss=0.6087, acc=74.85%, coarse=9.95%]
Training:  75%|███████▌  | 518/687 [03:15<01:07,  2.50it/s, loss=0.6087, acc=74.85%, coarse=9.95%]
Training:  75%|███████▌  | 518/687 [03:16<01:07,  2.50it/s, loss=0.7998, acc=74.86%, coarse=9.95%]
Training:  76%|███████▌  | 519/687 [03:16<01:06,  2.53it/s, loss=0.7998, acc=74.86%, coarse=9.95%]
Training:  76%|███████▌  | 519/687 [03:16<01:06,  2.53it/s, loss=0.5845, acc=74.86%, coarse=9.95%]
Training:  76%|███████▌  | 520/687 [03:16<01:08,  2.45it/s, loss=0.5845, acc=74.86%, coarse=9.95%]
Training:  76%|███████▌  | 520/687 [03:16<01:08,  2.45it/s, loss=0.4814, acc=74.87%, coarse=9.95%]
Training:  76%|███████▌  | 521/687 [03:16<01:06,  2.51it/s, loss=0.4814, acc=74.87%, coarse=9.95%]
Training:  76%|███████▌  | 521/687 [03:17<01:06,  2.51it/s, loss=0.7959, acc=74.87%, coarse=9.95%]
Training:  76%|███████▌  | 522/687 [03:17<01:06,  2.48it/s, loss=0.7959, acc=74.87%, coarse=9.95%]
Training:  76%|███████▌  | 522/687 [03:17<01:06,  2.48it/s, loss=0.6935, acc=74.87%, coarse=9.95%]
Training:  76%|███████▌  | 523/687 [03:17<01:04,  2.54it/s, loss=0.6935, acc=74.87%, coarse=9.95%]
Training:  76%|███████▌  | 523/687 [03:18<01:04,  2.54it/s, loss=0.8463, acc=74.89%, coarse=9.95%]
Training:  76%|███████▋  | 524/687 [03:18<01:05,  2.47it/s, loss=0.8463, acc=74.89%, coarse=9.95%]
Training:  76%|███████▋  | 524/687 [03:18<01:05,  2.47it/s, loss=0.7440, acc=74.89%, coarse=9.95%]
Training:  76%|███████▋  | 525/687 [03:18<01:02,  2.57it/s, loss=0.7440, acc=74.89%, coarse=9.95%]
Training:  76%|███████▋  | 525/687 [03:18<01:02,  2.57it/s, loss=0.7309, acc=74.89%, coarse=9.95%]
Training:  77%|███████▋  | 526/687 [03:18<01:01,  2.64it/s, loss=0.7309, acc=74.89%, coarse=9.95%]
Training:  77%|███████▋  | 526/687 [03:19<01:01,  2.64it/s, loss=0.6579, acc=74.90%, coarse=9.95%]
Training:  77%|███████▋  | 527/687 [03:19<00:59,  2.67it/s, loss=0.6579, acc=74.90%, coarse=9.95%]
Training:  77%|███████▋  | 527/687 [03:19<00:59,  2.67it/s, loss=0.5790, acc=74.92%, coarse=9.95%]
Training:  77%|███████▋  | 528/687 [03:19<00:59,  2.69it/s, loss=0.5790, acc=74.92%, coarse=9.95%]
Training:  77%|███████▋  | 528/687 [03:19<00:59,  2.69it/s, loss=0.4764, acc=74.93%, coarse=9.95%]
Training:  77%|███████▋  | 529/687 [03:19<00:58,  2.68it/s, loss=0.4764, acc=74.93%, coarse=9.95%]
Training:  77%|███████▋  | 529/687 [03:20<00:58,  2.68it/s, loss=0.5941, acc=74.94%, coarse=9.96%]
Training:  77%|███████▋  | 530/687 [03:20<00:57,  2.71it/s, loss=0.5941, acc=74.94%, coarse=9.96%]
Training:  77%|███████▋  | 530/687 [03:20<00:57,  2.71it/s, loss=0.8236, acc=74.94%, coarse=9.96%]
Training:  77%|███████▋  | 531/687 [03:20<00:56,  2.75it/s, loss=0.8236, acc=74.94%, coarse=9.96%]
Training:  77%|███████▋  | 531/687 [03:21<00:56,  2.75it/s, loss=0.9604, acc=74.93%, coarse=9.96%]
Training:  77%|███████▋  | 532/687 [03:21<00:55,  2.77it/s, loss=0.9604, acc=74.93%, coarse=9.96%]
Training:  77%|███████▋  | 532/687 [03:21<00:55,  2.77it/s, loss=0.7055, acc=74.95%, coarse=9.96%]
Training:  78%|███████▊  | 533/687 [03:21<00:56,  2.71it/s, loss=0.7055, acc=74.95%, coarse=9.96%]
Training:  78%|███████▊  | 533/687 [03:21<00:56,  2.71it/s, loss=0.8118, acc=74.94%, coarse=9.96%]
Training:  78%|███████▊  | 534/687 [03:21<00:59,  2.58it/s, loss=0.8118, acc=74.94%, coarse=9.96%]
Training:  78%|███████▊  | 534/687 [03:22<00:59,  2.58it/s, loss=0.5693, acc=74.95%, coarse=9.96%]
Training:  78%|███████▊  | 535/687 [03:22<00:59,  2.55it/s, loss=0.5693, acc=74.95%, coarse=9.96%]
Training:  78%|███████▊  | 535/687 [03:22<00:59,  2.55it/s, loss=0.3910, acc=74.97%, coarse=9.96%]
Training:  78%|███████▊  | 536/687 [03:22<01:01,  2.47it/s, loss=0.3910, acc=74.97%, coarse=9.96%]
Training:  78%|███████▊  | 536/687 [03:23<01:01,  2.47it/s, loss=0.5264, acc=74.98%, coarse=9.96%]
Training:  78%|███████▊  | 537/687 [03:23<01:00,  2.49it/s, loss=0.5264, acc=74.98%, coarse=9.96%]
Training:  78%|███████▊  | 537/687 [03:23<01:00,  2.49it/s, loss=0.5455, acc=74.97%, coarse=9.96%]
Training:  78%|███████▊  | 538/687 [03:23<01:02,  2.40it/s, loss=0.5455, acc=74.97%, coarse=9.96%]
Training:  78%|███████▊  | 538/687 [03:24<01:02,  2.40it/s, loss=0.6229, acc=74.99%, coarse=9.96%]
Training:  78%|███████▊  | 539/687 [03:24<01:05,  2.27it/s, loss=0.6229, acc=74.99%, coarse=9.96%]
Training:  78%|███████▊  | 539/687 [03:24<01:05,  2.27it/s, loss=0.5670, acc=75.00%, coarse=9.96%]
Training:  79%|███████▊  | 540/687 [03:24<01:07,  2.19it/s, loss=0.5670, acc=75.00%, coarse=9.96%]
Training:  79%|███████▊  | 540/687 [03:25<01:07,  2.19it/s, loss=0.7378, acc=75.00%, coarse=9.96%]
Training:  79%|███████▊  | 541/687 [03:25<01:09,  2.10it/s, loss=0.7378, acc=75.00%, coarse=9.96%]
Training:  79%|███████▊  | 541/687 [03:25<01:09,  2.10it/s, loss=0.6805, acc=75.00%, coarse=9.96%]
Training:  79%|███████▉  | 542/687 [03:25<01:10,  2.05it/s, loss=0.6805, acc=75.00%, coarse=9.96%]
Training:  79%|███████▉  | 542/687 [03:26<01:10,  2.05it/s, loss=0.5904, acc=75.01%, coarse=9.96%]
Training:  79%|███████▉  | 543/687 [03:26<01:09,  2.06it/s, loss=0.5904, acc=75.01%, coarse=9.96%]
Training:  79%|███████▉  | 543/687 [03:26<01:09,  2.06it/s, loss=0.7096, acc=75.02%, coarse=9.96%]
Training:  79%|███████▉  | 544/687 [03:26<01:10,  2.02it/s, loss=0.7096, acc=75.02%, coarse=9.96%]
Training:  79%|███████▉  | 544/687 [03:27<01:10,  2.02it/s, loss=0.5757, acc=75.03%, coarse=9.96%]
Training:  79%|███████▉  | 545/687 [03:27<01:22,  1.73it/s, loss=0.5757, acc=75.03%, coarse=9.96%]
Training:  79%|███████▉  | 545/687 [03:27<01:22,  1.73it/s, loss=0.7740, acc=75.04%, coarse=9.96%]
Training:  79%|███████▉  | 546/687 [03:27<01:16,  1.83it/s, loss=0.7740, acc=75.04%, coarse=9.96%]
Training:  79%|███████▉  | 546/687 [03:28<01:16,  1.83it/s, loss=0.8803, acc=75.04%, coarse=9.96%]
Training:  80%|███████▉  | 547/687 [03:28<01:12,  1.92it/s, loss=0.8803, acc=75.04%, coarse=9.96%]
Training:  80%|███████▉  | 547/687 [03:28<01:12,  1.92it/s, loss=0.6916, acc=75.03%, coarse=9.96%]
Training:  80%|███████▉  | 548/687 [03:28<01:10,  1.98it/s, loss=0.6916, acc=75.03%, coarse=9.96%]
Training:  80%|███████▉  | 548/687 [03:29<01:10,  1.98it/s, loss=0.7326, acc=75.04%, coarse=9.96%]
Training:  80%|███████▉  | 549/687 [03:29<01:08,  2.02it/s, loss=0.7326, acc=75.04%, coarse=9.96%]
Training:  80%|███████▉  | 549/687 [03:29<01:08,  2.02it/s, loss=0.4974, acc=75.03%, coarse=9.97%]
Training:  80%|████████  | 550/687 [03:29<01:07,  2.04it/s, loss=0.4974, acc=75.03%, coarse=9.97%]
Training:  80%|████████  | 550/687 [03:30<01:07,  2.04it/s, loss=0.5510, acc=75.04%, coarse=9.97%]
Training:  80%|████████  | 551/687 [03:30<01:08,  2.00it/s, loss=0.5510, acc=75.04%, coarse=9.97%]
Training:  80%|████████  | 551/687 [03:30<01:08,  2.00it/s, loss=0.8132, acc=75.04%, coarse=9.97%]
Training:  80%|████████  | 552/687 [03:30<01:08,  1.98it/s, loss=0.8132, acc=75.04%, coarse=9.97%]
Training:  80%|████████  | 552/687 [03:31<01:08,  1.98it/s, loss=0.7028, acc=75.03%, coarse=9.97%]
Training:  80%|████████  | 553/687 [03:31<01:09,  1.93it/s, loss=0.7028, acc=75.03%, coarse=9.97%]
Training:  80%|████████  | 553/687 [03:31<01:09,  1.93it/s, loss=0.8187, acc=75.03%, coarse=9.97%]
Training:  81%|████████  | 554/687 [03:31<01:08,  1.93it/s, loss=0.8187, acc=75.03%, coarse=9.97%]
Training:  81%|████████  | 554/687 [03:32<01:08,  1.93it/s, loss=0.6720, acc=75.04%, coarse=9.97%]
Training:  81%|████████  | 555/687 [03:32<01:09,  1.91it/s, loss=0.6720, acc=75.04%, coarse=9.97%]
Training:  81%|████████  | 555/687 [03:32<01:09,  1.91it/s, loss=0.8242, acc=75.02%, coarse=9.97%]
Training:  81%|████████  | 556/687 [03:32<01:04,  2.05it/s, loss=0.8242, acc=75.02%, coarse=9.97%]
Training:  81%|████████  | 556/687 [03:33<01:04,  2.05it/s, loss=0.5121, acc=75.03%, coarse=9.97%]
Training:  81%|████████  | 557/687 [03:33<01:02,  2.08it/s, loss=0.5121, acc=75.03%, coarse=9.97%]
Training:  81%|████████  | 557/687 [03:33<01:02,  2.08it/s, loss=0.8394, acc=75.03%, coarse=9.97%]
Training:  81%|████████  | 558/687 [03:33<00:58,  2.20it/s, loss=0.8394, acc=75.03%, coarse=9.97%]
Training:  81%|████████  | 558/687 [03:34<00:58,  2.20it/s, loss=0.5402, acc=75.04%, coarse=9.97%]
Training:  81%|████████▏ | 559/687 [03:34<00:57,  2.22it/s, loss=0.5402, acc=75.04%, coarse=9.97%]
Training:  81%|████████▏ | 559/687 [03:34<00:57,  2.22it/s, loss=0.6964, acc=75.05%, coarse=9.97%]
Training:  82%|████████▏ | 560/687 [03:34<00:56,  2.26it/s, loss=0.6964, acc=75.05%, coarse=9.97%]
Training:  82%|████████▏ | 560/687 [03:34<00:56,  2.26it/s, loss=0.6121, acc=75.04%, coarse=9.97%]
Training:  82%|████████▏ | 561/687 [03:34<00:54,  2.33it/s, loss=0.6121, acc=75.04%, coarse=9.97%]
Training:  82%|████████▏ | 561/687 [03:35<00:54,  2.33it/s, loss=0.6370, acc=75.05%, coarse=9.97%]
Training:  82%|████████▏ | 562/687 [03:35<00:52,  2.39it/s, loss=0.6370, acc=75.05%, coarse=9.97%]
Training:  82%|████████▏ | 562/687 [03:35<00:52,  2.39it/s, loss=0.9026, acc=75.05%, coarse=9.97%]
Training:  82%|████████▏ | 563/687 [03:35<00:50,  2.45it/s, loss=0.9026, acc=75.05%, coarse=9.97%]
Training:  82%|████████▏ | 563/687 [03:35<00:50,  2.45it/s, loss=0.7859, acc=75.05%, coarse=9.97%]
Training:  82%|████████▏ | 564/687 [03:35<00:48,  2.55it/s, loss=0.7859, acc=75.05%, coarse=9.97%]
Training:  82%|████████▏ | 564/687 [03:36<00:48,  2.55it/s, loss=0.6259, acc=75.07%, coarse=9.97%]
Training:  82%|████████▏ | 565/687 [03:36<00:46,  2.61it/s, loss=0.6259, acc=75.07%, coarse=9.97%]
Training:  82%|████████▏ | 565/687 [03:36<00:46,  2.61it/s, loss=0.7222, acc=75.06%, coarse=9.97%]
Training:  82%|████████▏ | 566/687 [03:36<00:45,  2.67it/s, loss=0.7222, acc=75.06%, coarse=9.97%]
Training:  82%|████████▏ | 566/687 [03:37<00:45,  2.67it/s, loss=0.5992, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 567/687 [03:37<00:44,  2.72it/s, loss=0.5992, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 567/687 [03:37<00:44,  2.72it/s, loss=0.6041, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 568/687 [03:37<00:42,  2.77it/s, loss=0.6041, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 568/687 [03:37<00:42,  2.77it/s, loss=0.7557, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 569/687 [03:37<00:42,  2.79it/s, loss=0.7557, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 569/687 [03:38<00:42,  2.79it/s, loss=0.8357, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 570/687 [03:38<00:41,  2.80it/s, loss=0.8357, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 570/687 [03:38<00:41,  2.80it/s, loss=0.7849, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 571/687 [03:38<00:41,  2.81it/s, loss=0.7849, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 571/687 [03:38<00:41,  2.81it/s, loss=0.5708, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 572/687 [03:38<00:40,  2.82it/s, loss=0.5708, acc=75.07%, coarse=9.97%]
Training:  83%|████████▎ | 572/687 [03:39<00:40,  2.82it/s, loss=0.5531, acc=75.06%, coarse=9.97%]
Training:  83%|████████▎ | 573/687 [03:39<00:40,  2.85it/s, loss=0.5531, acc=75.06%, coarse=9.97%]
Training:  83%|████████▎ | 573/687 [03:39<00:40,  2.85it/s, loss=0.5393, acc=75.07%, coarse=9.97%]
Training:  84%|████████▎ | 574/687 [03:39<00:39,  2.87it/s, loss=0.5393, acc=75.07%, coarse=9.97%]
Training:  84%|████████▎ | 574/687 [03:39<00:39,  2.87it/s, loss=0.4917, acc=75.09%, coarse=9.97%]
Training:  84%|████████▎ | 575/687 [03:39<00:38,  2.87it/s, loss=0.4917, acc=75.09%, coarse=9.97%]
Training:  84%|████████▎ | 575/687 [03:40<00:38,  2.87it/s, loss=0.4984, acc=75.10%, coarse=9.97%]
Training:  84%|████████▍ | 576/687 [03:40<00:39,  2.79it/s, loss=0.4984, acc=75.10%, coarse=9.97%]
Training:  84%|████████▍ | 576/687 [03:40<00:39,  2.79it/s, loss=0.5577, acc=75.12%, coarse=9.98%]
Training:  84%|████████▍ | 577/687 [03:40<00:39,  2.81it/s, loss=0.5577, acc=75.12%, coarse=9.98%]
Training:  84%|████████▍ | 577/687 [03:40<00:39,  2.81it/s, loss=0.8299, acc=75.12%, coarse=9.98%]
Training:  84%|████████▍ | 578/687 [03:40<00:38,  2.82it/s, loss=0.8299, acc=75.12%, coarse=9.98%]
Training:  84%|████████▍ | 578/687 [03:41<00:38,  2.82it/s, loss=0.6331, acc=75.13%, coarse=9.98%]
Training:  84%|████████▍ | 579/687 [03:41<00:38,  2.84it/s, loss=0.6331, acc=75.13%, coarse=9.98%]
Training:  84%|████████▍ | 579/687 [03:41<00:38,  2.84it/s, loss=0.5658, acc=75.13%, coarse=9.98%]
Training:  84%|████████▍ | 580/687 [03:41<00:37,  2.83it/s, loss=0.5658, acc=75.13%, coarse=9.98%]
Training:  84%|████████▍ | 580/687 [03:41<00:37,  2.83it/s, loss=0.5091, acc=75.14%, coarse=9.98%]
Training:  85%|████████▍ | 581/687 [03:41<00:37,  2.84it/s, loss=0.5091, acc=75.14%, coarse=9.98%]
Training:  85%|████████▍ | 581/687 [03:42<00:37,  2.84it/s, loss=0.5936, acc=75.14%, coarse=9.98%]
Training:  85%|████████▍ | 582/687 [03:42<00:37,  2.83it/s, loss=0.5936, acc=75.14%, coarse=9.98%]
Training:  85%|████████▍ | 582/687 [03:42<00:37,  2.83it/s, loss=0.6967, acc=75.16%, coarse=9.98%]
Training:  85%|████████▍ | 583/687 [03:42<00:36,  2.84it/s, loss=0.6967, acc=75.16%, coarse=9.98%]
Training:  85%|████████▍ | 583/687 [03:43<00:36,  2.84it/s, loss=0.6414, acc=75.18%, coarse=9.98%]
Training:  85%|████████▌ | 584/687 [03:43<00:36,  2.79it/s, loss=0.6414, acc=75.18%, coarse=9.98%]
Training:  85%|████████▌ | 584/687 [03:43<00:36,  2.79it/s, loss=0.9551, acc=75.18%, coarse=9.98%]
Training:  85%|████████▌ | 585/687 [03:43<00:37,  2.74it/s, loss=0.9551, acc=75.18%, coarse=9.98%]
Training:  85%|████████▌ | 585/687 [03:43<00:37,  2.74it/s, loss=0.6570, acc=75.19%, coarse=9.98%]
Training:  85%|████████▌ | 586/687 [03:43<00:36,  2.76it/s, loss=0.6570, acc=75.19%, coarse=9.98%]
Training:  85%|████████▌ | 586/687 [03:44<00:36,  2.76it/s, loss=0.5908, acc=75.20%, coarse=9.98%]
Training:  85%|████████▌ | 587/687 [03:44<00:36,  2.71it/s, loss=0.5908, acc=75.20%, coarse=9.98%]
Training:  85%|████████▌ | 587/687 [03:44<00:36,  2.71it/s, loss=0.5593, acc=75.19%, coarse=9.98%]
Training:  86%|████████▌ | 588/687 [03:44<00:36,  2.69it/s, loss=0.5593, acc=75.19%, coarse=9.98%]
Training:  86%|████████▌ | 588/687 [03:44<00:36,  2.69it/s, loss=0.8695, acc=75.19%, coarse=9.98%]
Training:  86%|████████▌ | 589/687 [03:44<00:35,  2.76it/s, loss=0.8695, acc=75.19%, coarse=9.98%]
Training:  86%|████████▌ | 589/687 [03:45<00:35,  2.76it/s, loss=0.5704, acc=75.20%, coarse=9.98%]
Training:  86%|████████▌ | 590/687 [03:45<00:34,  2.79it/s, loss=0.5704, acc=75.20%, coarse=9.98%]
Training:  86%|████████▌ | 590/687 [03:45<00:34,  2.79it/s, loss=0.6472, acc=75.20%, coarse=9.98%]
Training:  86%|████████▌ | 591/687 [03:45<00:34,  2.81it/s, loss=0.6472, acc=75.20%, coarse=9.98%]
Training:  86%|████████▌ | 591/687 [03:45<00:34,  2.81it/s, loss=0.5290, acc=75.22%, coarse=9.98%]
Training:  86%|████████▌ | 592/687 [03:45<00:35,  2.68it/s, loss=0.5290, acc=75.22%, coarse=9.98%]
Training:  86%|████████▌ | 592/687 [03:46<00:35,  2.68it/s, loss=0.6277, acc=75.23%, coarse=9.98%]
Training:  86%|████████▋ | 593/687 [03:46<00:35,  2.64it/s, loss=0.6277, acc=75.23%, coarse=9.98%]
Training:  86%|████████▋ | 593/687 [03:46<00:35,  2.64it/s, loss=0.8179, acc=75.23%, coarse=9.98%]
Training:  86%|████████▋ | 594/687 [03:46<00:35,  2.63it/s, loss=0.8179, acc=75.23%, coarse=9.98%]
Training:  86%|████████▋ | 594/687 [03:47<00:35,  2.63it/s, loss=0.6134, acc=75.24%, coarse=9.98%]
Training:  87%|████████▋ | 595/687 [03:47<00:35,  2.62it/s, loss=0.6134, acc=75.24%, coarse=9.98%]
Training:  87%|████████▋ | 595/687 [03:47<00:35,  2.62it/s, loss=0.6272, acc=75.25%, coarse=9.98%]
Training:  87%|████████▋ | 596/687 [03:47<00:34,  2.63it/s, loss=0.6272, acc=75.25%, coarse=9.98%]
Training:  87%|████████▋ | 596/687 [03:47<00:34,  2.63it/s, loss=0.6853, acc=75.25%, coarse=9.98%]
Training:  87%|████████▋ | 597/687 [03:47<00:34,  2.62it/s, loss=0.6853, acc=75.25%, coarse=9.98%]
Training:  87%|████████▋ | 597/687 [03:48<00:34,  2.62it/s, loss=0.6472, acc=75.26%, coarse=9.98%]
Training:  87%|████████▋ | 598/687 [03:48<00:34,  2.59it/s, loss=0.6472, acc=75.26%, coarse=9.98%]
Training:  87%|████████▋ | 598/687 [03:48<00:34,  2.59it/s, loss=0.6096, acc=75.27%, coarse=9.99%]
Training:  87%|████████▋ | 599/687 [03:48<00:33,  2.65it/s, loss=0.6096, acc=75.27%, coarse=9.99%]
Training:  87%|████████▋ | 599/687 [03:49<00:33,  2.65it/s, loss=0.5749, acc=75.27%, coarse=9.99%]
Training:  87%|████████▋ | 600/687 [03:49<00:32,  2.70it/s, loss=0.5749, acc=75.27%, coarse=9.99%]
Training:  87%|████████▋ | 600/687 [03:49<00:32,  2.70it/s, loss=0.4892, acc=75.28%, coarse=9.99%]
Training:  87%|████████▋ | 601/687 [03:49<00:31,  2.74it/s, loss=0.4892, acc=75.28%, coarse=9.99%]
Training:  87%|████████▋ | 601/687 [03:49<00:31,  2.74it/s, loss=0.4371, acc=75.30%, coarse=9.99%]
Training:  88%|████████▊ | 602/687 [03:49<00:30,  2.76it/s, loss=0.4371, acc=75.30%, coarse=9.99%]
Training:  88%|████████▊ | 602/687 [03:50<00:30,  2.76it/s, loss=0.6165, acc=75.31%, coarse=9.99%]
Training:  88%|████████▊ | 603/687 [03:50<00:30,  2.75it/s, loss=0.6165, acc=75.31%, coarse=9.99%]
Training:  88%|████████▊ | 603/687 [03:50<00:30,  2.75it/s, loss=0.5494, acc=75.31%, coarse=9.99%]
Training:  88%|████████▊ | 604/687 [03:50<00:29,  2.80it/s, loss=0.5494, acc=75.31%, coarse=9.99%]
Training:  88%|████████▊ | 604/687 [03:50<00:29,  2.80it/s, loss=0.7487, acc=75.31%, coarse=9.99%]
Training:  88%|████████▊ | 605/687 [03:50<00:29,  2.79it/s, loss=0.7487, acc=75.31%, coarse=9.99%]
Training:  88%|████████▊ | 605/687 [03:51<00:29,  2.79it/s, loss=0.6281, acc=75.32%, coarse=9.99%]
Training:  88%|████████▊ | 606/687 [03:51<00:28,  2.81it/s, loss=0.6281, acc=75.32%, coarse=9.99%]
Training:  88%|████████▊ | 606/687 [03:51<00:28,  2.81it/s, loss=0.5553, acc=75.34%, coarse=9.99%]
Training:  88%|████████▊ | 607/687 [03:51<00:28,  2.82it/s, loss=0.5553, acc=75.34%, coarse=9.99%]
Training:  88%|████████▊ | 607/687 [03:51<00:28,  2.82it/s, loss=0.5584, acc=75.35%, coarse=9.99%]
Training:  89%|████████▊ | 608/687 [03:51<00:28,  2.74it/s, loss=0.5584, acc=75.35%, coarse=9.99%]
Training:  89%|████████▊ | 608/687 [03:52<00:28,  2.74it/s, loss=0.6842, acc=75.36%, coarse=9.99%]
Training:  89%|████████▊ | 609/687 [03:52<00:29,  2.61it/s, loss=0.6842, acc=75.36%, coarse=9.99%]
Training:  89%|████████▊ | 609/687 [03:52<00:29,  2.61it/s, loss=0.6934, acc=75.37%, coarse=9.99%]
Training:  89%|████████▉ | 610/687 [03:52<00:29,  2.60it/s, loss=0.6934, acc=75.37%, coarse=9.99%]
Training:  89%|████████▉ | 610/687 [03:53<00:29,  2.60it/s, loss=0.5718, acc=75.39%, coarse=10.00%]
Training:  89%|████████▉ | 611/687 [03:53<00:29,  2.60it/s, loss=0.5718, acc=75.39%, coarse=10.00%]
Training:  89%|████████▉ | 611/687 [03:53<00:29,  2.60it/s, loss=0.6051, acc=75.39%, coarse=10.00%]
Training:  89%|████████▉ | 612/687 [03:53<00:28,  2.66it/s, loss=0.6051, acc=75.39%, coarse=10.00%]
Training:  89%|████████▉ | 612/687 [03:53<00:28,  2.66it/s, loss=0.7206, acc=75.39%, coarse=10.00%]
Training:  89%|████████▉ | 613/687 [03:53<00:27,  2.69it/s, loss=0.7206, acc=75.39%, coarse=10.00%]
Training:  89%|████████▉ | 613/687 [03:54<00:27,  2.69it/s, loss=0.6137, acc=75.40%, coarse=10.00%]
Training:  89%|████████▉ | 614/687 [03:54<00:26,  2.72it/s, loss=0.6137, acc=75.40%, coarse=10.00%]
Training:  89%|████████▉ | 614/687 [03:54<00:26,  2.72it/s, loss=0.7124, acc=75.39%, coarse=10.00%]
Training:  90%|████████▉ | 615/687 [03:54<00:26,  2.71it/s, loss=0.7124, acc=75.39%, coarse=10.00%]
Training:  90%|████████▉ | 615/687 [03:54<00:26,  2.71it/s, loss=0.7007, acc=75.40%, coarse=10.00%]
Training:  90%|████████▉ | 616/687 [03:54<00:25,  2.74it/s, loss=0.7007, acc=75.40%, coarse=10.00%]
Training:  90%|████████▉ | 616/687 [03:55<00:25,  2.74it/s, loss=0.8553, acc=75.41%, coarse=10.00%]
Training:  90%|████████▉ | 617/687 [03:55<00:27,  2.54it/s, loss=0.8553, acc=75.41%, coarse=10.00%]
Training:  90%|████████▉ | 617/687 [03:55<00:27,  2.54it/s, loss=0.3883, acc=75.43%, coarse=10.00%]
Training:  90%|████████▉ | 618/687 [03:55<00:28,  2.43it/s, loss=0.3883, acc=75.43%, coarse=10.00%]
Training:  90%|████████▉ | 618/687 [03:56<00:28,  2.43it/s, loss=0.5883, acc=75.42%, coarse=10.00%]
Training:  90%|█████████ | 619/687 [03:56<00:28,  2.38it/s, loss=0.5883, acc=75.42%, coarse=10.00%]
Training:  90%|█████████ | 619/687 [03:56<00:28,  2.38it/s, loss=0.6044, acc=75.43%, coarse=10.00%]
Training:  90%|█████████ | 620/687 [03:56<00:28,  2.36it/s, loss=0.6044, acc=75.43%, coarse=10.00%]
Training:  90%|█████████ | 620/687 [03:57<00:28,  2.36it/s, loss=0.5732, acc=75.44%, coarse=10.00%]
Training:  90%|█████████ | 621/687 [03:57<00:28,  2.28it/s, loss=0.5732, acc=75.44%, coarse=10.00%]
Training:  90%|█████████ | 621/687 [03:57<00:28,  2.28it/s, loss=0.5331, acc=75.44%, coarse=10.00%]
Training:  91%|█████████ | 622/687 [03:57<00:26,  2.41it/s, loss=0.5331, acc=75.44%, coarse=10.00%]
Training:  91%|█████████ | 622/687 [03:57<00:26,  2.41it/s, loss=0.7583, acc=75.45%, coarse=10.00%]
Training:  91%|█████████ | 623/687 [03:57<00:26,  2.46it/s, loss=0.7583, acc=75.45%, coarse=10.00%]
Training:  91%|█████████ | 623/687 [03:58<00:26,  2.46it/s, loss=0.5402, acc=75.47%, coarse=10.00%]
Training:  91%|█████████ | 624/687 [03:58<00:24,  2.57it/s, loss=0.5402, acc=75.47%, coarse=10.00%]
Training:  91%|█████████ | 624/687 [03:58<00:24,  2.57it/s, loss=0.7867, acc=75.47%, coarse=10.01%]
Training:  91%|█████████ | 625/687 [03:58<00:23,  2.64it/s, loss=0.7867, acc=75.47%, coarse=10.01%]
Training:  91%|█████████ | 625/687 [03:58<00:23,  2.64it/s, loss=0.6634, acc=75.46%, coarse=10.01%]
Training:  91%|█████████ | 626/687 [03:58<00:22,  2.71it/s, loss=0.6634, acc=75.46%, coarse=10.01%]
Training:  91%|█████████ | 626/687 [03:59<00:22,  2.71it/s, loss=0.6722, acc=75.47%, coarse=10.01%]
Training:  91%|█████████▏| 627/687 [03:59<00:21,  2.74it/s, loss=0.6722, acc=75.47%, coarse=10.01%]
Training:  91%|█████████▏| 627/687 [03:59<00:21,  2.74it/s, loss=0.7867, acc=75.48%, coarse=10.01%]
Training:  91%|█████████▏| 628/687 [03:59<00:21,  2.71it/s, loss=0.7867, acc=75.48%, coarse=10.01%]
Training:  91%|█████████▏| 628/687 [04:00<00:21,  2.71it/s, loss=0.6853, acc=75.47%, coarse=10.01%]
Training:  92%|█████████▏| 629/687 [04:00<00:21,  2.65it/s, loss=0.6853, acc=75.47%, coarse=10.01%]
Training:  92%|█████████▏| 629/687 [04:00<00:21,  2.65it/s, loss=0.6916, acc=75.48%, coarse=10.01%]
Training:  92%|█████████▏| 630/687 [04:00<00:21,  2.67it/s, loss=0.6916, acc=75.48%, coarse=10.01%]
Training:  92%|█████████▏| 630/687 [04:00<00:21,  2.67it/s, loss=0.5573, acc=75.49%, coarse=10.01%]
Training:  92%|█████████▏| 631/687 [04:00<00:20,  2.71it/s, loss=0.5573, acc=75.49%, coarse=10.01%]
Training:  92%|█████████▏| 631/687 [04:01<00:20,  2.71it/s, loss=0.7919, acc=75.50%, coarse=10.01%]
Training:  92%|█████████▏| 632/687 [04:01<00:19,  2.76it/s, loss=0.7919, acc=75.50%, coarse=10.01%]
Training:  92%|█████████▏| 632/687 [04:01<00:19,  2.76it/s, loss=0.5767, acc=75.51%, coarse=10.01%]
Training:  92%|█████████▏| 633/687 [04:01<00:19,  2.81it/s, loss=0.5767, acc=75.51%, coarse=10.01%]
Training:  92%|█████████▏| 633/687 [04:01<00:19,  2.81it/s, loss=0.8219, acc=75.51%, coarse=10.01%]
Training:  92%|█████████▏| 634/687 [04:01<00:18,  2.84it/s, loss=0.8219, acc=75.51%, coarse=10.01%]
Training:  92%|█████████▏| 634/687 [04:02<00:18,  2.84it/s, loss=0.7217, acc=75.50%, coarse=10.01%]
Training:  92%|█████████▏| 635/687 [04:02<00:18,  2.85it/s, loss=0.7217, acc=75.50%, coarse=10.01%]
Training:  92%|█████████▏| 635/687 [04:02<00:18,  2.85it/s, loss=0.6309, acc=75.52%, coarse=10.01%]
Training:  93%|█████████▎| 636/687 [04:02<00:17,  2.87it/s, loss=0.6309, acc=75.52%, coarse=10.01%]
Training:  93%|█████████▎| 636/687 [04:02<00:17,  2.87it/s, loss=1.0572, acc=75.50%, coarse=10.01%]
Training:  93%|█████████▎| 637/687 [04:02<00:17,  2.87it/s, loss=1.0572, acc=75.50%, coarse=10.01%]
Training:  93%|█████████▎| 637/687 [04:03<00:17,  2.87it/s, loss=0.7375, acc=75.49%, coarse=10.01%]
Training:  93%|█████████▎| 638/687 [04:03<00:17,  2.88it/s, loss=0.7375, acc=75.49%, coarse=10.01%]
Training:  93%|█████████▎| 638/687 [04:03<00:17,  2.88it/s, loss=0.6725, acc=75.49%, coarse=10.01%]
Training:  93%|█████████▎| 639/687 [04:03<00:16,  2.88it/s, loss=0.6725, acc=75.49%, coarse=10.01%]
Training:  93%|█████████▎| 639/687 [04:03<00:16,  2.88it/s, loss=0.6815, acc=75.48%, coarse=10.01%]
Training:  93%|█████████▎| 640/687 [04:03<00:16,  2.78it/s, loss=0.6815, acc=75.48%, coarse=10.01%]
Training:  93%|█████████▎| 640/687 [04:04<00:16,  2.78it/s, loss=0.5505, acc=75.49%, coarse=10.01%]
Training:  93%|█████████▎| 641/687 [04:04<00:16,  2.72it/s, loss=0.5505, acc=75.49%, coarse=10.01%]
Training:  93%|█████████▎| 641/687 [04:04<00:16,  2.72it/s, loss=0.5409, acc=75.50%, coarse=10.01%]
Training:  93%|█████████▎| 642/687 [04:04<00:16,  2.71it/s, loss=0.5409, acc=75.50%, coarse=10.01%]
Training:  93%|█████████▎| 642/687 [04:05<00:16,  2.71it/s, loss=0.6423, acc=75.50%, coarse=10.01%]
Training:  94%|█████████▎| 643/687 [04:05<00:15,  2.75it/s, loss=0.6423, acc=75.50%, coarse=10.01%]
Training:  94%|█████████▎| 643/687 [04:05<00:15,  2.75it/s, loss=0.6349, acc=75.51%, coarse=10.01%]
Training:  94%|█████████▎| 644/687 [04:05<00:15,  2.79it/s, loss=0.6349, acc=75.51%, coarse=10.01%]
Training:  94%|█████████▎| 644/687 [04:05<00:15,  2.79it/s, loss=0.6469, acc=75.52%, coarse=10.01%]
Training:  94%|█████████▍| 645/687 [04:05<00:14,  2.81it/s, loss=0.6469, acc=75.52%, coarse=10.01%]
Training:  94%|█████████▍| 645/687 [04:06<00:14,  2.81it/s, loss=0.5617, acc=75.52%, coarse=10.01%]
Training:  94%|█████████▍| 646/687 [04:06<00:14,  2.74it/s, loss=0.5617, acc=75.52%, coarse=10.01%]
Training:  94%|█████████▍| 646/687 [04:06<00:14,  2.74it/s, loss=0.8805, acc=75.52%, coarse=10.01%]
Training:  94%|█████████▍| 647/687 [04:06<00:15,  2.67it/s, loss=0.8805, acc=75.52%, coarse=10.01%]