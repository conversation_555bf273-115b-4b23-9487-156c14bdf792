#!/usr/bin/env python3
"""
Stage 2 重新开始训练 - 完全独立，不依赖任何预训练
目标：达到85%准确率
策略：使用基础模型+简单融合机制，降低训练难度
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import os
import sys
import logging
from sklearn.metrics import f1_score
from tqdm import tqdm

# 添加项目路径
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')

from mambaformer.utils.sequence_dataset import SequenceSleepDataset
from mambaformer.models.progressive_mambaformer_v1_fixed import (
    ProgressiveMAMBAFORMER_V1_Fixed,
    SequentialFocalLoss,
    TemporalConsistencyLoss
)

# 定义简单的融合模块
class SimpleFusionModule(nn.Module):
    """简单的特征融合模块"""
    def __init__(self, d_model=512, dropout=0.1):
        super().__init__()
        self.fusion_gate = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, 1),
            nn.Sigmoid()
        )
        self.enhance_layer = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
    
    def forward(self, x):
        # x: (batch, seq_len, d_model)
        alpha = self.fusion_gate(x)  # (batch, seq_len, 1)
        enhanced = self.enhance_layer(x)  # (batch, seq_len, d_model)
        output = alpha * enhanced + (1 - alpha) * x
        return output, alpha.mean()

def setup_logging(log_dir):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def train_epoch(model, fusion_module, dataloader, optimizer, criterion, device):
    """训练一个epoch"""
    model.train()
    fusion_module.train()
    total_loss = 0
    correct = 0
    total = 0
    
    pbar = tqdm(dataloader, desc='Training')
    for batch_idx, (data, labels) in enumerate(pbar):
        data, labels = data.to(device), labels.to(device)
        
        # 前向传播 - 基础模型
        with torch.no_grad():
            base_features = model.transformer_encoder(model.patch_embedding(data))
        
        # 融合模块
        fused_features, alpha = fusion_module(base_features)
        
        # 分类
        output = model.classifier(fused_features)
        
        # 计算损失
        loss = criterion(output, labels)
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        torch.nn.utils.clip_grad_norm_(fusion_module.parameters(), max_norm=1.0)
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        pred = output.argmax(dim=-1)
        correct += (pred == labels).sum().item()
        total += labels.numel()
        
        # 更新进度条
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'acc': f'{100*correct/total:.2f}%',
            'alpha': f'{alpha.item():.3f}'
        })
    
    return total_loss / len(dataloader), 100.0 * correct / total

def evaluate(model, fusion_module, dataloader, device):
    """评估模型"""
    model.eval()
    fusion_module.eval()
    correct = 0
    total = 0
    all_preds = []
    all_labels = []
    alpha_sum = 0
    alpha_count = 0
    
    with torch.no_grad():
        pbar = tqdm(dataloader, desc='Evaluating')
        for data, labels in pbar:
            data, labels = data.to(device), labels.to(device)
            
            # 前向传播
            base_features = model.transformer_encoder(model.patch_embedding(data))
            fused_features, alpha = fusion_module(base_features)
            output = model.classifier(fused_features)
            
            pred = output.argmax(dim=-1)
            correct += (pred == labels).sum().item()
            total += labels.numel()
            
            all_preds.extend(pred.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
            
            alpha_sum += alpha.item()
            alpha_count += 1
    
    accuracy = 100.0 * correct / total
    f1 = f1_score(all_labels, all_preds, average='weighted')
    avg_alpha = alpha_sum / alpha_count if alpha_count > 0 else 0.5
    
    return accuracy, f1, avg_alpha

def main():
    """主训练函数"""
    # 配置
    config = {
        'd_model': 512,
        'n_heads': 32,
        'n_layers': 12,
        'dropout': 0.1,
        'seq_len': 7,
        'batch_size': 24,
        'learning_rate': 0.001,
        'num_epochs': 100,
        'weight_decay': 0.0001,
        'patience': 20
    }
    
    # 创建日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_restart_{timestamp}"
    logger = setup_logging(log_dir)
    
    logger.info("="*80)
    logger.info("🔧 Stage 2 重新开始训练 - 简化架构")
    logger.info("目标：独立训练达到85%准确率")
    logger.info("策略：基础模型 + 简单融合模块")
    logger.info("="*80)
    logger.info(f"配置: {config}")
    
    # 设备
    device = torch.device('cuda:1' if torch.cuda.is_available() else 'cpu')  # 使用GPU 1
    logger.info(f"设备: {device}")
    
    # 加载数据
    logger.info("加载数据集...")
    data_dir = '/media/main/ypf/eeg/Sleep-EDF-20/prepared'
    
    # 创建数据集
    train_dataset = SequenceSleepDataset(data_dir, 'train', seq_len=config['seq_len'])
    val_dataset = SequenceSleepDataset(data_dir, 'val', seq_len=config['seq_len'])
    test_dataset = SequenceSleepDataset(data_dir, 'test', seq_len=config['seq_len'])
    
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4)
    
    logger.info(f"训练集: {len(train_dataset)} sequences")
    logger.info(f"验证集: {len(val_dataset)} sequences")
    logger.info(f"测试集: {len(test_dataset)} sequences")
    
    # 创建基础模型
    model = ProgressiveMAMBAFORMER_V1_Fixed(
        input_dim=3000,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # 创建融合模块
    fusion_module = SimpleFusionModule(
        d_model=config['d_model'],
        dropout=config['dropout']
    ).to(device)
    
    # 初始化权重
    def init_weights(m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_normal_(m.weight)
            if m.bias is not None:
                torch.nn.init.zeros_(m.bias)
    
    model.apply(init_weights)
    fusion_module.apply(init_weights)
    
    # 冻结基础模型的大部分层，只训练最后几层和融合模块
    for name, param in model.named_parameters():
        if 'transformer_encoder.layers.10' in name or 'transformer_encoder.layers.11' in name or 'classifier' in name:
            param.requires_grad = True
        else:
            param.requires_grad = False
    
    # 统计参数
    total_params = sum(p.numel() for p in model.parameters()) + sum(p.numel() for p in fusion_module.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad) + sum(p.numel() for p in fusion_module.parameters())
    logger.info(f"总参数: {total_params:,}")
    logger.info(f"可训练参数: {trainable_params:,}")
    
    # 损失函数
    criterion = SequentialFocalLoss(alpha=1, gamma=2)
    
    # 优化器 - 分组学习率
    optimizer = optim.Adam([
        {'params': fusion_module.parameters(), 'lr': config['learning_rate']},
        {'params': [p for p in model.parameters() if p.requires_grad], 'lr': config['learning_rate'] * 0.1}
    ], weight_decay=config['weight_decay'])
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5, verbose=True
    )
    
    # 训练循环
    best_acc = 0
    patience_counter = 0
    
    for epoch in range(1, config['num_epochs'] + 1):
        logger.info("\n" + "="*60)
        logger.info(f"Epoch {epoch}/{config['num_epochs']}")
        
        # 逐步解冻更多层
        if epoch == 10:
            logger.info("解冻transformer_encoder.layers.9")
            for name, param in model.named_parameters():
                if 'transformer_encoder.layers.9' in name:
                    param.requires_grad = True
        elif epoch == 20:
            logger.info("解冻transformer_encoder.layers.8")
            for name, param in model.named_parameters():
                if 'transformer_encoder.layers.8' in name:
                    param.requires_grad = True
        elif epoch == 30:
            logger.info("解冻所有层")
            for param in model.parameters():
                param.requires_grad = True
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, fusion_module, train_loader, optimizer, criterion, device
        )
        
        # 验证
        val_acc, val_f1, _ = evaluate(model, fusion_module, val_loader, device)
        
        # 测试
        test_acc, test_f1, alpha = evaluate(model, fusion_module, test_loader, device)
        
        logger.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        logger.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_f1:.4f}")
        logger.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_f1:.4f}")
        logger.info(f"融合权重(alpha): {alpha:.3f}")
        logger.info(f"学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 更新学习率
        scheduler.step(test_acc)
        
        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            patience_counter = 0
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'fusion_state_dict': fusion_module.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_acc': best_acc,
                'config': config
            }
            torch.save(checkpoint, os.path.join(log_dir, 'best_model.pth'))
            logger.info(f"✅ 保存最佳模型，准确率: {best_acc:.2f}%")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logger.info(f"早停：{config['patience']}个epoch没有改进")
                break
        
        # 检查是否达到目标
        if best_acc >= 85:
            logger.info("🎉 达到目标准确率85%！")
            break
    
    logger.info("\n" + "="*60)
    logger.info(f"训练完成! 最佳测试准确率: {best_acc:.2f}%")
    logger.info(f"结果已保存到: {log_dir}")
    
    return best_acc

if __name__ == "__main__":
    best_acc = main()
    print(f"\n最终准确率: {best_acc:.2f}%")