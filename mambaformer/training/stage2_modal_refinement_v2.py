#!/usr/bin/env python3
"""
Stage 2: 模态内特征精炼 (简化版)
- EEG: 局部注意力
- EOG/EMG: 轻量级处理
独立训练，不加载Stage 1权重
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

import numpy as np
import os
import glob
import logging
from datetime import datetime
from pathlib import Path
import json
from tqdm import tqdm

# 导入必要的模块
import sys
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')
from models.sequential_mambaformer_v2 import PositionalEncoding
from utils.train_utils import EarlyStopping

# ===================== 数据集类 =====================

class SimpleSleepDataset(torch.utils.data.Dataset):
    """简化的睡眠数据集"""
    def __init__(self, file_list):
        self.samples = []
        self.labels = []
        
        for file_path in file_list:
            data = np.load(file_path)
            epochs = data['x']  # (n_epochs, 3000, 4)
            labels = data['y']  # (n_epochs,)
            
            # 简单的下采样到固定长度
            for i in range(len(epochs)):
                # 下采样到300个时间点
                downsampled = epochs[i][::10, :]  # (300, 4)
                self.samples.append(downsampled)
                self.labels.append(labels[i])
        
        self.samples = np.array(self.samples, dtype=np.float32)
        self.labels = np.array(self.labels, dtype=np.int64)
        
        print(f"加载了 {len(self.samples)} 个样本")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        return torch.FloatTensor(self.samples[idx]), torch.LongTensor([self.labels[idx]]).squeeze()

# ===================== Stage 2 简化模型 =====================

class Stage2SimplifiedModel(nn.Module):
    """Stage 2: 简化的模态精炼模型"""
    def __init__(self, n_classes=5, d_model=128, n_heads=4, dropout=0.15):
        super().__init__()
        
        # CNN特征提取
        self.conv1 = nn.Conv1d(4, 64, kernel_size=5, stride=2, padding=2)
        self.bn1 = nn.BatchNorm1d(64)
        self.conv2 = nn.Conv1d(64, 128, kernel_size=5, stride=2, padding=2)
        self.bn2 = nn.BatchNorm1d(128)
        self.conv3 = nn.Conv1d(128, d_model, kernel_size=5, stride=2, padding=2)
        self.bn3 = nn.BatchNorm1d(d_model)
        
        self.dropout = nn.Dropout(dropout)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model*4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=3)
        
        # 分类头
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, n_classes)
        )
        
        # 渐进式融合权重
        self.alpha = nn.Parameter(torch.tensor(0.2))
        
    def forward(self, x):
        """
        x: (batch, time_steps, channels)
        """
        # 转换维度用于CNN
        x = x.transpose(1, 2)  # (batch, channels, time_steps)
        
        # CNN特征提取
        x = F.gelu(self.bn1(self.conv1(x)))
        x = self.dropout(x)
        
        x = F.gelu(self.bn2(self.conv2(x)))
        x = self.dropout(x)
        
        x = F.gelu(self.bn3(self.conv3(x)))
        x = self.dropout(x)
        
        # 转换回序列格式
        x = x.transpose(1, 2)  # (batch, seq_len, d_model)
        
        # Transformer处理
        x = self.transformer(x)
        
        # 全局池化
        x = x.mean(dim=1)  # (batch, d_model)
        
        # 分类
        logits = self.classifier(x)
        
        return logits, self.alpha

# ===================== 训练脚本 =====================

def train_stage2_simplified():
    # 设置日志
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = Path(f'/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_simplified_{timestamp}')
    log_dir.mkdir(parents=True, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'training.log'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("="*80)
    logger.info("🎯 Stage 2: 模态内特征精炼 (简化版)")
    logger.info("目标: ≥86% accuracy")
    logger.info("="*80)
    
    # 配置
    config = {
        'n_classes': 5,
        'd_model': 128,
        'n_heads': 4,
        'dropout': 0.15,
        'batch_size': 64,
        'learning_rate': 0.001,
        'num_epochs': 50,
        'weight_decay': 0.0001,
        'patience': 10,
    }
    
    logger.info(f"配置: {config}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"设备: {device}")
    
    # 加载数据
    logger.info("加载数据集...")
    
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 划分数据集
    n_files = len(all_files)
    n_train = int(0.7 * n_files)
    n_val = int(0.15 * n_files)
    
    train_files = all_files[:n_train]
    val_files = all_files[n_train:n_train+n_val]
    test_files = all_files[n_train+n_val:]
    
    train_dataset = SimpleSleepDataset(train_files)
    val_dataset = SimpleSleepDataset(val_files)
    test_dataset = SimpleSleepDataset(test_files)
    
    logger.info(f"训练集: {len(train_dataset)} samples")
    logger.info(f"验证集: {len(val_dataset)} samples")
    logger.info(f"测试集: {len(test_dataset)} samples")
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # 创建模型
    model = Stage2SimplifiedModel(
        n_classes=config['n_classes'],
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        dropout=config['dropout']
    ).to(device)
    
    # 统计参数量
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"创建Stage2SimplifiedModel: 参数量={total_params:,}")
    
    # 优化器和调度器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=10,
        T_mult=2,
        eta_min=1e-6
    )
    
    criterion = nn.CrossEntropyLoss()
    early_stopping = EarlyStopping(patience=config['patience'], delta=0.001)
    
    best_acc = 0
    best_model_path = log_dir / 'best_model.pth'
    
    # 训练循环
    for epoch in range(config['num_epochs']):
        logger.info("\n" + "="*60)
        logger.info(f"Epoch {epoch+1}/{config['num_epochs']}")
        
        # 训练阶段
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0
        
        pbar = tqdm(train_loader, desc='Training')
        for batch_idx, (data, target) in enumerate(pbar):
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            logits, alpha = model(data)
            
            # 计算损失
            loss = criterion(logits, target)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 统计
            train_loss += loss.item()
            _, predicted = logits.max(1)
            train_total += target.size(0)
            train_correct += predicted.eq(target).sum().item()
            
            # 更新进度条
            current_acc = 100. * train_correct / train_total
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{current_acc:.2f}%',
                'alpha': f'{alpha.item():.4f}'
            })
        
        train_acc = 100. * train_correct / train_total
        avg_train_loss = train_loss / len(train_loader)
        
        # 验证阶段
        model.eval()
        val_correct = 0
        val_total = 0
        val_loss = 0
        
        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(device), target.to(device)
                logits, _ = model(data)
                loss = criterion(logits, target)
                val_loss += loss.item()
                _, predicted = logits.max(1)
                val_total += target.size(0)
                val_correct += predicted.eq(target).sum().item()
        
        val_acc = 100. * val_correct / val_total
        avg_val_loss = val_loss / len(val_loader)
        
        # 测试阶段
        test_correct = 0
        test_total = 0
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(device), target.to(device)
                logits, _ = model(data)
                _, predicted = logits.max(1)
                test_total += target.size(0)
                test_correct += predicted.eq(target).sum().item()
        
        test_acc = 100. * test_correct / test_total
        
        # 记录日志
        logger.info(f"训练 - Loss: {avg_train_loss:.4f}, Acc: {train_acc:.2f}%")
        logger.info(f"  Alpha值: {alpha.item():.4f}")
        logger.info(f"验证 - Loss: {avg_val_loss:.4f}, Acc: {val_acc:.2f}%")
        logger.info(f"测试 - Acc: {test_acc:.2f}%")
        logger.info(f"学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'config': config
            }, best_model_path)
            logger.info(f"✅ 保存最佳模型，准确率: {test_acc:.2f}%")
            
            if test_acc >= 86.0:
                logger.info("🎉 达到目标准确率 86%!")
        
        # Early stopping
        early_stopping(avg_val_loss)
        if early_stopping.early_stop:
            logger.info("Early stopping triggered")
            break
        
        scheduler.step()
    
    # 训练结束
    logger.info("\n" + "="*80)
    logger.info("训练完成！")
    logger.info(f"最佳测试准确率: {best_acc:.2f}%")
    
    if best_acc >= 86.0:
        logger.info("✅ Stage 2 成功达到目标!")
    else:
        logger.info(f"❌ Stage 2 未达到目标 (当前: {best_acc:.2f}%, 目标: 86%)")

if __name__ == "__main__":
    train_stage2_simplified()