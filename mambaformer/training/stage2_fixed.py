#!/usr/bin/env python3
"""
Stage 2 修复版独立训练 - 解决准确率停滞问题
使用全新训练策略，不加载任何预训练权重
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import os
import sys
import logging
from sklearn.metrics import f1_score

# 添加项目路径
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')

from mambaformer.models.progressive_mambaformer_v1_fixed import (
    ProgressiveMAMBAFORMER_V1_Fixed,
    LightweightCrossModalAttention
)
from mambaformer.data.dataset import SleepDataset, create_datasets
from mambaformer.models.losses import SequentialFocalLoss, TemporalConsistencyLoss

def setup_logging(log_dir):
    """设置日志"""
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def train_epoch(model, dataloader, optimizer, criterion_main, criterion_aux, criterion_temporal, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    for batch_idx, (data, labels) in enumerate(dataloader):
        data, labels = data.to(device), labels.to(device)
        
        # 前向传播
        output, aux_output, alpha = model(data, return_aux=True, return_alpha=True)
        
        # 计算损失
        loss_main = criterion_main(output, labels)
        loss_aux = criterion_aux(aux_output, labels)
        loss_temporal = criterion_temporal(output)
        
        # 组合损失
        loss = loss_main + 0.3 * loss_aux + 0.1 * loss_temporal
        
        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        pred = output.argmax(dim=-1)
        correct += (pred == labels).sum().item()
        total += labels.numel()
        
        if batch_idx % 50 == 0:
            print(f'  Batch {batch_idx}/{len(dataloader)}, Loss: {loss.item():.4f}')
    
    return total_loss / len(dataloader), 100.0 * correct / total

def evaluate(model, dataloader, device):
    """评估模型"""
    model.eval()
    correct = 0
    total = 0
    all_preds = []
    all_labels = []
    alpha_sum = 0
    alpha_count = 0
    
    with torch.no_grad():
        for data, labels in dataloader:
            data, labels = data.to(device), labels.to(device)
            output, _, alpha = model(data, return_aux=True, return_alpha=True)
            
            pred = output.argmax(dim=-1)
            correct += (pred == labels).sum().item()
            total += labels.numel()
            
            all_preds.extend(pred.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
            
            alpha_sum += alpha.sum().item()
            alpha_count += alpha.numel()
    
    accuracy = 100.0 * correct / total
    f1 = f1_score(all_labels, all_preds, average='weighted')
    avg_alpha = alpha_sum / alpha_count if alpha_count > 0 else 0.5
    
    return accuracy, f1, avg_alpha

def main():
    """主训练函数"""
    # 配置
    config = {
        'd_model': 512,
        'n_heads': 32,
        'n_layers': 12,
        'dropout': 0.1,  # 降低dropout
        'seq_len': 7,
        'attention_heads': 4,
        'batch_size': 24,  # 调整batch size
        'learning_rate': 0.0005,  # 调整学习率
        'num_epochs': 100,  # 更多训练epoch
        'weight_decay': 0.0001,
        'patience': 20
    }
    
    # 创建日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_fixed_{timestamp}"
    logger = setup_logging(log_dir)
    
    logger.info("="*80)
    logger.info("🔧 Stage 2 修复版独立训练 - 全新策略")
    logger.info("目标：从零开始训练达到85%以上准确率")
    logger.info("策略：优化的超参数、更长训练时间")
    logger.info("="*80)
    logger.info(f"配置: {config}")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"设备: {device}")
    
    # 加载数据
    logger.info("加载数据集...")
    data_dir = '/media/main/ypf/eeg/Sleep-EDF-20/prepared'
    train_dataset, val_dataset, test_dataset = create_datasets(data_dir, seq_len=config['seq_len'])
    
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4)
    
    logger.info(f"训练集: {len(train_dataset)} sequences")
    logger.info(f"验证集: {len(val_dataset)} sequences")
    logger.info(f"测试集: {len(test_dataset)} sequences")
    
    # 创建模型 - 完全从零开始
    model = ProgressiveMAMBAFORMER_V1_Fixed(
        input_dim=3000,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # Stage 2: 添加轻量级跨模态注意力
    model.cross_modal_attention = LightweightCrossModalAttention(
        d_model=config['d_model'],
        n_heads=config['attention_heads'],
        dropout=config['dropout']
    ).to(device)
    
    # Xavier初始化
    def init_weights(m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_normal_(m.weight)
            if m.bias is not None:
                torch.nn.init.zeros_(m.bias)
    
    model.apply(init_weights)
    
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logger.info(f"总参数: {total_params:,}")
    logger.info(f"可训练参数: {trainable_params:,}")
    
    # 损失函数
    criterion_main = SequentialFocalLoss(alpha=1, gamma=2)
    criterion_aux = SequentialFocalLoss(alpha=0.5, gamma=1.5)
    criterion_temporal = TemporalConsistencyLoss(weight=0.1)
    
    # 优化器
    optimizer = optim.Adam(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度器 - CosineAnnealingWarmRestarts
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, 
        T_0=20,  # 第一个周期的epoch数
        T_mult=2,  # 每个周期的增长倍数
        eta_min=1e-6
    )
    
    # 训练循环
    best_acc = 0
    patience_counter = 0
    
    for epoch in range(1, config['num_epochs'] + 1):
        logger.info("\n" + "="*60)
        logger.info(f"Epoch {epoch}/{config['num_epochs']}")
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, optimizer, criterion_main, criterion_aux, 
            criterion_temporal, device
        )
        
        # 验证
        val_acc, val_f1, _ = evaluate(model, val_loader, device)
        
        # 测试
        test_acc, test_f1, alpha = evaluate(model, test_loader, device)
        
        logger.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        logger.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_f1:.4f}")
        logger.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_f1:.4f}")
        logger.info(f"融合权重(alpha): {alpha:.3f}")
        logger.info(f"学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 更新学习率
        scheduler.step()
        
        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_acc': best_acc,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            logger.info(f"✅ 保存最佳模型，准确率: {best_acc:.2f}%")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logger.info(f"早停：{config['patience']}个epoch没有改进")
                break
        
        # 检查是否达到目标
        if best_acc >= 85:
            logger.info("🎉 达到目标准确率85%！")
            break
    
    logger.info("\n" + "="*60)
    logger.info(f"训练完成! 最佳测试准确率: {best_acc:.2f}%")
    logger.info(f"结果已保存到: {log_dir}")

if __name__ == "__main__":
    main()