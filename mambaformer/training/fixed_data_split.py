#!/usr/bin/env python3
"""
修复数据泄露问题的数据划分工具
确保同一受试者的所有数据都在同一个集合中
"""

import os
import glob
import random
from collections import defaultdict

def get_subject_id(filename):
    """从文件名提取受试者ID
    SC4XXYZZZ.npz -> XX (两位数字)
    """
    basename = os.path.basename(filename)
    # SC4后面的两位数字是受试者ID
    subject_id = basename[3:5]
    return subject_id

def get_subject_based_split(data_dir, train_ratio=0.7, val_ratio=0.15, seed=42):
    """
    按受试者划分数据集，避免数据泄露
    
    Args:
        data_dir: 数据目录路径
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        seed: 随机种子
    
    Returns:
        train_files, val_files, test_files
    """
    random.seed(seed)
    
    # 获取所有文件
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 按受试者分组
    subject_files = defaultdict(list)
    for file in all_files:
        subject_id = get_subject_id(file)
        subject_files[subject_id].append(file)
    
    # 获取所有受试者ID
    subjects = list(subject_files.keys())
    random.shuffle(subjects)
    
    # 计算划分
    n_subjects = len(subjects)
    n_train = int(n_subjects * train_ratio)
    n_val = int(n_subjects * val_ratio)
    
    # 划分受试者
    train_subjects = subjects[:n_train]
    val_subjects = subjects[n_train:n_train+n_val]
    test_subjects = subjects[n_train+n_val:]
    
    # 收集文件
    train_files = []
    val_files = []
    test_files = []
    
    for subject in train_subjects:
        train_files.extend(subject_files[subject])
    for subject in val_subjects:
        val_files.extend(subject_files[subject])
    for subject in test_subjects:
        test_files.extend(subject_files[subject])
    
    # 排序以确保一致性
    train_files.sort()
    val_files.sort()
    test_files.sort()
    
    print(f"数据划分统计:")
    print(f"  总受试者数: {n_subjects}")
    print(f"  训练集: {len(train_subjects)}个受试者, {len(train_files)}个文件")
    print(f"  验证集: {len(val_subjects)}个受试者, {len(val_files)}个文件")
    print(f"  测试集: {len(test_subjects)}个受试者, {len(test_files)}个文件")
    print(f"训练集受试者: {sorted(train_subjects)}")
    print(f"验证集受试者: {sorted(val_subjects)}")
    print(f"测试集受试者: {sorted(test_subjects)}")
    
    # 验证无重叠
    assert len(set(train_subjects) & set(val_subjects)) == 0, "训练集和验证集有重叠！"
    assert len(set(train_subjects) & set(test_subjects)) == 0, "训练集和测试集有重叠！"
    assert len(set(val_subjects) & set(test_subjects)) == 0, "验证集和测试集有重叠！"
    print("✅ 验证通过：各数据集之间无受试者重叠")
    
    return train_files, val_files, test_files

if __name__ == "__main__":
    # 测试
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    train_files, val_files, test_files = get_subject_based_split(data_dir)
    
    print(f"\n文件示例:")
    print(f"训练集前3个文件: {[os.path.basename(f) for f in train_files[:3]]}")
    print(f"验证集前3个文件: {[os.path.basename(f) for f in val_files[:3]]}")
    print(f"测试集前3个文件: {[os.path.basename(f) for f in test_files[:3]]}")