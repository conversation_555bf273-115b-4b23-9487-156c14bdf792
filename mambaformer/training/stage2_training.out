2025-08-16 18:55:50,055 - INFO - ================================================================================
2025-08-16 18:55:50,055 - INFO - 🎯 Stage 2: 模态内特征精炼 - 独立训练
2025-08-16 18:55:50,055 - INFO - 核心策略：
2025-08-16 18:55:50,055 - INFO - - EEG: 局部注意力（window_size=50）
2025-08-16 18:55:50,055 - INFO - - EOG/EMG: 轻量级Mamba（d_state=8）
2025-08-16 18:55:50,055 - INFO - 目标: ≥86% accuracy
2025-08-16 18:55:50,055 - INFO - ================================================================================
2025-08-16 18:55:50,055 - INFO - 配置: {'n_classes': 5, 'd_model': 128, 'n_heads': 4, 'n_layers': 3, 'dropout': 0.15, 'seq_len': 5, 'batch_size': 32, 'learning_rate': 0.001, 'num_epochs': 100, 'weight_decay': 0.0001, 'patience': 15, 'aux_weight': 0.2}
2025-08-16 18:55:50,082 - INFO - 设备: cuda
2025-08-16 18:55:50,082 - INFO - 加载数据集...
2025-08-16 18:55:50,082 - INFO - 从 0 个文件加载了 0 个epochs, 创建了 0 个序列
2025-08-16 18:55:50,083 - INFO - 创建序列数据集: 0个序列, 序列长度=5, 通道数=3, 总epochs=0
2025-08-16 18:55:50,083 - INFO - 从 0 个文件加载了 0 个epochs, 创建了 0 个序列
2025-08-16 18:55:50,083 - INFO - 创建序列数据集: 0个序列, 序列长度=5, 通道数=3, 总epochs=0
2025-08-16 18:55:50,083 - INFO - 从 0 个文件加载了 0 个epochs, 创建了 0 个序列
2025-08-16 18:55:50,083 - INFO - 创建序列数据集: 0个序列, 序列长度=5, 通道数=3, 总epochs=0
2025-08-16 18:55:50,083 - INFO - 训练集: 0 sequences
2025-08-16 18:55:50,083 - INFO - 验证集: 0 sequences
2025-08-16 18:55:50,083 - INFO - 测试集: 0 sequences
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_modal_refinement.py", line 542, in <module>
    train_stage2()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_modal_refinement.py", line 357, in train_stage2
    train_loader = DataLoader(
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/dataloader.py", line 385, in __init__
    sampler = RandomSampler(dataset, generator=generator)  # type: ignore[arg-type]
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/sampler.py", line 156, in __init__
    raise ValueError(
ValueError: num_samples should be a positive integer value, but got num_samples=0
