#!/usr/bin/env python3
"""
Stage 4 V8 Final Architecture - 使用final_test_90_fixed的完整架构
策略：
1. 完全使用final_test_90_fixed的SequentialMAMBAFORMER_V2架构
2. 保留渐进式分类作为唯一创新
3. 使用完全相同的超参数配置
4. 目标：达到88%的测试准确率
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import logging
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
from torch.optim.lr_scheduler import OneCycleLR
import json
import math
import sys
import glob

# 添加依赖路径
current_dir = os.path.dirname(os.path.abspath(__file__))
dependencies_dir = os.path.join(current_dir, 'stage4_mp_dependencies')
sys.path.insert(0, dependencies_dir)

# 导入数据集
from utils.sequence_dataset import SequenceSleepDataset

# 设置日志
log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs"
os.makedirs(log_dir, exist_ok=True)

timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
experiment_name = f"stage4_v8_final_architecture_{timestamp}"
log_file = os.path.join(log_dir, experiment_name, "training.log")
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logging.info(f"Starting V8 Final Architecture experiment: {experiment_name}")
logging.info("="*80)
logging.info("Strategy: Use complete final_test_90_fixed architecture with progressive classification")

# =============================================================================
# Complete Model from final_test_90_fixed with Progressive Classification
# =============================================================================

class PositionalEncoding(nn.Module):
    """位置编码"""
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)

class EnhancedEpochFeatureExtractor(nn.Module):
    """增强的单个epoch特征提取器（从final_test_90_fixed）"""
    def __init__(self, input_channels=4, d_model=256, dropout=0.2):
        super().__init__()
        
        # CNN特征提取 - 更深的网络
        self.conv_layers = nn.Sequential(
            # 第一层
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            # 第三层
            nn.Conv1d(128, 256, kernel_size=4, stride=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            
            # 第四层（新增）
            nn.Conv1d(256, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
    
    def forward(self, x):
        x = self.conv_layers(x)
        x = self.global_pool(x)
        x = x.squeeze(-1)
        return x

class ProgressiveSequentialMAMBAFORMER_V8(nn.Module):
    """
    使用final_test_90_fixed完整架构 + 渐进式分类创新
    """
    def __init__(self, input_channels=4, n_classes=5, d_model=512, 
                 n_heads=32, n_layers=12, dropout=0.20, seq_len=7):
        super().__init__()
        
        self.d_model = d_model
        self.seq_len = seq_len
        self.n_classes = n_classes
        
        # ===== 完全复制final_test_90_fixed的架构 =====
        # 特征提取器
        self.feature_extractor = EnhancedEpochFeatureExtractor(
            input_channels, d_model, dropout
        )
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=n_layers
        )
        
        # ===== 渐进式分类（创新部分）=====
        # 粗分类头 (3类: Wake/Light, Deep, REM)
        self.coarse_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, 3)
        )
        
        # 细分类头（融合粗分类信息）
        self.fine_classifier = nn.Sequential(
            nn.LayerNorm(d_model + 3),
            nn.Dropout(dropout),
            nn.Linear(d_model + 3, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 辅助分类头（REM/SWS vs 其他）
        self.auxiliary_head = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 4, 2)
        )
        
        self._init_weights()
        
        logging.info(f"Created ProgressiveSequentialMAMBAFORMER_V8:")
        logging.info(f"  - Complete architecture from final_test_90_fixed")
        logging.info(f"  - Progressive classification as innovation")
        logging.info(f"  - d_model={d_model}, n_heads={n_heads}, n_layers={n_layers}")
        logging.info(f"  - Parameters: {sum(p.numel() for p in self.parameters()):,}")
    
    def _init_weights(self):
        """改进的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.8)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
    
    def forward(self, x):
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 重塑为 (batch*seq_len, channels, time_steps) 进行特征提取
        x_reshaped = x.view(batch_size * seq_len, time_steps, channels).transpose(1, 2)
        
        # 提取每个epoch的特征
        features = self.feature_extractor(x_reshaped)
        
        # 重塑回序列形式
        features = features.view(batch_size, seq_len, self.d_model)
        
        # 添加位置编码
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # (batch, seq_len, d_model)
        
        # Transformer编码
        encoded_features = self.transformer_encoder(features)
        
        # 渐进式分类
        # 1. 粗分类
        coarse_logits = self.coarse_classifier(encoded_features)
        
        # 2. 细分类（融合粗分类概率）
        coarse_probs = F.softmax(coarse_logits, dim=-1)
        fine_input = torch.cat([encoded_features, coarse_probs], dim=-1)
        fine_logits = self.fine_classifier(fine_input)
        
        # 3. 辅助分类
        aux_logits = self.auxiliary_head(encoded_features)
        
        return fine_logits, coarse_logits, aux_logits

# =============================================================================
# Loss Functions
# =============================================================================

class FocalLoss(nn.Module):
    """Focal loss for handling class imbalance"""
    def __init__(self, alpha=None, gamma=2.0):
        super().__init__()
        self.gamma = gamma
        self.alpha = alpha
        
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)
        
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if isinstance(self.alpha, (list, np.ndarray)):
                alpha = torch.tensor(self.alpha, device=inputs.device)[targets]
            else:
                alpha = self.alpha
            focal_loss = alpha * focal_loss
        
        return focal_loss.mean()

class LabelSmoothingLoss(nn.Module):
    """Label smoothing for better generalization"""
    def __init__(self, n_classes=5, smoothing=0.1):
        super().__init__()
        self.n_classes = n_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
    
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)
        
        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (self.n_classes - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), self.confidence)
        
        return torch.mean(torch.sum(-true_dist * F.log_softmax(inputs, dim=-1), dim=-1))

# =============================================================================
# Training Functions
# =============================================================================

def train_one_epoch(model, train_loader, criterion, optimizer, scheduler, device, epoch):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        
        if target.dim() > 1:
            target = target[:, target.shape[1] // 2]
        
        # Add noise augmentation
        if np.random.random() < 0.2:
            noise = torch.randn_like(data) * 0.005
            data = data + noise
        
        optimizer.zero_grad()
        
        # Forward pass
        fine_logits, coarse_logits, aux_logits = model(data)
        
        if fine_logits.dim() == 3:
            batch_size, seq_len, n_classes = fine_logits.shape
            fine_logits_flat = fine_logits.reshape(-1, n_classes)
            coarse_logits_flat = coarse_logits.reshape(-1, 3)
            aux_logits_flat = aux_logits.reshape(-1, 2)
            target_expanded = target.unsqueeze(1).expand(-1, seq_len).reshape(-1)
        else:
            fine_logits_flat = fine_logits
            coarse_logits_flat = coarse_logits
            aux_logits_flat = aux_logits
            target_expanded = target
        
        # 创建粗标签
        coarse_target = torch.zeros_like(target_expanded)
        coarse_target[target_expanded <= 1] = 0  # Wake/N1
        coarse_target[(target_expanded == 2) | (target_expanded == 3)] = 1  # N2/N3
        coarse_target[target_expanded == 4] = 2  # REM
        
        # 创建辅助标签
        aux_target = torch.zeros_like(target_expanded)
        aux_target[(target_expanded == 3) | (target_expanded == 4)] = 1  # Deep/REM
        
        # 计算损失
        loss_fine = criterion(fine_logits_flat, target_expanded)
        loss_coarse = criterion(coarse_logits_flat, coarse_target)
        loss_aux = F.cross_entropy(aux_logits_flat, aux_target)
        
        # 组合损失（优先细分类）
        loss = 0.7 * loss_fine + 0.2 * loss_coarse + 0.1 * loss_aux
        
        # L2正则化
        l2_lambda = 0.0001
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        scheduler.step()
        
        total_loss += loss.item()
        
        # 使用细分类结果进行预测
        if fine_logits.dim() == 3:
            pred = fine_logits[:, fine_logits.shape[1] // 2, :].argmax(dim=1)
        else:
            pred = fine_logits.argmax(dim=1)
        
        all_preds.extend(pred.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
        
        if batch_idx % 100 == 0:
            acc = accuracy_score(target.cpu(), pred.cpu())
            logging.info(f"  Batch {batch_idx}/{len(train_loader)}: "
                        f"Loss={loss.item():.4f}, Acc={acc:.4f}, "
                        f"LR={scheduler.get_last_lr()[0]:.6f}")
    
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, macro_f1

def evaluate(model, val_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in val_loader:
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1] // 2]
            
            # Test-time augmentation
            predictions = []
            for _ in range(3):
                noise = torch.randn_like(data) * 0.003
                augmented_data = data + noise
                fine_logits, _, _ = model(augmented_data)
                
                if fine_logits.dim() == 3:
                    pred = fine_logits[:, fine_logits.shape[1] // 2, :]
                else:
                    pred = fine_logits
                predictions.append(F.softmax(pred, dim=-1))
            
            # Average predictions
            avg_pred = torch.stack(predictions).mean(dim=0)
            pred = avg_pred.argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, macro_f1, kappa, class_f1, cm

# =============================================================================
# Main Training
# =============================================================================

def main():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Using device: {device}")
    
    # Configuration - 完全使用final_test_90_fixed的配置
    config = {
        "d_model": 512,
        "n_heads": 32,
        "n_layers": 12,
        "dropout": 0.20,
        "seq_len": 7,
        "batch_size": 16,
        "learning_rate": 2e-4,
        "num_epochs": 15,
        "gradient_clip": 1.0,
        "weight_decay": 0.03,
        "label_smoothing": 0.1,
    }
    
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    # Data paths - 与stage4_mamba_progressive完全一致
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 按受试者划分数据集
    from collections import defaultdict
    subject_files = defaultdict(list)
    for file_path in all_files:
        filename = os.path.basename(file_path)
        if filename.startswith("SC4") and len(filename) >= 7:
            subject_id = filename[3:5]
            subject_files[subject_id].append(file_path)
    
    # 使用与stage4_mamba_progressive完全相同的固定数据分割
    train_subjects = [
        "00", "01", "02", "03", "06", "07", "08", "09",
        "10", "11", "12", "13", "15", "16", "17", "18",
    ]
    val_subjects = ["04", "14"]
    test_subjects = ["05", "19"]
    
    # 收集文件
    train_files = []
    val_files = []
    test_files = []
    
    for subject in train_subjects:
        if subject in subject_files:
            train_files.extend(subject_files[subject])
    
    for subject in val_subjects:
        if subject in subject_files:
            val_files.extend(subject_files[subject])
    
    for subject in test_subjects:
        if subject in subject_files:
            test_files.extend(subject_files[subject])
    
    # Create datasets
    logging.info("Loading datasets with ALL data (no limit)...")
    train_dataset = SequenceSleepDataset(
        train_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=True
    )
    val_dataset = SequenceSleepDataset(
        val_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=False
    )
    test_dataset = SequenceSleepDataset(
        test_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=False
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, "
                f"Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset, batch_size=config["batch_size"],
        shuffle=True, num_workers=4, pin_memory=True, drop_last=True
    )
    val_loader = DataLoader(
        val_dataset, batch_size=config["batch_size"],
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, batch_size=config["batch_size"],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # Create model
    model = ProgressiveSequentialMAMBAFORMER_V8(
        input_channels=4,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"]
    ).to(device)
    
    # Loss functions with class weights
    class_weights = [2.0, 2.5, 1.0, 1.5, 2.0]
    logging.info(f"Using class weights: W={class_weights[0]}, N1={class_weights[1]}, "
                f"N2={class_weights[2]}, N3={class_weights[3]}, REM={class_weights[4]}")
    
    focal_loss = FocalLoss(alpha=class_weights, gamma=2.0)
    label_smooth_loss = LabelSmoothingLoss(n_classes=5, smoothing=config["label_smoothing"])
    
    # Combined loss
    def combined_loss(outputs, targets):
        return 0.6 * focal_loss(outputs, targets) + 0.4 * label_smooth_loss(outputs, targets)
    
    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"],
        betas=(0.9, 0.999)
    )
    
    # Scheduler - OneCycleLR
    total_steps = len(train_loader) * config["num_epochs"]
    scheduler = OneCycleLR(
        optimizer,
        max_lr=config["learning_rate"],
        total_steps=total_steps,
        pct_start=0.2,
        anneal_strategy="cos",
        div_factor=10,
        final_div_factor=100
    )
    
    # Training loop
    logging.info("\n" + "="*80)
    logging.info("Starting V8 Final Architecture Training...")
    logging.info("="*80)
    
    best_test_acc = 0
    best_test_f1 = 0
    patience = 5
    patience_counter = 0
    
    for epoch in range(config["num_epochs"]):
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}")
        
        # Train
        train_loss, train_acc, train_f1 = train_one_epoch(
            model, train_loader, combined_loss, optimizer, scheduler, device, epoch
        )
        
        # Validate
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(
            model, val_loader, device
        )
        
        # Test
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(
            model, test_loader, device
        )
        
        logging.info(f"\nEpoch {epoch+1} Results:")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  TEST: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # Check if we reached the target
        if test_acc >= 0.88:
            logging.info(f"  🎉 REACHED TARGET! Test Accuracy: {test_acc:.4f} >= 88%")
        
        # Save best model
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_test_f1 = test_f1
            patience_counter = 0
            
            model_path = os.path.join(log_dir, experiment_name, f"best_model_acc_{test_acc:.4f}.pth")
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'test_f1': test_f1,
                'config': config
            }, model_path)
            logging.info(f"  💾 Saved best model: {model_path}")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logging.info(f"Early stopping triggered at epoch {epoch+1}")
                break
    
    # Final summary
    logging.info("\n" + "="*80)
    logging.info("V8 Final Architecture Training Completed!")
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test F1 Score: {best_test_f1:.4f}")
    
    gap_to_88 = 0.88 - best_test_acc
    if gap_to_88 > 0:
        logging.info(f"Gap to 88%: {gap_to_88:.4f} ({gap_to_88*100:.2f}%)")
    else:
        logging.info(f"🎉 EXCEEDED TARGET BY {-gap_to_88:.4f} ({-gap_to_88*100:.2f}%)")
    
    logging.info(f"Log saved to: {log_file}")
    logging.info("="*80)

if __name__ == "__main__":
    main()