#!/usr/bin/env python3
"""
自动化训练监控和优化脚本
持续监控训练进度，分析问题，并自动调整参数进行优化
"""

import os
import time
import json
import subprocess
import re
from datetime import datetime
import numpy as np
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_optimize.log'),
        logging.StreamHandler()
    ]
)

class TrainingOptimizer:
    def __init__(self):
        self.training_history = []
        self.best_accuracy = 0
        self.optimization_count = 0
        self.target_accuracy = 85.0
        
    def parse_training_log(self, log_file):
        """解析训练日志，提取关键指标"""
        if not os.path.exists(log_file):
            return None
            
        metrics = {
            'epochs': [],
            'train_acc': [],
            'test_acc': [],
            'f1_scores': [],
            'n1_recall': [],
            'best_acc': 0
        }
        
        with open(log_file, 'r') as f:
            lines = f.readlines()
            
        for line in lines:
            # 提取测试准确率
            if '测试 - Acc:' in line:
                match = re.search(r'测试 - Acc: ([\d.]+)%', line)
                if match:
                    acc = float(match.group(1))
                    metrics['test_acc'].append(acc)
                    metrics['best_acc'] = max(metrics['best_acc'], acc)
                    
            # 提取F1分数
            if 'F1:' in line:
                match = re.search(r'F1: ([\d.]+)', line)
                if match:
                    f1 = float(match.group(1))
                    metrics['f1_scores'].append(f1)
                    
            # 提取训练准确率
            if '训练 - Loss:' in line and 'Acc:' in line:
                match = re.search(r'Acc: ([\d.]+)%', line)
                if match:
                    acc = float(match.group(1))
                    metrics['train_acc'].append(acc)
                    
        return metrics
    
    def analyze_training_problems(self, metrics):
        """分析训练中的问题"""
        problems = []
        
        if not metrics:
            return ["无法读取训练日志"]
            
        # 检查是否达到目标
        if metrics['best_acc'] >= self.target_accuracy:
            return []
            
        # 检查过拟合
        if len(metrics['train_acc']) > 0 and len(metrics['test_acc']) > 0:
            last_train = metrics['train_acc'][-1] if metrics['train_acc'] else 0
            last_test = metrics['test_acc'][-1] if metrics['test_acc'] else 0
            gap = last_train - last_test
            
            if gap > 5:
                problems.append(f"过拟合严重: 训练{last_train:.2f}% vs 测试{last_test:.2f}%")
                
        # 检查收敛速度
        if len(metrics['test_acc']) >= 3:
            recent_accs = metrics['test_acc'][-3:]
            if max(recent_accs) - min(recent_accs) < 0.5:
                problems.append("训练已停滞，需要调整学习率或模型结构")
                
        # 检查F1分数
        if metrics['f1_scores']:
            avg_f1 = np.mean(metrics['f1_scores'])
            if avg_f1 < 0.75:
                problems.append(f"F1分数过低: {avg_f1:.3f}，存在类别不平衡问题")
                
        return problems
    
    def generate_optimization_config(self, problems, current_config):
        """根据问题生成优化配置"""
        new_config = current_config.copy()
        
        for problem in problems:
            if "过拟合" in problem:
                # 增加正则化
                new_config['dropout'] = min(new_config.get('dropout', 0.15) + 0.05, 0.3)
                new_config['weight_decay'] = min(new_config.get('weight_decay', 2e-4) * 1.5, 1e-3)
                logging.info(f"增加正则化: dropout={new_config['dropout']}, weight_decay={new_config['weight_decay']}")
                
            elif "停滞" in problem:
                # 调整学习率和模型结构
                new_config['learning_rate'] = new_config.get('learning_rate', 3e-4) * 0.5
                new_config['n_transformer_layers'] = min(new_config.get('n_transformer_layers', 1) + 1, 3)
                logging.info(f"调整学习率和模型: lr={new_config['learning_rate']}, layers={new_config['n_transformer_layers']}")
                
            elif "F1分数过低" in problem:
                # 调整类别权重
                new_config['focal_gamma'] = min(new_config.get('focal_gamma', 2.0) + 0.5, 3.0)
                new_config['n1_weight'] = min(new_config.get('n1_weight', 5.0) + 1.0, 8.0)
                logging.info(f"调整类别平衡: gamma={new_config['focal_gamma']}, n1_weight={new_config['n1_weight']}")
                
        return new_config
    
    def update_training_script(self, config):
        """更新训练脚本的配置"""
        script_path = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py'
        
        with open(script_path, 'r') as f:
            content = f.read()
            
        # 更新配置参数
        for key, value in config.items():
            if key == 'dropout':
                pattern = r'"dropout":\s*[\d.]+,'
                replacement = f'"dropout": {value},'
                content = re.sub(pattern, replacement, content)
                
            elif key == 'learning_rate':
                pattern = r'"learning_rate":\s*[\d.e-]+,'
                replacement = f'"learning_rate": {value},'
                content = re.sub(pattern, replacement, content)
                
            elif key == 'weight_decay':
                pattern = r'"weight_decay":\s*[\d.e-]+,'
                replacement = f'"weight_decay": {value},'
                content = re.sub(pattern, replacement, content)
                
            elif key == 'focal_gamma':
                pattern = r'gamma=[\d.]+\)'
                replacement = f'gamma={value})'
                content = re.sub(pattern, replacement, content)
                
        with open(script_path, 'w') as f:
            f.write(content)
            
        logging.info("训练脚本已更新")
        
    def start_training(self):
        """启动新的训练"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"training_auto_{timestamp}.log"
        
        cmd = f"cd /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training && python stage4_mamba_progressive.py > {log_file} 2>&1 &"
        subprocess.run(cmd, shell=True)
        
        logging.info(f"新训练已启动，日志: {log_file}")
        return log_file
        
    def monitor_and_optimize(self):
        """主循环：监控训练并自动优化"""
        current_config = {
            'dropout': 0.15,
            'learning_rate': 3e-4,
            'weight_decay': 2e-4,
            'focal_gamma': 2.0,
            'n1_weight': 5.0,
            'n_transformer_layers': 2
        }
        
        while self.best_accuracy < self.target_accuracy and self.optimization_count < 10:
            # 启动训练
            log_file = self.start_training()
            logging.info(f"开始第{self.optimization_count + 1}次优化训练")
            
            # 等待训练完成（检查log文件）
            training_complete = False
            check_interval = 300  # 每5分钟检查一次
            max_wait = 7200  # 最多等待2小时
            waited = 0
            
            while not training_complete and waited < max_wait:
                time.sleep(check_interval)
                waited += check_interval
                
                # 检查训练是否完成
                if os.path.exists(log_file):
                    with open(log_file, 'r') as f:
                        content = f.read()
                        if "训练完成" in content or "Early stopping" in content:
                            training_complete = True
                            
                # 定期分析进度
                metrics = self.parse_training_log(log_file)
                if metrics:
                    logging.info(f"当前最佳准确率: {metrics['best_acc']:.2f}%")
                    
                    if metrics['best_acc'] >= self.target_accuracy:
                        logging.info(f"🎉 目标达成！准确率: {metrics['best_acc']:.2f}%")
                        self.best_accuracy = metrics['best_acc']
                        return True
                        
            # 分析训练结果
            metrics = self.parse_training_log(log_file)
            problems = self.analyze_training_problems(metrics)
            
            if metrics:
                self.best_accuracy = max(self.best_accuracy, metrics['best_acc'])
                self.training_history.append({
                    'iteration': self.optimization_count,
                    'config': current_config.copy(),
                    'best_acc': metrics['best_acc'],
                    'problems': problems
                })
                
            logging.info(f"第{self.optimization_count + 1}次训练完成")
            logging.info(f"最佳准确率: {self.best_accuracy:.2f}%")
            logging.info(f"识别的问题: {problems}")
            
            # 生成新配置
            if problems:
                current_config = self.generate_optimization_config(problems, current_config)
                self.update_training_script(current_config)
                
            self.optimization_count += 1
            
            # 保存历史记录
            with open('optimization_history.json', 'w') as f:
                json.dump(self.training_history, f, indent=2)
                
        return self.best_accuracy >= self.target_accuracy


if __name__ == "__main__":
    optimizer = TrainingOptimizer()
    success = optimizer.monitor_and_optimize()
    
    if success:
        logging.info(f"✅ 优化成功！达到目标准确率 {optimizer.target_accuracy}%")
    else:
        logging.info(f"优化结束，最佳准确率: {optimizer.best_accuracy:.2f}%")
        
    # 打印优化历史
    logging.info("\n优化历史:")
    for record in optimizer.training_history:
        logging.info(f"第{record['iteration'] + 1}次: {record['best_acc']:.2f}%")