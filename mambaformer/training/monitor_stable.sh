#\!/bin/bash
# 监控Stage 2稳定版训练进度

LOG_DIR="/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_stable_20250816_002454"
LOG_FILE="$LOG_DIR/training.log"

echo "========================================"
echo "监控Stage 2稳定版训练"
echo "目标: 保持≥85%准确率，原始特征≥95%"
echo "========================================"

if [ \! -f "$LOG_FILE" ]; then
    echo "日志文件不存在: $LOG_FILE"
    exit 1
fi

# 获取最新的epoch信息
echo -e "\n最新训练状态:"
tail -100 "$LOG_FILE"  < /dev/null |  grep -E "Epoch [0-9]+/[0-9]+" | tail -1

# 显示所有epoch的结果
echo -e "\n训练历史:"
grep -E "(测试|Alpha值|原始特征保留)" "$LOG_FILE" | tail -20

# 检查是否达到目标
echo -e "\n检查目标达成情况:"
BEST_ACC=$(grep "保存最佳模型" "$LOG_FILE" | tail -1 | grep -oE "[0-9]+\.[0-9]+%" || echo "N/A")
echo "当前最佳准确率: $BEST_ACC"

# 检查是否还在运行
if pgrep -f "stage2_stable.py" > /dev/null; then
    echo -e "\n✅ 训练仍在进行中..."
    echo "进程ID: $(pgrep -f stage2_stable.py)"
else
    echo -e "\n⚠️ 训练已停止"
    
    # 显示最终结果
    echo -e "\n最终结果:"
    tail -20 "$LOG_FILE" | grep -E "(训练完成|最终|Alpha值|原始特征)" 
fi

# 显示最佳模型信息
if [ -f "$LOG_DIR/best_model.pth" ]; then
    echo -e "\n最佳模型已保存: $LOG_DIR/best_model.pth"
    ls -lh "$LOG_DIR/best_model.pth"
fi
