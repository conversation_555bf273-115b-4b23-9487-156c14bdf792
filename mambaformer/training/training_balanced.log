2025-08-18 02:57:32,496 - INFO - ================================================================================
2025-08-18 02:57:32,497 - INFO - 🎯 Stage 4: Mamba-Transformer混合架构 + 渐进式分类策略
2025-08-18 02:57:32,497 - INFO - 核心创新：
2025-08-18 02:57:32,497 - INFO - 1. Mamba组件用于长程依赖建模
2025-08-18 02:57:32,497 - INFO - 2. 模态内特征精炼（EEG局部注意力，EOG/EMG轻量级Mamba）
2025-08-18 02:57:32,497 - INFO - 3. 渐进式分类策略（粗分类→细分类）
2025-08-18 02:57:32,497 - INFO - 4. 不确定性估计和置信度决策
2025-08-18 02:57:32,497 - INFO - 目标: ≥85% accuracy
2025-08-18 02:57:32,497 - INFO - ================================================================================
2025-08-18 02:57:32,497 - INFO - 配置: {'n_classes': 5, 'd_model': 128, 'n_heads': 8, 'n_mamba_layers': 1, 'n_transformer_layers': 2, 'dropout': 0.15, 'seq_len': 5, 'batch_size': 64, 'learning_rate': 0.0003, 'num_epochs': 20, 'weight_decay': 0.0001, 'patience': 8, 'coarse_weight': 0.25, 'uncertainty_weight': 0.05, 'use_improved_classifier': True, 'use_focal_loss': True, 'use_mixup': False, 'use_label_smoothing': True}
2025-08-18 02:57:32,521 - INFO - 设备: cuda
2025-08-18 02:57:32,522 - INFO - 训练集受试者: ['00', '01', '02', '03', '06', '07', '08', '09', '10', '11', '12', '13', '15', '16', '17', '18']
2025-08-18 02:57:32,522 - INFO - 验证集受试者: ['04', '14']
2025-08-18 02:57:32,522 - INFO - 测试集受试者: ['05', '19']
2025-08-18 02:57:32,522 - INFO - 加载数据集...
2025-08-18 02:57:34,330 - INFO - 从 31 个文件加载了 33190 个epochs, 创建了 33066 个序列
2025-08-18 02:57:34,330 - INFO - 创建序列数据集: 33066个序列, 序列长度=5, 通道数=4, 总epochs=33190
2025-08-18 02:57:34,556 - INFO - 从 4 个文件加载了 4391 个epochs, 创建了 4375 个序列
2025-08-18 02:57:34,556 - INFO - 创建序列数据集: 4375个序列, 序列长度=5, 通道数=4, 总epochs=4391
2025-08-18 02:57:34,919 - INFO - 从 4 个文件加载了 4727 个epochs, 创建了 4711 个序列
2025-08-18 02:57:34,920 - INFO - 创建序列数据集: 4711个序列, 序列长度=5, 通道数=4, 总epochs=4727
2025-08-18 02:57:35,622 - INFO - 类别分布: [6414, 2097, 13775, 4841, 5939]
2025-08-18 02:57:35,622 - INFO - 类别权重: [0.7215846180915833, 2.207078695297241, 0.3359886705875397, 0.956051230430603, 0.7792968153953552]
2025-08-18 02:57:35,622 - INFO - 训练集: 33066 sequences
2025-08-18 02:57:35,623 - INFO - 验证集: 4375 sequences
2025-08-18 02:57:35,623 - INFO - 测试集: 4711 sequences
2025-08-18 02:57:35,645 - INFO - 使用改进的渐进式分类器（确定组vs过渡组）
2025-08-18 02:57:35,662 - INFO - 创建Stage4MambaProgressiveModel: 参数量=1,160,275
2025-08-18 02:57:35,865 - INFO - 使用改进的损失函数（含Focal Loss）
/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py:1567: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler() if device.type == 'cuda' else None
2025-08-18 02:57:37,080 - INFO - 混合精度训练: 启用
2025-08-18 02:57:37,080 - INFO - 
============================================================
2025-08-18 02:57:37,080 - INFO - Epoch 1/20

Training:   0%|          | 0/517 [00:00<?, ?it/s]/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py:1178: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast(enabled=use_amp):

Training:   0%|          | 0/517 [00:01<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py", line 1786, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py", line 1580, in main
    train_loss, train_acc, loss_components = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py", line 1191, in train_epoch
    loss, loss_dict = criterion(outputs, labels)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py", line 967, in forward
    if self.use_label_smoothing and self.label_smoothing is not None:
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1940, in __getattr__
    raise AttributeError(
AttributeError: 'ImprovedProgressiveLoss' object has no attribute 'use_label_smoothing'
