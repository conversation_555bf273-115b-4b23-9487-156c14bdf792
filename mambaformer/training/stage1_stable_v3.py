#!/usr/bin/env python3
"""
🎯 第1阶段稳定训练v3：修复warmup问题，稳定达到85%
从78.29%的基础上继续优化
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import (
    confusion_matrix,
    classification_report,
    accuracy_score,
    f1_score,
    cohen_kappa_score,
)
import warnings

warnings.filterwarnings("ignore")

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.progressive_mambaformer_v1_fixed import (
    ProgressiveMAMBAFORMER_V1_Fixed,
    SequentialFocalLoss,
    TemporalConsistencyLoss
)
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class FocalLoss(nn.Module):
    def __init__(self, alpha=None, gamma=2.0, device="cuda"):
        super().__init__()
        self.gamma = gamma
        if alpha is not None:
            self.alpha = torch.tensor(alpha).to(device)
        else:
            self.alpha = None
        self.device = device

    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)

        ce_loss = F.cross_entropy(inputs, targets, reduction="none")
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** self.gamma * ce_loss

        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss

        return focal_loss.mean()


class LabelSmoothingLoss(nn.Module):
    def __init__(self, n_classes=5, smoothing=0.1):
        super().__init__()
        self.n_classes = n_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing

    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)

        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (self.n_classes - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), self.confidence)

        return torch.mean(torch.sum(-true_dist * F.log_softmax(inputs, dim=-1), dim=-1))


def train_one_epoch(model, data_loader, focal_loss, ls_loss, optimizer, device, epoch, config):
    """训练一个epoch - 稳定版本"""
    model.train()
    running_loss = 0.0
    correct_predictions = 0
    total_predictions = 0
    
    progress_bar = tqdm(data_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']}")
    
    for batch_idx, (data, target) in enumerate(progress_bar):
        data, target = data.to(device), target.to(device)
        
        # 转换数据格式
        data = data.permute(0, 1, 3, 2)
        
        # 极少的数据增强
        if torch.rand(1).item() < 0.05:  # 只有5%概率
            noise_std = data.std() * 0.002  # 极小的噪声
            noise = torch.randn_like(data) * noise_std
            data = data + noise
        
        optimizer.zero_grad()
        main_output, aux_output = model(data)
        
        # 组合损失
        loss1 = focal_loss(main_output, target)
        loss2 = ls_loss(main_output, target)
        loss = 0.6 * loss1 + 0.4 * loss2
        
        # 极小的时间一致性损失
        temp_loss = TemporalConsistencyLoss(weight=0.005)
        loss += temp_loss(main_output)
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
        optimizer.step()
        
        # 计算准确率
        _, predicted = torch.max(main_output[:, main_output.size(1)//2, :], dim=-1)
        true_labels = target[:, target.size(1)//2]
        correct_predictions += (predicted == true_labels).sum().item()
        total_predictions += true_labels.size(0)
        
        running_loss += loss.item()
        
        current_acc = 100 * correct_predictions / total_predictions
        current_lr = optimizer.param_groups[0]['lr']
        progress_bar.set_postfix({
            'loss': f'{running_loss/(batch_idx+1):.4f}',
            'acc': f'{current_acc:.2f}%',
            'lr': f'{current_lr:.7f}'
        })
    
    epoch_loss = running_loss / len(data_loader)
    epoch_acc = 100 * correct_predictions / total_predictions
    
    return epoch_loss, epoch_acc


def evaluate(model, data_loader, device):
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc="Evaluating"):
            data = data.to(device)
            data = data.permute(0, 1, 3, 2)
            main_output, _ = model(data)
            
            predictions = torch.argmax(main_output[:, main_output.size(1)//2, :], dim=-1)
            targets = target[:, target.size(1)//2]
            
            all_predictions.extend(predictions.cpu().numpy())
            all_targets.extend(targets.numpy())
    
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    
    accuracy = accuracy_score(all_targets, all_predictions)
    f1_macro = f1_score(all_targets, all_predictions, average='macro')
    kappa = cohen_kappa_score(all_targets, all_predictions)
    
    report = classification_report(all_targets, all_predictions, 
                                 target_names=['W', 'N1', 'N2', 'N3', 'REM'],
                                 output_dict=True)
    
    cm = confusion_matrix(all_targets, all_predictions)
    
    return {
        'accuracy': accuracy,
        'f1_macro': f1_macro,
        'kappa': kappa,
        'classification_report': report,
        'confusion_matrix': cm
    }


def main():
    config = {
        "d_model": 512,
        "n_heads": 32,
        "n_layers": 12,
        "dropout": 0.15,
        "seq_len": 7,
        "batch_size": 16,
        "learning_rate": 1e-4,  # 保守的学习率
        "num_epochs": 25,  # 更多训练轮数
        "gradient_clip": 0.3,  # 更严格的梯度裁剪
        "weight_decay": 0.005,  # 降低权重衰减
        "label_smoothing": 0.1,
    }

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_stable_v3_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(os.path.join(log_dir, "training.log")),
            logging.StreamHandler(),
        ],
    )

    logging.info("=" * 80)
    logging.info("🎯 第1阶段稳定训练v3：修复warmup问题")
    logging.info("目标：从78.29%稳定提升到85%+")
    logging.info("=" * 80)
    logging.info(f"配置: {config}")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"Device: {device}")

    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"

    # 数据集
    train_files = [
        "SC4181E0.npz", "SC4182E0.npz", "SC4161E0.npz", "SC4162E0.npz",
        "SC4131E0.npz", "SC4101E0.npz", "SC4102E0.npz", "SC4111E0.npz",
        "SC4112E0.npz", "SC4041E0.npz", "SC4042E0.npz", "SC4191E0.npz",
        "SC4192E0.npz", "SC4061E0.npz", "SC4062E0.npz", "SC4091E0.npz",
        "SC4092E0.npz", "SC4121E0.npz", "SC4122E0.npz", "SC4141E0.npz",
        "SC4142E0.npz", "SC4051E0.npz", "SC4052E0.npz", "SC4081E0.npz",
        "SC4082E0.npz", "SC4151E0.npz", "SC4152E0.npz", "SC4171E0.npz",
        "SC4172E0.npz",
    ]
    val_files = [
        "SC4021E0.npz", "SC4022E0.npz", "SC4031E0.npz",
        "SC4032E0.npz", "SC4071E0.npz", "SC4072E0.npz",
    ]
    test_files = [
        "SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"
    ]

    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]

    logging.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=True,
    )

    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )

    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")

    model = ProgressiveMAMBAFORMER_V1_Fixed(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)

    # 加载最佳模型（78.29%）
    best_model_path = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_full_fixed_20250815_031333/best_model.pth"
    if os.path.exists(best_model_path):
        logging.info(f"加载最佳模型: {best_model_path}")
        checkpoint = torch.load(best_model_path)
        model.load_state_dict(checkpoint['model_state_dict'])
        logging.info(f"✅ 成功加载模型 (Val Acc: {checkpoint.get('val_acc', 77.87):.2f}%)")
    
    # 评估初始性能
    logging.info("\n评估初始性能...")
    initial_test = evaluate(model, test_loader, device)
    initial_acc = initial_test['accuracy'] * 100
    logging.info(f"初始测试准确率: {initial_acc:.2f}%")

    # 调整N1的权重
    class_weights = torch.tensor([2.0, 6.0, 1.0, 1.5, 2.0]).to(device)
    focal_loss = FocalLoss(alpha=class_weights, gamma=2.0, device=device)
    ls_loss = LabelSmoothingLoss(n_classes=5, smoothing=config["label_smoothing"])

    # 使用较低的学习率
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"],
    )

    # 使用温和的学习率调度
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=8,  # 每8个epoch重启
        T_mult=1,  # 保持周期不变
        eta_min=1e-6
    )

    best_val_acc = 77.87
    best_test_acc = initial_acc
    best_epoch = -1
    patience = 12
    patience_counter = 0
    no_improve_epochs = 0
    
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_acc': [],
        'test_acc': [],
        'lr': []
    }
    
    for epoch in range(config["num_epochs"]):
        logging.info(f"\n{'='*60}")
        logging.info(f"Epoch {epoch+1}/{config['num_epochs']}")
        
        current_lr = optimizer.param_groups[0]['lr']
        logging.info(f"当前学习率: {current_lr:.7f}")
        
        # 前3个epoch使用更低的学习率
        if epoch < 3:
            warmup_lr = config["learning_rate"] * (0.3 + 0.2 * epoch)  # 0.3, 0.5, 0.7
            for param_group in optimizer.param_groups:
                param_group['lr'] = warmup_lr
            logging.info(f"Warmup学习率: {warmup_lr:.7f}")
        
        train_loss, train_acc = train_one_epoch(
            model, train_loader, focal_loss, ls_loss, 
            optimizer, device, epoch, config
        )
        
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        
        # 验证
        val_metrics = evaluate(model, val_loader, device)
        val_acc = val_metrics['accuracy'] * 100
        
        # 测试
        test_metrics = evaluate(model, test_loader, device)
        test_acc = test_metrics['accuracy'] * 100
        
        logging.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_metrics['f1_macro']:.4f}")
        logging.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_metrics['f1_macro']:.4f}")
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_acc'].append(val_acc)
        history['test_acc'].append(test_acc)
        history['lr'].append(optimizer.param_groups[0]['lr'])
        
        # 检查是否达到目标
        if test_acc >= 85.0:
            logging.info(f"🎉🎉🎉 达到目标！测试准确率: {test_acc:.2f}% >= 85%")
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config,
                'history': history,
            }, os.path.join(log_dir, 'target_achieved_model.pth'))
            
            # Git commit
            os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '✅ Stage 1达到85%目标: {test_acc:.2f}%'")
            
            # 生成结果摘要
            result_summary = {
                'stage': '第1阶段稳定训练v3',
                'test_accuracy': test_acc,
                'test_f1_macro': test_metrics['f1_macro'],
                'test_kappa': test_metrics['kappa'],
                'best_epoch': epoch,
                'config': config,
                'timestamp': timestamp,
            }
            
            with open(os.path.join(log_dir, 'result_summary.json'), 'w') as f:
                json.dump(result_summary, f, indent=2)
            
            # 启动下一阶段
            logging.info("启动第2阶段训练...")
            os.system("python /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_cross_attention.py &")
            break
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_val_acc = val_acc
            best_epoch = epoch
            patience_counter = 0
            no_improve_epochs = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config,
                'history': history,
            }, os.path.join(log_dir, 'best_model.pth'))
            logging.info(f"✅ 保存最佳模型 (Test: {test_acc:.2f}%)")
        else:
            patience_counter += 1
            no_improve_epochs += 1
            
            # 如果长时间没有改善，微调学习率
            if no_improve_epochs >= 5 and epoch >= 3:
                for param_group in optimizer.param_groups:
                    param_group['lr'] *= 0.5
                logging.info(f"降低学习率到: {optimizer.param_groups[0]['lr']:.7f}")
                no_improve_epochs = 0
            
            if patience_counter >= patience:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
        
        # 学习率调度
        if epoch >= 3:  # warmup后才使用scheduler
            scheduler.step()
        
        # 显示各类别性能
        logging.info("\n测试集各类别性能:")
        for class_name in ['W', 'N1', 'N2', 'N3', 'REM']:
            class_metrics = test_metrics['classification_report'][class_name]
            logging.info(f"{class_name:>4}: Precision={class_metrics['precision']:.3f}, "
                        f"Recall={class_metrics['recall']:.3f}, "
                        f"F1={class_metrics['f1-score']:.3f}")
    
    logging.info(f"\n{'='*60}")
    logging.info(f"训练完成! 最佳测试准确率: {best_test_acc:.2f}% (Epoch {best_epoch+1})")
    
    # 保存训练历史
    with open(os.path.join(log_dir, 'training_history.json'), 'w') as f:
        json.dump(history, f, indent=2)
    
    if best_test_acc < 85.0:
        logging.info(f"⚠️ 未达到85%目标 (最佳: {best_test_acc:.2f}%)")
        logging.info("需要进一步调优")
    
    return best_test_acc


if __name__ == "__main__":
    final_acc = main()
    print(f"\n最终准确率: {final_acc:.2f}%")