#!/usr/bin/env python3
"""
监控Stage 2训练进度
"""

import os
import time
import json
from pathlib import Path
from datetime import datetime

def check_process(pid):
    """检查进程是否运行"""
    try:
        os.kill(pid, 0)
        return True
    except ProcessLookupError:
        return False

def get_latest_log_dir():
    """获取最新的训练日志目录"""
    log_base = Path("/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs")
    stage2_dirs = sorted([d for d in log_base.iterdir() if d.name.startswith("stage2_continue_")])
    if stage2_dirs:
        return stage2_dirs[-1]
    return None

def parse_training_log(log_file):
    """解析训练日志获取最新状态"""
    if not log_file.exists():
        return None
    
    latest_info = {
        'current_epoch': 0,
        'best_accuracy': 0.0,
        'latest_train_acc': 0.0,
        'latest_val_acc': 0.0,
        'latest_test_acc': 0.0
    }
    
    with open(log_file, 'r') as f:
        lines = f.readlines()
    
    for line in reversed(lines):
        if "Epoch" in line and "/" in line:
            # 提取当前epoch
            try:
                epoch_str = line.split("Epoch")[1].split("/")[0].strip()
                latest_info['current_epoch'] = int(epoch_str)
            except:
                pass
        
        if "测试 - Acc:" in line:
            # 提取测试准确率
            try:
                acc_str = line.split("Acc:")[1].split("%")[0].strip()
                latest_info['latest_test_acc'] = float(acc_str)
            except:
                pass
                
        if "验证 - Acc:" in line:
            # 提取验证准确率
            try:
                acc_str = line.split("Acc:")[1].split("%")[0].strip()
                latest_info['latest_val_acc'] = float(acc_str)
            except:
                pass
                
        if "训练 - Loss:" in line:
            # 提取训练准确率
            try:
                if "Acc:" in line:
                    acc_str = line.split("Acc:")[1].split("%")[0].strip()
                    latest_info['latest_train_acc'] = float(acc_str)
            except:
                pass
                
        if "保存最佳模型" in line:
            # 提取最佳准确率
            try:
                acc_str = line.split("准确率:")[1].split("%")[0].strip()
                latest_info['best_accuracy'] = float(acc_str)
            except:
                pass
        
        if "达到85%目标" in line:
            # 检查是否达到目标
            latest_info['target_achieved'] = True
            try:
                acc_str = line.split("准确率:")[1].split("%")[0].strip()
                latest_info['final_accuracy'] = float(acc_str)
            except:
                pass
    
    return latest_info

def main():
    """主监控函数"""
    PID = 662310  # 训练进程PID
    
    print("="*60)
    print("Stage 2 训练监控")
    print("="*60)
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查进程状态
    if check_process(PID):
        print(f"✅ 训练进程 (PID: {PID}) 正在运行")
    else:
        print(f"❌ 训练进程 (PID: {PID}) 未运行")
        return
    
    # 获取最新日志目录
    log_dir = get_latest_log_dir()
    if not log_dir:
        print("未找到训练日志目录")
        return
    
    print(f"日志目录: {log_dir.name}")
    
    # 解析训练日志
    log_file = log_dir / "training.log"
    if log_file.exists():
        info = parse_training_log(log_file)
        if info:
            print(f"\n当前进度:")
            print(f"  Epoch: {info['current_epoch']}/30")
            print(f"  最新训练准确率: {info['latest_train_acc']:.2f}%")
            print(f"  最新验证准确率: {info['latest_val_acc']:.2f}%")
            print(f"  最新测试准确率: {info['latest_test_acc']:.2f}%")
            print(f"  最佳测试准确率: {info['best_accuracy']:.2f}%")
            
            if info['best_accuracy'] >= 85.0:
                print(f"\n🎉 已达到85%目标！最佳准确率: {info['best_accuracy']:.2f}%")
            else:
                gap = 85.0 - info['best_accuracy']
                print(f"\n距离85%目标还差: {gap:.2f}%")
    
    # 检查最佳模型
    best_model = log_dir / "best_model.pth"
    if best_model.exists():
        size_mb = best_model.stat().st_size / (1024 * 1024)
        print(f"\n最佳模型已保存: {size_mb:.2f} MB")
        
    # 检查结果摘要
    summary_file = log_dir / "result_summary.json"
    if summary_file.exists():
        with open(summary_file, 'r') as f:
            summary = json.load(f)
        print(f"\n训练完成！最终准确率: {summary['final_accuracy']*100:.2f}%")

if __name__ == "__main__":
    main()