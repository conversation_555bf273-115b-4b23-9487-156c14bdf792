2025-08-16 20:10:12,848 - INFO - ================================================================================
2025-08-16 20:10:12,849 - INFO - 🎯 Stage 2: 模态内特征精炼 (简化版)
2025-08-16 20:10:12,849 - INFO - 目标: ≥86% accuracy
2025-08-16 20:10:12,849 - INFO - ================================================================================
2025-08-16 20:10:12,849 - INFO - 配置: {'n_classes': 5, 'd_model': 128, 'n_heads': 4, 'dropout': 0.15, 'batch_size': 64, 'learning_rate': 0.001, 'num_epochs': 50, 'weight_decay': 0.0001, 'patience': 10}
2025-08-16 20:10:12,878 - INFO - 设备: cuda
2025-08-16 20:10:12,878 - INFO - 加载数据集...
2025-08-16 20:10:15,316 - INFO - 训练集: 28023 samples
2025-08-16 20:10:15,317 - INFO - 验证集: 5814 samples
2025-08-16 20:10:15,317 - INFO - 测试集: 8471 samples
2025-08-16 20:10:15,695 - INFO - 创建Stage2SimplifiedModel: 参数量=737,350
2025-08-16 20:10:16,892 - INFO - 
============================================================
2025-08-16 20:10:16,892 - INFO - Epoch 1/50
加载了 28023 个样本
加载了 5814 个样本
加载了 8471 个样本

Training:   0%|          | 0/438 [00:00<?, ?it/s]
Training:   0%|          | 0/438 [00:00<?, ?it/s, loss=1.6053, acc=17.19%, alpha=0.2000]
Training:   0%|          | 1/438 [00:00<06:33,  1.11it/s, loss=1.6053, acc=17.19%, alpha=0.2000]
Training:   0%|          | 1/438 [00:00<06:33,  1.11it/s, loss=1.2336, acc=41.41%, alpha=0.2000]
Training:   0%|          | 1/438 [00:00<06:33,  1.11it/s, loss=1.1437, acc=48.44%, alpha=0.2000]
Training:   0%|          | 1/438 [00:00<06:33,  1.11it/s, loss=1.1809, acc=50.39%, alpha=0.2000]
Training:   0%|          | 1/438 [00:00<06:33,  1.11it/s, loss=1.0424, acc=53.44%, alpha=0.2000]
Training:   0%|          | 1/438 [00:00<06:33,  1.11it/s, loss=1.0361, acc=55.21%, alpha=0.2000]
Training:   0%|          | 1/438 [00:01<06:33,  1.11it/s, loss=0.9299, acc=57.59%, alpha=0.2000]
Training:   2%|▏         | 7/438 [00:01<00:48,  8.93it/s, loss=0.9299, acc=57.59%, alpha=0.2000]
Training:   2%|▏         | 7/438 [00:01<00:48,  8.93it/s, loss=0.9220, acc=58.40%, alpha=0.2000]
Training:   2%|▏         | 7/438 [00:01<00:48,  8.93it/s, loss=0.8153, acc=59.55%, alpha=0.2000]
Training:   2%|▏         | 7/438 [00:01<00:48,  8.93it/s, loss=0.8538, acc=60.47%, alpha=0.2000]
Training:   2%|▏         | 7/438 [00:01<00:48,  8.93it/s, loss=0.8767, acc=61.22%, alpha=0.2000]
Training:   2%|▏         | 7/438 [00:01<00:48,  8.93it/s, loss=0.7115, acc=61.98%, alpha=0.2000]
Training:   3%|▎         | 12/438 [00:01<00:28, 15.15it/s, loss=0.7115, acc=61.98%, alpha=0.2000]
Training:   3%|▎         | 12/438 [00:01<00:28, 15.15it/s, loss=0.7862, acc=62.38%, alpha=0.2000]
Training:   3%|▎         | 12/438 [00:01<00:28, 15.15it/s, loss=0.9159, acc=62.72%, alpha=0.2000]
Training:   3%|▎         | 12/438 [00:01<00:28, 15.15it/s, loss=0.8644, acc=62.71%, alpha=0.2000]
Training:   3%|▎         | 12/438 [00:01<00:28, 15.15it/s, loss=1.0215, acc=62.50%, alpha=0.2000]
Training:   4%|▎         | 16/438 [00:01<00:21, 19.63it/s, loss=1.0215, acc=62.50%, alpha=0.2000]
Training:   4%|▎         | 16/438 [00:01<00:21, 19.63it/s, loss=0.9860, acc=62.78%, alpha=0.2000]
Training:   4%|▎         | 16/438 [00:01<00:21, 19.63it/s, loss=0.6418, acc=63.80%, alpha=0.2000]
Training:   4%|▎         | 16/438 [00:01<00:21, 19.63it/s, loss=0.8996, acc=63.98%, alpha=0.2000]
Training:   4%|▎         | 16/438 [00:01<00:21, 19.63it/s, loss=0.9519, acc=64.38%, alpha=0.2000]
Training:   5%|▍         | 20/438 [00:01<00:18, 23.14it/s, loss=0.9519, acc=64.38%, alpha=0.2000]
Training:   5%|▍         | 20/438 [00:01<00:18, 23.14it/s, loss=0.9433, acc=64.14%, alpha=0.2000]
Training:   5%|▍         | 20/438 [00:01<00:18, 23.14it/s, loss=0.7117, acc=64.56%, alpha=0.2000]
Training:   5%|▍         | 20/438 [00:01<00:18, 23.14it/s, loss=0.7978, acc=64.88%, alpha=0.2000]
Training:   5%|▍         | 20/438 [00:01<00:18, 23.14it/s, loss=0.8064, acc=65.04%, alpha=0.2000]
Training:   5%|▌         | 24/438 [00:01<00:16, 24.83it/s, loss=0.8064, acc=65.04%, alpha=0.2000]
Training:   5%|▌         | 24/438 [00:01<00:16, 24.83it/s, loss=1.0471, acc=64.62%, alpha=0.2000]
Training:   5%|▌         | 24/438 [00:01<00:16, 24.83it/s, loss=0.7587, acc=64.84%, alpha=0.2000]
Training:   5%|▌         | 24/438 [00:01<00:16, 24.83it/s, loss=0.6971, acc=65.39%, alpha=0.2000]
Training:   5%|▌         | 24/438 [00:01<00:16, 24.83it/s, loss=0.8143, acc=65.40%, alpha=0.2000]
Training:   6%|▋         | 28/438 [00:01<00:15, 27.09it/s, loss=0.8143, acc=65.40%, alpha=0.2000]
Training:   6%|▋         | 28/438 [00:01<00:15, 27.09it/s, loss=0.7570, acc=65.57%, alpha=0.2000]
Training:   6%|▋         | 28/438 [00:01<00:15, 27.09it/s, loss=0.8211, acc=65.62%, alpha=0.2000]
Training:   6%|▋         | 28/438 [00:01<00:15, 27.09it/s, loss=0.9143, acc=65.62%, alpha=0.2000]
Training:   6%|▋         | 28/438 [00:01<00:15, 27.09it/s, loss=0.8970, acc=65.43%, alpha=0.2000]
Training:   7%|▋         | 32/438 [00:01<00:14, 28.51it/s, loss=0.8970, acc=65.43%, alpha=0.2000]
Training:   7%|▋         | 32/438 [00:01<00:14, 28.51it/s, loss=0.8387, acc=65.58%, alpha=0.2000]
Training:   7%|▋         | 32/438 [00:01<00:14, 28.51it/s, loss=0.6119, acc=65.81%, alpha=0.2000]
Training:   7%|▋         | 32/438 [00:01<00:14, 28.51it/s, loss=0.8488, acc=65.71%, alpha=0.2000]
Training:   7%|▋         | 32/438 [00:01<00:14, 28.51it/s, loss=0.6389, acc=66.02%, alpha=0.2000]
Training:   8%|▊         | 36/438 [00:01<00:13, 30.12it/s, loss=0.6389, acc=66.02%, alpha=0.2000]
Training:   8%|▊         | 36/438 [00:01<00:13, 30.12it/s, loss=0.8483, acc=66.22%, alpha=0.2000]
Training:   8%|▊         | 36/438 [00:01<00:13, 30.12it/s, loss=0.7059, acc=66.45%, alpha=0.2000]
Training:   8%|▊         | 36/438 [00:01<00:13, 30.12it/s, loss=0.8804, acc=66.55%, alpha=0.2000]
Training:   8%|▊         | 36/438 [00:01<00:13, 30.12it/s, loss=0.8038, acc=66.45%, alpha=0.2000]
Training:   9%|▉         | 40/438 [00:01<00:12, 31.92it/s, loss=0.8038, acc=66.45%, alpha=0.2000]
Training:   9%|▉         | 40/438 [00:01<00:12, 31.92it/s, loss=0.6792, acc=66.54%, alpha=0.2000]
Training:   9%|▉         | 40/438 [00:02<00:12, 31.92it/s, loss=1.0629, acc=66.22%, alpha=0.2000]
Training:   9%|▉         | 40/438 [00:02<00:12, 31.92it/s, loss=0.6629, acc=66.28%, alpha=0.2000]
Training:   9%|▉         | 40/438 [00:02<00:12, 31.92it/s, loss=0.6653, acc=66.51%, alpha=0.2000]
Training:  10%|█         | 44/438 [00:02<00:12, 32.36it/s, loss=0.6653, acc=66.51%, alpha=0.2000]
Training:  10%|█         | 44/438 [00:02<00:12, 32.36it/s, loss=0.6496, acc=66.67%, alpha=0.2000]
Training:  10%|█         | 44/438 [00:02<00:12, 32.36it/s, loss=0.7841, acc=66.81%, alpha=0.2000]
Training:  10%|█         | 44/438 [00:02<00:12, 32.36it/s, loss=0.7622, acc=66.82%, alpha=0.2000]
Training:  10%|█         | 44/438 [00:02<00:12, 32.36it/s, loss=0.6619, acc=67.06%, alpha=0.2000]
Training:  11%|█         | 48/438 [00:02<00:12, 31.04it/s, loss=0.6619, acc=67.06%, alpha=0.2000]
Training:  11%|█         | 48/438 [00:02<00:12, 31.04it/s, loss=0.6875, acc=67.19%, alpha=0.2000]
Training:  11%|█         | 48/438 [00:02<00:12, 31.04it/s, loss=0.6869, acc=67.31%, alpha=0.2000]
Training:  11%|█         | 48/438 [00:02<00:12, 31.04it/s, loss=0.5890, acc=67.56%, alpha=0.2000]
Training:  11%|█         | 48/438 [00:02<00:12, 31.04it/s, loss=0.5527, acc=67.79%, alpha=0.2000]
Training:  12%|█▏        | 52/438 [00:02<00:12, 31.02it/s, loss=0.5527, acc=67.79%, alpha=0.2000]
Training:  12%|█▏        | 52/438 [00:02<00:12, 31.02it/s, loss=0.7562, acc=67.92%, alpha=0.2000]
Training:  12%|█▏        | 52/438 [00:02<00:12, 31.02it/s, loss=0.7917, acc=68.03%, alpha=0.2000]
Training:  12%|█▏        | 52/438 [00:02<00:12, 31.02it/s, loss=0.6212, acc=68.18%, alpha=0.2000]
Training:  12%|█▏        | 52/438 [00:02<00:12, 31.02it/s, loss=0.7990, acc=68.16%, alpha=0.2000]
Training:  13%|█▎        | 56/438 [00:02<00:12, 31.27it/s, loss=0.7990, acc=68.16%, alpha=0.2000]
Training:  13%|█▎        | 56/438 [00:02<00:12, 31.27it/s, loss=0.7526, acc=68.26%, alpha=0.2000]
Training:  13%|█▎        | 56/438 [00:02<00:12, 31.27it/s, loss=0.9341, acc=68.18%, alpha=0.2000]
Training:  13%|█▎        | 56/438 [00:02<00:12, 31.27it/s, loss=0.6998, acc=68.33%, alpha=0.2000]
Training:  13%|█▎        | 56/438 [00:02<00:12, 31.27it/s, loss=0.5721, acc=68.54%, alpha=0.2000]
Training:  14%|█▎        | 60/438 [00:02<00:11, 31.58it/s, loss=0.5721, acc=68.54%, alpha=0.2000]
Training:  14%|█▎        | 60/438 [00:02<00:11, 31.58it/s, loss=0.6518, acc=68.65%, alpha=0.2000]
Training:  14%|█▎        | 60/438 [00:02<00:11, 31.58it/s, loss=0.6259, acc=68.80%, alpha=0.2000]
Training:  14%|█▎        | 60/438 [00:02<00:11, 31.58it/s, loss=0.5973, acc=68.97%, alpha=0.2000]
Training:  14%|█▎        | 60/438 [00:02<00:11, 31.58it/s, loss=0.6291, acc=69.14%, alpha=0.2000]
Training:  15%|█▍        | 64/438 [00:02<00:11, 32.65it/s, loss=0.6291, acc=69.14%, alpha=0.2000]
Training:  15%|█▍        | 64/438 [00:02<00:11, 32.65it/s, loss=0.8127, acc=69.21%, alpha=0.2000]
Training:  15%|█▍        | 64/438 [00:02<00:11, 32.65it/s, loss=0.8067, acc=69.20%, alpha=0.2000]
Training:  15%|█▍        | 64/438 [00:02<00:11, 32.65it/s, loss=0.6642, acc=69.26%, alpha=0.2000]
Training:  15%|█▍        | 64/438 [00:02<00:11, 32.65it/s, loss=0.5746, acc=69.37%, alpha=0.2000]
Training:  16%|█▌        | 68/438 [00:02<00:11, 33.34it/s, loss=0.5746, acc=69.37%, alpha=0.2000]
Training:  16%|█▌        | 68/438 [00:02<00:11, 33.34it/s, loss=0.5362, acc=69.50%, alpha=0.2000]
Training:  16%|█▌        | 68/438 [00:02<00:11, 33.34it/s, loss=0.7962, acc=69.51%, alpha=0.2000]
Training:  16%|█▌        | 68/438 [00:02<00:11, 33.34it/s, loss=0.6405, acc=69.59%, alpha=0.2000]
Training:  16%|█▌        | 68/438 [00:02<00:11, 33.34it/s, loss=0.6765, acc=69.68%, alpha=0.2000]
Training:  16%|█▋        | 72/438 [00:02<00:11, 31.12it/s, loss=0.6765, acc=69.68%, alpha=0.2000]
Training:  16%|█▋        | 72/438 [00:03<00:11, 31.12it/s, loss=0.6832, acc=69.73%, alpha=0.2000]
Training:  16%|█▋        | 72/438 [00:03<00:11, 31.12it/s, loss=0.5714, acc=69.89%, alpha=0.2000]
Training:  16%|█▋        | 72/438 [00:03<00:11, 31.12it/s, loss=0.5919, acc=70.00%, alpha=0.2000]
Training:  16%|█▋        | 72/438 [00:03<00:11, 31.12it/s, loss=0.6735, acc=70.11%, alpha=0.2000]
Training:  17%|█▋        | 76/438 [00:03<00:11, 31.05it/s, loss=0.6735, acc=70.11%, alpha=0.2000]
Training:  17%|█▋        | 76/438 [00:03<00:11, 31.05it/s, loss=0.7007, acc=70.07%, alpha=0.2000]
Training:  17%|█▋        | 76/438 [00:03<00:11, 31.05it/s, loss=0.7691, acc=70.03%, alpha=0.2000]
Training:  17%|█▋        | 76/438 [00:03<00:11, 31.05it/s, loss=0.5827, acc=70.17%, alpha=0.2000]
Training:  17%|█▋        | 76/438 [00:03<00:11, 31.05it/s, loss=0.6363, acc=70.29%, alpha=0.2000]
Training:  18%|█▊        | 80/438 [00:03<00:11, 30.80it/s, loss=0.6363, acc=70.29%, alpha=0.2000]
Training:  18%|█▊        | 80/438 [00:03<00:11, 30.80it/s, loss=0.5822, acc=70.39%, alpha=0.2000]
Training:  18%|█▊        | 80/438 [00:03<00:11, 30.80it/s, loss=0.8450, acc=70.39%, alpha=0.2000]
Training:  18%|█▊        | 80/438 [00:03<00:11, 30.80it/s, loss=0.5931, acc=70.54%, alpha=0.2000]
Training:  18%|█▊        | 80/438 [00:03<00:11, 30.80it/s, loss=0.5607, acc=70.67%, alpha=0.2000]
Training:  19%|█▉        | 84/438 [00:03<00:11, 31.29it/s, loss=0.5607, acc=70.67%, alpha=0.2000]
Training:  19%|█▉        | 84/438 [00:03<00:11, 31.29it/s, loss=0.4976, acc=70.83%, alpha=0.2000]
Training:  19%|█▉        | 84/438 [00:03<00:11, 31.29it/s, loss=0.5224, acc=70.97%, alpha=0.2000]
Training:  19%|█▉        | 84/438 [00:03<00:11, 31.29it/s, loss=0.7493, acc=70.98%, alpha=0.2000]
Training:  19%|█▉        | 84/438 [00:03<00:11, 31.29it/s, loss=0.5113, acc=71.09%, alpha=0.2000]
Training:  20%|██        | 88/438 [00:03<00:10, 31.87it/s, loss=0.5113, acc=71.09%, alpha=0.2000]
Training:  20%|██        | 88/438 [00:03<00:10, 31.87it/s, loss=0.6651, acc=71.14%, alpha=0.2000]
Training:  20%|██        | 88/438 [00:03<00:10, 31.87it/s, loss=0.9179, acc=71.11%, alpha=0.2000]
Training:  20%|██        | 88/438 [00:03<00:10, 31.87it/s, loss=0.5324, acc=71.21%, alpha=0.2000]
Training:  20%|██        | 88/438 [00:03<00:10, 31.87it/s, loss=0.6516, acc=71.21%, alpha=0.2000]
Training:  21%|██        | 92/438 [00:03<00:10, 32.67it/s, loss=0.6516, acc=71.21%, alpha=0.2000]
Training:  21%|██        | 92/438 [00:03<00:10, 32.67it/s, loss=0.6706, acc=71.25%, alpha=0.2000]
Training:  21%|██        | 92/438 [00:03<00:10, 32.67it/s, loss=0.8272, acc=71.23%, alpha=0.2000]
Training:  21%|██        | 92/438 [00:03<00:10, 32.67it/s, loss=0.6013, acc=71.30%, alpha=0.2000]
Training:  21%|██        | 92/438 [00:03<00:10, 32.67it/s, loss=0.4968, acc=71.44%, alpha=0.2000]
Training:  22%|██▏       | 96/438 [00:03<00:11, 30.86it/s, loss=0.4968, acc=71.44%, alpha=0.2000]
Training:  22%|██▏       | 96/438 [00:03<00:11, 30.86it/s, loss=0.5705, acc=71.52%, alpha=0.2000]
Training:  22%|██▏       | 96/438 [00:03<00:11, 30.86it/s, loss=0.7594, acc=71.51%, alpha=0.2000]
Training:  22%|██▏       | 96/438 [00:03<00:11, 30.86it/s, loss=0.7739, acc=71.50%, alpha=0.2000]
Training:  22%|██▏       | 96/438 [00:03<00:11, 30.86it/s, loss=0.6084, acc=71.55%, alpha=0.2000]
Training:  23%|██▎       | 100/438 [00:03<00:10, 30.92it/s, loss=0.6084, acc=71.55%, alpha=0.2000]
Training:  23%|██▎       | 100/438 [00:03<00:10, 30.92it/s, loss=0.5939, acc=71.58%, alpha=0.2000]
Training:  23%|██▎       | 100/438 [00:03<00:10, 30.92it/s, loss=0.7281, acc=71.57%, alpha=0.2000]
Training:  23%|██▎       | 100/438 [00:03<00:10, 30.92it/s, loss=0.5030, acc=71.68%, alpha=0.2000]
Training:  23%|██▎       | 100/438 [00:03<00:10, 30.92it/s, loss=0.5593, acc=71.71%, alpha=0.2000]
Training:  24%|██▎       | 104/438 [00:03<00:10, 31.34it/s, loss=0.5593, acc=71.71%, alpha=0.2000]
Training:  24%|██▎       | 104/438 [00:04<00:10, 31.34it/s, loss=0.5629, acc=71.74%, alpha=0.2000]
Training:  24%|██▎       | 104/438 [00:04<00:10, 31.34it/s, loss=0.6617, acc=71.74%, alpha=0.2000]
Training:  24%|██▎       | 104/438 [00:04<00:10, 31.34it/s, loss=0.8221, acc=71.70%, alpha=0.2000]
Training:  24%|██▎       | 104/438 [00:04<00:10, 31.34it/s, loss=0.5557, acc=71.76%, alpha=0.2000]
Training:  25%|██▍       | 108/438 [00:04<00:10, 31.30it/s, loss=0.5557, acc=71.76%, alpha=0.2000]
Training:  25%|██▍       | 108/438 [00:04<00:10, 31.30it/s, loss=0.4280, acc=71.86%, alpha=0.2000]
Training:  25%|██▍       | 108/438 [00:04<00:10, 31.30it/s, loss=0.7277, acc=71.86%, alpha=0.2000]
Training:  25%|██▍       | 108/438 [00:04<00:10, 31.30it/s, loss=0.6379, acc=71.86%, alpha=0.2000]
Training:  25%|██▍       | 108/438 [00:04<00:10, 31.30it/s, loss=0.6474, acc=71.90%, alpha=0.2000]
Training:  26%|██▌       | 112/438 [00:04<00:10, 32.09it/s, loss=0.6474, acc=71.90%, alpha=0.2000]
Training:  26%|██▌       | 112/438 [00:04<00:10, 32.09it/s, loss=0.5931, acc=71.92%, alpha=0.2000]
Training:  26%|██▌       | 112/438 [00:04<00:10, 32.09it/s, loss=0.4620, acc=72.01%, alpha=0.2000]
Training:  26%|██▌       | 112/438 [00:04<00:10, 32.09it/s, loss=0.4549, acc=72.12%, alpha=0.2000]
Training:  26%|██▌       | 112/438 [00:04<00:10, 32.09it/s, loss=0.5928, acc=72.14%, alpha=0.2000]
Training:  26%|██▋       | 116/438 [00:04<00:09, 32.90it/s, loss=0.5928, acc=72.14%, alpha=0.2000]
Training:  26%|██▋       | 116/438 [00:04<00:09, 32.90it/s, loss=0.9870, acc=72.04%, alpha=0.2000]
Training:  26%|██▋       | 116/438 [00:04<00:09, 32.90it/s, loss=0.5991, acc=72.07%, alpha=0.2000]
Training:  26%|██▋       | 116/438 [00:04<00:09, 32.90it/s, loss=0.8375, acc=72.09%, alpha=0.2000]
Training:  26%|██▋       | 116/438 [00:04<00:09, 32.90it/s, loss=0.3875, acc=72.21%, alpha=0.2000]
Training:  27%|██▋       | 120/438 [00:04<00:10, 31.66it/s, loss=0.3875, acc=72.21%, alpha=0.2000]
Training:  27%|██▋       | 120/438 [00:04<00:10, 31.66it/s, loss=0.5784, acc=72.26%, alpha=0.2000]
Training:  27%|██▋       | 120/438 [00:04<00:10, 31.66it/s, loss=0.8384, acc=72.30%, alpha=0.2000]
Training:  27%|██▋       | 120/438 [00:04<00:10, 31.66it/s, loss=0.6491, acc=72.33%, alpha=0.2000]
Training:  27%|██▋       | 120/438 [00:04<00:10, 31.66it/s, loss=0.5892, acc=72.37%, alpha=0.2000]
Training:  28%|██▊       | 124/438 [00:04<00:09, 32.38it/s, loss=0.5892, acc=72.37%, alpha=0.2000]
Training:  28%|██▊       | 124/438 [00:04<00:09, 32.38it/s, loss=0.5984, acc=72.40%, alpha=0.2000]
Training:  28%|██▊       | 124/438 [00:04<00:09, 32.38it/s, loss=0.6491, acc=72.43%, alpha=0.2000]
Training:  28%|██▊       | 124/438 [00:04<00:09, 32.38it/s, loss=0.6580, acc=72.49%, alpha=0.2000]
Training:  28%|██▊       | 124/438 [00:04<00:09, 32.38it/s, loss=0.5575, acc=72.55%, alpha=0.2000]
Training:  29%|██▉       | 128/438 [00:04<00:09, 32.16it/s, loss=0.5575, acc=72.55%, alpha=0.2000]
Training:  29%|██▉       | 128/438 [00:04<00:09, 32.16it/s, loss=0.6572, acc=72.54%, alpha=0.2000]
Training:  29%|██▉       | 128/438 [00:04<00:09, 32.16it/s, loss=0.5827, acc=72.61%, alpha=0.2000]
Training:  29%|██▉       | 128/438 [00:04<00:09, 32.16it/s, loss=0.4412, acc=72.70%, alpha=0.2000]
Training:  29%|██▉       | 128/438 [00:04<00:09, 32.16it/s, loss=0.6570, acc=72.68%, alpha=0.2000]
Training:  30%|███       | 132/438 [00:04<00:09, 31.95it/s, loss=0.6570, acc=72.68%, alpha=0.2000]
Training:  30%|███       | 132/438 [00:04<00:09, 31.95it/s, loss=0.5606, acc=72.74%, alpha=0.2000]
Training:  30%|███       | 132/438 [00:04<00:09, 31.95it/s, loss=0.7451, acc=72.75%, alpha=0.2000]
Training:  30%|███       | 132/438 [00:04<00:09, 31.95it/s, loss=0.5771, acc=72.80%, alpha=0.2000]
Training:  30%|███       | 132/438 [00:04<00:09, 31.95it/s, loss=0.7761, acc=72.78%, alpha=0.2000]
Training:  31%|███       | 136/438 [00:04<00:09, 32.84it/s, loss=0.7761, acc=72.78%, alpha=0.2000]
Training:  31%|███       | 136/438 [00:05<00:09, 32.84it/s, loss=0.5497, acc=72.86%, alpha=0.2000]
Training:  31%|███       | 136/438 [00:05<00:09, 32.84it/s, loss=0.7064, acc=72.87%, alpha=0.2000]
Training:  31%|███       | 136/438 [00:05<00:09, 32.84it/s, loss=0.8200, acc=72.85%, alpha=0.2000]
Training:  31%|███       | 136/438 [00:05<00:09, 32.84it/s, loss=0.7187, acc=72.86%, alpha=0.2000]
Training:  32%|███▏      | 140/438 [00:05<00:09, 32.99it/s, loss=0.7187, acc=72.86%, alpha=0.2000]
Training:  32%|███▏      | 140/438 [00:05<00:09, 32.99it/s, loss=0.6047, acc=72.88%, alpha=0.2000]
Training:  32%|███▏      | 140/438 [00:05<00:09, 32.99it/s, loss=0.6582, acc=72.89%, alpha=0.2000]
Training:  32%|███▏      | 140/438 [00:05<00:09, 32.99it/s, loss=0.6377, acc=72.91%, alpha=0.2000]
Training:  32%|███▏      | 140/438 [00:05<00:09, 32.99it/s, loss=0.5523, acc=72.99%, alpha=0.2000]
Training:  33%|███▎      | 144/438 [00:05<00:08, 33.06it/s, loss=0.5523, acc=72.99%, alpha=0.2000]
Training:  33%|███▎      | 144/438 [00:05<00:08, 33.06it/s, loss=0.4851, acc=73.06%, alpha=0.2000]
Training:  33%|███▎      | 144/438 [00:05<00:08, 33.06it/s, loss=0.7166, acc=73.05%, alpha=0.2000]
Training:  33%|███▎      | 144/438 [00:05<00:08, 33.06it/s, loss=0.5465, acc=73.10%, alpha=0.2000]
Training:  33%|███▎      | 144/438 [00:05<00:08, 33.06it/s, loss=0.6066, acc=73.11%, alpha=0.2000]
Training:  34%|███▍      | 148/438 [00:05<00:08, 32.63it/s, loss=0.6066, acc=73.11%, alpha=0.2000]
Training:  34%|███▍      | 148/438 [00:05<00:08, 32.63it/s, loss=0.4855, acc=73.18%, alpha=0.2000]
Training:  34%|███▍      | 148/438 [00:05<00:08, 32.63it/s, loss=0.5731, acc=73.24%, alpha=0.2000]
Training:  34%|███▍      | 148/438 [00:05<00:08, 32.63it/s, loss=0.6913, acc=73.22%, alpha=0.2000]
Training:  34%|███▍      | 148/438 [00:05<00:08, 32.63it/s, loss=0.6709, acc=73.24%, alpha=0.2000]
Training:  35%|███▍      | 152/438 [00:05<00:08, 32.96it/s, loss=0.6709, acc=73.24%, alpha=0.2000]
Training:  35%|███▍      | 152/438 [00:05<00:08, 32.96it/s, loss=0.5867, acc=73.29%, alpha=0.2000]
Training:  35%|███▍      | 152/438 [00:05<00:08, 32.96it/s, loss=0.5685, acc=73.29%, alpha=0.2000]
Training:  35%|███▍      | 152/438 [00:05<00:08, 32.96it/s, loss=0.5806, acc=73.31%, alpha=0.2000]
Training:  35%|███▍      | 152/438 [00:05<00:08, 32.96it/s, loss=0.5684, acc=73.37%, alpha=0.2000]
Training:  36%|███▌      | 156/438 [00:05<00:08, 32.93it/s, loss=0.5684, acc=73.37%, alpha=0.2000]
Training:  36%|███▌      | 156/438 [00:05<00:08, 32.93it/s, loss=0.4238, acc=73.44%, alpha=0.2000]
Training:  36%|███▌      | 156/438 [00:05<00:08, 32.93it/s, loss=0.7393, acc=73.47%, alpha=0.2000]
Training:  36%|███▌      | 156/438 [00:05<00:08, 32.93it/s, loss=0.6543, acc=73.50%, alpha=0.2000]
Training:  36%|███▌      | 156/438 [00:05<00:08, 32.93it/s, loss=0.5853, acc=73.50%, alpha=0.2000]
Training:  37%|███▋      | 160/438 [00:05<00:08, 33.11it/s, loss=0.5853, acc=73.50%, alpha=0.2000]
Training:  37%|███▋      | 160/438 [00:05<00:08, 33.11it/s, loss=0.7110, acc=73.52%, alpha=0.2000]
Training:  37%|███▋      | 160/438 [00:05<00:08, 33.11it/s, loss=0.6054, acc=73.57%, alpha=0.2000]
Training:  37%|███▋      | 160/438 [00:05<00:08, 33.11it/s, loss=0.6476, acc=73.60%, alpha=0.2000]
Training:  37%|███▋      | 160/438 [00:05<00:08, 33.11it/s, loss=0.4609, acc=73.65%, alpha=0.2000]
Training:  37%|███▋      | 164/438 [00:05<00:08, 33.88it/s, loss=0.4609, acc=73.65%, alpha=0.2000]
Training:  37%|███▋      | 164/438 [00:05<00:08, 33.88it/s, loss=0.4696, acc=73.68%, alpha=0.2000]
Training:  37%|███▋      | 164/438 [00:05<00:08, 33.88it/s, loss=0.6581, acc=73.71%, alpha=0.2000]
Training:  37%|███▋      | 164/438 [00:05<00:08, 33.88it/s, loss=0.6971, acc=73.69%, alpha=0.2000]
Training:  37%|███▋      | 164/438 [00:05<00:08, 33.88it/s, loss=0.6135, acc=73.73%, alpha=0.2000]
Training:  38%|███▊      | 168/438 [00:05<00:07, 34.20it/s, loss=0.6135, acc=73.73%, alpha=0.2000]
Training:  38%|███▊      | 168/438 [00:05<00:07, 34.20it/s, loss=0.3747, acc=73.80%, alpha=0.2000]
Training:  38%|███▊      | 168/438 [00:05<00:07, 34.20it/s, loss=0.6614, acc=73.80%, alpha=0.2000]
Training:  38%|███▊      | 168/438 [00:06<00:07, 34.20it/s, loss=0.7245, acc=73.81%, alpha=0.2000]
Training:  38%|███▊      | 168/438 [00:06<00:07, 34.20it/s, loss=0.6181, acc=73.85%, alpha=0.2000]
Training:  39%|███▉      | 172/438 [00:06<00:07, 33.27it/s, loss=0.6181, acc=73.85%, alpha=0.2000]
Training:  39%|███▉      | 172/438 [00:06<00:07, 33.27it/s, loss=0.4142, acc=73.89%, alpha=0.2000]
Training:  39%|███▉      | 172/438 [00:06<00:07, 33.27it/s, loss=0.3220, acc=73.97%, alpha=0.2000]
Training:  39%|███▉      | 172/438 [00:06<00:07, 33.27it/s, loss=0.6918, acc=73.98%, alpha=0.2000]
Training:  39%|███▉      | 172/438 [00:06<00:07, 33.27it/s, loss=0.5075, acc=74.05%, alpha=0.2000]
Training:  40%|████      | 176/438 [00:06<00:08, 32.64it/s, loss=0.5075, acc=74.05%, alpha=0.2000]
Training:  40%|████      | 176/438 [00:06<00:08, 32.64it/s, loss=0.6324, acc=74.05%, alpha=0.2000]
Training:  40%|████      | 176/438 [00:06<00:08, 32.64it/s, loss=0.5899, acc=74.07%, alpha=0.2000]
Training:  40%|████      | 176/438 [00:06<00:08, 32.64it/s, loss=0.8108, acc=74.04%, alpha=0.2000]
Training:  40%|████      | 176/438 [00:06<00:08, 32.64it/s, loss=0.7075, acc=74.05%, alpha=0.2000]
Training:  41%|████      | 180/438 [00:06<00:07, 32.45it/s, loss=0.7075, acc=74.05%, alpha=0.2000]
Training:  41%|████      | 180/438 [00:06<00:07, 32.45it/s, loss=0.6402, acc=74.10%, alpha=0.2000]
Training:  41%|████      | 180/438 [00:06<00:07, 32.45it/s, loss=0.6189, acc=74.12%, alpha=0.2000]
Training:  41%|████      | 180/438 [00:06<00:07, 32.45it/s, loss=0.4695, acc=74.19%, alpha=0.2000]
Training:  41%|████      | 180/438 [00:06<00:07, 32.45it/s, loss=0.5327, acc=74.24%, alpha=0.2000]
Training:  42%|████▏     | 184/438 [00:06<00:08, 31.52it/s, loss=0.5327, acc=74.24%, alpha=0.2000]
Training:  42%|████▏     | 184/438 [00:06<00:08, 31.52it/s, loss=0.5963, acc=74.27%, alpha=0.2000]
Training:  42%|████▏     | 184/438 [00:06<00:08, 31.52it/s, loss=0.4183, acc=74.31%, alpha=0.2000]
Training:  42%|████▏     | 184/438 [00:06<00:08, 31.52it/s, loss=0.5354, acc=74.33%, alpha=0.2000]
Training:  42%|████▏     | 184/438 [00:06<00:08, 31.52it/s, loss=0.4749, acc=74.38%, alpha=0.2000]
Training:  43%|████▎     | 188/438 [00:06<00:07, 32.62it/s, loss=0.4749, acc=74.38%, alpha=0.2000]
Training:  43%|████▎     | 188/438 [00:06<00:07, 32.62it/s, loss=0.5486, acc=74.42%, alpha=0.2000]
Training:  43%|████▎     | 188/438 [00:06<00:07, 32.62it/s, loss=0.5545, acc=74.43%, alpha=0.2000]
Training:  43%|████▎     | 188/438 [00:06<00:07, 32.62it/s, loss=0.6287, acc=74.45%, alpha=0.2000]
Training:  43%|████▎     | 188/438 [00:06<00:07, 32.62it/s, loss=0.4212, acc=74.50%, alpha=0.2000]
Training:  44%|████▍     | 192/438 [00:06<00:07, 33.60it/s, loss=0.4212, acc=74.50%, alpha=0.2000]
Training:  44%|████▍     | 192/438 [00:06<00:07, 33.60it/s, loss=0.6780, acc=74.51%, alpha=0.2000]
Training:  44%|████▍     | 192/438 [00:06<00:07, 33.60it/s, loss=0.6879, acc=74.52%, alpha=0.2000]
Training:  44%|████▍     | 192/438 [00:06<00:07, 33.60it/s, loss=0.5970, acc=74.54%, alpha=0.2000]
Training:  44%|████▍     | 192/438 [00:06<00:07, 33.60it/s, loss=0.4599, acc=74.59%, alpha=0.2000]
Training:  45%|████▍     | 196/438 [00:06<00:07, 33.38it/s, loss=0.4599, acc=74.59%, alpha=0.2000]
Training:  45%|████▍     | 196/438 [00:06<00:07, 33.38it/s, loss=0.5268, acc=74.64%, alpha=0.2000]
Training:  45%|████▍     | 196/438 [00:06<00:07, 33.38it/s, loss=0.5453, acc=74.64%, alpha=0.2000]
Training:  45%|████▍     | 196/438 [00:06<00:07, 33.38it/s, loss=0.6767, acc=74.68%, alpha=0.2000]
Training:  45%|████▍     | 196/438 [00:06<00:07, 33.38it/s, loss=0.6378, acc=74.67%, alpha=0.2000]
Training:  46%|████▌     | 200/438 [00:06<00:07, 32.47it/s, loss=0.6378, acc=74.67%, alpha=0.2000]
Training:  46%|████▌     | 200/438 [00:06<00:07, 32.47it/s, loss=0.9525, acc=74.63%, alpha=0.2000]
Training:  46%|████▌     | 200/438 [00:06<00:07, 32.47it/s, loss=0.5556, acc=74.65%, alpha=0.2000]
Training:  46%|████▌     | 200/438 [00:07<00:07, 32.47it/s, loss=0.5787, acc=74.67%, alpha=0.2000]
Training:  46%|████▌     | 200/438 [00:07<00:07, 32.47it/s, loss=0.5914, acc=74.67%, alpha=0.2000]
Training:  47%|████▋     | 204/438 [00:07<00:07, 31.65it/s, loss=0.5914, acc=74.67%, alpha=0.2000]
Training:  47%|████▋     | 204/438 [00:07<00:07, 31.65it/s, loss=0.5371, acc=74.70%, alpha=0.2000]
Training:  47%|████▋     | 204/438 [00:07<00:07, 31.65it/s, loss=0.5771, acc=74.70%, alpha=0.2000]
Training:  47%|████▋     | 204/438 [00:07<00:07, 31.65it/s, loss=0.4772, acc=74.74%, alpha=0.2000]
Training:  47%|████▋     | 204/438 [00:07<00:07, 31.65it/s, loss=0.4478, acc=74.77%, alpha=0.2000]
Training:  47%|████▋     | 208/438 [00:07<00:06, 32.88it/s, loss=0.4478, acc=74.77%, alpha=0.2000]
Training:  47%|████▋     | 208/438 [00:07<00:06, 32.88it/s, loss=0.5956, acc=74.78%, alpha=0.2000]
Training:  47%|████▋     | 208/438 [00:07<00:06, 32.88it/s, loss=0.5367, acc=74.79%, alpha=0.2000]
Training:  47%|████▋     | 208/438 [00:07<00:06, 32.88it/s, loss=0.6151, acc=74.80%, alpha=0.2000]
Training:  47%|████▋     | 208/438 [00:07<00:06, 32.88it/s, loss=0.6022, acc=74.79%, alpha=0.2000]
Training:  48%|████▊     | 212/438 [00:07<00:06, 33.80it/s, loss=0.6022, acc=74.79%, alpha=0.2000]
Training:  48%|████▊     | 212/438 [00:07<00:06, 33.80it/s, loss=0.4506, acc=74.84%, alpha=0.2000]
Training:  48%|████▊     | 212/438 [00:07<00:06, 33.80it/s, loss=0.4561, acc=74.88%, alpha=0.2000]
Training:  48%|████▊     | 212/438 [00:07<00:06, 33.80it/s, loss=0.6012, acc=74.91%, alpha=0.2000]
Training:  48%|████▊     | 212/438 [00:07<00:06, 33.80it/s, loss=0.9345, acc=74.88%, alpha=0.2000]
Training:  49%|████▉     | 216/438 [00:07<00:06, 34.12it/s, loss=0.9345, acc=74.88%, alpha=0.2000]
Training:  49%|████▉     | 216/438 [00:07<00:06, 34.12it/s, loss=0.6376, acc=74.89%, alpha=0.2000]
Training:  49%|████▉     | 216/438 [00:07<00:06, 34.12it/s, loss=0.5531, acc=74.90%, alpha=0.2000]
Training:  49%|████▉     | 216/438 [00:07<00:06, 34.12it/s, loss=0.7082, acc=74.89%, alpha=0.2000]
Training:  49%|████▉     | 216/438 [00:07<00:06, 34.12it/s, loss=0.6647, acc=74.89%, alpha=0.2000]
Training:  50%|█████     | 220/438 [00:07<00:06, 34.04it/s, loss=0.6647, acc=74.89%, alpha=0.2000]
Training:  50%|█████     | 220/438 [00:07<00:06, 34.04it/s, loss=0.4644, acc=74.94%, alpha=0.2000]
Training:  50%|█████     | 220/438 [00:07<00:06, 34.04it/s, loss=0.6336, acc=74.96%, alpha=0.2000]
Training:  50%|█████     | 220/438 [00:07<00:06, 34.04it/s, loss=0.5823, acc=74.96%, alpha=0.2000]
Training:  50%|█████     | 220/438 [00:07<00:06, 34.04it/s, loss=0.5461, acc=74.98%, alpha=0.2000]
Training:  51%|█████     | 224/438 [00:07<00:06, 32.70it/s, loss=0.5461, acc=74.98%, alpha=0.2000]
Training:  51%|█████     | 224/438 [00:07<00:06, 32.70it/s, loss=0.5041, acc=74.99%, alpha=0.2000]
Training:  51%|█████     | 224/438 [00:07<00:06, 32.70it/s, loss=0.4533, acc=75.03%, alpha=0.2000]
Training:  51%|█████     | 224/438 [00:07<00:06, 32.70it/s, loss=0.3923, acc=75.09%, alpha=0.2000]
Training:  51%|█████     | 224/438 [00:07<00:06, 32.70it/s, loss=0.3852, acc=75.12%, alpha=0.2000]
Training:  52%|█████▏    | 228/438 [00:07<00:06, 32.16it/s, loss=0.3852, acc=75.12%, alpha=0.2000]
Training:  52%|█████▏    | 228/438 [00:07<00:06, 32.16it/s, loss=0.6537, acc=75.12%, alpha=0.2000]
Training:  52%|█████▏    | 228/438 [00:07<00:06, 32.16it/s, loss=0.6236, acc=75.12%, alpha=0.2000]
Training:  52%|█████▏    | 228/438 [00:07<00:06, 32.16it/s, loss=0.7690, acc=75.12%, alpha=0.2000]
Training:  52%|█████▏    | 228/438 [00:07<00:06, 32.16it/s, loss=0.4697, acc=75.18%, alpha=0.2000]
Training:  53%|█████▎    | 232/438 [00:07<00:06, 32.00it/s, loss=0.4697, acc=75.18%, alpha=0.2000]
Training:  53%|█████▎    | 232/438 [00:07<00:06, 32.00it/s, loss=0.6528, acc=75.19%, alpha=0.2000]
Training:  53%|█████▎    | 232/438 [00:07<00:06, 32.00it/s, loss=0.7756, acc=75.19%, alpha=0.2000]
Training:  53%|█████▎    | 232/438 [00:07<00:06, 32.00it/s, loss=0.4885, acc=75.20%, alpha=0.2000]
Training:  53%|█████▎    | 232/438 [00:08<00:06, 32.00it/s, loss=0.6577, acc=75.19%, alpha=0.2000]
Training:  54%|█████▍    | 236/438 [00:08<00:06, 32.15it/s, loss=0.6577, acc=75.19%, alpha=0.2000]
Training:  54%|█████▍    | 236/438 [00:08<00:06, 32.15it/s, loss=0.5144, acc=75.23%, alpha=0.2000]
Training:  54%|█████▍    | 236/438 [00:08<00:06, 32.15it/s, loss=0.5976, acc=75.22%, alpha=0.2000]
Training:  54%|█████▍    | 236/438 [00:08<00:06, 32.15it/s, loss=0.6246, acc=75.22%, alpha=0.2000]
Training:  54%|█████▍    | 236/438 [00:08<00:06, 32.15it/s, loss=0.6194, acc=75.21%, alpha=0.2000]
Training:  55%|█████▍    | 240/438 [00:08<00:06, 32.95it/s, loss=0.6194, acc=75.21%, alpha=0.2000]
Training:  55%|█████▍    | 240/438 [00:08<00:06, 32.95it/s, loss=0.6887, acc=75.20%, alpha=0.2000]
Training:  55%|█████▍    | 240/438 [00:08<00:06, 32.95it/s, loss=0.5662, acc=75.22%, alpha=0.2000]
Training:  55%|█████▍    | 240/438 [00:08<00:06, 32.95it/s, loss=0.5628, acc=75.23%, alpha=0.2000]
Training:  55%|█████▍    | 240/438 [00:08<00:06, 32.95it/s, loss=0.4692, acc=75.28%, alpha=0.2000]
Training:  56%|█████▌    | 244/438 [00:08<00:05, 32.83it/s, loss=0.4692, acc=75.28%, alpha=0.2000]
Training:  56%|█████▌    | 244/438 [00:08<00:05, 32.83it/s, loss=0.5280, acc=75.29%, alpha=0.2000]
Training:  56%|█████▌    | 244/438 [00:08<00:05, 32.83it/s, loss=0.5638, acc=75.30%, alpha=0.2000]
Training:  56%|█████▌    | 244/438 [00:08<00:05, 32.83it/s, loss=0.4767, acc=75.32%, alpha=0.2000]
Training:  56%|█████▌    | 244/438 [00:08<00:05, 32.83it/s, loss=0.8003, acc=75.30%, alpha=0.2000]
Training:  57%|█████▋    | 248/438 [00:08<00:05, 31.97it/s, loss=0.8003, acc=75.30%, alpha=0.2000]
Training:  57%|█████▋    | 248/438 [00:08<00:05, 31.97it/s, loss=0.6094, acc=75.30%, alpha=0.2000]
Training:  57%|█████▋    | 248/438 [00:08<00:05, 31.97it/s, loss=0.5344, acc=75.33%, alpha=0.2000]
Training:  57%|█████▋    | 248/438 [00:08<00:05, 31.97it/s, loss=0.5814, acc=75.31%, alpha=0.2000]
Training:  57%|█████▋    | 248/438 [00:08<00:05, 31.97it/s, loss=0.4878, acc=75.33%, alpha=0.2000]
Training:  58%|█████▊    | 252/438 [00:08<00:05, 31.57it/s, loss=0.4878, acc=75.33%, alpha=0.2000]
Training:  58%|█████▊    | 252/438 [00:08<00:05, 31.57it/s, loss=0.5206, acc=75.36%, alpha=0.2000]
Training:  58%|█████▊    | 252/438 [00:08<00:05, 31.57it/s, loss=0.5688, acc=75.38%, alpha=0.2000]
Training:  58%|█████▊    | 252/438 [00:08<00:05, 31.57it/s, loss=0.4946, acc=75.40%, alpha=0.2000]
Training:  58%|█████▊    | 252/438 [00:08<00:05, 31.57it/s, loss=0.4447, acc=75.45%, alpha=0.2000]
Training:  58%|█████▊    | 256/438 [00:08<00:05, 31.11it/s, loss=0.4447, acc=75.45%, alpha=0.2000]
Training:  58%|█████▊    | 256/438 [00:08<00:05, 31.11it/s, loss=0.3901, acc=75.50%, alpha=0.2000]
Training:  58%|█████▊    | 256/438 [00:08<00:05, 31.11it/s, loss=0.4005, acc=75.54%, alpha=0.2000]
Training:  58%|█████▊    | 256/438 [00:08<00:05, 31.11it/s, loss=0.6841, acc=75.53%, alpha=0.2000]
Training:  58%|█████▊    | 256/438 [00:08<00:05, 31.11it/s, loss=0.7175, acc=75.54%, alpha=0.2000]
Training:  59%|█████▉    | 260/438 [00:08<00:05, 31.23it/s, loss=0.7175, acc=75.54%, alpha=0.2000]
Training:  59%|█████▉    | 260/438 [00:08<00:05, 31.23it/s, loss=0.5907, acc=75.53%, alpha=0.2000]
Training:  59%|█████▉    | 260/438 [00:08<00:05, 31.23it/s, loss=0.7950, acc=75.51%, alpha=0.2000]
Training:  59%|█████▉    | 260/438 [00:08<00:05, 31.23it/s, loss=0.6073, acc=75.50%, alpha=0.2000]
Training:  59%|█████▉    | 260/438 [00:08<00:05, 31.23it/s, loss=0.5063, acc=75.51%, alpha=0.2000]
Training:  60%|██████    | 264/438 [00:08<00:05, 32.91it/s, loss=0.5063, acc=75.51%, alpha=0.2000]
Training:  60%|██████    | 264/438 [00:08<00:05, 32.91it/s, loss=0.4626, acc=75.52%, alpha=0.2000]
Training:  60%|██████    | 264/438 [00:08<00:05, 32.91it/s, loss=0.6211, acc=75.53%, alpha=0.2000]
Training:  60%|██████    | 264/438 [00:08<00:05, 32.91it/s, loss=0.5145, acc=75.55%, alpha=0.2000]
Training:  60%|██████    | 264/438 [00:08<00:05, 32.91it/s, loss=0.6436, acc=75.54%, alpha=0.2000]
Training:  60%|██████    | 264/438 [00:09<00:05, 32.91it/s, loss=0.4690, acc=75.56%, alpha=0.2000]
Training:  61%|██████▏   | 269/438 [00:09<00:04, 35.63it/s, loss=0.4690, acc=75.56%, alpha=0.2000]
Training:  61%|██████▏   | 269/438 [00:09<00:04, 35.63it/s, loss=0.8265, acc=75.55%, alpha=0.2000]
Training:  61%|██████▏   | 269/438 [00:09<00:04, 35.63it/s, loss=0.6171, acc=75.56%, alpha=0.2000]
Training:  61%|██████▏   | 269/438 [00:09<00:04, 35.63it/s, loss=0.4415, acc=75.58%, alpha=0.2000]
Training:  61%|██████▏   | 269/438 [00:09<00:04, 35.63it/s, loss=0.5602, acc=75.58%, alpha=0.2000]
Training:  62%|██████▏   | 273/438 [00:09<00:04, 35.42it/s, loss=0.5602, acc=75.58%, alpha=0.2000]
Training:  62%|██████▏   | 273/438 [00:09<00:04, 35.42it/s, loss=0.4333, acc=75.61%, alpha=0.2000]
Training:  62%|██████▏   | 273/438 [00:09<00:04, 35.42it/s, loss=0.6099, acc=75.60%, alpha=0.2000]
Training:  62%|██████▏   | 273/438 [00:09<00:04, 35.42it/s, loss=0.5815, acc=75.61%, alpha=0.2000]
Training:  62%|██████▏   | 273/438 [00:09<00:04, 35.42it/s, loss=0.5610, acc=75.63%, alpha=0.2000]
Training:  63%|██████▎   | 277/438 [00:09<00:04, 34.48it/s, loss=0.5610, acc=75.63%, alpha=0.2000]
Training:  63%|██████▎   | 277/438 [00:09<00:04, 34.48it/s, loss=0.4388, acc=75.65%, alpha=0.2000]
Training:  63%|██████▎   | 277/438 [00:09<00:04, 34.48it/s, loss=0.6815, acc=75.65%, alpha=0.2000]
Training:  63%|██████▎   | 277/438 [00:09<00:04, 34.48it/s, loss=0.4263, acc=75.70%, alpha=0.2000]
Training:  63%|██████▎   | 277/438 [00:09<00:04, 34.48it/s, loss=0.5836, acc=75.71%, alpha=0.2000]
Training:  64%|██████▍   | 281/438 [00:09<00:04, 33.39it/s, loss=0.5836, acc=75.71%, alpha=0.2000]
Training:  64%|██████▍   | 281/438 [00:09<00:04, 33.39it/s, loss=0.6261, acc=75.71%, alpha=0.2000]
Training:  64%|██████▍   | 281/438 [00:09<00:04, 33.39it/s, loss=0.3519, acc=75.74%, alpha=0.2000]
Training:  64%|██████▍   | 281/438 [00:09<00:04, 33.39it/s, loss=0.4879, acc=75.76%, alpha=0.2000]
Training:  64%|██████▍   | 281/438 [00:09<00:04, 33.39it/s, loss=0.5052, acc=75.77%, alpha=0.2000]
Training:  65%|██████▌   | 285/438 [00:09<00:04, 33.23it/s, loss=0.5052, acc=75.77%, alpha=0.2000]
Training:  65%|██████▌   | 285/438 [00:09<00:04, 33.23it/s, loss=0.7971, acc=75.76%, alpha=0.2000]
Training:  65%|██████▌   | 285/438 [00:09<00:04, 33.23it/s, loss=0.5079, acc=75.75%, alpha=0.2000]
Training:  65%|██████▌   | 285/438 [00:09<00:04, 33.23it/s, loss=0.6034, acc=75.76%, alpha=0.2000]
Training:  65%|██████▌   | 285/438 [00:09<00:04, 33.23it/s, loss=0.6820, acc=75.76%, alpha=0.2000]
Training:  66%|██████▌   | 289/438 [00:09<00:04, 33.01it/s, loss=0.6820, acc=75.76%, alpha=0.2000]
Training:  66%|██████▌   | 289/438 [00:09<00:04, 33.01it/s, loss=0.5324, acc=75.79%, alpha=0.2000]
Training:  66%|██████▌   | 289/438 [00:09<00:04, 33.01it/s, loss=0.5070, acc=75.80%, alpha=0.2000]
Training:  66%|██████▌   | 289/438 [00:09<00:04, 33.01it/s, loss=0.5191, acc=75.81%, alpha=0.2000]
Training:  66%|██████▌   | 289/438 [00:09<00:04, 33.01it/s, loss=0.4809, acc=75.84%, alpha=0.2000]
Training:  67%|██████▋   | 293/438 [00:09<00:04, 34.19it/s, loss=0.4809, acc=75.84%, alpha=0.2000]
Training:  67%|██████▋   | 293/438 [00:09<00:04, 34.19it/s, loss=0.6251, acc=75.84%, alpha=0.2000]
Training:  67%|██████▋   | 293/438 [00:09<00:04, 34.19it/s, loss=0.4788, acc=75.85%, alpha=0.2000]
Training:  67%|██████▋   | 293/438 [00:09<00:04, 34.19it/s, loss=0.7303, acc=75.83%, alpha=0.2000]
Training:  67%|██████▋   | 293/438 [00:09<00:04, 34.19it/s, loss=0.8126, acc=75.81%, alpha=0.2000]
Training:  68%|██████▊   | 297/438 [00:09<00:04, 32.97it/s, loss=0.8126, acc=75.81%, alpha=0.2000]
Training:  68%|██████▊   | 297/438 [00:09<00:04, 32.97it/s, loss=0.6367, acc=75.82%, alpha=0.2000]
Training:  68%|██████▊   | 297/438 [00:09<00:04, 32.97it/s, loss=0.6503, acc=75.82%, alpha=0.2000]
Training:  68%|██████▊   | 297/438 [00:09<00:04, 32.97it/s, loss=0.4213, acc=75.84%, alpha=0.2000]
Training:  68%|██████▊   | 297/438 [00:09<00:04, 32.97it/s, loss=0.6091, acc=75.86%, alpha=0.2000]
Training:  69%|██████▊   | 301/438 [00:09<00:04, 33.31it/s, loss=0.6091, acc=75.86%, alpha=0.2000]
Training:  69%|██████▊   | 301/438 [00:09<00:04, 33.31it/s, loss=0.6372, acc=75.86%, alpha=0.2000]
Training:  69%|██████▊   | 301/438 [00:10<00:04, 33.31it/s, loss=0.4517, acc=75.88%, alpha=0.2000]
Training:  69%|██████▊   | 301/438 [00:10<00:04, 33.31it/s, loss=0.4640, acc=75.90%, alpha=0.2000]
Training:  69%|██████▊   | 301/438 [00:10<00:04, 33.31it/s, loss=0.6507, acc=75.91%, alpha=0.2000]
Training:  70%|██████▉   | 305/438 [00:10<00:04, 32.91it/s, loss=0.6507, acc=75.91%, alpha=0.2000]
Training:  70%|██████▉   | 305/438 [00:10<00:04, 32.91it/s, loss=0.6727, acc=75.90%, alpha=0.2000]
Training:  70%|██████▉   | 305/438 [00:10<00:04, 32.91it/s, loss=0.6927, acc=75.89%, alpha=0.2000]
Training:  70%|██████▉   | 305/438 [00:10<00:04, 32.91it/s, loss=0.6326, acc=75.90%, alpha=0.2000]
Training:  70%|██████▉   | 305/438 [00:10<00:04, 32.91it/s, loss=0.3804, acc=75.95%, alpha=0.2000]
Training:  71%|███████   | 309/438 [00:10<00:03, 32.61it/s, loss=0.3804, acc=75.95%, alpha=0.2000]
Training:  71%|███████   | 309/438 [00:10<00:03, 32.61it/s, loss=0.5117, acc=75.96%, alpha=0.2000]
Training:  71%|███████   | 309/438 [00:10<00:03, 32.61it/s, loss=0.4821, acc=75.96%, alpha=0.2000]
Training:  71%|███████   | 309/438 [00:10<00:03, 32.61it/s, loss=0.7011, acc=75.94%, alpha=0.2000]
Training:  71%|███████   | 309/438 [00:10<00:03, 32.61it/s, loss=0.5527, acc=75.95%, alpha=0.2000]
Training:  71%|███████▏  | 313/438 [00:10<00:03, 32.50it/s, loss=0.5527, acc=75.95%, alpha=0.2000]
Training:  71%|███████▏  | 313/438 [00:10<00:03, 32.50it/s, loss=0.5348, acc=75.98%, alpha=0.2000]
Training:  71%|███████▏  | 313/438 [00:10<00:03, 32.50it/s, loss=0.5487, acc=75.99%, alpha=0.2000]
Training:  71%|███████▏  | 313/438 [00:10<00:03, 32.50it/s, loss=0.6795, acc=76.01%, alpha=0.2000]
Training:  71%|███████▏  | 313/438 [00:10<00:03, 32.50it/s, loss=0.7703, acc=76.00%, alpha=0.2000]
Training:  72%|███████▏  | 317/438 [00:10<00:03, 33.32it/s, loss=0.7703, acc=76.00%, alpha=0.2000]
Training:  72%|███████▏  | 317/438 [00:10<00:03, 33.32it/s, loss=0.6455, acc=76.01%, alpha=0.2000]
Training:  72%|███████▏  | 317/438 [00:10<00:03, 33.32it/s, loss=0.6081, acc=76.02%, alpha=0.2000]
Training:  72%|███████▏  | 317/438 [00:10<00:03, 33.32it/s, loss=0.5501, acc=76.02%, alpha=0.2000]
Training:  72%|███████▏  | 317/438 [00:10<00:03, 33.32it/s, loss=0.4043, acc=76.05%, alpha=0.2000]
Training:  73%|███████▎  | 321/438 [00:10<00:03, 33.83it/s, loss=0.4043, acc=76.05%, alpha=0.2000]
Training:  73%|███████▎  | 321/438 [00:10<00:03, 33.83it/s, loss=0.5040, acc=76.07%, alpha=0.2000]
Training:  73%|███████▎  | 321/438 [00:10<00:03, 33.83it/s, loss=0.5849, acc=76.07%, alpha=0.2000]
Training:  73%|███████▎  | 321/438 [00:10<00:03, 33.83it/s, loss=0.5829, acc=76.06%, alpha=0.2000]
Training:  73%|███████▎  | 321/438 [00:10<00:03, 33.83it/s, loss=0.6096, acc=76.06%, alpha=0.2000]
Training:  74%|███████▍  | 325/438 [00:10<00:03, 33.75it/s, loss=0.6096, acc=76.06%, alpha=0.2000]
Training:  74%|███████▍  | 325/438 [00:10<00:03, 33.75it/s, loss=0.5519, acc=76.06%, alpha=0.2000]
Training:  74%|███████▍  | 325/438 [00:10<00:03, 33.75it/s, loss=0.5889, acc=76.05%, alpha=0.2000]
Training:  74%|███████▍  | 325/438 [00:10<00:03, 33.75it/s, loss=0.4436, acc=76.08%, alpha=0.2000]
Training:  74%|███████▍  | 325/438 [00:10<00:03, 33.75it/s, loss=0.7400, acc=76.06%, alpha=0.2000]
Training:  74%|███████▍  | 325/438 [00:10<00:03, 33.75it/s, loss=0.5675, acc=76.08%, alpha=0.2000]
Training:  75%|███████▌  | 330/438 [00:10<00:02, 37.26it/s, loss=0.5675, acc=76.08%, alpha=0.2000]
Training:  75%|███████▌  | 330/438 [00:10<00:02, 37.26it/s, loss=0.4497, acc=76.10%, alpha=0.2000]
Training:  75%|███████▌  | 330/438 [00:10<00:02, 37.26it/s, loss=0.6758, acc=76.09%, alpha=0.2000]
Training:  75%|███████▌  | 330/438 [00:10<00:02, 37.26it/s, loss=0.6792, acc=76.07%, alpha=0.2000]
Training:  75%|███████▌  | 330/438 [00:10<00:02, 37.26it/s, loss=0.6352, acc=76.06%, alpha=0.2000]
Training:  75%|███████▌  | 330/438 [00:10<00:02, 37.26it/s, loss=0.6912, acc=76.05%, alpha=0.2000]
Training:  76%|███████▋  | 335/438 [00:10<00:02, 39.89it/s, loss=0.6912, acc=76.05%, alpha=0.2000]
Training:  76%|███████▋  | 335/438 [00:10<00:02, 39.89it/s, loss=0.5397, acc=76.06%, alpha=0.2000]
Training:  76%|███████▋  | 335/438 [00:10<00:02, 39.89it/s, loss=0.5277, acc=76.07%, alpha=0.2000]
Training:  76%|███████▋  | 335/438 [00:10<00:02, 39.89it/s, loss=0.4623, acc=76.08%, alpha=0.2000]
Training:  76%|███████▋  | 335/438 [00:10<00:02, 39.89it/s, loss=0.5697, acc=76.09%, alpha=0.2000]
Training:  76%|███████▋  | 335/438 [00:11<00:02, 39.89it/s, loss=0.7095, acc=76.09%, alpha=0.2000]
Training:  78%|███████▊  | 340/438 [00:11<00:02, 41.77it/s, loss=0.7095, acc=76.09%, alpha=0.2000]
Training:  78%|███████▊  | 340/438 [00:11<00:02, 41.77it/s, loss=0.5832, acc=76.10%, alpha=0.2000]
Training:  78%|███████▊  | 340/438 [00:11<00:02, 41.77it/s, loss=0.4407, acc=76.12%, alpha=0.2000]
Training:  78%|███████▊  | 340/438 [00:11<00:02, 41.77it/s, loss=0.4949, acc=76.14%, alpha=0.2000]
Training:  78%|███████▊  | 340/438 [00:11<00:02, 41.77it/s, loss=0.5850, acc=76.14%, alpha=0.2000]
Training:  78%|███████▊  | 340/438 [00:11<00:02, 41.77it/s, loss=0.7288, acc=76.13%, alpha=0.2000]
Training:  79%|███████▉  | 345/438 [00:11<00:02, 41.23it/s, loss=0.7288, acc=76.13%, alpha=0.2000]
Training:  79%|███████▉  | 345/438 [00:11<00:02, 41.23it/s, loss=0.3928, acc=76.16%, alpha=0.2000]
Training:  79%|███████▉  | 345/438 [00:11<00:02, 41.23it/s, loss=0.4672, acc=76.18%, alpha=0.2000]
Training:  79%|███████▉  | 345/438 [00:11<00:02, 41.23it/s, loss=0.4811, acc=76.20%, alpha=0.2000]
Training:  79%|███████▉  | 345/438 [00:11<00:02, 41.23it/s, loss=0.4172, acc=76.23%, alpha=0.2000]
Training:  79%|███████▉  | 345/438 [00:11<00:02, 41.23it/s, loss=0.5836, acc=76.23%, alpha=0.2000]
Training:  80%|███████▉  | 350/438 [00:11<00:02, 39.53it/s, loss=0.5836, acc=76.23%, alpha=0.2000]
Training:  80%|███████▉  | 350/438 [00:11<00:02, 39.53it/s, loss=0.7848, acc=76.22%, alpha=0.2000]
Training:  80%|███████▉  | 350/438 [00:11<00:02, 39.53it/s, loss=0.7085, acc=76.22%, alpha=0.2000]
Training:  80%|███████▉  | 350/438 [00:11<00:02, 39.53it/s, loss=0.3683, acc=76.24%, alpha=0.2000]
Training:  80%|███████▉  | 350/438 [00:11<00:02, 39.53it/s, loss=0.3169, acc=76.29%, alpha=0.2000]
Training:  81%|████████  | 354/438 [00:11<00:02, 36.79it/s, loss=0.3169, acc=76.29%, alpha=0.2000]
Training:  81%|████████  | 354/438 [00:11<00:02, 36.79it/s, loss=0.5994, acc=76.31%, alpha=0.2000]
Training:  81%|████████  | 354/438 [00:11<00:02, 36.79it/s, loss=0.3235, acc=76.34%, alpha=0.2000]
Training:  81%|████████  | 354/438 [00:11<00:02, 36.79it/s, loss=0.6726, acc=76.35%, alpha=0.2000]
Training:  81%|████████  | 354/438 [00:11<00:02, 36.79it/s, loss=0.5214, acc=76.36%, alpha=0.2000]
Training:  82%|████████▏ | 358/438 [00:11<00:02, 37.45it/s, loss=0.5214, acc=76.36%, alpha=0.2000]
Training:  82%|████████▏ | 358/438 [00:11<00:02, 37.45it/s, loss=0.6377, acc=76.38%, alpha=0.2000]
Training:  82%|████████▏ | 358/438 [00:11<00:02, 37.45it/s, loss=0.6445, acc=76.37%, alpha=0.2000]
Training:  82%|████████▏ | 358/438 [00:11<00:02, 37.45it/s, loss=0.7249, acc=76.36%, alpha=0.2000]
Training:  82%|████████▏ | 358/438 [00:11<00:02, 37.45it/s, loss=0.5766, acc=76.37%, alpha=0.2000]
Training:  82%|████████▏ | 358/438 [00:11<00:02, 37.45it/s, loss=0.6395, acc=76.36%, alpha=0.2000]
Training:  83%|████████▎ | 363/438 [00:11<00:01, 39.56it/s, loss=0.6395, acc=76.36%, alpha=0.2000]
Training:  83%|████████▎ | 363/438 [00:11<00:01, 39.56it/s, loss=0.5947, acc=76.37%, alpha=0.2000]
Training:  83%|████████▎ | 363/438 [00:11<00:01, 39.56it/s, loss=0.7495, acc=76.35%, alpha=0.2000]
Training:  83%|████████▎ | 363/438 [00:11<00:01, 39.56it/s, loss=0.5569, acc=76.37%, alpha=0.2000]
Training:  83%|████████▎ | 363/438 [00:11<00:01, 39.56it/s, loss=0.4685, acc=76.38%, alpha=0.2000]
Training:  83%|████████▎ | 363/438 [00:11<00:01, 39.56it/s, loss=0.5971, acc=76.38%, alpha=0.2000]
Training:  84%|████████▍ | 368/438 [00:11<00:01, 41.24it/s, loss=0.5971, acc=76.38%, alpha=0.2000]
Training:  84%|████████▍ | 368/438 [00:11<00:01, 41.24it/s, loss=0.7546, acc=76.35%, alpha=0.2000]
Training:  84%|████████▍ | 368/438 [00:11<00:01, 41.24it/s, loss=0.5099, acc=76.36%, alpha=0.2000]
Training:  84%|████████▍ | 368/438 [00:11<00:01, 41.24it/s, loss=0.6284, acc=76.36%, alpha=0.2000]
Training:  84%|████████▍ | 368/438 [00:11<00:01, 41.24it/s, loss=0.4899, acc=76.37%, alpha=0.2000]
Training:  84%|████████▍ | 368/438 [00:11<00:01, 41.24it/s, loss=0.5423, acc=76.37%, alpha=0.2000]
Training:  85%|████████▌ | 373/438 [00:11<00:01, 42.89it/s, loss=0.5423, acc=76.37%, alpha=0.2000]
Training:  85%|████████▌ | 373/438 [00:11<00:01, 42.89it/s, loss=0.7303, acc=76.37%, alpha=0.2000]
Training:  85%|████████▌ | 373/438 [00:11<00:01, 42.89it/s, loss=0.7571, acc=76.38%, alpha=0.2000]
Training:  85%|████████▌ | 373/438 [00:11<00:01, 42.89it/s, loss=0.7178, acc=76.38%, alpha=0.2000]
Training:  85%|████████▌ | 373/438 [00:11<00:01, 42.89it/s, loss=0.7602, acc=76.36%, alpha=0.2000]
Training:  85%|████████▌ | 373/438 [00:11<00:01, 42.89it/s, loss=0.5808, acc=76.37%, alpha=0.2000]
Training:  86%|████████▋ | 378/438 [00:11<00:01, 42.16it/s, loss=0.5808, acc=76.37%, alpha=0.2000]
Training:  86%|████████▋ | 378/438 [00:11<00:01, 42.16it/s, loss=0.4917, acc=76.38%, alpha=0.2000]
Training:  86%|████████▋ | 378/438 [00:12<00:01, 42.16it/s, loss=0.6019, acc=76.38%, alpha=0.2000]
Training:  86%|████████▋ | 378/438 [00:12<00:01, 42.16it/s, loss=0.4752, acc=76.40%, alpha=0.2000]
Training:  86%|████████▋ | 378/438 [00:12<00:01, 42.16it/s, loss=0.6022, acc=76.39%, alpha=0.2000]
Training:  86%|████████▋ | 378/438 [00:12<00:01, 42.16it/s, loss=0.4758, acc=76.41%, alpha=0.2000]
Training:  87%|████████▋ | 383/438 [00:12<00:01, 39.18it/s, loss=0.4758, acc=76.41%, alpha=0.2000]
Training:  87%|████████▋ | 383/438 [00:12<00:01, 39.18it/s, loss=0.5972, acc=76.41%, alpha=0.2000]
Training:  87%|████████▋ | 383/438 [00:12<00:01, 39.18it/s, loss=0.4639, acc=76.43%, alpha=0.2000]
Training:  87%|████████▋ | 383/438 [00:12<00:01, 39.18it/s, loss=0.6231, acc=76.43%, alpha=0.2000]
Training:  87%|████████▋ | 383/438 [00:12<00:01, 39.18it/s, loss=0.4759, acc=76.44%, alpha=0.2000]
Training:  88%|████████▊ | 387/438 [00:12<00:01, 36.65it/s, loss=0.4759, acc=76.44%, alpha=0.2000]
Training:  88%|████████▊ | 387/438 [00:12<00:01, 36.65it/s, loss=0.4935, acc=76.45%, alpha=0.2000]
Training:  88%|████████▊ | 387/438 [00:12<00:01, 36.65it/s, loss=0.4280, acc=76.46%, alpha=0.2000]
Training:  88%|████████▊ | 387/438 [00:12<00:01, 36.65it/s, loss=0.5805, acc=76.46%, alpha=0.2000]
Training:  88%|████████▊ | 387/438 [00:12<00:01, 36.65it/s, loss=0.5664, acc=76.47%, alpha=0.2000]
Training:  89%|████████▉ | 391/438 [00:12<00:01, 36.24it/s, loss=0.5664, acc=76.47%, alpha=0.2000]
Training:  89%|████████▉ | 391/438 [00:12<00:01, 36.24it/s, loss=0.7300, acc=76.46%, alpha=0.2000]
Training:  89%|████████▉ | 391/438 [00:12<00:01, 36.24it/s, loss=0.4908, acc=76.48%, alpha=0.2000]
Training:  89%|████████▉ | 391/438 [00:12<00:01, 36.24it/s, loss=0.4335, acc=76.50%, alpha=0.2000]
Training:  89%|████████▉ | 391/438 [00:12<00:01, 36.24it/s, loss=0.5902, acc=76.51%, alpha=0.2000]
Training:  90%|█████████ | 395/438 [00:12<00:01, 35.08it/s, loss=0.5902, acc=76.51%, alpha=0.2000]
Training:  90%|█████████ | 395/438 [00:12<00:01, 35.08it/s, loss=0.7101, acc=76.50%, alpha=0.2000]
Training:  90%|█████████ | 395/438 [00:12<00:01, 35.08it/s, loss=0.6967, acc=76.48%, alpha=0.2000]
Training:  90%|█████████ | 395/438 [00:12<00:01, 35.08it/s, loss=0.5463, acc=76.49%, alpha=0.2000]
Training:  90%|█████████ | 395/438 [00:12<00:01, 35.08it/s, loss=0.4845, acc=76.50%, alpha=0.2000]
Training:  91%|█████████ | 399/438 [00:12<00:01, 33.77it/s, loss=0.4845, acc=76.50%, alpha=0.2000]
Training:  91%|█████████ | 399/438 [00:12<00:01, 33.77it/s, loss=0.3309, acc=76.53%, alpha=0.2000]
Training:  91%|█████████ | 399/438 [00:12<00:01, 33.77it/s, loss=0.4131, acc=76.54%, alpha=0.2000]
Training:  91%|█████████ | 399/438 [00:12<00:01, 33.77it/s, loss=0.3968, acc=76.57%, alpha=0.2000]
Training:  91%|█████████ | 399/438 [00:12<00:01, 33.77it/s, loss=0.6817, acc=76.56%, alpha=0.2000]
Training:  91%|█████████ | 399/438 [00:12<00:01, 33.77it/s, loss=0.3851, acc=76.58%, alpha=0.2000]
Training:  92%|█████████▏| 404/438 [00:12<00:00, 35.65it/s, loss=0.3851, acc=76.58%, alpha=0.2000]
Training:  92%|█████████▏| 404/438 [00:12<00:00, 35.65it/s, loss=0.6164, acc=76.56%, alpha=0.2000]
Training:  92%|█████████▏| 404/438 [00:12<00:00, 35.65it/s, loss=0.7071, acc=76.55%, alpha=0.2000]
Training:  92%|█████████▏| 404/438 [00:12<00:00, 35.65it/s, loss=0.4706, acc=76.57%, alpha=0.2000]
Training:  92%|█████████▏| 404/438 [00:12<00:00, 35.65it/s, loss=0.5779, acc=76.57%, alpha=0.2000]
Training:  93%|█████████▎| 408/438 [00:12<00:00, 34.29it/s, loss=0.5779, acc=76.57%, alpha=0.2000]
Training:  93%|█████████▎| 408/438 [00:12<00:00, 34.29it/s, loss=0.4192, acc=76.60%, alpha=0.2000]
Training:  93%|█████████▎| 408/438 [00:12<00:00, 34.29it/s, loss=0.5712, acc=76.61%, alpha=0.2000]
Training:  93%|█████████▎| 408/438 [00:12<00:00, 34.29it/s, loss=0.7713, acc=76.60%, alpha=0.2000]
Training:  93%|█████████▎| 408/438 [00:12<00:00, 34.29it/s, loss=0.6038, acc=76.60%, alpha=0.2000]
Training:  94%|█████████▍| 412/438 [00:12<00:00, 32.83it/s, loss=0.6038, acc=76.60%, alpha=0.2000]
Training:  94%|█████████▍| 412/438 [00:13<00:00, 32.83it/s, loss=0.4595, acc=76.61%, alpha=0.2000]
Training:  94%|█████████▍| 412/438 [00:13<00:00, 32.83it/s, loss=0.5667, acc=76.62%, alpha=0.2000]
Training:  94%|█████████▍| 412/438 [00:13<00:00, 32.83it/s, loss=0.3761, acc=76.63%, alpha=0.2000]
Training:  94%|█████████▍| 412/438 [00:13<00:00, 32.83it/s, loss=0.4871, acc=76.65%, alpha=0.2000]
Training:  95%|█████████▍| 416/438 [00:13<00:00, 31.80it/s, loss=0.4871, acc=76.65%, alpha=0.2000]
Training:  95%|█████████▍| 416/438 [00:13<00:00, 31.80it/s, loss=0.6801, acc=76.65%, alpha=0.2000]
Training:  95%|█████████▍| 416/438 [00:13<00:00, 31.80it/s, loss=0.6538, acc=76.65%, alpha=0.2000]
Training:  95%|█████████▍| 416/438 [00:13<00:00, 31.80it/s, loss=0.6670, acc=76.64%, alpha=0.2000]
Training:  95%|█████████▍| 416/438 [00:13<00:00, 31.80it/s, loss=0.4052, acc=76.67%, alpha=0.2000]
Training:  96%|█████████▌| 420/438 [00:13<00:00, 30.76it/s, loss=0.4052, acc=76.67%, alpha=0.2000]
Training:  96%|█████████▌| 420/438 [00:13<00:00, 30.76it/s, loss=0.5267, acc=76.68%, alpha=0.2000]
Training:  96%|█████████▌| 420/438 [00:13<00:00, 30.76it/s, loss=0.5480, acc=76.70%, alpha=0.2000]
Training:  96%|█████████▌| 420/438 [00:13<00:00, 30.76it/s, loss=0.6613, acc=76.68%, alpha=0.2000]
Training:  96%|█████████▌| 420/438 [00:13<00:00, 30.76it/s, loss=0.5406, acc=76.68%, alpha=0.2000]
Training:  97%|█████████▋| 424/438 [00:13<00:00, 30.82it/s, loss=0.5406, acc=76.68%, alpha=0.2000]
Training:  97%|█████████▋| 424/438 [00:13<00:00, 30.82it/s, loss=0.5085, acc=76.69%, alpha=0.2000]
Training:  97%|█████████▋| 424/438 [00:13<00:00, 30.82it/s, loss=0.5067, acc=76.70%, alpha=0.2000]
Training:  97%|█████████▋| 424/438 [00:13<00:00, 30.82it/s, loss=0.4234, acc=76.71%, alpha=0.2000]
Training:  97%|█████████▋| 424/438 [00:13<00:00, 30.82it/s, loss=0.5269, acc=76.72%, alpha=0.2000]
Training:  98%|█████████▊| 428/438 [00:13<00:00, 31.44it/s, loss=0.5269, acc=76.72%, alpha=0.2000]
Training:  98%|█████████▊| 428/438 [00:13<00:00, 31.44it/s, loss=0.4141, acc=76.73%, alpha=0.2000]
Training:  98%|█████████▊| 428/438 [00:13<00:00, 31.44it/s, loss=0.5068, acc=76.74%, alpha=0.2000]
Training:  98%|█████████▊| 428/438 [00:13<00:00, 31.44it/s, loss=0.5003, acc=76.75%, alpha=0.2000]
Training:  98%|█████████▊| 428/438 [00:13<00:00, 31.44it/s, loss=0.5926, acc=76.75%, alpha=0.2000]
Training:  99%|█████████▊| 432/438 [00:13<00:00, 31.62it/s, loss=0.5926, acc=76.75%, alpha=0.2000]
Training:  99%|█████████▊| 432/438 [00:13<00:00, 31.62it/s, loss=0.4745, acc=76.77%, alpha=0.2000]
Training:  99%|█████████▊| 432/438 [00:13<00:00, 31.62it/s, loss=0.4208, acc=76.78%, alpha=0.2000]
Training:  99%|█████████▊| 432/438 [00:13<00:00, 31.62it/s, loss=0.4203, acc=76.80%, alpha=0.2000]
Training:  99%|█████████▊| 432/438 [00:13<00:00, 31.62it/s, loss=0.5675, acc=76.81%, alpha=0.2000]
Training: 100%|█████████▉| 436/438 [00:13<00:00, 32.20it/s, loss=0.5675, acc=76.81%, alpha=0.2000]
Training: 100%|█████████▉| 436/438 [00:13<00:00, 32.20it/s, loss=0.6982, acc=76.81%, alpha=0.2000]
Training: 100%|█████████▉| 436/438 [00:13<00:00, 32.20it/s, loss=0.4833, acc=76.81%, alpha=0.2000]
Training: 100%|██████████| 438/438 [00:13<00:00, 31.52it/s, loss=0.4833, acc=76.81%, alpha=0.2000]
2025-08-16 20:10:32,382 - INFO - 训练 - Loss: 0.6198, Acc: 76.81%
2025-08-16 20:10:32,383 - INFO -   Alpha值: 0.2000
2025-08-16 20:10:32,383 - INFO - 验证 - Loss: 0.5971, Acc: 81.54%
2025-08-16 20:10:32,383 - INFO - 测试 - Acc: 68.22%
2025-08-16 20:10:32,383 - INFO - 学习率: 0.001000
2025-08-16 20:10:32,416 - INFO - ✅ 保存最佳模型，准确率: 68.22%
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_modal_refinement_v2.py", line 376, in <module>
    train_stage2_simplified()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_modal_refinement_v2.py", line 358, in train_stage2_simplified
    early_stopping(avg_val_loss)
TypeError: __call__() missing 1 required positional argument: 'model'
