#!/usr/bin/env python3
"""监控所有Stage的独立训练进度"""

import glob
import time
import os
import re

def get_latest_logs():
    """获取所有stage的最新日志"""
    # Stage 2 - 包括improved版本
    stage2_logs = glob.glob("/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_independent_*/training.log")
    stage2_improved_logs = glob.glob("/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_improved_*/training.log")
    all_stage2_logs = stage2_logs + stage2_improved_logs
    
    stage3_logs = glob.glob("/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage3_independent_*/training.log")
    
    stage2_log = max(all_stage2_logs, key=os.path.getmtime) if all_stage2_logs else None
    stage3_log = max(stage3_logs, key=os.path.getmtime) if stage3_logs else None
    
    return stage2_log, stage3_log

def parse_log(log_file):
    """解析日志文件获取训练信息"""
    if not log_file or not os.path.exists(log_file):
        return None
    
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        info = {
            'best_acc': 0,
            'current_epoch': 0,
            'total_epochs': 0,
            'last_test_acc': 0,
            'last_train_acc': 0,
            'alpha': 0
        }
        
        for line in reversed(lines):
            if "最佳测试准确率:" in line or "保存最佳模型，准确率:" in line:
                try:
                    if "最佳测试准确率:" in line:
                        info['best_acc'] = float(line.split("准确率: ")[1].split("%")[0])
                    else:
                        acc = float(line.split("准确率: ")[1].split("%")[0])
                        info['best_acc'] = max(info['best_acc'], acc)
                except:
                    pass
            elif "Epoch " in line and "/" in line and "======" in line:
                try:
                    match = re.search(r"Epoch (\d+)/(\d+)", line)
                    if match:
                        info['current_epoch'] = int(match.group(1))
                        info['total_epochs'] = int(match.group(2))
                except:
                    pass
            elif "测试 - Acc:" in line:
                try:
                    info['last_test_acc'] = float(line.split("Acc: ")[1].split("%")[0])
                except:
                    pass
            elif "训练 - " in line and "Acc:" in line:
                try:
                    info['last_train_acc'] = float(line.split("Acc: ")[1].split("%")[0])
                except:
                    pass
            elif "融合权重(alpha):" in line or "Alpha值:" in line:
                try:
                    if "融合权重(alpha):" in line:
                        info['alpha'] = float(line.split("alpha): ")[1].strip())
                    else:
                        info['alpha'] = float(line.split("Alpha值: ")[1].split()[0])
                except:
                    pass
        
        return info
    except Exception as e:
        print(f"解析日志出错: {e}")
        return None

def monitor():
    """监控训练进度"""
    print("开始监控训练进度...")
    print("="*80)
    
    while True:
        try:
            stage2_log, stage3_log = get_latest_logs()
            
            # 解析日志
            stage2_info = parse_log(stage2_log) if stage2_log else None
            stage3_info = parse_log(stage3_log) if stage3_log else None
            
            # 清屏并显示
            os.system('clear')
            print("="*80)
            print("渐进式融合策略 - 独立训练监控")
            print("="*80)
            print()
            
            # Stage 2信息
            print("【Stage 2 - 轻量级跨模态注意力】")
            if stage2_info:
                progress = stage2_info['best_acc'] / 85 * 100
                bar_length = int(progress / 100 * 30)
                print(f"  Epoch: {stage2_info['current_epoch']}/{stage2_info['total_epochs']}")
                print(f"  训练准确率: {stage2_info['last_train_acc']:.2f}%")
                print(f"  测试准确率: {stage2_info['last_test_acc']:.2f}%")
                print(f"  最佳准确率: {stage2_info['best_acc']:.2f}%")
                print(f"  融合权重α: {stage2_info['alpha']:.3f}")
                print(f"  进度: [{'█' * bar_length}{'░' * (30-bar_length)}] {progress:.1f}%")
                if stage2_info['best_acc'] >= 85:
                    print("  ✅ 已达到目标!")
            else:
                print("  ⏳ 等待启动...")
            
            print()
            
            # Stage 3信息
            print("【Stage 3 - 完整架构】")
            if stage3_info:
                progress = stage3_info['best_acc'] / 88 * 100
                bar_length = int(progress / 100 * 30)
                print(f"  Epoch: {stage3_info['current_epoch']}/{stage3_info['total_epochs']}")
                print(f"  训练准确率: {stage3_info['last_train_acc']:.2f}%")
                print(f"  测试准确率: {stage3_info['last_test_acc']:.2f}%")
                print(f"  最佳准确率: {stage3_info['best_acc']:.2f}%")
                print(f"  进度: [{'█' * bar_length}{'░' * (30-bar_length)}] {progress:.1f}%")
                if stage3_info['best_acc'] >= 88:
                    print("  ✅ 已达到目标!")
            else:
                print("  ⏳ 等待启动...")
            
            print()
            print("="*80)
            print("目标: Stage 2 ≥85% | Stage 3 ≥88%")
            print("按Ctrl+C退出监控")
            
            # 检查是否都达到目标
            if stage2_info and stage3_info:
                if stage2_info['best_acc'] >= 85 and stage3_info['best_acc'] >= 88:
                    print("\n🎉 所有Stage都已达到目标准确率！")
                    break
            
            time.sleep(30)  # 每30秒更新一次
            
        except KeyboardInterrupt:
            print("\n监控已停止")
            break
        except Exception as e:
            print(f"监控出错: {e}")
            time.sleep(10)

if __name__ == "__main__":
    monitor()