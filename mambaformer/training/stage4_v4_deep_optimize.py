#!/usr/bin/env python3
"""
Stage 4 V4: Deep Optimization Version
基于V3的成功，进行深度优化
- 增加模型容量 (d_model 128->256)
- 使用OneCycleLR + Warmup
- 强化数据增强和正则化
- 完整20轮训练
- 混合Mamba-Transformer架构优化
"""

import os
import sys
import json
import random
import logging
import warnings
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional

import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
from torch.optim import AdamW
from torch.optim.lr_scheduler import OneCycleLR
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix, cohen_kappa_score
from sklearn.preprocessing import StandardScaler
from tqdm import tqdm

warnings.filterwarnings('ignore')

# 设置随机种子
def set_seed(seed=42):
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

set_seed(42)

# 设置项目根目录
project_root = Path("/media/main/ypf/eeg/Cross-Modal-Transformer")
sys.path.insert(0, str(project_root))

# 配置日志
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_dir = project_root / "mambaformer" / "logs" / f"stage4_v4_deep_{timestamp}"
log_dir.mkdir(parents=True, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / "training.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ========================= 配置参数 =========================
config = {
    # 模型参数 - 增强容量
    "d_model": 256,  # 增加模型维度
    "n_heads": 16,   # 增加注意力头数
    "n_mamba_layers": 3,  # 增加Mamba层
    "n_transformer_layers": 3,  # 增加Transformer层
    "n_refinement_layers": 2,  # 模态细化层数
    "ff_dim": 1024,  # 前馈网络维度
    "dropout": 0.15,
    "label_smoothing": 0.1,
    
    # 训练参数
    "batch_size": 16,  # 减小批次大小以支持更大模型
    "learning_rate": 1e-4,  # 降低学习率
    "num_epochs": 20,  # 增加训练轮数
    "early_stopping_patience": 5,
    "gradient_clip": 1.0,
    "weight_decay": 0.01,
    
    # 数据参数
    "seq_len": 5,
    "n_channels": 4,
    "n_classes": 5,
    
    # 渐进式分类参数
    "coarse_classes": 3,
    "progressive_epochs": [5, 10, 15],  # 渐进式训练阶段
    "progressive_weights": [0.5, 0.3, 0.1],  # 粗分类权重逐步降低
    
    # 增强参数
    "use_augmentation": True,
    "augmentation_prob": 0.4,  # 增加增强概率
    "mixup_alpha": 0.3,
    "use_tta": True,
    "tta_augmentations": 5,
    
    # 设备设置
    "device": torch.device("cuda" if torch.cuda.is_available() else "cpu"),
    "num_workers": 4,
    
    # 文件路径
    "data_dir": project_root / "prepared_data",
    "checkpoint_dir": log_dir / "checkpoints",
}

config["checkpoint_dir"].mkdir(exist_ok=True)

# ========================= 数据集定义 =========================
class SequenceSleepDataset(Dataset):
    def __init__(
        self,
        files: List[Path],
        seq_len: int = 5,
        transform=None,
        augment: bool = False,
        augment_prob: float = 0.3,
        is_train: bool = True,
        logger=None
    ):
        self.seq_len = seq_len
        self.transform = transform
        self.augment = augment and is_train
        self.augment_prob = augment_prob
        self.is_train = is_train
        self.logger = logger
        
        self.sequences = []
        self.labels = []
        self.coarse_labels = []
        
        # 标签映射
        self.fine_to_coarse = {
            0: 0,  # Wake -> 清醒
            1: 1,  # N1 -> 浅睡眠
            2: 1,  # N2 -> 浅睡眠
            3: 2,  # N3 -> 深睡眠
            4: 0,  # REM -> 清醒（活跃态）
        }
        
        total_epochs = 0
        for file_path in files:
            try:
                data = np.load(file_path)
                features = data['features']
                labels = data['labels']
                
                # 标准化
                if features.shape[0] > 0:
                    features = self._normalize_features(features)
                    
                    # 创建序列
                    for i in range(len(features) - seq_len + 1):
                        seq_features = features[i:i+seq_len]
                        seq_labels = labels[i:i+seq_len]
                        
                        # 使用中心标签
                        center_label = seq_labels[seq_len // 2]
                        
                        self.sequences.append(seq_features)
                        self.labels.append(center_label)
                        self.coarse_labels.append(self.fine_to_coarse[center_label])
                        
                    total_epochs += len(features)
                    
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"加载文件 {file_path} 时出错: {e}")
        
        self.sequences = np.array(self.sequences, dtype=np.float32)
        self.labels = np.array(self.labels, dtype=np.int64)
        self.coarse_labels = np.array(self.coarse_labels, dtype=np.int64)
        
        if self.logger:
            self.logger.info(f"从 {len(files)} 个文件加载了 {total_epochs} 个epochs, "
                           f"创建了 {len(self.sequences)} 个序列")
            self.logger.info(f"创建序列数据集: {len(self.sequences)}个序列, "
                           f"序列长度={seq_len}, 通道数={self.sequences.shape[-1]}, "
                           f"总epochs={total_epochs}")
    
    def _normalize_features(self, features):
        """标准化特征"""
        mean = features.mean(axis=0, keepdims=True)
        std = features.std(axis=0, keepdims=True) + 1e-8
        return (features - mean) / std
    
    def _augment_sequence(self, sequence):
        """增强序列数据"""
        if not self.augment or random.random() > self.augment_prob:
            return sequence
        
        aug_sequence = sequence.copy()
        
        # 时间偏移
        if random.random() < 0.5:
            shift = random.randint(-2, 2)
            aug_sequence = np.roll(aug_sequence, shift, axis=0)
        
        # 幅度缩放
        if random.random() < 0.5:
            scale = random.uniform(0.9, 1.1)
            aug_sequence = aug_sequence * scale
        
        # 高斯噪声
        if random.random() < 0.3:
            noise = np.random.normal(0, 0.01, aug_sequence.shape)
            aug_sequence = aug_sequence + noise
        
        # 通道dropout
        if random.random() < 0.2:
            channel_mask = np.random.binomial(1, 0.9, (1, 1, aug_sequence.shape[-1]))
            aug_sequence = aug_sequence * channel_mask
        
        return aug_sequence.astype(np.float32)
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        sequence = self.sequences[idx]
        label = self.labels[idx]
        coarse_label = self.coarse_labels[idx]
        
        if self.augment:
            sequence = self._augment_sequence(sequence)
        
        if self.transform:
            sequence = self.transform(sequence)
        
        return (
            torch.FloatTensor(sequence),
            torch.LongTensor([label]).squeeze(),
            torch.LongTensor([coarse_label]).squeeze()
        )

# ========================= 模型组件 =========================
class MambaBlock(nn.Module):
    """增强的Mamba块"""
    def __init__(self, d_model, d_state=16, d_conv=4, expand=2):
        super().__init__()
        self.d_model = d_model
        self.d_state = d_state
        self.d_conv = d_conv
        self.expand = expand
        d_inner = int(self.expand * d_model)
        
        self.in_proj = nn.Linear(d_model, d_inner * 2)
        
        self.conv1d = nn.Conv1d(
            in_channels=d_inner,
            out_channels=d_inner,
            kernel_size=d_conv,
            padding=d_conv - 1,
            groups=d_inner
        )
        
        self.x_proj = nn.Linear(d_inner, d_state + d_state + 1)
        self.dt_proj = nn.Linear(d_state, d_inner)
        
        self.A = nn.Parameter(torch.randn(d_inner, d_state))
        self.D = nn.Parameter(torch.randn(d_inner))
        
        self.out_proj = nn.Linear(d_inner, d_model)
        self.layer_norm = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(0.1)
        
        self._init_parameters()
    
    def _init_parameters(self):
        nn.init.xavier_uniform_(self.in_proj.weight)
        nn.init.xavier_uniform_(self.out_proj.weight)
        nn.init.xavier_uniform_(self.x_proj.weight)
        nn.init.xavier_uniform_(self.dt_proj.weight)
        
        dt_init_std = 0.02
        nn.init.uniform_(self.dt_proj.weight, -dt_init_std, dt_init_std)
        
        nn.init.normal_(self.A, mean=0, std=0.01)
        nn.init.normal_(self.D, mean=0, std=0.01)
    
    def forward(self, x):
        batch, seq_len, _ = x.shape
        residual = x
        x = self.layer_norm(x)
        
        xz = self.in_proj(x)
        x, z = xz.chunk(2, dim=-1)
        
        x = x.transpose(1, 2)
        x = self.conv1d(x)[:, :, :seq_len]
        x = x.transpose(1, 2)
        
        x = F.silu(x)
        
        y = self.selective_scan(x, self.A, self.D)
        y = y * F.silu(z)
        
        output = self.dropout(self.out_proj(y))
        return output + residual
    
    def selective_scan(self, x, A, D):
        batch, seq_len, d_inner = x.shape
        d_state = A.shape[-1]
        
        deltaBC = self.x_proj(x)
        delta, B, C = torch.split(deltaBC, [1, d_state, d_state], dim=-1)
        delta = F.softplus(self.dt_proj(delta.squeeze(-1)))
        
        y = torch.zeros_like(x)
        h = torch.zeros(batch, d_inner, d_state, device=x.device)
        
        for t in range(seq_len):
            h = h + delta[:, t].unsqueeze(-1) * (x[:, t].unsqueeze(-1) * B[:, t].unsqueeze(1) - h)
            y[:, t] = (h * C[:, t].unsqueeze(1)).sum(dim=-1)
        
        return y + x * D

class MultiHeadSelfAttention(nn.Module):
    """多头自注意力机制"""
    def __init__(self, d_model, n_heads, dropout=0.1):
        super().__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, x, mask=None):
        batch_size, seq_len, _ = x.shape
        residual = x
        x = self.layer_norm(x)
        
        Q = self.w_q(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.w_k(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        V = self.w_v(x).view(batch_size, seq_len, self.n_heads, self.d_k).transpose(1, 2)
        
        scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.d_k)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        context = torch.matmul(attn_weights, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, seq_len, self.d_model)
        
        output = self.dropout(self.w_o(context))
        return output + residual

class TransformerBlock(nn.Module):
    """Transformer块"""
    def __init__(self, d_model, n_heads, ff_dim, dropout=0.1):
        super().__init__()
        self.attention = MultiHeadSelfAttention(d_model, n_heads, dropout)
        self.feed_forward = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, ff_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(ff_dim, d_model),
            nn.Dropout(dropout)
        )
        
    def forward(self, x):
        x = self.attention(x)
        x = x + self.feed_forward(x)
        return x

class ModalRefinementModule(nn.Module):
    """模态细化模块"""
    def __init__(self, d_model, n_heads=4, dropout=0.1):
        super().__init__()
        
        # EEG通道的局部注意力
        self.eeg_attention = MultiHeadSelfAttention(d_model // 2, n_heads // 2, dropout)
        
        # EOG/EMG通道的轻量级Mamba
        self.other_mamba = MambaBlock(d_model // 2, d_state=8, d_conv=3)
        
        # 跨模态交互
        self.cross_modal = nn.MultiheadAttention(d_model, n_heads, dropout=dropout, batch_first=True)
        
        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
    def forward(self, x, channel_mask=None):
        batch_size, seq_len, d_model = x.shape
        
        # 分离EEG和其他通道
        eeg_features = x[:, :, :d_model//2]
        other_features = x[:, :, d_model//2:]
        
        # 处理EEG通道
        eeg_refined = self.eeg_attention(eeg_features)
        
        # 处理其他通道
        other_refined = self.other_mamba(other_features)
        
        # 合并特征
        combined = torch.cat([eeg_refined, other_refined], dim=-1)
        
        # 跨模态交互
        cross_modal_out, _ = self.cross_modal(combined, combined, combined)
        
        # 融合
        fused = self.fusion(torch.cat([combined, cross_modal_out], dim=-1))
        
        return fused

class HybridMAMBAFORMER(nn.Module):
    """混合MAMBA-Transformer模型"""
    def __init__(self, config):
        super().__init__()
        
        # 输入投影
        self.input_proj = nn.Linear(config["n_channels"], config["d_model"])
        self.pos_encoding = nn.Parameter(torch.randn(1, config["seq_len"], config["d_model"]))
        
        # Mamba层
        self.mamba_layers = nn.ModuleList([
            MambaBlock(config["d_model"], d_state=16, d_conv=4)
            for _ in range(config["n_mamba_layers"])
        ])
        
        # Transformer层
        self.transformer_layers = nn.ModuleList([
            TransformerBlock(config["d_model"], config["n_heads"], config["ff_dim"], config["dropout"])
            for _ in range(config["n_transformer_layers"])
        ])
        
        # 模态细化层
        self.refinement_layers = nn.ModuleList([
            ModalRefinementModule(config["d_model"], config["n_heads"] // 2, config["dropout"])
            for _ in range(config["n_refinement_layers"])
        ])
        
        # 渐进式分类头
        self.coarse_head = nn.Sequential(
            nn.LayerNorm(config["d_model"]),
            nn.Linear(config["d_model"], config["d_model"] // 2),
            nn.GELU(),
            nn.Dropout(config["dropout"]),
            nn.Linear(config["d_model"] // 2, config["coarse_classes"])
        )
        
        self.fine_head = nn.Sequential(
            nn.LayerNorm(config["d_model"]),
            nn.Linear(config["d_model"], config["d_model"] // 2),
            nn.GELU(),
            nn.Dropout(config["dropout"]),
            nn.Linear(config["d_model"] // 2, config["n_classes"])
        )
        
        self.dropout = nn.Dropout(config["dropout"])
        
    def forward(self, x, return_features=False):
        # 输入投影
        x = self.input_proj(x)
        x = x + self.pos_encoding
        x = self.dropout(x)
        
        # Mamba处理
        for mamba_layer in self.mamba_layers:
            x = mamba_layer(x)
        
        # Transformer处理
        for transformer_layer in self.transformer_layers:
            x = transformer_layer(x)
        
        # 模态细化
        for refinement_layer in self.refinement_layers:
            x = refinement_layer(x)
        
        # 全局池化
        features = x.mean(dim=1)
        
        # 分类
        coarse_logits = self.coarse_head(features)
        fine_logits = self.fine_head(features)
        
        if return_features:
            return fine_logits, coarse_logits, features
        return fine_logits, coarse_logits

# ========================= 损失函数 =========================
class ProgressiveFocalLoss(nn.Module):
    """渐进式Focal Loss"""
    def __init__(self, alpha=None, gamma=2.0, label_smoothing=0.1):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.label_smoothing = label_smoothing
        
    def forward(self, inputs, targets, weight=1.0):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none', label_smoothing=self.label_smoothing)
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss
        
        return (focal_loss * weight).mean()

# ========================= 训练函数 =========================
def mixup_data(x, y, alpha=0.2):
    """Mixup数据增强"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1
    
    batch_size = x.size()[0]
    index = torch.randperm(batch_size).to(x.device)
    
    mixed_x = lam * x + (1 - lam) * x[index]
    y_a, y_b = y, y[index]
    
    return mixed_x, y_a, y_b, lam

def train_epoch(model, dataloader, criterion_fine, criterion_coarse, optimizer, config, epoch, scaler=None):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    fine_preds = []
    fine_labels = []
    
    # 计算渐进权重
    if epoch < config["progressive_epochs"][0]:
        coarse_weight = config["progressive_weights"][0]
    elif epoch < config["progressive_epochs"][1]:
        coarse_weight = config["progressive_weights"][1]
    elif epoch < config["progressive_epochs"][2]:
        coarse_weight = config["progressive_weights"][2]
    else:
        coarse_weight = 0.1
    
    fine_weight = 1.0 - coarse_weight
    
    pbar = tqdm(dataloader, desc=f"训练 Epoch {epoch+1}")
    for batch_idx, (sequences, labels, coarse_labels) in enumerate(pbar):
        sequences = sequences.to(config["device"])
        labels = labels.to(config["device"])
        coarse_labels = coarse_labels.to(config["device"])
        
        # Mixup
        if config["use_augmentation"] and random.random() < 0.5:
            sequences, labels_a, labels_b, lam = mixup_data(sequences, labels, config["mixup_alpha"])
            coarse_a = coarse_labels
            coarse_b = coarse_labels[torch.randperm(len(coarse_labels))]
        else:
            labels_a = labels_b = labels
            coarse_a = coarse_b = coarse_labels
            lam = 1
        
        # 前向传播
        with torch.cuda.amp.autocast(enabled=(scaler is not None)):
            fine_logits, coarse_logits = model(sequences)
            
            # 计算损失
            if lam == 1:
                loss_fine = criterion_fine(fine_logits, labels)
                loss_coarse = criterion_coarse(coarse_logits, coarse_labels)
            else:
                loss_fine = lam * criterion_fine(fine_logits, labels_a) + (1 - lam) * criterion_fine(fine_logits, labels_b)
                loss_coarse = lam * criterion_coarse(coarse_logits, coarse_a) + (1 - lam) * criterion_coarse(coarse_logits, coarse_b)
            
            # 总损失
            loss = fine_weight * loss_fine + coarse_weight * loss_coarse
            
            # L2正则化
            l2_reg = sum(p.pow(2.0).sum() for p in model.parameters())
            loss = loss + config["weight_decay"] * l2_reg
        
        # 反向传播
        optimizer.zero_grad()
        
        if scaler is not None:
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), config["gradient_clip"])
            scaler.step(optimizer)
            scaler.update()
        else:
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), config["gradient_clip"])
            optimizer.step()
        
        total_loss += loss.item()
        
        # 记录预测
        fine_preds.extend(fine_logits.argmax(dim=1).cpu().numpy())
        fine_labels.extend(labels.cpu().numpy())
        
        # 更新进度条
        if batch_idx % 10 == 0:
            acc = accuracy_score(fine_labels, fine_preds)
            pbar.set_postfix({"Loss": f"{loss.item():.4f}", "Acc": f"{acc:.4f}"})
    
    avg_loss = total_loss / len(dataloader)
    accuracy = accuracy_score(fine_labels, fine_preds)
    
    return avg_loss, accuracy

def evaluate(model, dataloader, criterion_fine, criterion_coarse, config, use_tta=False):
    """评估模型"""
    model.eval()
    total_loss = 0
    fine_preds = []
    fine_labels = []
    coarse_preds = []
    coarse_labels_list = []
    
    with torch.no_grad():
        for sequences, labels, coarse_labels in tqdm(dataloader, desc="评估"):
            sequences = sequences.to(config["device"])
            labels = labels.to(config["device"])
            coarse_labels = coarse_labels.to(config["device"])
            
            if use_tta and config["use_tta"]:
                # 测试时增强
                fine_logits_list = []
                coarse_logits_list = []
                
                for _ in range(config["tta_augmentations"]):
                    # 添加轻微噪声
                    aug_sequences = sequences + torch.randn_like(sequences) * 0.01
                    fine_logits, coarse_logits = model(aug_sequences)
                    fine_logits_list.append(fine_logits)
                    coarse_logits_list.append(coarse_logits)
                
                fine_logits = torch.stack(fine_logits_list).mean(dim=0)
                coarse_logits = torch.stack(coarse_logits_list).mean(dim=0)
            else:
                fine_logits, coarse_logits = model(sequences)
            
            loss_fine = criterion_fine(fine_logits, labels)
            loss_coarse = criterion_coarse(coarse_logits, coarse_labels)
            loss = 0.7 * loss_fine + 0.3 * loss_coarse
            
            total_loss += loss.item()
            
            fine_preds.extend(fine_logits.argmax(dim=1).cpu().numpy())
            fine_labels.extend(labels.cpu().numpy())
            coarse_preds.extend(coarse_logits.argmax(dim=1).cpu().numpy())
            coarse_labels_list.extend(coarse_labels.cpu().numpy())
    
    avg_loss = total_loss / len(dataloader)
    
    # 计算指标
    accuracy = accuracy_score(fine_labels, fine_preds)
    f1 = f1_score(fine_labels, fine_preds, average='macro')
    kappa = cohen_kappa_score(fine_labels, fine_preds)
    coarse_accuracy = accuracy_score(coarse_labels_list, coarse_preds)
    
    # 分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(fine_labels, fine_preds, target_names=class_names, digits=3)
    
    # 混淆矩阵
    cm = confusion_matrix(fine_labels, fine_preds)
    
    return {
        'loss': avg_loss,
        'accuracy': accuracy,
        'f1': f1,
        'kappa': kappa,
        'coarse_accuracy': coarse_accuracy,
        'report': report,
        'confusion_matrix': cm,
        'predictions': fine_preds,
        'labels': fine_labels
    }

# ========================= 主训练流程 =========================
def main():
    logger.info("="*50)
    logger.info("Stage 4 V4: Deep Optimization Training")
    logger.info("="*50)
    logger.info(f"配置: {json.dumps(config, indent=2, default=str)}")
    
    # 准备数据
    data_dir = config["data_dir"]
    all_files = sorted(list(data_dir.glob("*.npz")))
    
    # 固定的训练/验证/测试划分
    train_files = [f for f in all_files if not any(x in f.stem for x in ['SC4001E0', 'SC4002E0', 'SC4011E0', 'SC4012E0', 'SC4021E0', 'SC4022E0', 'SC4031E0', 'SC4032E0'])]
    val_files = [f for f in all_files if any(x in f.stem for x in ['SC4001E0', 'SC4002E0', 'SC4011E0', 'SC4012E0'])]
    test_files = [f for f in all_files if any(x in f.stem for x in ['SC4021E0', 'SC4022E0', 'SC4031E0', 'SC4032E0'])]
    
    logger.info(f"训练文件数: {len(train_files)}")
    logger.info(f"验证文件数: {len(val_files)}")
    logger.info(f"测试文件数: {len(test_files)}")
    
    # 创建数据集
    logger.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files, 
        seq_len=config["seq_len"],
        augment=config["use_augmentation"],
        augment_prob=config["augmentation_prob"],
        is_train=True,
        logger=logger
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        augment=False,
        is_train=False,
        logger=logger
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        augment=False,
        is_train=False,
        logger=logger
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=config["num_workers"],
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=config["num_workers"],
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=config["num_workers"],
        pin_memory=True
    )
    
    logger.info(f"训练集: {len(train_dataset)} sequences")
    logger.info(f"验证集: {len(val_dataset)} sequences")
    logger.info(f"测试集: {len(test_dataset)} sequences")
    
    # 创建模型
    logger.info("创建模型...")
    model = HybridMAMBAFORMER(config).to(config["device"])
    
    # 计算类别权重
    train_labels = train_dataset.labels
    class_counts = np.bincount(train_labels)
    class_weights = 1.0 / (class_counts + 1)
    class_weights = class_weights / class_weights.sum() * len(class_weights)
    class_weights = torch.FloatTensor(class_weights).to(config["device"])
    
    coarse_labels = train_dataset.coarse_labels
    coarse_counts = np.bincount(coarse_labels)
    coarse_weights = 1.0 / (coarse_counts + 1)
    coarse_weights = coarse_weights / coarse_weights.sum() * len(coarse_weights)
    coarse_weights = torch.FloatTensor(coarse_weights).to(config["device"])
    
    logger.info(f"类别权重: {class_weights.cpu().numpy()}")
    logger.info(f"粗分类权重: {coarse_weights.cpu().numpy()}")
    
    # 损失函数
    criterion_fine = ProgressiveFocalLoss(
        alpha=class_weights,
        gamma=2.0,
        label_smoothing=config["label_smoothing"]
    )
    
    criterion_coarse = ProgressiveFocalLoss(
        alpha=coarse_weights,
        gamma=2.0,
        label_smoothing=config["label_smoothing"]
    )
    
    # 优化器和调度器
    optimizer = AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"]
    )
    
    # 使用OneCycleLR调度器
    total_steps = len(train_loader) * config["num_epochs"]
    scheduler = OneCycleLR(
        optimizer,
        max_lr=config["learning_rate"] * 10,
        total_steps=total_steps,
        pct_start=0.1,  # warmup占10%
        anneal_strategy='cos',
        div_factor=25,
        final_div_factor=1000
    )
    
    # 混合精度训练
    scaler = torch.cuda.amp.GradScaler() if torch.cuda.is_available() else None
    
    # 训练循环
    best_val_accuracy = 0
    best_val_f1 = 0
    patience_counter = 0
    
    logger.info("开始训练...")
    for epoch in range(config["num_epochs"]):
        logger.info(f"\nEpoch {epoch+1}/{config['num_epochs']}")
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, criterion_fine, criterion_coarse,
            optimizer, config, epoch, scaler
        )
        
        # 更新学习率
        if epoch < config["num_epochs"] - 1:  # OneCycleLR需要每个batch更新
            for _ in range(len(train_loader)):
                scheduler.step()
        
        logger.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2%}")
        
        # 验证
        val_results = evaluate(model, val_loader, criterion_fine, criterion_coarse, config, use_tta=True)
        
        logger.info(f"验证 - Loss: {val_results['loss']:.4f}, Acc: {val_results['accuracy']:.2%}, "
                   f"F1: {val_results['f1']:.4f}, Kappa: {val_results['kappa']:.4f}")
        logger.info(f"粗分类准确率: {val_results['coarse_accuracy']:.2%}")
        
        # 保存最佳模型
        if val_results['accuracy'] > best_val_accuracy:
            best_val_accuracy = val_results['accuracy']
            best_val_f1 = val_results['f1']
            patience_counter = 0
            
            logger.info(f"✅ 新的最佳结果: Acc={best_val_accuracy:.2%}, F1={best_val_f1:.4f}")
            
            # 保存模型
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'best_accuracy': best_val_accuracy,
                'best_f1': best_val_f1,
                'config': config
            }
            torch.save(checkpoint, config["checkpoint_dir"] / "best_model.pth")
            
            # 保存详细结果
            with open(log_dir / "best_results.txt", "w") as f:
                f.write(f"Epoch: {epoch+1}\n")
                f.write(f"Accuracy: {val_results['accuracy']:.4f}\n")
                f.write(f"F1 Score: {val_results['f1']:.4f}\n")
                f.write(f"Kappa: {val_results['kappa']:.4f}\n")
                f.write(f"Coarse Accuracy: {val_results['coarse_accuracy']:.4f}\n\n")
                f.write("Classification Report:\n")
                f.write(val_results['report'])
                f.write("\n\nConfusion Matrix:\n")
                f.write(str(val_results['confusion_matrix']))
        else:
            patience_counter += 1
            
        # 早停
        if patience_counter >= config["early_stopping_patience"]:
            logger.info(f"早停触发 (patience={config['early_stopping_patience']})")
            break
        
        logger.info(f"当前学习率: {optimizer.param_groups[0]['lr']:.6f}")
    
    # 加载最佳模型进行测试
    logger.info("\n" + "="*50)
    logger.info("加载最佳模型进行最终测试...")
    
    checkpoint = torch.load(config["checkpoint_dir"] / "best_model.pth")
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 最终测试
    test_results = evaluate(model, test_loader, criterion_fine, criterion_coarse, config, use_tta=True)
    
    logger.info("="*50)
    logger.info("最终测试结果:")
    logger.info(f"准确率: {test_results['accuracy']:.4f} ({test_results['accuracy']*100:.2f}%)")
    logger.info(f"Macro F1: {test_results['f1']:.4f}")
    logger.info(f"Cohen's Kappa: {test_results['kappa']:.4f}")
    logger.info(f"粗分类准确率: {test_results['coarse_accuracy']:.4f} ({test_results['coarse_accuracy']*100:.2f}%)")
    logger.info("\n详细分类报告:")
    logger.info(test_results['report'])
    logger.info("\n混淆矩阵:")
    
    cm = test_results['confusion_matrix']
    logger.info("     Wake   N1   N2   N3  REM")
    for i, row in enumerate(cm):
        class_name = ['Wake', 'N1', 'N2', 'N3', 'REM'][i]
        logger.info(f"{class_name:4} {row}")
    
    # 保存最终结果
    final_results = {
        'test_accuracy': float(test_results['accuracy']),
        'test_f1': float(test_results['f1']),
        'test_kappa': float(test_results['kappa']),
        'test_coarse_accuracy': float(test_results['coarse_accuracy']),
        'best_val_accuracy': float(best_val_accuracy),
        'best_val_f1': float(best_val_f1),
        'best_epoch': int(checkpoint['epoch']),
        'total_params': sum(p.numel() for p in model.parameters()),
        'trainable_params': sum(p.numel() for p in model.parameters() if p.requires_grad)
    }
    
    with open(log_dir / "final_results.json", "w") as f:
        json.dump(final_results, f, indent=2)
    
    logger.info(f"\n✅ 训练完成！结果保存在: {log_dir}")
    logger.info(f"最终测试准确率: {test_results['accuracy']*100:.2f}%")
    
    return test_results['accuracy']

if __name__ == "__main__":
    accuracy = main()
    print(f"\n{'='*50}")
    print(f"V4 Deep Optimization 完成!")
    print(f"最终准确率: {accuracy*100:.2f}%")
    print(f"{'='*50}")