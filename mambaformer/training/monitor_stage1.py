#!/usr/bin/env python3
"""
监控Stage 1训练进度
"""
import time
import os
import re
import subprocess
import sys

def get_latest_log():
    """获取最新的训练日志"""
    log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/"
    
    # 查找最新的stage1_simple目录
    dirs = [d for d in os.listdir(log_dir) if d.startswith("stage1_simple_")]
    if not dirs:
        return None
    
    latest_dir = sorted(dirs)[-1]
    log_file = os.path.join(log_dir, latest_dir, "training.log")
    
    if os.path.exists(log_file):
        return log_file
    return None

def parse_log(log_file):
    """解析训练日志"""
    if not os.path.exists(log_file):
        return None
    
    with open(log_file, 'r') as f:
        lines = f.readlines()
    
    # 查找最新的epoch信息
    current_epoch = 0
    train_acc = 0
    test_acc = 0
    best_test_acc = 0
    
    for line in reversed(lines):
        # 查找测试准确率
        if "测试 - Acc:" in line:
            match = re.search(r"测试 - Acc: ([\d.]+)%", line)
            if match:
                test_acc = float(match.group(1))
                
        # 查找训练准确率
        if "训练 - Loss:" in line and "Acc:" in line:
            match = re.search(r"Acc: ([\d.]+)%", line)
            if match:
                train_acc = float(match.group(1))
                
        # 查找当前epoch
        if "Epoch " in line and "/" in line:
            match = re.search(r"Epoch (\d+)/", line)
            if match:
                current_epoch = int(match.group(1))
                
        # 查找最佳测试准确率
        if "保存最佳模型" in line:
            match = re.search(r"Test: ([\d.]+)%", line)
            if match:
                best_test_acc = max(best_test_acc, float(match.group(1)))
                
        # 检查是否达到目标
        if "达到目标" in line:
            match = re.search(r"测试准确率: ([\d.]+)%", line)
            if match:
                return {
                    'status': 'COMPLETED',
                    'final_acc': float(match.group(1)),
                    'message': f"🎉 达到85%目标! 最终准确率: {match.group(1)}%"
                }
    
    return {
        'status': 'TRAINING',
        'epoch': current_epoch,
        'train_acc': train_acc,
        'test_acc': test_acc,
        'best_test_acc': best_test_acc
    }

def check_process():
    """检查训练进程是否在运行"""
    try:
        result = subprocess.run(['pgrep', '-f', 'stage1_simple.py'], 
                              capture_output=True, text=True)
        return len(result.stdout.strip()) > 0
    except:
        return False

def main():
    print("="*60)
    print("🔍 监控Stage 1训练进度")
    print("="*60)
    
    while True:
        log_file = get_latest_log()
        if not log_file:
            print("未找到训练日志")
            time.sleep(10)
            continue
            
        status = parse_log(log_file)
        if not status:
            print("无法解析日志")
            time.sleep(10)
            continue
            
        # 清屏并显示状态
        os.system('clear')
        print("="*60)
        print("📊 Stage 1 训练监控")
        print("="*60)
        print(f"日志文件: {log_file}")
        print("-"*60)
        
        if status['status'] == 'COMPLETED':
            print(status['message'])
            print("="*60)
            print("训练完成! 准备启动下一阶段...")
            break
        elif status['status'] == 'TRAINING':
            print(f"当前Epoch: {status['epoch']}")
            print(f"训练准确率: {status['train_acc']:.2f}%")
            print(f"测试准确率: {status['test_acc']:.2f}%")
            print(f"最佳测试准确率: {status['best_test_acc']:.2f}%")
            
            # 检查进程
            if check_process():
                print(f"状态: 🟢 训练中...")
            else:
                print(f"状态: 🔴 训练已停止")
                
            # 进度条
            target = 85.0
            progress = min(100, (status['test_acc'] / target) * 100)
            bar_length = 40
            filled = int(bar_length * progress / 100)
            bar = '█' * filled + '░' * (bar_length - filled)
            print(f"\n进度: [{bar}] {progress:.1f}%")
            print(f"距离目标: {target - status['test_acc']:.2f}%")
            
        print("\n按Ctrl+C退出监控")
        time.sleep(30)  # 每30秒更新一次

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n监控已停止")