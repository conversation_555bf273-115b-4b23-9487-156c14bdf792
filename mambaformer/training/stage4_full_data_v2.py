#!/usr/bin/env python3
"""
Stage 4: 完整数据版本 - Mamba-Transformer混合架构 + 渐进式分类策略
修复版：解决数据泄露问题，使用按受试者划分的数据集
使用完整数据：每个文件的所有epochs（约1000个）
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
from sklearn.metrics import classification_report, confusion_matrix, f1_score

import numpy as np
import os
import glob
import logging
from datetime import datetime
from pathlib import Path
import json
from tqdm import tqdm
from collections import defaultdict
import random

# 导入必要的模块
import sys
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')
from utils.sequence_dataset import SequenceSleepDataset
from models.sequential_mambaformer_v2 import PositionalEncoding
from utils.train_utils import EarlyStopping

# ===================== 数据划分工具 =====================

def get_subject_id(filename):
    """从文件名提取受试者ID"""
    basename = os.path.basename(filename)
    subject_id = basename[3:5]  # SC4后面的两位数字
    return subject_id

def get_subject_based_split(data_dir, train_ratio=0.7, val_ratio=0.15, seed=42):
    """按受试者划分数据集，避免数据泄露"""
    random.seed(seed)
    
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 按受试者分组
    subject_files = defaultdict(list)
    for file in all_files:
        subject_id = get_subject_id(file)
        subject_files[subject_id].append(file)
    
    # 获取所有受试者ID并打乱
    subjects = list(subject_files.keys())
    random.shuffle(subjects)
    
    # 计算划分
    n_subjects = len(subjects)
    n_train = int(n_subjects * train_ratio)
    n_val = int(n_subjects * val_ratio)
    
    # 划分受试者
    train_subjects = subjects[:n_train]
    val_subjects = subjects[n_train:n_train+n_val]
    test_subjects = subjects[n_train+n_val:]
    
    # 收集文件
    train_files = []
    val_files = []
    test_files = []
    
    for subject in train_subjects:
        train_files.extend(subject_files[subject])
    for subject in val_subjects:
        val_files.extend(subject_files[subject])
    for subject in test_subjects:
        test_files.extend(subject_files[subject])
    
    # 排序确保一致性
    train_files.sort()
    val_files.sort()
    test_files.sort()
    
    return train_files, val_files, test_files, {
        'train_subjects': sorted(train_subjects),
        'val_subjects': sorted(val_subjects),
        'test_subjects': sorted(test_subjects)
    }

# ===================== Mamba组件 =====================

class SimplifiedMambaBlock(nn.Module):
    """简化的Mamba块"""
    def __init__(self, d_model, d_state=16, d_conv=4, expand_factor=2):
        super().__init__()
        self.d_model = d_model
        self.d_inner = d_model * expand_factor
        
        self.in_proj = nn.Linear(d_model, self.d_inner * 2)
        self.conv1d = nn.Conv1d(
            self.d_inner, self.d_inner, 
            kernel_size=d_conv, padding=d_conv//2, 
            groups=self.d_inner
        )
        
        self.x_proj = nn.Linear(self.d_inner, d_state * 2 + 1)
        self.dt_proj = nn.Linear(1, self.d_inner)
        
        A = torch.arange(1, d_state + 1).reshape(1, d_state).repeat(self.d_inner, 1)
        self.A_log = nn.Parameter(torch.log(A))
        self.D = nn.Parameter(torch.ones(self.d_inner))
        
        self.out_proj = nn.Linear(self.d_inner, d_model)
        self.activation = nn.SiLU()
        self.norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        batch_size, seq_len, _ = x.shape
        residual = x
        
        x_proj = self.in_proj(x)
        x_conv, x_gate = x_proj.chunk(2, dim=-1)
        
        x_conv = x_conv.transpose(1, 2)
        x_conv = self.conv1d(x_conv)
        if x_conv.size(2) != seq_len:
            x_conv = x_conv[:, :, :seq_len]
        x_conv = x_conv.transpose(1, 2)
        x_conv = self.activation(x_conv)
        
        if x_conv.size(1) != x_gate.size(1):
            min_seq_len = min(x_conv.size(1), x_gate.size(1))
            x_conv = x_conv[:, :min_seq_len, :]
            x_gate = x_gate[:, :min_seq_len, :]
        
        y = x_conv * F.silu(x_gate)
        output = self.out_proj(y)
        
        if output.size(1) != residual.size(1):
            min_seq_len = min(output.size(1), residual.size(1))
            output = output[:, :min_seq_len, :]
            residual = residual[:, :min_seq_len, :]
        
        output = self.norm(output + residual)
        return output

# ===================== 渐进式分类器 =====================

class ProgressiveClassifier(nn.Module):
    """渐进式分类器：粗分类→细分类"""
    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()
        
        # 粗分类器: 3类 (W, NREM, REM)
        self.coarse_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 3)
        )
        
        # 细分类器: NREM子分类 (N1, N2, N3)
        self.nrem_refiner = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 3)
        )
        
        # 最终5分类
        self.final_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, 5)
        )
        
        # 温度缩放
        self.temperature = nn.Parameter(torch.tensor(1.5))
        
    def forward(self, features, return_all=False):
        # 粗分类
        coarse_logits = self.coarse_classifier(features)
        coarse_probs = F.softmax(coarse_logits / self.temperature, dim=-1)
        
        # NREM细分类
        nrem_logits = self.nrem_refiner(features)
        nrem_probs = F.softmax(nrem_logits / self.temperature, dim=-1)
        
        # 最终分类
        final_logits = self.final_classifier(features)
        
        if return_all:
            return {
                'final_logits': final_logits,
                'coarse_logits': coarse_logits,
                'nrem_logits': nrem_logits,
                'coarse_probs': coarse_probs,
                'nrem_probs': nrem_probs
            }
        return final_logits

# ===================== Stage 4 模型 =====================

class Stage4MambaProgressiveModel(nn.Module):
    """Stage 4: 完整的Mamba-Transformer混合架构"""
    def __init__(self, n_classes=5, d_model=128, n_heads=8, 
                 n_mamba_layers=2, n_transformer_layers=2, dropout=0.15):
        super().__init__()
        
        # CNN特征提取
        # 使用全部4通道: 2 EEG + 1 EOG + 1 EMG
        self.conv1 = nn.Conv1d(4, 64, kernel_size=50, stride=6, padding=25)
        self.bn1 = nn.BatchNorm1d(64)
        self.conv2 = nn.Conv1d(64, 128, kernel_size=8, stride=1, padding=4)
        self.bn2 = nn.BatchNorm1d(128)
        self.conv3 = nn.Conv1d(128, d_model, kernel_size=8, stride=1, padding=4)
        self.bn3 = nn.BatchNorm1d(d_model)
        
        self.pool = nn.MaxPool1d(kernel_size=8, stride=8)
        self.dropout = nn.Dropout(dropout)
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model)
        
        # Mamba层（长程依赖）
        self.mamba_layers = nn.ModuleList([
            SimplifiedMambaBlock(d_model)
            for _ in range(n_mamba_layers)
        ])
        
        # Transformer层（短程依赖）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model*4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_transformer_layers)
        
        # 渐进式分类器
        self.classifier = ProgressiveClassifier(d_model, dropout)
        
        # MC Dropout用于不确定性估计
        self.mc_dropout = nn.Dropout(0.2)
        
    def forward(self, x, return_uncertainty=False):
        # 处理不同的输入形状
        batch_size = x.shape[0]
        if len(x.shape) == 4:  # (batch, seq_len, time_steps, channels)
            _, seq_len, time_steps, channels = x.shape
            # 展平序列维度进行处理
            x = x.reshape(batch_size * seq_len, time_steps, channels)
            need_reshape = True
        else:  # (batch, time_steps, channels)
            seq_len = 1
            need_reshape = False
        
        # CNN特征提取
        x = x.transpose(1, 2)  # (batch*seq_len, channels, time_steps)
        
        x = F.gelu(self.bn1(self.conv1(x)))
        x = self.dropout(x)
        x = self.pool(x)
        
        x = F.gelu(self.bn2(self.conv2(x)))
        x = self.dropout(x)
        x = self.pool(x)
        
        x = F.gelu(self.bn3(self.conv3(x)))
        x = self.dropout(x)
        
        # 自适应池化
        if x.size(2) > 60:
            x = F.adaptive_avg_pool1d(x, 60)
        
        x = x.transpose(1, 2)  # (batch*seq_len, seq_len, d_model)
        
        # 位置编码
        x = self.pos_encoding(x)
        
        # Mamba层
        for layer in self.mamba_layers:
            x = layer(x)
        
        # Transformer层
        x = self.transformer(x)
        
        # 全局池化
        x = x.mean(dim=1)  # (batch*seq_len, d_model)
        
        # 如果输入是序列，需要还原批次维度
        if need_reshape:
            x = x.reshape(batch_size, seq_len, -1)
            # 对序列进行平均池化
            x = x.mean(dim=1)  # (batch, d_model)
        
        # 不确定性估计
        if return_uncertainty:
            # 执行多次前向传播
            n_samples = 5
            predictions = []
            for _ in range(n_samples):
                x_mc = self.mc_dropout(x)
                pred = self.classifier(x_mc, return_all=True)
                predictions.append(pred['final_logits'])
            
            predictions = torch.stack(predictions)
            mean_pred = predictions.mean(dim=0)
            uncertainty = predictions.var(dim=0).mean(dim=-1)
            
            return mean_pred, uncertainty
        
        # 渐进式分类
        outputs = self.classifier(x, return_all=True)
        return outputs

# ===================== 训练脚本 =====================

def train_stage4_full_data():
    # 设置日志
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = Path(f'/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_full_{timestamp}')
    log_dir.mkdir(parents=True, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'training.log'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("="*80)
    logger.info("🎯 Stage 4 (完整数据版): Mamba-Transformer混合架构 + 渐进式分类策略")
    logger.info("✅ 使用完整数据：每个文件约1000个epochs")
    logger.info("✅ 已修复数据泄露问题：按受试者划分数据集")
    logger.info("目标: ≥85% accuracy (真实泛化性能)")
    logger.info("="*80)
    
    # 配置 - 为完整数据调整
    config = {
        'n_classes': 5,
        'd_model': 128,
        'n_heads': 8,
        'n_mamba_layers': 2,
        'n_transformer_layers': 2,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 16,  # 减小batch size因为数据量更大
        'learning_rate': 0.0003,  # 降低学习率for stability
        'num_epochs': 10,  # 减少epochs因为数据量大
        'weight_decay': 0.0001,
        'patience': 5,
        'coarse_weight': 0.3,
        'uncertainty_weight': 0.1,
    }
    
    logger.info(f"配置: {config}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"设备: {device}")
    
    # 加载数据（使用修复的数据划分）
    logger.info("加载数据集（按受试者划分）...")
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    
    train_files, val_files, test_files, subject_info = get_subject_based_split(data_dir)
    
    logger.info(f"训练集受试者: {subject_info['train_subjects']}")
    logger.info(f"验证集受试者: {subject_info['val_subjects']}")
    logger.info(f"测试集受试者: {subject_info['test_subjects']}")
    
    # 使用完整数据，不限制每个文件的样本数
    logger.info("加载完整数据（不限制max_samples_per_file）...")
    train_dataset = SequenceSleepDataset(train_files, seq_len=config['seq_len'], max_samples_per_file=None, use_channels=4)
    val_dataset = SequenceSleepDataset(val_files, seq_len=config['seq_len'], max_samples_per_file=None, use_channels=4)
    test_dataset = SequenceSleepDataset(test_files, seq_len=config['seq_len'], max_samples_per_file=None, use_channels=4)
    
    logger.info(f"训练集: {len(train_dataset)} sequences (完整数据)")
    logger.info(f"验证集: {len(val_dataset)} sequences (完整数据)")
    logger.info(f"测试集: {len(test_dataset)} sequences (完整数据)")
    
    # 显示实际epoch数
    logger.info(f"训练集总epochs: {train_dataset.total_epochs}")
    logger.info(f"验证集总epochs: {val_dataset.total_epochs}")
    logger.info(f"测试集总epochs: {test_dataset.total_epochs}")
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # 创建模型
    model = Stage4MambaProgressiveModel(
        n_classes=config['n_classes'],
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_mamba_layers=config['n_mamba_layers'],
        n_transformer_layers=config['n_transformer_layers'],
        dropout=config['dropout']
    ).to(device)
    
    # 统计参数量
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"创建Stage4MambaProgressiveModel: 参数量={total_params:,}")
    
    # 优化器和调度器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=10,
        T_mult=2,
        eta_min=1e-6
    )
    
    # 损失函数
    criterion_main = nn.CrossEntropyLoss()
    criterion_coarse = nn.CrossEntropyLoss()
    
    # Early stopping
    early_stopping = EarlyStopping(patience=config['patience'], delta=0.001)
    
    best_acc = 0
    best_model_path = log_dir / 'best_model.pth'
    history = {'train_loss': [], 'train_acc': [], 'val_acc': [], 'test_acc': []}
    
    # 粗分类标签映射
    def get_coarse_label(fine_label):
        # W=0, N1=1, N2=2, N3=3, REM=4
        if fine_label == 0:
            return 0  # W
        elif fine_label in [1, 2, 3]:
            return 1  # NREM
        else:
            return 2  # REM
    
    # 训练循环
    for epoch in range(config['num_epochs']):
        logger.info("\n" + "="*60)
        logger.info(f"Epoch {epoch+1}/{config['num_epochs']}")
        
        # 训练阶段
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0
        coarse_correct = 0
        
        pbar = tqdm(train_loader, desc='Training')
        for batch_idx, (data, target) in enumerate(pbar):
            data, target = data.to(device), target.to(device)
            
            # 处理序列标签：取中间epoch作为序列标签
            if len(target.shape) == 2:  # (batch, seq_len)
                target = target[:, 2]  # 取中间位置
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(data)
            
            # 计算损失
            main_loss = criterion_main(outputs['final_logits'], target)
            
            # 粗分类损失
            coarse_target = torch.tensor([get_coarse_label(t.item()) for t in target]).to(device)
            coarse_loss = criterion_coarse(outputs['coarse_logits'], coarse_target)
            
            # 总损失
            loss = main_loss + config['coarse_weight'] * coarse_loss
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 统计
            train_loss += loss.item()
            _, predicted = outputs['final_logits'].max(1)
            train_total += target.size(0)
            train_correct += predicted.eq(target).sum().item()
            
            _, coarse_pred = outputs['coarse_logits'].max(1)
            coarse_correct += coarse_pred.eq(coarse_target).sum().item()
            
            # 更新进度条
            current_acc = 100. * train_correct / train_total
            coarse_acc = 100. * coarse_correct / train_total
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{current_acc:.2f}%',
                'coarse': f'{coarse_acc:.2f}%'
            })
        
        train_acc = 100. * train_correct / train_total
        avg_train_loss = train_loss / len(train_loader)
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(train_acc)
        
        # 验证阶段
        model.eval()
        val_correct = 0
        val_total = 0
        val_predictions = []
        val_targets = []
        
        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(device), target.to(device)
                # 处理序列标签
                if len(target.shape) == 2:
                    target = target[:, 2]
                outputs = model(data)
                _, predicted = outputs['final_logits'].max(1)
                val_total += target.size(0)
                val_correct += predicted.eq(target).sum().item()
                val_predictions.extend(predicted.cpu().numpy())
                val_targets.extend(target.cpu().numpy())
        
        val_acc = 100. * val_correct / val_total
        val_f1 = f1_score(val_targets, val_predictions, average='weighted')
        history['val_acc'].append(val_acc)
        
        # 测试阶段
        test_correct = 0
        test_total = 0
        test_predictions = []
        test_targets = []
        test_coarse_correct = 0
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(device), target.to(device)
                # 处理序列标签
                if len(target.shape) == 2:
                    target = target[:, 2]
                outputs = model(data)
                _, predicted = outputs['final_logits'].max(1)
                test_total += target.size(0)
                test_correct += predicted.eq(target).sum().item()
                test_predictions.extend(predicted.cpu().numpy())
                test_targets.extend(target.cpu().numpy())
                
                # 粗分类统计
                coarse_target = torch.tensor([get_coarse_label(t.item()) for t in target]).to(device)
                _, coarse_pred = outputs['coarse_logits'].max(1)
                test_coarse_correct += coarse_pred.eq(coarse_target).sum().item()
        
        test_acc = 100. * test_correct / test_total
        test_f1 = f1_score(test_targets, test_predictions, average='weighted')
        test_coarse_acc = 100. * test_coarse_correct / test_total
        history['test_acc'].append(test_acc)
        
        # 记录日志
        logger.info(f"训练 - Loss: {avg_train_loss:.4f}, Acc: {train_acc:.2f}%")
        logger.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_f1:.4f}")
        logger.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_f1:.4f}, Coarse Acc: {test_coarse_acc:.2f}%")
        logger.info(f"学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            best_epoch = epoch
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'test_f1': test_f1,
                'config': config,
                'subject_info': subject_info
            }, best_model_path)
            logger.info(f"✅ 保存最佳模型，准确率: {test_acc:.2f}%")
        
        # Early stopping
        early_stopping(-val_acc, model)
        if early_stopping.early_stop:
            logger.info("Early stopping triggered")
            break
        
        scheduler.step()
    
    # 训练结束，进行最终评估
    logger.info("\n" + "="*80)
    logger.info("训练完成！")
    logger.info(f"最佳测试准确率: {best_acc:.2f}% (Epoch {best_epoch+1})")
    
    # 加载最佳模型进行详细评估
    checkpoint = torch.load(best_model_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # 详细测试评估
    logger.info("\n" + "="*80)
    logger.info("最终评估结果 (Evaluation Results)")
    logger.info("="*80)
    
    all_predictions = []
    all_targets = []
    all_coarse_predictions = []
    all_coarse_targets = []
    
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            # 处理序列标签
            if len(target.shape) == 2:
                target = target[:, 2]
            outputs = model(data)
            
            _, predicted = outputs['final_logits'].max(1)
            all_predictions.extend(predicted.cpu().numpy())
            all_targets.extend(target.cpu().numpy())
            
            # 粗分类
            coarse_target = [get_coarse_label(t.item()) for t in target]
            _, coarse_pred = outputs['coarse_logits'].max(1)
            all_coarse_predictions.extend(coarse_pred.cpu().numpy())
            all_coarse_targets.extend(coarse_target)
    
    # 5分类结果
    logger.info("\n### 5分类结果 (Fine-grained Classification)")
    class_names = ['W', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(all_targets, all_predictions, target_names=class_names)
    logger.info(f"\n{report}")
    
    # 混淆矩阵
    cm = confusion_matrix(all_targets, all_predictions)
    logger.info("\n混淆矩阵 (Confusion Matrix):")
    logger.info("     W   N1   N2   N3  REM")
    for i, row in enumerate(cm):
        logger.info(f"{class_names[i]:3s} {row}")
    
    # 粗分类结果
    logger.info("\n### 粗分类结果 (Coarse Classification)")
    coarse_names = ['W', 'NREM', 'REM']
    coarse_report = classification_report(all_coarse_targets, all_coarse_predictions, target_names=coarse_names)
    logger.info(f"\n{coarse_report}")
    
    # 总结
    logger.info("\n" + "="*80)
    logger.info("总结 (Summary)")
    logger.info("="*80)
    logger.info(f"最佳5分类准确率: {best_acc:.2f}%")
    logger.info(f"数据集划分:")
    logger.info(f"  训练集: {subject_info['train_subjects']}")
    logger.info(f"  验证集: {subject_info['val_subjects']}")
    logger.info(f"  测试集: {subject_info['test_subjects']}")
    logger.info("✅ 无数据泄露：各集合间无受试者重叠")
    
    # 保存历史记录
    with open(log_dir / 'history.json', 'w') as f:
        json.dump(history, f, indent=2)
    
    logger.info(f"\n训练日志和模型保存在: {log_dir}")

if __name__ == "__main__":
    train_stage4_full_data()