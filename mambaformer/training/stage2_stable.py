#!/usr/bin/env python3
"""
Stage 2: 最小影响版本 - 极度保守的跨模态注意力
目标：保持接近Stage 1的88%准确率，同时引入最小程度的新特征
策略：
1. alpha初始化为-5 (sigmoid(-5)≈0.007，即99.3%原始特征)
2. 只解冻最后1层transformer
3. 极低的学习率
4. 严格的alpha上限约束(最大0.05，即95%原始特征)
"""

import os
import sys
import json
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR
from datetime import datetime
from pathlib import Path
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
from tqdm import tqdm

# 添加项目路径
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')

from mambaformer.utils.sequence_dataset import SequenceSleepDataset
from mambaformer.models.progressive_mambaformer_v1_fixed import (
    ProgressiveMAMBAFORMER_V1_Fixed,
    SequentialFocalLoss,
    TemporalConsistencyLoss
)


class MinimalCrossModalAttention(nn.Module):
    """
    最小影响的跨模态注意力模块
    设计原则：
    1. 几乎完全保留原始特征(>95%)
    2. 新特征权重极小(<5%)
    3. 严格约束防止过度融合
    """
    def __init__(self, d_model=512, n_heads=8, dropout=0.1):
        super().__init__()
        
        # 多头自注意力层
        self.attention = nn.MultiheadAttention(
            d_model, 
            n_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 简化的前馈网络（更小的扩展比例）
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model),  # 不扩展维度
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model)
        )
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 极小的融合权重（初始化为-5，sigmoid(-5)≈0.007）
        self.alpha = nn.Parameter(torch.tensor([-5.0]))
        
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, d_model)
        Returns:
            enhanced_x: (batch, seq_len, d_model) - 几乎等于原始x
        """
        # 保存原始输入
        residual = x
        
        # 自注意力
        attn_out, _ = self.attention(x, x, x)
        x = self.norm1(x + self.dropout(attn_out) * 0.1)  # 进一步减小影响
        
        # 前馈网络
        ffn_out = self.ffn(x)
        x = self.norm2(x + self.dropout(ffn_out) * 0.1)  # 进一步减小影响
        
        # 极小权重融合
        alpha = torch.sigmoid(self.alpha)
        # 严格约束alpha不超过0.05（5%新特征）
        alpha = torch.clamp(alpha, max=0.05)
        
        # 融合：至少95%原始特征
        enhanced = (1 - alpha) * residual + alpha * x
        
        return enhanced


def train_stage2(config):
    """Stage 2训练主函数 - 最小影响版本"""
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = Path(f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_stable_{timestamp}")
    exp_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(exp_dir / 'training.log'),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 Stage 2: 最小影响版本 - 保守的跨模态注意力")
    logging.info("目标：保持≥85%准确率，原始特征保留≥95%")
    logging.info("="*80)
    logging.info(f"配置: {config}")
    logging.info(f"Device: {device}")
    
    # 数据集路径
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"
    
    # 文件列表（与Stage 1相同的划分）
    train_files = [
        "SC4041E0.npz", "SC4042E0.npz", "SC4051E0.npz", "SC4052E0.npz",
        "SC4061E0.npz", "SC4062E0.npz", "SC4081E0.npz", "SC4082E0.npz",
        "SC4091E0.npz", "SC4092E0.npz", "SC4101E0.npz", "SC4102E0.npz",
        "SC4111E0.npz", "SC4112E0.npz", "SC4121E0.npz", "SC4122E0.npz",
        "SC4131E0.npz", "SC4141E0.npz", "SC4142E0.npz", "SC4151E0.npz",
        "SC4152E0.npz", "SC4161E0.npz", "SC4162E0.npz", "SC4171E0.npz",
        "SC4172E0.npz", "SC4181E0.npz", "SC4182E0.npz", "SC4191E0.npz", "SC4192E0.npz"
    ]
    val_files = [
        "SC4021E0.npz", "SC4022E0.npz", "SC4031E0.npz",
        "SC4032E0.npz", "SC4071E0.npz", "SC4072E0.npz",
    ]
    test_files = [
        "SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"
    ]
    
    # 完整路径
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # 加载数据集
    logging.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=True,
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )
    
    # 创建数据加载器 - 使用更小的batch size确保稳定
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")
    
    # 创建基础模型（Stage 1）
    base_model = ProgressiveMAMBAFORMER_V1_Fixed(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)
    
    # 加载Stage 1的最佳模型
    stage1_model_path = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_simple_20250815_042107/best_model.pth"
    if os.path.exists(stage1_model_path):
        logging.info(f"加载Stage 1模型: {stage1_model_path}")
        checkpoint = torch.load(stage1_model_path, map_location=device)
        base_model.load_state_dict(checkpoint['model_state_dict'])
        logging.info("✅ 成功加载Stage 1模型(88%准确率)")
    else:
        logging.error(f"Stage 1模型不存在: {stage1_model_path}")
        return
    
    # 冻结基础模型的大部分参数（只保留最后1层transformer可训练）
    for name, param in base_model.named_parameters():
        # 只允许最后1层transformer和分类器可训练
        if "transformer_encoder.layers.11" in name or \
           "classifier" in name:
            param.requires_grad = True
        else:
            param.requires_grad = False
    
    # 创建最小影响的跨模态注意力模块
    attention_module = MinimalCrossModalAttention(
        d_model=config["d_model"],
        n_heads=config["attention_heads"],
        dropout=config["dropout"]
    ).to(device)
    
    # 统计参数
    base_trainable = sum(p.numel() for p in base_model.parameters() if p.requires_grad)
    attention_params = sum(p.numel() for p in attention_module.parameters())
    total_params = sum(p.numel() for p in base_model.parameters()) + attention_params
    
    logging.info(f"基础模型可训练参数: {base_trainable:,}")
    logging.info(f"注意力模块参数: {attention_params:,}")
    logging.info(f"总参数: {total_params:,}")
    logging.info(f"可训练比例: {(base_trainable + attention_params) / total_params * 100:.2f}%")
    
    # 定义前向传播函数
    def forward_pass(data, model, attention_module):
        """组合前向传播 - 最小影响版本"""
        batch_size, seq_len, channels, time_steps = data.shape
        
        # 重塑数据
        x_reshaped = data.view(batch_size * seq_len, channels, time_steps)
        
        # 特征提取
        features = model.feature_extractor(x_reshaped)
        features = features.view(batch_size, seq_len, config["d_model"])
        
        # 位置编码
        features = features.transpose(0, 1)
        features = model.pos_encoder(features)
        features = features.transpose(0, 1)
        
        # 通过前11层transformer（冻结的）
        for i in range(11):
            features = model.transformer_encoder.layers[i](features)
        
        # 应用最小影响的跨模态注意力（Stage 2的核心）
        features = attention_module(features)
        
        # 通过最后1层transformer（可训练的）
        features = model.transformer_encoder.layers[11](features)
        
        # 分类
        main_output = model.classifier(features)
        aux_output = model.auxiliary_head(features) if hasattr(model, 'auxiliary_head') else None
        
        return main_output, aux_output
    
    # 评估函数
    def evaluate(model, attention_module, dataloader):
        model.eval()
        attention_module.eval()
        
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for data, labels in tqdm(dataloader, desc="评估"):
                data = data.to(device)
                data = data.permute(0, 1, 3, 2)  # (B, S, C, T)
                
                main_output, _ = forward_pass(data, model, attention_module)
                
                preds = main_output.argmax(dim=-1)
                
                all_preds.append(preds.cpu().numpy())
                all_labels.append(labels.numpy())
        
        all_preds = np.concatenate(all_preds).flatten()
        all_labels = np.concatenate(all_labels).flatten()
        
        accuracy = accuracy_score(all_labels, all_preds)
        f1 = f1_score(all_labels, all_preds, average='weighted')
        
        return {'accuracy': accuracy, 'f1': f1}
    
    # 评估初始性能
    logging.info("\n评估初始性能（应该接近Stage 1的88%）...")
    initial_metrics = evaluate(base_model, attention_module, test_loader)
    logging.info(f"初始测试准确率: {initial_metrics['accuracy']*100:.2f}%")
    logging.info(f"初始alpha值: {torch.sigmoid(attention_module.alpha).item():.6f}")
    
    # 设置优化器（极低的学习率）
    optimizer_groups = [
        {'params': [p for p in base_model.parameters() if p.requires_grad], 'lr': config["base_lr"]},
        {'params': attention_module.parameters(), 'lr': config["attention_lr"]}
    ]
    optimizer = optim.AdamW(optimizer_groups, weight_decay=config["weight_decay"])
    
    # 学习率调度器
    scheduler = CosineAnnealingLR(optimizer, T_max=config["num_epochs"], eta_min=1e-7)
    
    # 损失函数
    criterion_main = SequentialFocalLoss(alpha=1, gamma=2)
    criterion_temporal = TemporalConsistencyLoss(weight=0.05)  # 更小的权重
    
    # 训练循环
    best_acc = initial_metrics['accuracy']
    patience_counter = 0
    
    for epoch in range(config["num_epochs"]):
        logging.info(f"\n{'='*60}")
        logging.info(f"Epoch {epoch+1}/{config['num_epochs']}")
        
        # 训练模式
        base_model.train()
        attention_module.train()
        
        train_loss = 0
        train_preds = []
        train_labels = []
        
        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}")
        for batch_idx, (data, labels) in enumerate(progress_bar):
            data = data.to(device)
            labels = labels.to(device)
            data = data.permute(0, 1, 3, 2)
            
            optimizer.zero_grad()
            
            # 前向传播
            main_output, aux_output = forward_pass(data, base_model, attention_module)
            
            # 计算损失
            main_loss = criterion_main(main_output, labels)
            temporal_loss = criterion_temporal(main_output)
            
            # 总损失（简化版，去掉辅助损失）
            loss = main_loss + 0.02 * temporal_loss  # 更小的temporal权重
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(
                list(base_model.parameters()) + list(attention_module.parameters()),
                config["gradient_clip"]
            )
            
            optimizer.step()
            
            # 记录
            train_loss += loss.item()
            preds = main_output.argmax(dim=-1)
            train_preds.append(preds.cpu().numpy())
            train_labels.append(labels.cpu().numpy())
            
            # 更新进度条
            if batch_idx % 10 == 0:
                alpha = torch.sigmoid(attention_module.alpha).item()
                progress_bar.set_postfix({
                    'loss': f"{loss.item():.4f}",
                    'alpha': f"{alpha:.6f}",
                    'orig%': f"{(1-alpha)*100:.2f}%"
                })
        
        # 计算训练指标
        train_preds = np.concatenate(train_preds).flatten()
        train_labels = np.concatenate(train_labels).flatten()
        train_acc = accuracy_score(train_labels, train_preds)
        avg_loss = train_loss / len(train_loader)
        
        # 验证
        val_metrics = evaluate(base_model, attention_module, val_loader)
        test_metrics = evaluate(base_model, attention_module, test_loader)
        
        # 获取当前alpha值
        current_alpha = torch.sigmoid(attention_module.alpha).item()
        clamped_alpha = min(current_alpha, 0.05)
        
        # 记录结果
        logging.info(f"训练 - Loss: {avg_loss:.4f}, Acc: {train_acc*100:.2f}%")
        logging.info(f"验证 - Acc: {val_metrics['accuracy']*100:.2f}%, F1: {val_metrics['f1']:.4f}")
        logging.info(f"测试 - Acc: {test_metrics['accuracy']*100:.2f}%, F1: {test_metrics['f1']:.4f}")
        logging.info(f"Alpha值: {current_alpha:.6f} (clamped: {clamped_alpha:.6f})")
        logging.info(f"原始特征保留: {(1-clamped_alpha)*100:.2f}%, 新特征: {clamped_alpha*100:.2f}%")
        
        # 保存最佳模型
        if test_metrics['accuracy'] > best_acc:
            best_acc = test_metrics['accuracy']
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'base_model_state_dict': base_model.state_dict(),
                'attention_module_state_dict': attention_module.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_accuracy': best_acc,
                'alpha': current_alpha,
                'config': config
            }, exp_dir / 'best_model.pth')
            
            logging.info(f"✅ 保存最佳模型，准确率: {best_acc*100:.2f}%")
        else:
            patience_counter += 1
        
        # 检查是否达到目标
        if test_metrics['accuracy'] >= 0.85:
            logging.info(f"🎉 达到目标准确率85%！当前: {test_metrics['accuracy']*100:.2f}%")
            if epoch >= 3:  # 至少训练3个epoch确保稳定
                break
        
        # 早停
        if patience_counter >= config["patience"]:
            logging.info(f"Early stopping at epoch {epoch+1}")
            break
        
        # 更新学习率
        scheduler.step()
    
    # 最终结果
    logging.info(f"\n{'='*60}")
    logging.info(f"训练完成! 最佳测试准确率: {best_acc*100:.2f}%")
    
    # 加载最佳模型查看最终alpha
    best_checkpoint = torch.load(exp_dir / 'best_model.pth', map_location=device)
    final_alpha = best_checkpoint['alpha']
    
    logging.info(f"最终alpha值: {final_alpha:.6f}")
    logging.info(f"最终原始特征保留: {(1-min(final_alpha, 0.05))*100:.2f}%")
    logging.info(f"相对Stage 1(88%)的变化: {(best_acc - 0.88)*100:+.2f}%")
    
    # 保存结果摘要
    summary = {
        'stage': 'Stage 2 - Minimal Impact Cross-Modal Attention',
        'initial_accuracy': initial_metrics['accuracy'],
        'final_accuracy': best_acc,
        'final_alpha': float(final_alpha),
        'original_feature_ratio': float(1 - min(final_alpha, 0.05)),
        'improvement_vs_stage1': float(best_acc - 0.88),
        'config': config
    }
    
    with open(exp_dir / 'result_summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    return best_acc


def main():
    # 配置 - 极度保守的设置
    config = {
        # 模型配置
        'd_model': 512,
        'n_heads': 32,
        'n_layers': 12,
        'dropout': 0.15,
        'seq_len': 7,
        
        # Stage 2特定配置
        'attention_heads': 4,  # 更少的注意力头
        
        # 训练配置 - 极低的学习率
        'batch_size': 8,        # 更小的batch size
        'base_lr': 5e-7,        # 极低的基础模型学习率
        'attention_lr': 1e-6,   # 极低的注意力模块学习率
        'num_epochs': 15,
        'gradient_clip': 0.3,   # 更严格的梯度裁剪
        'weight_decay': 1e-5,
        'patience': 7,
    }
    
    # 开始训练
    final_acc = train_stage2(config)
    
    return final_acc


if __name__ == "__main__":
    main()