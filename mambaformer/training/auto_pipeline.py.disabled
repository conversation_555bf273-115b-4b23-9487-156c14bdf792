#!/usr/bin/env python3
"""
自动化训练流程 - 监控Stage 1完成后自动启动后续阶段
"""

import os
import time
import subprocess
import logging
from pathlib import Path
from datetime import datetime


class AutoPipeline:
    def __init__(self):
        self.log_dir = Path("/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs")
        self.training_scripts = {
            "stage1": "stage1_full_training_fixed.py",
            "stage2": "stage2_cross_attention.py",
            "stage3": "stage3_adaptive_gating.py",
            "stage4": "stage4_mamba_modeling.py",
            "stage5": "stage5_progressive_classification.py"
        }
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=[
                logging.FileHandler("auto_pipeline.log"),
                logging.StreamHandler()
            ]
        )
    
    def check_training_complete(self, stage_name):
        """检查某个阶段是否训练完成"""
        # 查找最新的日志目录
        stage_dirs = sorted([d for d in self.log_dir.glob(f"{stage_name}_*") if d.is_dir()],
                          key=lambda x: x.stat().st_mtime, reverse=True)
        
        if not stage_dirs:
            return False
        
        latest_dir = stage_dirs[0]
        log_file = latest_dir / "training.log"
        
        if not log_file.exists():
            return False
        
        # 检查是否包含完成标志
        with open(log_file, 'r') as f:
            content = f.read()
            if "训练完成" in content or "最终测试结果" in content:
                # 检查测试准确率
                if "准确率:" in content:
                    lines = content.split('\n')
                    for line in lines:
                        if "准确率:" in line and "%" in line:
                            try:
                                acc = float(line.split("准确率:")[1].split("%")[0])
                                logging.info(f"{stage_name} 完成，测试准确率: {acc:.2f}%")
                                return True
                            except:
                                pass
                return True
        
        return False
    
    def get_best_model_path(self, stage_name):
        """获取某个阶段的最佳模型路径"""
        stage_dirs = sorted([d for d in self.log_dir.glob(f"{stage_name}_*") if d.is_dir()],
                          key=lambda x: x.stat().st_mtime, reverse=True)
        
        if stage_dirs:
            best_model = stage_dirs[0] / "best_model.pth"
            if best_model.exists():
                return str(best_model)
        return None
    
    def is_training_running(self, stage_name):
        """检查某个阶段是否正在训练"""
        try:
            result = subprocess.run(['pgrep', '-f', self.training_scripts.get(stage_name, "")],
                                  capture_output=True, text=True)
            return bool(result.stdout.strip())
        except:
            return False
    
    def start_training(self, stage_name):
        """启动某个阶段的训练"""
        script_path = f"mambaformer/training/{self.training_scripts[stage_name]}"
        
        if not os.path.exists(script_path):
            logging.error(f"训练脚本不存在: {script_path}")
            return False
        
        logging.info(f"启动 {stage_name} 训练...")
        
        # 使用nohup在后台运行
        log_file = f"mambaformer/logs/{stage_name}_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        cmd = f"nohup python {script_path} > {log_file} 2>&1 &"
        
        subprocess.run(cmd, shell=True)
        time.sleep(5)  # 等待进程启动
        
        if self.is_training_running(stage_name):
            logging.info(f"✅ {stage_name} 训练已启动")
            return True
        else:
            logging.error(f"❌ {stage_name} 训练启动失败")
            return False
    
    def monitor_and_proceed(self, check_interval=60):
        """监控训练进度并自动进行下一阶段"""
        stages = ["stage1", "stage2", "stage3", "stage4", "stage5"]
        current_stage_idx = 0
        
        logging.info("="*60)
        logging.info("开始自动化训练流程")
        logging.info("="*60)
        
        while current_stage_idx < len(stages):
            current_stage = stages[current_stage_idx]
            
            # 检查当前阶段是否完成
            if self.check_training_complete(current_stage):
                logging.info(f"{current_stage} 已完成")
                
                # 获取测试结果
                best_model = self.get_best_model_path(current_stage)
                if best_model:
                    logging.info(f"最佳模型: {best_model}")
                
                current_stage_idx += 1
                
                # 如果还有下一阶段，启动它
                if current_stage_idx < len(stages):
                    next_stage = stages[current_stage_idx]
                    
                    # 检查下一阶段是否已经在运行
                    if not self.is_training_running(next_stage):
                        # 等待一段时间确保资源释放
                        time.sleep(10)
                        
                        # 启动下一阶段
                        if self.start_training(next_stage):
                            logging.info(f"成功启动 {next_stage}")
                        else:
                            logging.error(f"无法启动 {next_stage}，退出")
                            break
                    else:
                        logging.info(f"{next_stage} 已在运行")
            
            # 检查当前阶段是否在运行
            elif self.is_training_running(current_stage):
                logging.info(f"{current_stage} 正在训练中...")
            
            # 如果当前阶段既没完成也没运行，尝试启动
            else:
                logging.info(f"{current_stage} 未运行，尝试启动...")
                if not self.start_training(current_stage):
                    logging.error(f"无法启动 {current_stage}，退出")
                    break
            
            # 等待下次检查
            if current_stage_idx < len(stages):
                logging.info(f"等待 {check_interval} 秒后再次检查...")
                time.sleep(check_interval)
        
        logging.info("="*60)
        logging.info("所有阶段训练完成！")
        logging.info("="*60)
        
        # 生成最终报告
        self.generate_final_report(stages)
    
    def generate_final_report(self, stages):
        """生成最终实验报告"""
        report = ["# 渐进式融合实验最终报告\n"]
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        report.append("## 各阶段结果汇总\n")
        
        for stage in stages:
            stage_dirs = sorted([d for d in self.log_dir.glob(f"{stage}_*") if d.is_dir()],
                              key=lambda x: x.stat().st_mtime, reverse=True)
            
            if stage_dirs:
                latest_dir = stage_dirs[0]
                summary_file = latest_dir / "result_summary.json"
                
                if summary_file.exists():
                    import json
                    with open(summary_file, 'r') as f:
                        summary = json.load(f)
                    
                    report.append(f"\n### {stage.upper()}: {summary.get('stage', 'Unknown')}")
                    report.append(f"- 测试准确率: {summary.get('test_accuracy', 'N/A'):.2f}%")
                    report.append(f"- F1 Score: {summary.get('test_f1_macro', 'N/A'):.4f}")
                    report.append(f"- Cohen's Kappa: {summary.get('test_kappa', 'N/A'):.4f}")
                    report.append(f"- N1召回率: {summary.get('n1_recall', 'N/A'):.3f}")
                    report.append(f"- 最佳验证准确率: {summary.get('best_val_acc', 'N/A'):.2f}%\n")
        
        report_path = self.log_dir / f"final_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_path, 'w') as f:
            f.write('\n'.join(report))
        
        logging.info(f"最终报告已保存: {report_path}")


def main():
    pipeline = AutoPipeline()
    
    # 开始监控和自动推进
    pipeline.monitor_and_proceed(check_interval=120)  # 每2分钟检查一次


if __name__ == "__main__":
    main()