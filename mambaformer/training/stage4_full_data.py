#!/usr/bin/env python3
"""
Stage 4: 完整数据版本 - 使用所有epoch数据
修复版：解决数据泄露问题，使用按受试者划分的数据集
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
from sklearn.metrics import classification_report, confusion_matrix, f1_score

import numpy as np
import os
import glob
import logging
from datetime import datetime
from pathlib import Path
import json
from tqdm import tqdm
from collections import defaultdict
import random

# 导入必要的模块
import sys
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')
from utils.sequence_dataset import SequenceSleepDataset
from models.sequential_mambaformer_v2 import PositionalEncoding
from utils.train_utils import EarlyStopping

# 复制Stage4的所有模型定义
from stage4_mamba_progressive_fixed import (
    get_subject_id,
    get_subject_based_split,
    SimplifiedMambaBlock,
    ProgressiveClassifier,
    Stage4MambaProgressiveModel
)

def train_stage4_full_data():
    # 设置日志
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = Path(f'/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_full_{timestamp}')
    log_dir.mkdir(parents=True, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / 'training.log'),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("="*80)
    logger.info("🎯 Stage 4 (完整数据版): Mamba-Transformer混合架构 + 渐进式分类策略")
    logger.info("✅ 使用完整数据：每个文件约1000个epoch")
    logger.info("✅ 已修复数据泄露问题：按受试者划分数据集")
    logger.info("目标: ≥85% accuracy (真实泛化性能)")
    logger.info("="*80)
    
    # 配置 - 由于数据量增大，减少batch size和epochs
    config = {
        'n_classes': 5,
        'd_model': 128,
        'n_heads': 8,
        'n_mamba_layers': 2,
        'n_transformer_layers': 2,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 16,  # 减小batch size因为数据量更大
        'learning_rate': 0.0003,  # 稍微降低学习率
        'num_epochs': 15,  # 减少epoch数因为数据量更大
        'weight_decay': 0.0001,
        'patience': 5,
        'coarse_weight': 0.3,
        'uncertainty_weight': 0.1,
    }
    
    logger.info(f"配置: {config}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"设备: {device}")
    
    # 加载数据（使用修复的数据划分）
    logger.info("加载完整数据集（按受试者划分）...")
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    
    train_files, val_files, test_files, subject_info = get_subject_based_split(data_dir)
    
    logger.info(f"训练集受试者: {subject_info['train_subjects']}")
    logger.info(f"验证集受试者: {subject_info['val_subjects']}")
    logger.info(f"测试集受试者: {subject_info['test_subjects']}")
    
    # 使用完整数据
    logger.info("加载完整数据（不限制max_samples_per_file）...")
    train_dataset = SequenceSleepDataset(
        train_files, 
        seq_len=config['seq_len'], 
        max_samples_per_file=None,  # 使用所有数据
        use_channels=4
    )
    val_dataset = SequenceSleepDataset(
        val_files, 
        seq_len=config['seq_len'], 
        max_samples_per_file=None,
        use_channels=4
    )
    test_dataset = SequenceSleepDataset(
        test_files, 
        seq_len=config['seq_len'], 
        max_samples_per_file=None,
        use_channels=4
    )
    
    logger.info(f"训练集: {len(train_dataset)} sequences (完整数据)")
    logger.info(f"验证集: {len(val_dataset)} sequences (完整数据)")
    logger.info(f"测试集: {len(test_dataset)} sequences (完整数据)")
    
    # 计算实际的epoch数
    logger.info(f"训练集总epochs: {train_dataset.total_epochs}")
    logger.info(f"验证集总epochs: {val_dataset.total_epochs}")
    logger.info(f"测试集总epochs: {test_dataset.total_epochs}")
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # 创建模型
    model = Stage4MambaProgressiveModel(
        n_classes=config['n_classes'],
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_mamba_layers=config['n_mamba_layers'],
        n_transformer_layers=config['n_transformer_layers'],
        dropout=config['dropout']
    ).to(device)
    
    # 统计参数量
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"创建Stage4MambaProgressiveModel: 参数量={total_params:,}")
    
    # 优化器和调度器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=5,
        T_mult=2,
        eta_min=1e-6
    )
    
    # 损失函数
    criterion_main = nn.CrossEntropyLoss()
    criterion_coarse = nn.CrossEntropyLoss()
    
    # Early stopping
    early_stopping = EarlyStopping(patience=config['patience'], delta=0.001)
    
    best_acc = 0
    best_model_path = log_dir / 'best_model.pth'
    history = {'train_loss': [], 'train_acc': [], 'val_acc': [], 'test_acc': []}
    
    # 粗分类标签映射
    def get_coarse_label(fine_label):
        # W=0, N1=1, N2=2, N3=3, REM=4
        if fine_label == 0:
            return 0  # W
        elif fine_label in [1, 2, 3]:
            return 1  # NREM
        else:
            return 2  # REM
    
    # 训练循环
    for epoch in range(config['num_epochs']):
        logger.info("\n" + "="*60)
        logger.info(f"Epoch {epoch+1}/{config['num_epochs']}")
        
        # 训练阶段
        model.train()
        train_loss = 0
        train_correct = 0
        train_total = 0
        coarse_correct = 0
        
        pbar = tqdm(train_loader, desc='Training')
        for batch_idx, (data, target) in enumerate(pbar):
            data, target = data.to(device), target.to(device)
            
            # 处理序列标签：取中间epoch作为序列标签
            if len(target.shape) == 2:  # (batch, seq_len)
                target = target[:, 2]  # 取中间位置
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(data)
            
            # 计算损失
            main_loss = criterion_main(outputs['final_logits'], target)
            
            # 粗分类损失
            coarse_target = torch.tensor([get_coarse_label(t.item()) for t in target]).to(device)
            coarse_loss = criterion_coarse(outputs['coarse_logits'], coarse_target)
            
            # 总损失
            loss = main_loss + config['coarse_weight'] * coarse_loss
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # 统计
            train_loss += loss.item()
            _, predicted = outputs['final_logits'].max(1)
            train_total += target.size(0)
            train_correct += predicted.eq(target).sum().item()
            
            _, coarse_pred = outputs['coarse_logits'].max(1)
            coarse_correct += coarse_pred.eq(coarse_target).sum().item()
            
            # 更新进度条
            current_acc = 100. * train_correct / train_total
            coarse_acc = 100. * coarse_correct / train_total
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{current_acc:.2f}%',
                'coarse': f'{coarse_acc:.2f}%'
            })
            
            # 每100个batch打印一次
            if batch_idx % 100 == 0 and batch_idx > 0:
                logger.info(f"Batch {batch_idx}/{len(train_loader)}: Loss={loss.item():.4f}, Acc={current_acc:.2f}%")
        
        train_acc = 100. * train_correct / train_total
        avg_train_loss = train_loss / len(train_loader)
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(train_acc)
        
        # 验证阶段
        model.eval()
        val_correct = 0
        val_total = 0
        val_predictions = []
        val_targets = []
        
        with torch.no_grad():
            for data, target in tqdm(val_loader, desc='Validation'):
                data, target = data.to(device), target.to(device)
                # 处理序列标签
                if len(target.shape) == 2:
                    target = target[:, 2]
                outputs = model(data)
                _, predicted = outputs['final_logits'].max(1)
                val_total += target.size(0)
                val_correct += predicted.eq(target).sum().item()
                val_predictions.extend(predicted.cpu().numpy())
                val_targets.extend(target.cpu().numpy())
        
        val_acc = 100. * val_correct / val_total
        val_f1 = f1_score(val_targets, val_predictions, average='weighted')
        history['val_acc'].append(val_acc)
        
        # 测试阶段
        test_correct = 0
        test_total = 0
        test_predictions = []
        test_targets = []
        test_coarse_correct = 0
        
        with torch.no_grad():
            for data, target in tqdm(test_loader, desc='Testing'):
                data, target = data.to(device), target.to(device)
                # 处理序列标签
                if len(target.shape) == 2:
                    target = target[:, 2]
                outputs = model(data)
                _, predicted = outputs['final_logits'].max(1)
                test_total += target.size(0)
                test_correct += predicted.eq(target).sum().item()
                test_predictions.extend(predicted.cpu().numpy())
                test_targets.extend(target.cpu().numpy())
                
                # 粗分类统计
                coarse_target = torch.tensor([get_coarse_label(t.item()) for t in target]).to(device)
                _, coarse_pred = outputs['coarse_logits'].max(1)
                test_coarse_correct += coarse_pred.eq(coarse_target).sum().item()
        
        test_acc = 100. * test_correct / test_total
        test_f1 = f1_score(test_targets, test_predictions, average='weighted')
        test_coarse_acc = 100. * test_coarse_correct / test_total
        history['test_acc'].append(test_acc)
        
        # 记录日志
        logger.info(f"训练 - Loss: {avg_train_loss:.4f}, Acc: {train_acc:.2f}%")
        logger.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_f1:.4f}")
        logger.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_f1:.4f}, Coarse Acc: {test_coarse_acc:.2f}%")
        logger.info(f"学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            best_epoch = epoch
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'test_f1': test_f1,
                'config': config,
                'subject_info': subject_info
            }, best_model_path)
            logger.info(f"✅ 保存最佳模型，准确率: {test_acc:.2f}%")
        
        # Early stopping
        early_stopping(-val_acc, model)
        if early_stopping.early_stop:
            logger.info("Early stopping triggered")
            break
        
        scheduler.step()
    
    # 训练结束，进行最终评估
    logger.info("\n" + "="*80)
    logger.info("训练完成！")
    logger.info(f"最佳测试准确率: {best_acc:.2f}% (Epoch {best_epoch+1})")
    
    # 加载最佳模型进行详细评估
    checkpoint = torch.load(best_model_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # 详细测试评估
    logger.info("\n" + "="*80)
    logger.info("最终评估结果 (Evaluation Results)")
    logger.info("="*80)
    
    all_predictions = []
    all_targets = []
    all_coarse_predictions = []
    all_coarse_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(test_loader, desc='Final Evaluation'):
            data, target = data.to(device), target.to(device)
            # 处理序列标签
            if len(target.shape) == 2:
                target = target[:, 2]
            outputs = model(data)
            
            _, predicted = outputs['final_logits'].max(1)
            all_predictions.extend(predicted.cpu().numpy())
            all_targets.extend(target.cpu().numpy())
            
            # 粗分类
            coarse_target = [get_coarse_label(t.item()) for t in target]
            _, coarse_pred = outputs['coarse_logits'].max(1)
            all_coarse_predictions.extend(coarse_pred.cpu().numpy())
            all_coarse_targets.extend(coarse_target)
    
    # 5分类结果
    logger.info("\n### 5分类结果 (Fine-grained Classification)")
    class_names = ['W', 'N1', 'N2', 'N3', 'REM']
    # 获取实际存在的类别
    unique_targets = np.unique(all_targets)
    actual_class_names = [class_names[i] for i in unique_targets]
    
    report = classification_report(all_targets, all_predictions, 
                                  labels=unique_targets,
                                  target_names=actual_class_names)
    logger.info(f"\n{report}")
    
    # 混淆矩阵
    cm = confusion_matrix(all_targets, all_predictions, labels=unique_targets)
    logger.info("\n混淆矩阵 (Confusion Matrix):")
    logger.info(f"     {' '.join([f'{name:>5}' for name in actual_class_names])}")
    for i, row in enumerate(cm):
        logger.info(f"{actual_class_names[i]:3s} {row}")
    
    # 粗分类结果
    logger.info("\n### 粗分类结果 (Coarse Classification)")
    coarse_names = ['W', 'NREM', 'REM']
    unique_coarse = np.unique(all_coarse_targets)
    actual_coarse_names = [coarse_names[i] for i in unique_coarse]
    
    coarse_report = classification_report(all_coarse_targets, all_coarse_predictions,
                                         labels=unique_coarse,
                                         target_names=actual_coarse_names)
    logger.info(f"\n{coarse_report}")
    
    # 总结
    logger.info("\n" + "="*80)
    logger.info("总结 (Summary)")
    logger.info("="*80)
    logger.info(f"最佳5分类准确率: {best_acc:.2f}%")
    logger.info(f"数据集划分:")
    logger.info(f"  训练集: {subject_info['train_subjects']} ({train_dataset.total_epochs} epochs)")
    logger.info(f"  验证集: {subject_info['val_subjects']} ({val_dataset.total_epochs} epochs)")
    logger.info(f"  测试集: {subject_info['test_subjects']} ({test_dataset.total_epochs} epochs)")
    logger.info("✅ 无数据泄露：各集合间无受试者重叠")
    logger.info("✅ 使用完整数据：每个文件的所有epoch")
    
    # 保存历史记录
    with open(log_dir / 'history.json', 'w') as f:
        json.dump(history, f, indent=2)
    
    logger.info(f"\n训练日志和模型保存在: {log_dir}")

if __name__ == "__main__":
    train_stage4_full_data()