#!/usr/bin/env python3
"""
智能训练监控系统
- 自动检测训练失败
- 分析失败原因
- 清理失败的log
- 提供改进建议
"""

import os
import time
import glob
import re
import subprocess
from datetime import datetime
import json

class TrainingMonitor:
    def __init__(self):
        self.log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/"
        self.failed_logs = []
        self.successful_logs = []
        self.error_patterns = {
            'dimension_mismatch': r'RuntimeError.*shapes cannot be multiplied|size mismatch',
            'cuda_error': r'CUDA.*error|device-side assert',
            'memory_error': r'out of memory|CUDA out of memory',
            'import_error': r'ImportError|ModuleNotFoundError',
            'index_error': r'IndexError|index.*out of range',
            'gradient_explosion': r'gradient.*inf|nan',
            'convergence_failure': r'Loss.*nan|accuracy.*0\.00',
        }
    
    def check_log_status(self, log_file):
        """检查log文件状态"""
        try:
            # 检查文件大小
            file_size = os.path.getsize(log_file)
            if file_size < 10240:  # 小于10KB通常是失败的
                return 'FAILED', 'File too small'
            
            # 读取最后100行
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            if len(lines) < 10:
                return 'FAILED', 'Too few lines'
            
            # 检查最后几行
            last_lines = ''.join(lines[-50:])
            
            # 检查错误模式
            for error_type, pattern in self.error_patterns.items():
                if re.search(pattern, last_lines, re.IGNORECASE):
                    return 'FAILED', error_type
            
            # 检查是否正常完成
            if re.search(r'训练完成|Training completed|best.*accuracy|达到目标', last_lines):
                return 'COMPLETED', None
            
            # 检查是否还在运行
            if re.search(r'Epoch.*\d+/\d+', lines[-1] if lines else ''):
                return 'RUNNING', None
            
            # 检查准确率
            acc_matches = re.findall(r'[Aa]cc.*?(\d+\.\d+)%', last_lines)
            if acc_matches:
                last_acc = float(acc_matches[-1])
                if last_acc > 80:
                    return 'SUCCESSFUL', f'Accuracy: {last_acc}%'
                elif last_acc < 50:
                    return 'FAILED', f'Low accuracy: {last_acc}%'
            
            return 'UNKNOWN', None
            
        except Exception as e:
            return 'ERROR', str(e)
    
    def analyze_failure(self, log_file, error_type):
        """分析失败原因并提供建议"""
        suggestions = {
            'dimension_mismatch': [
                "检查输入数据维度",
                "确认模型架构与数据格式匹配",
                "检查 permute/reshape 操作",
                "验证 batch_size 和 seq_len"
            ],
            'cuda_error': [
                "检查GPU内存使用",
                "减小batch_size",
                "清理GPU缓存: torch.cuda.empty_cache()",
                "检查标签索引范围"
            ],
            'memory_error': [
                "减小batch_size",
                "使用gradient accumulation",
                "减少模型参数量",
                "使用mixed precision training"
            ],
            'gradient_explosion': [
                "减小学习率",
                "增加gradient clipping",
                "使用更稳定的优化器",
                "检查损失函数"
            ],
            'convergence_failure': [
                "调整学习率",
                "检查数据预处理",
                "使用预训练权重",
                "增加warmup步数"
            ],
            'index_error': [
                "检查数据集标签范围",
                "验证类别数量设置",
                "检查序列长度处理"
            ]
        }
        
        return suggestions.get(error_type, ["检查详细错误信息"])
    
    def clean_failed_logs(self):
        """清理失败的log文件"""
        log_patterns = [
            "stage*.log",
            "stage*_output*.log",
            "stage*_manual*.log"
        ]
        
        cleaned_count = 0
        
        for pattern in log_patterns:
            log_files = glob.glob(os.path.join(self.log_dir, pattern))
            
            for log_file in log_files:
                status, error_type = self.check_log_status(log_file)
                
                if status == 'FAILED':
                    file_size = os.path.getsize(log_file) / 1024  # KB
                    
                    if file_size < 100:  # 只删除小于100KB的失败文件
                        print(f"删除失败log: {os.path.basename(log_file)} ({file_size:.1f}KB, {error_type})")
                        os.remove(log_file)
                        cleaned_count += 1
                        
                        # 同时删除相关的空目录
                        dir_name = log_file.replace('.log', '')
                        if os.path.exists(dir_name) and os.path.isdir(dir_name):
                            try:
                                # 检查目录是否为空或只有小文件
                                dir_files = os.listdir(dir_name)
                                if len(dir_files) == 0 or (len(dir_files) == 1 and 'training.log' in dir_files):
                                    subprocess.run(['rm', '-rf', dir_name], check=True)
                                    print(f"  删除相关目录: {os.path.basename(dir_name)}")
                            except:
                                pass
        
        return cleaned_count
    
    def monitor_active_training(self):
        """监控正在进行的训练"""
        active_trainings = []
        
        # 查找最近的log文件
        recent_logs = []
        for pattern in ["stage*improved*.log", "stage*_20*.log"]:
            logs = glob.glob(os.path.join(self.log_dir, pattern))
            for log in logs:
                mtime = os.path.getmtime(log)
                if time.time() - mtime < 3600:  # 最近1小时内修改的
                    recent_logs.append(log)
        
        for log_file in recent_logs:
            status, info = self.check_log_status(log_file)
            
            if status in ['RUNNING', 'SUCCESSFUL']:
                # 获取最新的训练信息
                with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    
                # 查找最新的epoch和准确率
                epoch_info = None
                acc_info = None
                
                for line in reversed(lines[-50:]):
                    if not epoch_info and 'Epoch' in line:
                        epoch_match = re.search(r'Epoch\s+(\d+)/(\d+)', line)
                        if epoch_match:
                            epoch_info = f"Epoch {epoch_match.group(1)}/{epoch_match.group(2)}"
                    
                    if not acc_info and 'Acc' in line:
                        acc_match = re.search(r'[Aa]cc.*?(\d+\.\d+)%', line)
                        if acc_match:
                            acc_info = f"Acc: {acc_match.group(1)}%"
                    
                    if epoch_info and acc_info:
                        break
                
                active_trainings.append({
                    'file': os.path.basename(log_file),
                    'status': status,
                    'epoch': epoch_info or 'N/A',
                    'accuracy': acc_info or 'N/A'
                })
        
        return active_trainings
    
    def generate_report(self):
        """生成监控报告"""
        print("="*80)
        print(f"🔍 训练监控报告 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)
        
        # 清理失败的logs
        cleaned = self.clean_failed_logs()
        if cleaned > 0:
            print(f"\n✅ 已清理 {cleaned} 个失败的log文件")
        
        # 监控活跃的训练
        active = self.monitor_active_training()
        
        if active:
            print("\n📊 正在进行的训练:")
            print("-"*60)
            for training in active:
                print(f"📁 {training['file']}")
                print(f"   状态: {training['status']}")
                print(f"   进度: {training['epoch']}")
                print(f"   性能: {training['accuracy']}")
                print()
        
        # 统计成功的训练
        successful_stages = {}
        for stage in range(1, 6):
            pattern = f"stage{stage}_*/best_model.pth"
            models = glob.glob(os.path.join(self.log_dir, pattern))
            if models:
                # 获取最佳模型的准确率
                for model_path in models:
                    try:
                        checkpoint = torch.load(model_path, map_location='cpu')
                        acc = checkpoint.get('test_acc', checkpoint.get('val_acc', 0))
                        if acc > successful_stages.get(stage, 0):
                            successful_stages[stage] = acc
                    except:
                        pass
        
        if successful_stages:
            print("\n✅ 成功完成的阶段:")
            print("-"*60)
            for stage, acc in sorted(successful_stages.items()):
                print(f"Stage {stage}: {acc:.2f}%")
            
            avg_acc = sum(successful_stages.values()) / len(successful_stages)
            print(f"\n平均准确率: {avg_acc:.2f}%")
        
        # 检查失败模式
        failed_patterns = {}
        for pattern in ["stage*.log"]:
            logs = glob.glob(os.path.join(self.log_dir, pattern))
            for log in logs[-20:]:  # 只检查最近的20个
                status, error_type = self.check_log_status(log)
                if status == 'FAILED' and error_type:
                    failed_patterns[error_type] = failed_patterns.get(error_type, 0) + 1
        
        if failed_patterns:
            print("\n⚠️ 常见失败原因:")
            print("-"*60)
            for error_type, count in sorted(failed_patterns.items(), key=lambda x: x[1], reverse=True):
                print(f"{error_type}: {count}次")
                suggestions = self.analyze_failure(None, error_type)
                for i, suggestion in enumerate(suggestions[:2], 1):
                    print(f"  {i}. {suggestion}")
        
        print("\n" + "="*80)

def main():
    monitor = TrainingMonitor()
    
    # 先生成一次报告
    monitor.generate_report()
    
    # 持续监控
    print("\n开始持续监控... (按Ctrl+C退出)")
    
    try:
        import torch  # 延迟导入
    except:
        print("警告: 无法导入torch，部分功能可能受限")
    
    while True:
        try:
            time.sleep(300)  # 每5分钟检查一次
            os.system('clear')
            monitor.generate_report()
        except KeyboardInterrupt:
            print("\n监控已停止")
            break
        except Exception as e:
            print(f"监控错误: {e}")
            time.sleep(60)

if __name__ == "__main__":
    main()