"""
序列数据集类，支持多epoch滑动窗口
"""

import numpy as np
import torch
from torch.utils.data import Dataset
import logging

class SequenceSleepDataset(Dataset):
    """
    序列睡眠数据集
    - 支持多epoch序列（默认5个）
    - 滑动窗口步长为1
    - 序列不跨文件边界
    - 支持epoch级别的评估
    """
    def __init__(self, data_files, max_samples_per_file=150, seq_len=5, 
                 use_channels=4, transform=None, is_training=True):
        """
        Args:
            data_files: NPZ文件路径列表
            max_samples_per_file: 每个文件最大样本数
            seq_len: 序列长度（epoch数）
            use_channels: 使用的通道数（1-4）
            transform: 数据增强函数
            is_training: 是否为训练模式
        """
        self.data_files = data_files
        self.max_samples_per_file = max_samples_per_file
        self.seq_len = seq_len
        self.use_channels = use_channels
        self.transform = transform
        self.is_training = is_training
        
        # 加载所有数据并创建序列
        self.sequences = []
        self.labels = []
        self.sequence_info = []  # 存储每个序列的元信息
        self.epoch_to_file_mapping = {}  # epoch索引到文件的映射
        self.total_epochs = 0  # 总epoch数
        
        self._load_all_sequences()
        
        logging.info(f"创建序列数据集: {len(self.sequences)}个序列, "
                    f"序列长度={seq_len}, 通道数={use_channels}, "
                    f"总epochs={self.total_epochs}")
    
    def _load_all_sequences(self):
        """加载所有文件并创建序列"""
        global_epoch_idx = 0
        
        for file_idx, file_path in enumerate(self.data_files):
            try:
                # 加载单个文件
                data = np.load(file_path)
                x = data['x'][:, :, :self.use_channels]  # (n_epochs, 3000, channels)
                y = data['y']
                
                # 限制样本数
                if self.max_samples_per_file and len(x) > self.max_samples_per_file:
                    x = x[:self.max_samples_per_file]
                    y = y[:self.max_samples_per_file]
                
                # 记录这个文件的epoch范围
                file_start_epoch = global_epoch_idx
                file_end_epoch = global_epoch_idx + len(x)
                
                # 为每个epoch创建文件映射
                for local_epoch_idx in range(len(x)):
                    self.epoch_to_file_mapping[global_epoch_idx + local_epoch_idx] = {
                        'file_idx': file_idx,
                        'file_path': file_path,
                        'local_epoch_idx': local_epoch_idx,
                        'global_epoch_idx': global_epoch_idx + local_epoch_idx
                    }
                
                # 创建序列（滑动窗口）
                file_sequences, file_labels, file_seq_info = self._create_sequences_with_info(
                    x, y, file_start_epoch, file_idx, file_path)
                
                self.sequences.extend(file_sequences)
                self.labels.extend(file_labels)
                self.sequence_info.extend(file_seq_info)
                
                global_epoch_idx += len(x)
                
            except Exception as e:
                logging.warning(f"加载文件 {file_path} 失败: {e}")
                continue
        
        self.total_epochs = global_epoch_idx
        logging.info(f"从 {len(self.data_files)} 个文件加载了 {self.total_epochs} 个epochs, "
                    f"创建了 {len(self.sequences)} 个序列")
    
    def _create_sequences_with_info(self, data, labels, file_start_epoch, file_idx, file_path):
        """
        创建滑动窗口序列，同时记录元信息
        Args:
            data: (n_epochs, 3000, channels)
            labels: (n_epochs,)
            file_start_epoch: 该文件在全局中的起始epoch索引
            file_idx: 文件索引
            file_path: 文件路径
        Returns:
            sequences: list of (seq_len, 3000, channels)
            seq_labels: list of (seq_len,)
            seq_info: list of dict with sequence metadata
        """
        sequences = []
        seq_labels = []
        seq_info = []
        
        # 滑动窗口，步长为1
        for i in range(len(data) - self.seq_len + 1):
            seq_data = data[i:i + self.seq_len]  # (seq_len, 3000, channels)
            seq_label = labels[i:i + self.seq_len]  # (seq_len,)
            
            # 记录序列的元信息
            info = {
                'start_epoch_idx': file_start_epoch + i,  # 全局epoch索引
                'end_epoch_idx': file_start_epoch + i + self.seq_len - 1,
                'file_idx': file_idx,
                'file_path': file_path,
                'local_start_idx': i,  # 在文件内的起始索引
                'epoch_indices': list(range(file_start_epoch + i, file_start_epoch + i + self.seq_len))
            }
            
            sequences.append(seq_data)
            seq_labels.append(seq_label)
            seq_info.append(info)
        
        return sequences, seq_labels, seq_info
    
    def _create_sequences(self, data, labels):
        """
        兼容性函数，调用新的带info的函数
        """
        sequences, seq_labels, _ = self._create_sequences_with_info(data, labels, 0, 0, "")
        return sequences, seq_labels
    
    def __len__(self):
        return len(self.sequences)
    
    def __getitem__(self, idx):
        """
        返回一个序列
        Returns:
            x: tensor of shape (seq_len, 3000, channels)
            y: tensor of shape (seq_len,)
        """
        x = self.sequences[idx]  # (seq_len, 3000, channels)
        y = self.labels[idx]     # (seq_len,)
        
        # 转换为tensor
        x = torch.FloatTensor(x)
        y = torch.LongTensor(y)
        
        # 数据增强（仅训练时）
        if self.transform and self.is_training:
            x = self.transform(x)
        
        return x, y
    
    def get_sequence_info(self, idx):
        """
        获取序列的元信息
        Args:
            idx: 序列索引
        Returns:
            info: 包含序列元信息的字典
        """
        if idx < len(self.sequence_info):
            return self.sequence_info[idx]
        else:
            return None
    
    def get_total_epochs(self):
        """获取总的epoch数量"""
        return self.total_epochs
    
    def get_epoch_mapping(self):
        """获取epoch到文件的映射"""
        return self.epoch_to_file_mapping


class SequenceDataAugmentation:
    """序列级别的数据增强"""
    def __init__(self, p=0.5):
        self.p = p
    
    def __call__(self, x):
        """
        对序列进行数据增强
        Args:
            x: tensor of shape (seq_len, 3000, channels)
        """
        if torch.rand(1).item() < self.p:
            # 时间偏移（整个序列一起偏移）
            shift = torch.randint(-50, 50, (1,)).item()
            x = torch.roll(x, shifts=shift, dims=1)
            
            # 幅度缩放（整个序列使用相同缩放）
            scale = torch.FloatTensor(1).uniform_(0.9, 1.1).item()
            x = x * scale
            
            # 添加噪声（每个epoch独立）
            if torch.rand(1).item() < 0.3:
                noise = torch.randn_like(x) * 0.05
                x = x + noise
        
        return x


def create_sequence_dataloaders(fold_info, batch_size=16, seq_len=5, 
                               use_channels=3, max_samples_per_file=150):
    """
    创建序列数据加载器
    Args:
        fold_info: 包含train_files和test_files的字典
        batch_size: 批次大小（注意：相比单epoch需要更小）
        seq_len: 序列长度
        use_channels: 使用的通道数
        max_samples_per_file: 每个文件最大样本数
    Returns:
        train_loader, val_loader, test_loader
    """
    # 数据增强
    transform = SequenceDataAugmentation(p=0.5)
    
    # 训练集
    train_dataset = SequenceSleepDataset(
        fold_info['train_files'],
        max_samples_per_file=max_samples_per_file,
        seq_len=seq_len,
        use_channels=use_channels,
        transform=transform,
        is_training=True
    )
    
    # 验证集（从训练集分出20%）
    n_train = len(train_dataset)
    n_val = int(0.2 * n_train)
    n_train_split = n_train - n_val
    
    train_subset, val_subset = torch.utils.data.random_split(
        train_dataset, [n_train_split, n_val]
    )
    
    # 测试集
    test_dataset = SequenceSleepDataset(
        fold_info['test_files'],
        max_samples_per_file=None,  # 测试时使用所有数据
        seq_len=seq_len,
        use_channels=use_channels,
        transform=None,
        is_training=False
    )
    
    # 创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_subset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=4,
        pin_memory=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_subset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    return train_loader, val_loader, test_loader