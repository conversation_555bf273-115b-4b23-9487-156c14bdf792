#!/usr/bin/env python3
"""
Stage 3: 最小化改动策略 - 简单门控
核心思想：只添加一个简单的门控机制，不破坏原有特征
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import (
    confusion_matrix,
    classification_report,
    accuracy_score,
    f1_score,
    cohen_kappa_score,
)
import warnings

warnings.filterwarnings("ignore")

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.progressive_mambaformer_v1_fixed import ProgressiveMAMBAFORMER_V1_Fixed
from utils.sequence_dataset import SequenceSleepDataset


class SimpleGating(nn.Module):
    """
    极简的门控模块
    只学习一个权重来平衡不同特征
    """
    def __init__(self, d_model=512):
        super().__init__()
        # 简单的门控网络
        self.gate_net = nn.Sequential(
            nn.Linear(d_model, d_model // 8),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 8, 1),
            nn.Sigmoid()
        )
        
        # 特征变换（可选的轻量级处理）
        self.feature_transform = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 初始化为0.5附近，平衡原始和变换特征
        with torch.no_grad():
            self.gate_net[-2].bias.data.fill_(0.0)
        
    def forward(self, x):
        # x: [batch_size, seq_len, d_model]
        
        # 计算门控权重
        gate = self.gate_net(x)  # [batch_size, seq_len, 1]
        
        # 变换特征
        transformed = self.feature_transform(x)
        
        # 门控融合：平衡原始特征和变换特征
        output = gate * x + (1 - gate) * transformed
        
        return output, gate.mean()


def train_one_epoch(model, gating_module, data_loader, criterion, optimizer, device, epoch, config):
    """训练一个epoch"""
    model.eval()  # 基础模型保持eval模式
    gating_module.train()  # 只训练门控模块
    
    running_loss = 0.0
    running_gate = 0.0
    correct_predictions = 0
    total_predictions = 0
    
    progress_bar = tqdm(data_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']}")
    
    for batch_idx, (data, target) in enumerate(progress_bar):
        data, target = data.to(device), target.to(device)
        data = data.permute(0, 1, 3, 2)
        
        optimizer.zero_grad()
        
        # 通过基础模型（不计算梯度）
        with torch.no_grad():
            # 重塑数据
            batch_size, seq_len, channels, time_steps = data.shape
            x_reshaped = data.view(batch_size * seq_len, channels, time_steps)
            
            # 提取特征
            base_features = model.feature_extractor(x_reshaped)  # (batch*seq_len, d_model)
            base_features = base_features.view(batch_size, seq_len, model.d_model)
            
            # 添加位置编码
            base_features = base_features.transpose(0, 1)  # (seq_len, batch, d_model)
            base_features = model.pos_encoder(base_features)
            base_features = base_features.transpose(0, 1)  # (batch, seq_len, d_model)
            
            # 通过前10层encoder
            for i in range(10):
                base_features = model.transformer_encoder.layers[i](base_features)
        
        # 应用门控（只这部分计算梯度）
        gated_features, gate_value = gating_module(base_features)
        running_gate += gate_value.item()
        
        # 通过剩余的encoder层
        with torch.no_grad():
            for i in range(10, 12):
                gated_features = model.transformer_encoder.layers[i](gated_features)
        
        # 需要梯度来计算损失
        gated_features.requires_grad_(True)
        
        # 分类器
        main_output = model.classifier(gated_features)
        auxiliary_output = model.auxiliary_classifier(gated_features) if hasattr(model, 'auxiliary_classifier') else None
        
        # 计算准确率
        batch_size, seq_len, n_classes = main_output.shape
        _, predicted = torch.max(main_output[:, seq_len//2, :], dim=-1)
        true_labels = target[:, seq_len//2]
        correct_predictions += (predicted == true_labels).sum().item()
        total_predictions += true_labels.size(0)
        
        # 计算损失
        main_output_flat = main_output.reshape(-1, n_classes)
        target_flat = target.reshape(-1)
        
        main_loss = criterion(main_output_flat, target_flat)
        
        if auxiliary_output is not None:
            aux_output_flat = auxiliary_output.reshape(-1, n_classes)
            aux_loss = criterion(aux_output_flat, target_flat)
            total_loss = 0.9 * main_loss + 0.1 * aux_loss
        else:
            total_loss = main_loss
        
        total_loss.backward()
        
        # 只更新门控模块的梯度
        torch.nn.utils.clip_grad_norm_(gating_module.parameters(), config['gradient_clip'])
        
        optimizer.step()
        
        running_loss += total_loss.item()
        
        current_acc = 100 * correct_predictions / total_predictions
        avg_gate = running_gate / (batch_idx + 1)
        
        progress_bar.set_postfix({
            'loss': f'{running_loss/(batch_idx+1):.4f}',
            'acc': f'{current_acc:.2f}%',
            'gate': f'{avg_gate:.3f}',
            'lr': f'{optimizer.param_groups[0]["lr"]:.7f}'
        })
    
    epoch_loss = running_loss / len(data_loader)
    epoch_acc = 100 * correct_predictions / total_predictions
    
    return epoch_loss, epoch_acc


def evaluate(model, gating_module, data_loader, device):
    """评估模型"""
    model.eval()
    gating_module.eval()
    
    all_predictions = []
    all_targets = []
    all_gates = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc="Evaluating"):
            data = data.to(device)
            data = data.permute(0, 1, 3, 2)
            
            # 通过基础模型
            batch_size, seq_len, channels, time_steps = data.shape
            x_reshaped = data.view(batch_size * seq_len, channels, time_steps)
            
            # 提取特征
            base_features = model.feature_extractor(x_reshaped)
            base_features = base_features.view(batch_size, seq_len, model.d_model)
            
            # 添加位置编码
            base_features = base_features.transpose(0, 1)
            base_features = model.pos_encoder(base_features)
            base_features = base_features.transpose(0, 1)
            
            # 通过前10层
            for i in range(10):
                base_features = model.encoder.layers[i](base_features)
            
            # 应用门控
            gated_features, gate_value = gating_module(base_features)
            all_gates.append(gate_value.item())
            
            # 剩余层
            for i in range(10, 12):
                gated_features = model.transformer_encoder.layers[i](gated_features)
            
            # 分类
            main_output = model.classifier(gated_features)
            
            seq_len = main_output.size(1)
            predictions = torch.argmax(main_output[:, seq_len//2, :], dim=-1)
            targets = target[:, seq_len//2]
            
            all_predictions.extend(predictions.cpu().numpy())
            all_targets.extend(targets.numpy())
    
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    avg_gate = np.mean(all_gates)
    
    accuracy = accuracy_score(all_targets, all_predictions)
    f1_macro = f1_score(all_targets, all_predictions, average='macro')
    kappa = cohen_kappa_score(all_targets, all_predictions)
    
    report = classification_report(all_targets, all_predictions, 
                                 target_names=['W', 'N1', 'N2', 'N3', 'REM'],
                                 output_dict=True)
    
    cm = confusion_matrix(all_targets, all_predictions)
    
    return {
        'accuracy': accuracy,
        'f1_macro': f1_macro,
        'kappa': kappa,
        'classification_report': report,
        'confusion_matrix': cm,
        'avg_gate': avg_gate
    }


def main():
    config = {
        "d_model": 512,
        "n_heads": 32,
        "n_layers": 12,
        "dropout": 0.15,
        "seq_len": 7,
        "batch_size": 16,
        "learning_rate": 5e-5,  # 较低的学习率
        "num_epochs": 10,
        "gradient_clip": 0.5,
        "weight_decay": 1e-5,  # 很小的weight decay
    }

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage3_minimal_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(os.path.join(log_dir, "training.log")),
            logging.StreamHandler(),
        ],
    )

    logging.info("="*80)
    logging.info("🎯 Stage 3: 最小化改动策略 - 简单门控")
    logging.info("="*80)
    logging.info(f"配置: {config}")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"Device: {device}")

    # 数据准备
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"
    
    train_files = [
        "SC4181E0.npz", "SC4182E0.npz", "SC4161E0.npz", "SC4162E0.npz",
        "SC4131E0.npz", "SC4101E0.npz", "SC4102E0.npz", "SC4111E0.npz",
        "SC4112E0.npz", "SC4041E0.npz", "SC4042E0.npz", "SC4191E0.npz",
        "SC4192E0.npz", "SC4061E0.npz", "SC4062E0.npz", "SC4091E0.npz",
        "SC4092E0.npz", "SC4121E0.npz", "SC4122E0.npz", "SC4141E0.npz",
        "SC4142E0.npz", "SC4051E0.npz", "SC4052E0.npz", "SC4081E0.npz",
        "SC4082E0.npz", "SC4151E0.npz", "SC4152E0.npz", "SC4171E0.npz",
        "SC4172E0.npz",
    ]
    val_files = [
        "SC4021E0.npz", "SC4022E0.npz", "SC4031E0.npz",
        "SC4032E0.npz", "SC4071E0.npz", "SC4072E0.npz",
    ]
    test_files = [
        "SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"
    ]

    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]

    logging.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=200,
        is_training=True,
    )

    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=200,
        is_training=False,
    )

    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=200,
        is_training=False,
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")

    # 加载Stage 1的最佳模型
    base_model = ProgressiveMAMBAFORMER_V1_Fixed(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)
    
    # 加载Stage 1权重
    stage1_path = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_simple_20250815_042107/best_model.pth"
    if os.path.exists(stage1_path):
        logging.info(f"加载Stage 1最佳模型: {stage1_path}")
        checkpoint = torch.load(stage1_path)
        base_model.load_state_dict(checkpoint['model_state_dict'])
        logging.info(f"✅ 成功加载Stage 1模型")
    else:
        logging.error("找不到Stage 1的模型！")
        return
    
    # 冻结基础模型的所有参数
    for param in base_model.parameters():
        param.requires_grad = False
    
    # 创建门控模块
    gating_module = SimpleGating(d_model=config["d_model"]).to(device)
    
    # 统计参数
    gating_params = sum(p.numel() for p in gating_module.parameters())
    total_params = sum(p.numel() for p in base_model.parameters()) + gating_params
    logging.info(f"门控模块参数: {gating_params:,}")
    logging.info(f"总参数: {total_params:,}")
    logging.info(f"可训练比例: {100*gating_params/total_params:.2f}%")
    
    # 评估初始性能
    logging.info("\n评估初始性能（应该等于Stage 1的88%）...")
    initial_metrics = evaluate(base_model, gating_module, test_loader, device)
    initial_acc = initial_metrics['accuracy'] * 100
    logging.info(f"初始测试准确率: {initial_acc:.2f}%")
    logging.info(f"初始门控值: {initial_metrics['avg_gate']:.3f}")
    
    if abs(initial_acc - 88.0) > 1.0:
        logging.warning(f"⚠️ 初始准确率与Stage 1不符！期望~88%，实际{initial_acc:.2f}%")
    
    # 优化器 - 只优化门控模块
    optimizer = optim.AdamW(
        gating_module.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"]
    )
    
    # 学习率调度器
    scheduler = CosineAnnealingLR(optimizer, T_max=config["num_epochs"], eta_min=1e-6)
    
    # 损失函数
    criterion = nn.CrossEntropyLoss()
    
    best_test_acc = initial_acc
    best_epoch = -1
    patience = 5
    patience_counter = 0
    
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_acc': [],
        'test_acc': [],
        'gate_values': []
    }
    
    for epoch in range(config["num_epochs"]):
        logging.info(f"\n{'='*60}")
        logging.info(f"Epoch {epoch+1}/{config['num_epochs']}")
        
        # 训练
        train_loss, train_acc = train_one_epoch(
            base_model, gating_module, train_loader, 
            criterion, optimizer, device, epoch, config
        )
        
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        
        # 验证
        val_metrics = evaluate(base_model, gating_module, val_loader, device)
        val_acc = val_metrics['accuracy'] * 100
        
        # 测试
        test_metrics = evaluate(base_model, gating_module, test_loader, device)
        test_acc = test_metrics['accuracy'] * 100
        
        logging.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_metrics['f1_macro']:.4f}")
        logging.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_metrics['f1_macro']:.4f}")
        logging.info(f"平均门控值: {test_metrics['avg_gate']:.3f}")
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_acc'].append(val_acc)
        history['test_acc'].append(test_acc)
        history['gate_values'].append(test_metrics['avg_gate'])
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_epoch = epoch
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'base_model_state_dict': base_model.state_dict(),
                'gating_module_state_dict': gating_module.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config,
                'history': history,
            }, os.path.join(log_dir, 'best_model.pth'))
            logging.info(f"✅ 保存最佳模型 (Test: {test_acc:.2f}%)")
            
            if test_acc >= 89.0:
                logging.info(f"🎉 达到目标！测试准确率: {test_acc:.2f}% >= 89%")
                break
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
        
        # 调整学习率
        scheduler.step()
    
    logging.info(f"\n{'='*60}")
    logging.info(f"训练完成! 最佳测试准确率: {best_test_acc:.2f}% (Epoch {best_epoch+1})")
    logging.info(f"相对Stage 1的改进: {best_test_acc - initial_acc:+.2f}%")
    
    # 显示各类别性能
    if best_epoch >= 0:
        logging.info("\n最佳模型的各类别性能:")
        for class_name in ['W', 'N1', 'N2', 'N3', 'REM']:
            class_metrics = test_metrics['classification_report'][class_name]
            logging.info(f"{class_name:>4}: Precision={class_metrics['precision']:.3f}, "
                        f"Recall={class_metrics['recall']:.3f}, "
                        f"F1={class_metrics['f1-score']:.3f}")
    
    # 保存结果
    result_summary = {
        'stage': 'Stage 3 Minimal',
        'initial_accuracy': initial_acc,
        'final_accuracy': best_test_acc,
        'improvement': best_test_acc - initial_acc,
        'best_epoch': best_epoch + 1,
        'config': config,
        'gate_evolution': history['gate_values']
    }
    
    with open(os.path.join(log_dir, 'result_summary.json'), 'w') as f:
        json.dump(result_summary, f, indent=2)
    
    return best_test_acc


if __name__ == "__main__":
    final_acc = main()
    print(f"\n最终准确率: {final_acc:.2f}%")