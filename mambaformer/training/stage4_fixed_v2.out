2025-08-16 22:41:12,521 - INFO - ================================================================================
2025-08-16 22:41:12,521 - INFO - 🎯 Stage 4 (修复版): Mamba-Transformer混合架构 + 渐进式分类策略
2025-08-16 22:41:12,521 - INFO - ✅ 已修复数据泄露问题：按受试者划分数据集
2025-08-16 22:41:12,521 - INFO - 目标: ≥75% accuracy (真实泛化性能)
2025-08-16 22:41:12,522 - INFO - ================================================================================
2025-08-16 22:41:12,522 - INFO - 配置: {'n_classes': 5, 'd_model': 128, 'n_heads': 8, 'n_mamba_layers': 2, 'n_transformer_layers': 2, 'dropout': 0.15, 'seq_len': 5, 'batch_size': 32, 'learning_rate': 0.0005, 'num_epochs': 30, 'weight_decay': 0.0001, 'patience': 10, 'coarse_weight': 0.3, 'uncertainty_weight': 0.1}
2025-08-16 22:41:12,552 - INFO - 设备: cuda
2025-08-16 22:41:12,552 - INFO - 加载数据集（按受试者划分）...
2025-08-16 22:41:12,553 - INFO - 训练集受试者: ['01', '04', '05', '06', '09', '10', '11', '12', '13', '14', '15', '17', '18', '19']
2025-08-16 22:41:12,553 - INFO - 验证集受试者: ['02', '07', '16']
2025-08-16 22:41:12,553 - INFO - 测试集受试者: ['00', '03', '08']
2025-08-16 22:41:14,852 - INFO - 从 27 个文件加载了 4050 个epochs, 创建了 3942 个序列
2025-08-16 22:41:14,852 - INFO - 创建序列数据集: 3942个序列, 序列长度=5, 通道数=4, 总epochs=4050
2025-08-16 22:41:15,305 - INFO - 从 6 个文件加载了 900 个epochs, 创建了 876 个序列
2025-08-16 22:41:15,305 - INFO - 创建序列数据集: 876个序列, 序列长度=5, 通道数=4, 总epochs=900
2025-08-16 22:41:15,719 - INFO - 从 6 个文件加载了 900 个epochs, 创建了 876 个序列
2025-08-16 22:41:15,719 - INFO - 创建序列数据集: 876个序列, 序列长度=5, 通道数=4, 总epochs=900
2025-08-16 22:41:15,719 - INFO - 训练集: 3942 sequences
2025-08-16 22:41:15,719 - INFO - 验证集: 876 sequences
2025-08-16 22:41:15,719 - INFO - 测试集: 876 sequences
2025-08-16 22:41:16,001 - INFO - 创建Stage4MambaProgressiveModel: 参数量=869,390
2025-08-16 22:41:17,327 - INFO - 
============================================================
2025-08-16 22:41:17,327 - INFO - Epoch 1/30

Training:   0%|          | 0/124 [00:00<?, ?it/s]
Training:   0%|          | 0/124 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive_fixed.py", line 664, in <module>
    train_stage4_fixed()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive_fixed.py", line 475, in train_stage4_fixed
    main_loss = criterion_main(outputs['final_logits'], target)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/loss.py", line 1297, in forward
    return F.cross_entropy(
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/functional.py", line 3494, in cross_entropy
    return torch._C._nn.cross_entropy_loss(
ValueError: Expected input batch_size (160) to match target batch_size (32).
