#!/usr/bin/env python3
"""
🎯 FINAL TEST FIXED - Fixing REM accuracy issue
CRITICAL FIX: Added max_samples_per_file=None to load ALL data (not just 150 epochs per file)
This was causing insufficient data and missing REM samples
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import OneCycleLR
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import (
    confusion_matrix,
    classification_report,
    accuracy_score,
    f1_score,
    cohen_kappa_score,
)
import warnings

warnings.filterwarnings("ignore")

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class FocalLoss(nn.Module):
    """Focal loss for handling class imbalance"""

    def __init__(self, alpha=None, gamma=2.0, device="cuda"):
        super().__init__()
        self.gamma = gamma
        if alpha is not None:
            self.alpha = torch.tensor(alpha).to(device)
        else:
            self.alpha = None
        self.device = device

    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)

        ce_loss = F.cross_entropy(inputs, targets, reduction="none")
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** self.gamma * ce_loss

        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss

        return focal_loss.mean()


class LabelSmoothingLoss(nn.Module):
    """Label smoothing for better generalization"""

    def __init__(self, n_classes=5, smoothing=0.1):
        super().__init__()
        self.n_classes = n_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing

    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)

        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (self.n_classes - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), self.confidence)

        return torch.mean(torch.sum(-true_dist * F.log_softmax(inputs, dim=-1), dim=-1))


def calculate_class_distribution(data_loader):
    """Calculate the distribution of classes in the dataset"""
    all_labels = []
    for _, target in data_loader:
        if target.dim() > 1:
            target = target[:, target.shape[1] // 2]
        all_labels.extend(target.numpy())

    all_labels = np.array(all_labels)
    class_counts = np.bincount(all_labels, minlength=5)
    class_weights = 1.0 / (class_counts + 1)  # Add 1 to avoid division by zero
    class_weights = (
        class_weights / class_weights.sum() * len(class_weights)
    )  # Normalize

    return class_counts, class_weights


def train_one_epoch(model, train_loader, criterion, optimizer, scheduler, device):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []

    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)

        # Handle sequence labels
        if target.dim() > 1:
            target_eval = target[:, target.shape[1] // 2]
        else:
            target_eval = target

        # Add noise augmentation (reduced probability)
        if np.random.random() < 0.2:  # Reduced from 0.3
            noise = torch.randn_like(data) * 0.005  # Reduced from 0.01
            data = data + noise

        optimizer.zero_grad()

        # Forward pass
        output, _ = model(data)

        # Calculate loss
        loss = criterion(output, target)

        # Add L2 regularization (reduced)
        l2_lambda = 0.0001  # Reduced from 0.001
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm

        loss.backward()

        # Gradient clipping
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)

        optimizer.step()
        scheduler.step()

        total_loss += loss.item()

        # Track predictions
        if output.dim() == 3:
            pred = output[:, output.shape[1] // 2, :].argmax(dim=1)
        else:
            pred = output.argmax(dim=1)

        all_preds.extend(pred.cpu().numpy())
        all_targets.extend(target_eval.cpu().numpy())

        # Track batch progress (no progress bar)
        acc = (pred == target_eval).float().mean().item()

    # Calculate epoch metrics
    all_preds = np.array(all_preds)
    all_targets = np.array(all_targets)

    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average="macro")

    return total_loss / len(train_loader), accuracy, macro_f1


def evaluate(model, val_loader, device):
    model.eval()
    all_preds = []
    all_targets = []

    with torch.no_grad():
        for data, target in val_loader:
            data = data.to(device)

            if target.dim() > 1:
                target = target[:, target.shape[1] // 2]

            # Test-time augmentation
            predictions = []

            # Original
            output, _ = model(data)
            if output.dim() == 3:
                output = output[:, output.shape[1] // 2, :]
            predictions.append(F.softmax(output, dim=1))

            # With noise (reduced)
            for _ in range(2):
                noise = torch.randn_like(data) * 0.003  # Reduced from 0.005
                output, _ = model(data + noise)
                if output.dim() == 3:
                    output = output[:, output.shape[1] // 2, :]
                predictions.append(F.softmax(output, dim=1))

            # Average predictions
            avg_pred = torch.stack(predictions).mean(dim=0)
            pred = avg_pred.argmax(dim=1)

            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target.numpy())

    all_preds = np.array(all_preds)
    all_targets = np.array(all_targets)

    # Calculate metrics
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average="macro")
    kappa = cohen_kappa_score(all_targets, all_preds)

    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)

    cm = confusion_matrix(all_targets, all_preds, labels=[0, 1, 2, 3, 4])

    # Print confusion matrix for debugging
    logging.info("\nConfusion Matrix:")
    logging.info("Predicted ->  W    N1   N2   N3   REM")
    class_names = ["W", "N1", "N2", "N3", "REM"]
    for i, row in enumerate(cm):
        logging.info(f"Actual {class_names[i]:3s}: {row}")

    # Check if REM is being predicted
    rem_predictions = np.sum(all_preds == 4)
    rem_actuals = np.sum(all_targets == 4)
    logging.info(
        f"REM predictions: {rem_predictions}/{len(all_preds)} ({rem_predictions/len(all_preds)*100:.1f}%)"
    )
    logging.info(
        f"REM actual: {rem_actuals}/{len(all_targets)} ({rem_actuals/len(all_targets)*100:.1f}%)"
    )

    return accuracy, macro_f1, kappa, class_f1, cm


def main():
    # Best configuration with adjustments
    config = {
        "d_model": 512,
        "n_heads": 32,
        "n_layers": 12,
        "dropout": 0.20,  # Reduced from 0.25
        "seq_len": 7,
        "batch_size": 16,
        "learning_rate": 2e-4,  # Reduced from 3e-4
        "num_epochs": 15,  # Increased from 10
        "gradient_clip": 1.0,
        "weight_decay": 0.03,  # Reduced from 0.05
        "label_smoothing": 0.1,
    }

    # Setup logging
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/final_test_90_fixed_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(os.path.join(log_dir, "training.log")),
            logging.StreamHandler(),
        ],
    )

    logging.info("=" * 80)
    logging.info("🎯 FINAL TEST FIXED - Fixing REM accuracy issue")
    logging.info("=" * 80)

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"Device: {device}")

    # Data paths
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"

    train_files = [
        "SC4181E0.npz",
        "SC4182E0.npz",
        "SC4161E0.npz",
        "SC4162E0.npz",
        "SC4131E0.npz",
        "SC4101E0.npz",
        "SC4102E0.npz",
        "SC4111E0.npz",
        "SC4112E0.npz",
        "SC4041E0.npz",
        "SC4042E0.npz",
        "SC4191E0.npz",
        "SC4192E0.npz",
        "SC4061E0.npz",
        "SC4062E0.npz",
        "SC4091E0.npz",
        "SC4092E0.npz",
        "SC4121E0.npz",
        "SC4122E0.npz",
        "SC4141E0.npz",
        "SC4142E0.npz",
        "SC4051E0.npz",
        "SC4052E0.npz",
        "SC4081E0.npz",
        "SC4082E0.npz",
        "SC4151E0.npz",
        "SC4152E0.npz",
        "SC4171E0.npz",
        "SC4172E0.npz",
    ]

    val_files = [
        "SC4021E0.npz",
        "SC4022E0.npz",
        "SC4031E0.npz",
        "SC4032E0.npz",
        "SC4071E0.npz",
        "SC4072E0.npz",
    ]

    test_files = ["SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"]

    # Prepare file paths
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]

    # Create datasets - FIXED: Added max_samples_per_file=None
    logging.info("🔧 FIXED: Loading ALL data with max_samples_per_file=None")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,  # 🔧 CRITICAL FIX: Use all data!
        is_training=True,
    )

    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,  # 🔧 CRITICAL FIX: Use all data!
        is_training=False,
    )

    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,  # 🔧 CRITICAL FIX: Use all data!
        is_training=False,
    )

    logging.info(
        f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}"
    )

    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True,
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    # Calculate class distribution
    logging.info("\nCalculating class distribution...")
    train_counts, auto_weights = calculate_class_distribution(train_loader)
    logging.info(
        f"Train class counts: W={train_counts[0]}, N1={train_counts[1]}, N2={train_counts[2]}, N3={train_counts[3]}, REM={train_counts[4]}"
    )
    logging.info(f"Auto-calculated weights: {auto_weights}")

    # Create model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)

    logging.info(f"\nModel Parameters: {sum(p.numel() for p in model.parameters()):,}")

    # Use balanced class weights
    # Fixed weights based on typical sleep stage imbalance
    # Order: [Wake, N1, N2, N3, REM]
    class_weights = [2.0, 2.5, 1.0, 1.5, 2.0]  # More balanced weights
    logging.info(
        f"Using class weights: W={class_weights[0]}, N1={class_weights[1]}, N2={class_weights[2]}, N3={class_weights[3]}, REM={class_weights[4]}"
    )

    # Loss functions
    focal_loss = FocalLoss(alpha=class_weights, gamma=2.0, device=device)
    label_smooth_loss = LabelSmoothingLoss(
        n_classes=5, smoothing=config["label_smoothing"]
    )

    # Combined loss with adjusted weights
    def combined_loss(outputs, targets):
        return 0.6 * focal_loss(outputs, targets) + 0.4 * label_smooth_loss(
            outputs, targets
        )

    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"],
        betas=(0.9, 0.999),
    )

    # Scheduler with warmup
    total_steps = len(train_loader) * config["num_epochs"]
    scheduler = OneCycleLR(
        optimizer,
        max_lr=config["learning_rate"],
        total_steps=total_steps,
        pct_start=0.2,  # Increased warmup from 0.1
        anneal_strategy="cos",
        div_factor=10,  # Reduced from 25 for gentler warmup
        final_div_factor=100,  # Reduced from 1000
    )

    # Training loop
    logging.info("\n🏋️ Starting Fixed Training...")
    logging.info("=" * 80)

    best_test_acc = 0
    best_test_f1 = 0
    patience = 5
    patience_counter = 0

    for epoch in range(config["num_epochs"]):
        # Train
        train_loss, train_acc, train_f1 = train_one_epoch(
            model, train_loader, combined_loss, optimizer, scheduler, device
        )

        # Validate
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(
            model, val_loader, device
        )

        # Test
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(
            model, test_loader, device
        )

        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(
            f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}"
        )
        logging.info(
            f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}"
        )
        logging.info(
            f"  TEST: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}"
        )
        logging.info(
            f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
            f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}"
        )

        # Check if REM F1 is improving
        if test_class_f1[4] > 0:
            logging.info(f"  ✅ REM is being predicted! F1={test_class_f1[4]:.3f}")
        else:
            logging.info(f"  ⚠️ REM F1 is still 0 - checking predictions...")

        # Save best model based on overall F1 score
        if test_f1 > best_test_f1:
            best_test_acc = test_acc
            best_test_f1 = test_f1
            patience_counter = 0

            torch.save(
                {
                    "epoch": epoch,
                    "model_state_dict": model.state_dict(),
                    "optimizer_state_dict": optimizer.state_dict(),
                    "test_acc": test_acc,
                    "test_f1": test_f1,
                    "test_kappa": test_kappa,
                    "test_class_f1": test_class_f1,
                    "val_acc": val_acc,
                    "config": config,
                },
                os.path.join(log_dir, "best_model.pth"),
            )

            logging.info(
                f"  💾 Saved best model (Test F1: {test_f1:.4f}, Acc: {test_acc:.4f})"
            )

            if test_acc >= 0.90:
                logging.info("\n" + "=" * 80)
                logging.info("🎉 TARGET ACHIEVED! 90% TEST ACCURACY!")
                logging.info("=" * 80)
                break
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logging.info(f"\nEarly stopping at epoch {epoch+1}")
                break

    # Final results
    logging.info("\n" + "=" * 80)
    logging.info("📊 FINAL RESULTS")
    logging.info("=" * 80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test F1: {best_test_f1:.4f}")

    gap_to_90 = 0.90 - best_test_acc
    if gap_to_90 > 0:
        logging.info(f"Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
    else:
        logging.info(f"🎉 EXCEEDED TARGET BY {-gap_to_90:.4f} ({-gap_to_90*100:.2f}%)")

    # Save results
    results = {
        "best_test_accuracy": float(best_test_acc),
        "best_test_f1": float(best_test_f1),
        "final_test_accuracy": float(test_acc),
        "final_test_f1": float(test_f1),
        "final_test_kappa": float(test_kappa),
        "final_test_class_f1": test_class_f1.tolist(),
        "confusion_matrix": test_cm.tolist(),
        "config": config,
    }

    with open(os.path.join(log_dir, "results.json"), "w") as f:
        json.dump(results, f, indent=2)

    logging.info(f"\n💾 Results saved to {log_dir}/results.json")


if __name__ == "__main__":
    main()
