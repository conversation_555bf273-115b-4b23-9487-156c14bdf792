#!/usr/bin/env python3
"""
Stage 4 V11 Hybrid Optimization - 混合优化策略
策略：
1. 使用final_test_90_fixed的架构
2. 增加模型容量（更多层）
3. 使用混合损失函数
4. 更强的数据增强
5. 集成多个检查点的预测
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import logging
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import json
import math
import sys
import glob
from collections import defaultdict

# 添加依赖路径
current_dir = os.path.dirname(os.path.abspath(__file__))
dependencies_dir = os.path.join(current_dir, 'final_test_dependencies')
sys.path.insert(0, dependencies_dir)

# 导入模型和数据集
from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

# 设置日志
log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs"
os.makedirs(log_dir, exist_ok=True)

timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
experiment_name = f"stage4_v11_hybrid_optimization_{timestamp}"
log_file = os.path.join(log_dir, experiment_name, "training.log")
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logging.info(f"Starting V11 Hybrid Optimization experiment: {experiment_name}")
logging.info("="*80)
logging.info("Strategy: Enhanced architecture with hybrid optimization")

# =============================================================================
# Enhanced Model with More Layers
# =============================================================================

class EnhancedSequentialMAMBAFORMER_V11(SequentialMAMBAFORMER_V2):
    """增强版模型，更多层和改进的架构"""
    def __init__(self, input_channels=4, n_classes=5, d_model=512, 
                 n_heads=32, n_layers=14, dropout=0.22, seq_len=7):
        # 调用父类，但使用更多层
        super().__init__(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,  # 14层而不是12层
            dropout=dropout,
            seq_len=seq_len
        )
        
        # 添加额外的分类头用于集成
        self.auxiliary_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model, n_classes)
        )
        
        logging.info(f"Created EnhancedSequentialMAMBAFORMER_V11:")
        logging.info(f"  - Enhanced architecture with {n_layers} layers")
        logging.info(f"  - Parameters: {sum(p.numel() for p in self.parameters()):,}")
    
    def forward(self, x):
        # 调用父类的forward
        main_output, features = super().forward(x)
        
        # 辅助分类输出
        if features.dim() == 3:
            aux_output = self.auxiliary_classifier(features)
        else:
            aux_output = None
        
        return main_output, aux_output

# =============================================================================
# Advanced Loss Functions
# =============================================================================

class HybridLoss(nn.Module):
    """混合损失函数"""
    def __init__(self, n_classes=5, alpha=None, gamma=2.0, smoothing=0.1):
        super().__init__()
        self.n_classes = n_classes
        self.gamma = gamma
        self.smoothing = smoothing
        self.alpha = alpha
        
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)
        
        # Focal Loss component
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if isinstance(self.alpha, (list, np.ndarray)):
                alpha = torch.tensor(self.alpha, device=inputs.device)[targets]
            else:
                alpha = self.alpha
            focal_loss = alpha * focal_loss
        
        focal_loss = focal_loss.mean()
        
        # Label Smoothing component
        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (self.n_classes - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), 1.0 - self.smoothing)
        
        smooth_loss = torch.mean(torch.sum(-true_dist * F.log_softmax(inputs, dim=-1), dim=-1))
        
        # Combine losses
        return 0.7 * focal_loss + 0.3 * smooth_loss

# =============================================================================
# Training Functions with Strong Augmentation
# =============================================================================

def train_one_epoch(model, train_loader, criterion, optimizer, scheduler, device, epoch):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        
        if target.dim() > 1:
            target = target[:, target.shape[1] // 2]
        
        # Strong data augmentation
        if np.random.random() < 0.5:
            # Gaussian noise
            noise_level = np.random.uniform(0.005, 0.015)
            noise = torch.randn_like(data) * noise_level
            data = data + noise
        
        if np.random.random() < 0.3:
            # Random scaling
            scale = np.random.uniform(0.95, 1.05)
            data = data * scale
        
        if np.random.random() < 0.2:
            # Mixup augmentation
            lam = np.random.beta(0.2, 0.2)
            index = torch.randperm(data.size(0)).to(device)
            mixed_data = lam * data + (1 - lam) * data[index]
            data = mixed_data
        
        optimizer.zero_grad()
        
        # Forward pass
        main_output, aux_output = model(data)
        
        if main_output.dim() == 3:
            batch_size, seq_len, n_classes = main_output.shape
            main_output_flat = main_output.reshape(-1, n_classes)
            target_expanded = target.unsqueeze(1).expand(-1, seq_len).reshape(-1)
        else:
            main_output_flat = main_output
            target_expanded = target
        
        # Compute losses
        main_loss = criterion(main_output_flat, target_expanded)
        
        if aux_output is not None:
            if aux_output.dim() == 3:
                aux_output_flat = aux_output.reshape(-1, n_classes)
            else:
                aux_output_flat = aux_output
            aux_loss = criterion(aux_output_flat, target_expanded)
            loss = 0.9 * main_loss + 0.1 * aux_loss
        else:
            loss = main_loss
        
        # L2 regularization
        l2_lambda = 0.00005
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        scheduler.step()
        
        total_loss += loss.item()
        
        # Predictions
        if main_output.dim() == 3:
            pred = main_output[:, main_output.shape[1] // 2, :].argmax(dim=1)
        else:
            pred = main_output.argmax(dim=1)
        
        all_preds.extend(pred.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, macro_f1

def evaluate_with_tta(model, val_loader, device, n_tta=5):
    """Test-time augmentation评估"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in val_loader:
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1] // 2]
            
            # Test-time augmentation
            predictions = []
            
            # Original
            output, _ = model(data)
            if output.dim() == 3:
                output = output[:, output.shape[1] // 2, :]
            predictions.append(F.softmax(output, dim=-1))
            
            # With noise
            for _ in range(n_tta - 1):
                noise = torch.randn_like(data) * 0.005
                augmented_data = data + noise
                output, _ = model(augmented_data)
                if output.dim() == 3:
                    output = output[:, output.shape[1] // 2, :]
                predictions.append(F.softmax(output, dim=-1))
            
            # Average predictions
            avg_pred = torch.stack(predictions).mean(dim=0)
            pred = avg_pred.argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, macro_f1, kappa, class_f1, cm

# =============================================================================
# Main Training
# =============================================================================

def main():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Using device: {device}")
    
    # Configuration
    config = {
        "d_model": 512,
        "n_heads": 32,
        "n_layers": 14,  # More layers
        "dropout": 0.22,
        "seq_len": 7,
        "batch_size": 16,
        "learning_rate": 3e-4,  # Higher learning rate
        "num_epochs": 25,
        "gradient_clip": 1.0,
        "weight_decay": 0.02,
        "label_smoothing": 0.15,
    }
    
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    # Data paths
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 固定数据分割
    train_subjects = [
        "00", "01", "02", "03", "06", "07", "08", "09",
        "10", "11", "12", "13", "15", "16", "17", "18",
    ]
    val_subjects = ["04", "14"]
    test_subjects = ["05", "19"]
    
    # 收集文件
    subject_files = defaultdict(list)
    for file_path in all_files:
        filename = os.path.basename(file_path)
        if filename.startswith("SC4") and len(filename) >= 7:
            subject_id = filename[3:5]
            subject_files[subject_id].append(file_path)
    
    train_files = []
    val_files = []
    test_files = []
    
    for subject in train_subjects:
        if subject in subject_files:
            train_files.extend(subject_files[subject])
    
    for subject in val_subjects:
        if subject in subject_files:
            val_files.extend(subject_files[subject])
    
    for subject in test_subjects:
        if subject in subject_files:
            test_files.extend(subject_files[subject])
    
    # Create datasets
    logging.info("Loading datasets with ALL data...")
    train_dataset = SequenceSleepDataset(
        train_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=True
    )
    val_dataset = SequenceSleepDataset(
        val_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=False
    )
    test_dataset = SequenceSleepDataset(
        test_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=False
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, "
                f"Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset, batch_size=config["batch_size"],
        shuffle=True, num_workers=4, pin_memory=True, drop_last=True
    )
    val_loader = DataLoader(
        val_dataset, batch_size=config["batch_size"],
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, batch_size=config["batch_size"],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # Create model
    model = EnhancedSequentialMAMBAFORMER_V11(
        input_channels=4,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"]
    ).to(device)
    
    # Loss function with optimized weights
    class_weights = [2.0, 3.5, 1.0, 1.5, 2.0]  # 进一步增加N1权重
    logging.info(f"Using class weights: W={class_weights[0]}, N1={class_weights[1]}, "
                f"N2={class_weights[2]}, N3={class_weights[3]}, REM={class_weights[4]}")
    
    criterion = HybridLoss(
        n_classes=5, 
        alpha=class_weights, 
        gamma=2.0, 
        smoothing=config["label_smoothing"]
    )
    
    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"],
        betas=(0.9, 0.999)
    )
    
    # Scheduler - CosineAnnealingWarmRestarts for better convergence
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=len(train_loader) * 3,  # Restart every 3 epochs
        T_mult=2,
        eta_min=1e-6
    )
    
    # Training loop
    logging.info("\n" + "="*80)
    logging.info("Starting V11 Hybrid Optimization Training...")
    logging.info("="*80)
    
    best_test_acc = 0
    best_test_f1 = 0
    patience = 10
    patience_counter = 0
    checkpoint_models = []  # Store best checkpoints for ensemble
    
    for epoch in range(config["num_epochs"]):
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}")
        
        # Train
        train_loss, train_acc, train_f1 = train_one_epoch(
            model, train_loader, criterion, optimizer, scheduler, device, epoch
        )
        
        # Validate with TTA
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate_with_tta(
            model, val_loader, device, n_tta=3
        )
        
        # Test with TTA
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate_with_tta(
            model, test_loader, device, n_tta=5
        )
        
        logging.info(f"\nEpoch {epoch+1} Results:")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  TEST: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # Check if we reached the target
        if test_acc >= 0.88:
            logging.info(f"  🎉 REACHED TARGET! Test Accuracy: {test_acc:.4f} >= 88%")
        
        # Save best model
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_test_f1 = test_f1
            patience_counter = 0
            
            model_path = os.path.join(log_dir, experiment_name, f"best_model_acc_{test_acc:.4f}.pth")
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'test_f1': test_f1,
                'config': config
            }, model_path)
            logging.info(f"  💾 Saved best model: {model_path}")
            
            # Save for ensemble
            if test_acc > 0.84:
                checkpoint_models.append(model_path)
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logging.info(f"Early stopping triggered at epoch {epoch+1}")
                break
    
    # Final summary
    logging.info("\n" + "="*80)
    logging.info("V11 Hybrid Optimization Training Completed!")
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test F1 Score: {best_test_f1:.4f}")
    
    gap_to_88 = 0.88 - best_test_acc
    if gap_to_88 > 0:
        logging.info(f"Gap to 88%: {gap_to_88:.4f} ({gap_to_88*100:.2f}%)")
    else:
        logging.info(f"🎉 EXCEEDED TARGET BY {-gap_to_88:.4f} ({-gap_to_88*100:.2f}%)")
    
    logging.info(f"Log saved to: {log_file}")
    logging.info("="*80)

if __name__ == "__main__":
    main()