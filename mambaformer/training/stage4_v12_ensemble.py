#!/usr/bin/env python3
"""
Stage 4 V12 Ensemble - 集成多个最佳模型
策略：
1. 加载多个已训练的最佳模型
2. 使用投票或平均策略进行集成
3. 实现加权集成策略
4. 目标：通过集成达到88%的测试准确率
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import logging
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import json
import sys
import glob
from collections import defaultdict

# 添加依赖路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, os.path.join(current_dir, 'stage4_mp_dependencies'))
sys.path.insert(0, os.path.join(current_dir, 'final_test_dependencies'))

# 导入数据集
from utils.sequence_dataset import SequenceSleepDataset

# 设置日志
log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs"
os.makedirs(log_dir, exist_ok=True)

timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
experiment_name = f"stage4_v12_ensemble_{timestamp}"
log_file = os.path.join(log_dir, experiment_name, "ensemble_evaluation.log")
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logging.info(f"Starting V12 Ensemble experiment: {experiment_name}")
logging.info("="*80)
logging.info("Strategy: Ensemble multiple best models for improved accuracy")

# =============================================================================
# Model Loading Functions
# =============================================================================

def load_checkpoint(checkpoint_path, device):
    """加载模型检查点"""
    logging.info(f"Loading checkpoint: {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # 获取测试准确率
    test_acc = checkpoint.get('test_acc', 0.0)
    logging.info(f"  Model test accuracy: {test_acc:.4f}")
    
    return checkpoint['model_state_dict'], test_acc

def create_model_from_checkpoint(model_state_dict, device):
    """从检查点创建模型"""
    # 分析状态字典确定模型架构
    d_model = 512  # 默认值
    n_layers = 12  # 默认值
    
    # 检查是否有transformer层
    transformer_layers = [k for k in model_state_dict.keys() if 'transformer_encoder' in k]
    if transformer_layers:
        # 计算层数
        layer_indices = set()
        for key in transformer_layers:
            if 'layers.' in key:
                parts = key.split('.')
                for i, part in enumerate(parts):
                    if part == 'layers' and i+1 < len(parts):
                        try:
                            layer_idx = int(parts[i+1])
                            layer_indices.add(layer_idx)
                        except:
                            pass
        if layer_indices:
            n_layers = max(layer_indices) + 1
    
    # 检查d_model
    for key, value in model_state_dict.items():
        if 'transformer_encoder.layers.0.self_attn.in_proj_weight' in key:
            d_model = value.shape[1]
            break
    
    logging.info(f"  Detected architecture: d_model={d_model}, n_layers={n_layers}")
    
    # 尝试导入相应的模型
    try:
        if n_layers == 14:
            # V11模型
            from stage4_v11_hybrid_optimization import EnhancedSequentialMAMBAFORMER_V11
            model = EnhancedSequentialMAMBAFORMER_V11(
                input_channels=4, n_classes=5, d_model=d_model, 
                n_heads=32, n_layers=n_layers, dropout=0.2, seq_len=7
            ).to(device)
        elif 'coarse_classifier' in ' '.join(model_state_dict.keys()):
            # V8/V9渐进式模型
            from stage4_v8_final_architecture import ProgressiveSequentialMAMBAFORMER_V8
            model = ProgressiveSequentialMAMBAFORMER_V8(
                input_channels=4, n_classes=5, d_model=d_model,
                n_heads=32, n_layers=n_layers, dropout=0.2, seq_len=7
            ).to(device)
        else:
            # V10或标准模型
            from stage4_v10_optimized import OptimizedSequentialMAMBAFORMER_V10
            model = OptimizedSequentialMAMBAFORMER_V10(
                input_channels=4, n_classes=5, d_model=d_model,
                n_heads=32, n_layers=n_layers, dropout=0.2, seq_len=7
            ).to(device)
    except Exception as e:
        logging.warning(f"  Failed to load specific model: {e}")
        # 使用通用模型
        from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
        model = SequentialMAMBAFORMER_V2(
            input_channels=4, n_classes=5, d_model=d_model,
            n_heads=32, n_layers=n_layers, dropout=0.2, seq_len=7
        ).to(device)
    
    # 加载权重
    model.load_state_dict(model_state_dict, strict=False)
    model.eval()
    
    return model

# =============================================================================
# Ensemble Evaluation
# =============================================================================

def ensemble_predict(models, data, weights=None, method='average'):
    """集成预测"""
    predictions = []
    
    with torch.no_grad():
        for model in models:
            # 获取模型输出
            output = model(data)
            
            # 处理不同模型的输出格式
            if isinstance(output, tuple):
                output = output[0]  # 取主输出
            
            if output.dim() == 3:
                # 取中间时间步
                output = output[:, output.shape[1] // 2, :]
            
            # 转换为概率
            probs = F.softmax(output, dim=-1)
            predictions.append(probs)
    
    # 集成预测
    if method == 'average':
        # 简单平均
        if weights is None:
            ensemble_pred = torch.stack(predictions).mean(dim=0)
        else:
            # 加权平均
            weighted_preds = []
            for pred, weight in zip(predictions, weights):
                weighted_preds.append(pred * weight)
            ensemble_pred = torch.stack(weighted_preds).sum(dim=0)
    elif method == 'voting':
        # 投票
        votes = []
        for pred in predictions:
            vote = pred.argmax(dim=-1)
            votes.append(vote)
        votes = torch.stack(votes)
        # 获取众数
        ensemble_pred = torch.mode(votes, dim=0)[0]
        return ensemble_pred
    else:
        raise ValueError(f"Unknown ensemble method: {method}")
    
    return ensemble_pred.argmax(dim=-1)

def evaluate_ensemble(models, test_loader, device, weights=None, method='average'):
    """评估集成模型"""
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in test_loader:
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1] // 2]
            
            # 集成预测
            pred = ensemble_predict(models, data, weights, method)
            
            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target.numpy())
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, macro_f1, kappa, class_f1, cm

# =============================================================================
# Main Evaluation
# =============================================================================

def main():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Using device: {device}")
    
    # 找到所有最佳模型检查点
    model_paths = []
    
    # 查找V8-V10的最佳模型
    log_dirs = glob.glob(os.path.join(log_dir, "stage4_v*"))
    for dir_path in log_dirs:
        best_models = glob.glob(os.path.join(dir_path, "best_model_acc_*.pth"))
        if best_models:
            # 选择准确率最高的
            best_models.sort(key=lambda x: float(x.split('acc_')[1].split('.pth')[0]), reverse=True)
            if float(best_models[0].split('acc_')[1].split('.pth')[0]) > 0.83:  # 只选择83%以上的模型
                model_paths.append(best_models[0])
    
    # 添加已知的高性能模型
    known_good_models = [
        "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v10_optimized_20250820_005622/best_model_acc_0.8548.pth",
        "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v8_final_architecture_20250819_232253/best_model_acc_0.8595.pth",
        "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v9_improved_progressive_20250820_001529/best_model_acc_0.8512.pth",
    ]
    
    for path in known_good_models:
        if os.path.exists(path) and path not in model_paths:
            model_paths.append(path)
    
    if not model_paths:
        logging.error("No models found for ensemble!")
        return
    
    logging.info(f"Found {len(model_paths)} models for ensemble:")
    
    # 加载模型
    models = []
    model_accuracies = []
    
    for path in model_paths:
        try:
            state_dict, acc = load_checkpoint(path, device)
            model = create_model_from_checkpoint(state_dict, device)
            models.append(model)
            model_accuracies.append(acc)
        except Exception as e:
            logging.warning(f"Failed to load model {path}: {e}")
    
    if not models:
        logging.error("No models loaded successfully!")
        return
    
    logging.info(f"Successfully loaded {len(models)} models")
    
    # 计算权重（基于准确率）
    weights = np.array(model_accuracies)
    weights = weights / weights.sum()
    logging.info(f"Model weights based on accuracy: {weights}")
    
    # 加载测试数据
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 固定测试集
    test_subjects = ["05", "19"]
    
    subject_files = defaultdict(list)
    for file_path in all_files:
        filename = os.path.basename(file_path)
        if filename.startswith("SC4") and len(filename) >= 7:
            subject_id = filename[3:5]
            subject_files[subject_id].append(file_path)
    
    test_files = []
    for subject in test_subjects:
        if subject in subject_files:
            test_files.extend(subject_files[subject])
    
    logging.info(f"Loading test dataset from {len(test_files)} files...")
    test_dataset = SequenceSleepDataset(
        test_files, seq_len=7, max_samples_per_file=None, is_training=False
    )
    
    test_loader = DataLoader(
        test_dataset, batch_size=16, shuffle=False, num_workers=4, pin_memory=True
    )
    
    logging.info(f"Test dataset size: {len(test_dataset)}")
    
    # 评估不同的集成策略
    logging.info("\n" + "="*80)
    logging.info("Evaluating ensemble strategies...")
    logging.info("="*80)
    
    # 1. 简单平均
    logging.info("\n1. Simple Average Ensemble:")
    acc, f1, kappa, class_f1, cm = evaluate_ensemble(
        models, test_loader, device, weights=None, method='average'
    )
    logging.info(f"  TEST: Acc={acc:.4f}, F1={f1:.4f}, Kappa={kappa:.4f}")
    logging.info(f"  Class F1: W={class_f1[0]:.3f}, N1={class_f1[1]:.3f}, "
                f"N2={class_f1[2]:.3f}, N3={class_f1[3]:.3f}, REM={class_f1[4]:.3f}")
    
    if acc >= 0.88:
        logging.info(f"  🎉 REACHED TARGET with Simple Average! Accuracy: {acc:.4f} >= 88%")
    
    simple_avg_acc = acc
    
    # 2. 加权平均
    logging.info("\n2. Weighted Average Ensemble:")
    acc, f1, kappa, class_f1, cm = evaluate_ensemble(
        models, test_loader, device, weights=weights, method='average'
    )
    logging.info(f"  TEST: Acc={acc:.4f}, F1={f1:.4f}, Kappa={kappa:.4f}")
    logging.info(f"  Class F1: W={class_f1[0]:.3f}, N1={class_f1[1]:.3f}, "
                f"N2={class_f1[2]:.3f}, N3={class_f1[3]:.3f}, REM={class_f1[4]:.3f}")
    
    if acc >= 0.88:
        logging.info(f"  🎉 REACHED TARGET with Weighted Average! Accuracy: {acc:.4f} >= 88%")
    
    weighted_avg_acc = acc
    
    # 3. 投票
    logging.info("\n3. Voting Ensemble:")
    acc, f1, kappa, class_f1, cm = evaluate_ensemble(
        models, test_loader, device, weights=None, method='voting'
    )
    logging.info(f"  TEST: Acc={acc:.4f}, F1={f1:.4f}, Kappa={kappa:.4f}")
    logging.info(f"  Class F1: W={class_f1[0]:.3f}, N1={class_f1[1]:.3f}, "
                f"N2={class_f1[2]:.3f}, N3={class_f1[3]:.3f}, REM={class_f1[4]:.3f}")
    
    if acc >= 0.88:
        logging.info(f"  🎉 REACHED TARGET with Voting! Accuracy: {acc:.4f} >= 88%")
    
    voting_acc = acc
    
    # 最终总结
    logging.info("\n" + "="*80)
    logging.info("V12 Ensemble Evaluation Summary:")
    logging.info("="*80)
    
    best_acc = max(simple_avg_acc, weighted_avg_acc, voting_acc)
    best_method = "Simple Average" if simple_avg_acc == best_acc else \
                 "Weighted Average" if weighted_avg_acc == best_acc else "Voting"
    
    logging.info(f"Best ensemble method: {best_method}")
    logging.info(f"Best test accuracy: {best_acc:.4f} ({best_acc*100:.2f}%)")
    
    gap_to_88 = 0.88 - best_acc
    if gap_to_88 > 0:
        logging.info(f"Gap to 88%: {gap_to_88:.4f} ({gap_to_88*100:.2f}%)")
    else:
        logging.info(f"🎉 EXCEEDED TARGET BY {-gap_to_88:.4f} ({-gap_to_88*100:.2f}%)")
    
    logging.info(f"Log saved to: {log_file}")
    logging.info("="*80)

if __name__ == "__main__":
    main()