#!/usr/bin/env python3
"""
Stage 4: Mamba-Transformer混合架构 + 渐进式分类策略
完整实现原始方案的核心创新：
1. Mamba组件用于长程依赖建模
2. 模态内特征精炼（EEG局部注意力，EOG/EMG轻量级Mamba）
3. 渐进式分类策略（粗分类→细分类）
4. 不确定性估计和置信度决策
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, WeightedRandomSampler
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
from torch.cuda.amp import autocast, GradScaler

import numpy as np
import os
import glob
import logging
from datetime import datetime
from pathlib import Path
import json
from tqdm import tqdm
import math

# 导入必要的模块
import sys
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')
from utils.sequence_dataset import SequenceSleepDataset
from models.sequential_mambaformer_v2 import PositionalEncoding
from utils.train_utils import EarlyStopping

# ===================== Mamba组件 =====================

class SimplifiedMambaBlock(nn.Module):
    """简化的Mamba块实现（避免外部依赖）"""
    def __init__(self, d_model, d_state=16, d_conv=4, expand_factor=2):
        super().__init__()
        self.d_model = d_model
        self.d_inner = d_model * expand_factor
        
        # 输入投影
        self.in_proj = nn.Linear(d_model, self.d_inner * 2)
        
        # 1D卷积用于局部特征
        self.conv1d = nn.Conv1d(
            self.d_inner, self.d_inner, 
            kernel_size=d_conv, padding=d_conv//2, 
            groups=self.d_inner
        )
        
        # SSM参数
        self.x_proj = nn.Linear(self.d_inner, d_state * 2 + 1)
        self.dt_proj = nn.Linear(1, self.d_inner)
        
        # A矩阵（状态转移）
        A = torch.arange(1, d_state + 1).reshape(1, d_state).repeat(self.d_inner, 1)
        self.A_log = nn.Parameter(torch.log(A))
        
        # D参数（跳跃连接）
        self.D = nn.Parameter(torch.ones(self.d_inner))
        
        # 输出投影
        self.out_proj = nn.Linear(self.d_inner, d_model)
        
        self.activation = nn.SiLU()
        self.norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        """x: (batch, seq_len, d_model)"""
        batch_size, seq_len, _ = x.shape
        residual = x
        
        # 输入投影和激活
        x_proj = self.in_proj(x)
        x_conv, x_gate = x_proj.chunk(2, dim=-1)
        
        # 卷积处理
        x_conv = x_conv.transpose(1, 2)  # (batch, d_inner, seq_len)
        x_conv = self.conv1d(x_conv)
        # 确保序列长度匹配
        if x_conv.size(2) != seq_len:
            x_conv = x_conv[:, :, :seq_len]
        x_conv = x_conv.transpose(1, 2)  # (batch, seq_len, d_inner)
        x_conv = self.activation(x_conv)
        
        # 确保x_gate维度匹配
        if x_gate.size(1) != x_conv.size(1):
            # 如果维度不匹配，调整x_gate
            if x_gate.size(1) > x_conv.size(1):
                x_gate = x_gate[:, :x_conv.size(1), :]
            else:
                # 填充x_gate
                padding = x_conv.size(1) - x_gate.size(1)
                x_gate = F.pad(x_gate, (0, 0, 0, padding))
        
        # SSM处理（简化版）
        ssm_params = self.x_proj(x_conv)
        B, C, delta = torch.split(
            ssm_params, [self.A_log.shape[1], self.A_log.shape[1], 1], dim=-1
        )
        delta = F.softplus(delta)
        
        # 简化的状态空间计算
        A = -torch.exp(self.A_log)
        
        # 离散化
        deltaA = torch.exp(delta.unsqueeze(-2) * A.unsqueeze(0).unsqueeze(0))
        deltaB = delta.unsqueeze(-1) * B.unsqueeze(-2)
        
        # 扫描操作（简化版本）
        y = torch.zeros_like(x_conv)
        h = torch.zeros(batch_size, self.d_inner, self.A_log.shape[1], device=x.device)
        
        current_seq_len = x_conv.size(1)
        for t in range(current_seq_len):
            h = deltaA[:, t] * h + deltaB[:, t] * x_conv[:, t:t+1].transpose(-1, -2)
            y[:, t] = (h * C[:, t].unsqueeze(-2)).sum(dim=-1)
        
        # 添加跳跃连接
        y = y + x_conv * self.D
        
        # 门控
        y = y * self.activation(x_gate)
        
        # 输出投影
        output = self.out_proj(y)
        
        # 确保残差连接的维度匹配
        if output.size(1) != residual.size(1):
            if output.size(1) > residual.size(1):
                output = output[:, :residual.size(1), :]
            else:
                padding = residual.size(1) - output.size(1)
                output = F.pad(output, (0, 0, 0, padding))
        
        # 残差连接
        return self.norm(output + residual)


class LocalAttention(nn.Module):
    """局部注意力机制（用于EEG）"""
    def __init__(self, d_model, window_size=50, n_heads=4, dropout=0.15):
        super().__init__()
        self.window_size = window_size
        self.attention = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        self.norm = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        """x: (batch, seq_len, d_model)"""
        batch_size, seq_len, d_model = x.shape
        
        # 如果序列长度小于窗口大小，使用全局注意力
        if seq_len <= self.window_size:
            attn_out, _ = self.attention(x, x, x)
            return self.norm(x + self.dropout(attn_out))
        
        # 分割成窗口
        num_windows = (seq_len + self.window_size - 1) // self.window_size
        padded_len = num_windows * self.window_size
        
        # 填充到窗口大小的倍数
        if seq_len < padded_len:
            padding = padded_len - seq_len
            x_padded = F.pad(x, (0, 0, 0, padding))
        else:
            x_padded = x
            
        # 重塑为窗口
        x_windows = x_padded.reshape(batch_size, num_windows, self.window_size, d_model)
        x_windows = x_windows.reshape(batch_size * num_windows, self.window_size, d_model)
        
        # 在每个窗口内应用注意力
        attn_out, _ = self.attention(x_windows, x_windows, x_windows)
        
        # 重塑回原始形状
        attn_out = attn_out.reshape(batch_size, num_windows, self.window_size, d_model)
        attn_out = attn_out.reshape(batch_size, padded_len, d_model)
        
        # 移除填充
        if seq_len < padded_len:
            attn_out = attn_out[:, :seq_len]
            
        return self.norm(x + self.dropout(attn_out))


# ===================== 模态内特征精炼 =====================

class ModalRefinementModule(nn.Module):
    """模态内特征精炼模块"""
    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()
        
        # EEG: 局部注意力
        self.eeg_refinement = LocalAttention(
            d_model, window_size=50, n_heads=4, dropout=dropout
        )
        
        # EOG: 轻量级Mamba
        self.eog_refinement = SimplifiedMambaBlock(
            d_model, d_state=8, d_conv=4, expand_factor=1
        )
        
        # EMG: 轻量级Mamba
        self.emg_refinement = SimplifiedMambaBlock(
            d_model, d_state=8, d_conv=4, expand_factor=1
        )
        
    def forward(self, eeg_feat, eog_feat, emg_feat):
        """
        分别精炼各模态特征
        Args:
            eeg_feat: (batch, seq_len, d_model)
            eog_feat: (batch, seq_len, d_model)
            emg_feat: (batch, seq_len, d_model)
        """
        eeg_refined = self.eeg_refinement(eeg_feat)
        eog_refined = self.eog_refinement(eog_feat)
        emg_refined = self.emg_refinement(emg_feat)
        
        return eeg_refined, eog_refined, emg_refined


# ===================== N1增强模块 =====================

class N1EnhancementModule(nn.Module):
    """专门增强N1类别识别的模块"""
    def __init__(self, d_model=160, dropout=0.15):
        super().__init__()
        
        # N1特征提取器 - 关注过渡期特征
        self.n1_attention = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # N1 vs non-N1 二分类器
        self.n1_detector = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Linear(d_model // 2, 2)  # N1 vs non-N1
        )
        
        # 特征增强层
        self.feature_enhancer = nn.Sequential(
            nn.Linear(d_model, d_model * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 2, d_model)
        )
        
        # 残差连接
        self.residual_weight = nn.Parameter(torch.tensor(0.3))
        
    def forward(self, x, return_n1_logits=False):
        """
        Args:
            x: (batch, seq_len, d_model)
            return_n1_logits: 是否返回N1检测logits
        """
        # N1特征注意力
        n1_feat, _ = self.n1_attention(x, x, x)
        
        # N1检测
        n1_logits = self.n1_detector(n1_feat.mean(dim=1))  # 池化
        
        # 特征增强
        enhanced = self.feature_enhancer(n1_feat)
        
        # 残差连接
        output = x + self.residual_weight * enhanced
        
        if return_n1_logits:
            return output, n1_logits
        return output


# ===================== 渐进式分类器 =====================

class ImprovedProgressiveClassifier(nn.Module):
    """改进的渐进式分类策略 - 基于混淆度分组"""
    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()
        
        # 第一阶段：二分类 - 确定组(N2,N3) vs 过渡组(W,N1,REM)
        # 基于混淆矩阵分析：N2和N3识别率高，而W/N1/REM相互混淆严重
        self.coarse_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Linear(d_model // 2, 2)  # 2类：确定组 vs 过渡组
        )
        
        # 第二阶段A：确定组细分类器 (N2 vs N3)
        self.stable_refiner = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, 2)  # 2类：N2, N3
        )
        
        # 第二阶段B：过渡组细分类器 (Wake vs N1 vs REM)
        # 专门增强N1识别的网络设计
        self.transition_refiner = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model * 2),  # 增加容量以更好区分N1
            nn.GELU(),
            nn.LayerNorm(d_model * 2),
            nn.Dropout(dropout * 0.7),
            nn.Linear(d_model * 2, d_model),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model, 3)  # 3类：Wake, N1, REM
        )
        
        # 直接5分类器（用于对比和集成）
        self.direct_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, 5)
        )
        
        # 不确定性估计（用于MC Dropout）
        self.mc_dropout = nn.Dropout(dropout)
        
        # 温度缩放参数（用于校准）
        self.temperature = nn.Parameter(torch.ones(1) * 1.5)
        
    def forward(self, x, num_samples=1, return_uncertainty=False):
        """
        改进的渐进式分类
        Args:
            x: (batch, seq_len, d_model)
            num_samples: MC Dropout采样次数
            return_uncertainty: 是否返回不确定性估计
        """
        batch_size, seq_len, d_model = x.shape
        
        # 取序列的平均特征
        x_pooled = x.mean(dim=1)  # (batch, d_model)
        
        # 获取直接5分类结果（用于后续集成）
        direct_logits = self.direct_classifier(x_pooled)
        direct_probs = F.softmax(direct_logits / self.temperature, dim=-1)
        
        # 第一阶段：粗分类（确定组 vs 过渡组）
        if num_samples > 1 and self.training:
            # MC Dropout采样
            coarse_preds = []
            for _ in range(num_samples):
                x_drop = self.mc_dropout(x_pooled)
                pred = self.coarse_classifier(x_drop)
                coarse_preds.append(pred)
            coarse_logits = torch.stack(coarse_preds).mean(0)
            coarse_uncertainty = torch.stack(coarse_preds).std(0).mean(-1)
        else:
            coarse_logits = self.coarse_classifier(x_pooled)
            coarse_uncertainty = torch.zeros(batch_size, device=x.device)
        
        # 温度缩放
        coarse_probs = F.softmax(coarse_logits / self.temperature, dim=-1)
        
        # 获取粗分类预测
        coarse_pred = coarse_probs.argmax(dim=-1)  # 0:确定组(N2,N3), 1:过渡组(W,N1,REM)
        
        # 第二阶段：基于粗分类结果的细分类
        # 使用coarse_logits的dtype来确保类型匹配（支持混合精度）
        final_logits = torch.zeros(batch_size, 5, device=x.device, dtype=coarse_logits.dtype)
        
        # 确定组 -> N2(2), N3(3)
        stable_mask = (coarse_pred == 0)
        if stable_mask.any():
            stable_features = x_pooled[stable_mask]
            if num_samples > 1 and self.training:
                stable_preds = []
                for _ in range(num_samples):
                    stable_drop = self.mc_dropout(stable_features)
                    pred = self.stable_refiner(stable_drop)
                    stable_preds.append(pred)
                stable_logits = torch.stack(stable_preds).mean(0)
            else:
                stable_logits = self.stable_refiner(stable_features)
            
            # 映射到最终的5分类（N2:2, N3:3）
            final_logits[stable_mask, 2] = stable_logits[:, 0]  # N2
            final_logits[stable_mask, 3] = stable_logits[:, 1]  # N3
        
        # 过渡组 -> Wake(0), N1(1), REM(4)
        transition_mask = (coarse_pred == 1)
        if transition_mask.any():
            transition_features = x_pooled[transition_mask]
            if num_samples > 1 and self.training:
                transition_preds = []
                for _ in range(num_samples):
                    transition_drop = self.mc_dropout(transition_features)
                    pred = self.transition_refiner(transition_drop)
                    transition_preds.append(pred)
                transition_logits = torch.stack(transition_preds).mean(0)
            else:
                transition_logits = self.transition_refiner(transition_features)
            
            # 映射到最终的5分类
            final_logits[transition_mask, 0] = transition_logits[:, 0]  # Wake
            final_logits[transition_mask, 1] = transition_logits[:, 1]  # N1
            final_logits[transition_mask, 4] = transition_logits[:, 2]  # REM
        
        # 集成：将渐进式分类和直接分类结果加权融合
        # 对于高置信度的直接分类结果，给予更多权重
        direct_confidence = direct_probs.max(dim=-1)[0]
        high_conf_mask = direct_confidence > 0.8
        
        # 对高置信度样本，增加直接分类的权重
        if high_conf_mask.any():
            blend_weight = 0.3  # 直接分类的权重
            final_logits[high_conf_mask] = (1 - blend_weight) * final_logits[high_conf_mask] + \
                                          blend_weight * direct_logits[high_conf_mask]
        
        # 返回结果
        if return_uncertainty:
            return final_logits, coarse_probs, coarse_uncertainty
        else:
            return final_logits, coarse_probs


class ProgressiveClassifier(nn.Module):
    """渐进式分类策略"""
    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()
        
        # 第一阶段：粗分类器 (W vs NREM vs REM)
        self.coarse_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, 3)  # 3类：W, NREM, REM
        )
        
        # 第二阶段：NREM细分类器 (N1 vs N2 vs N3)
        self.nrem_refiner = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, 3)  # 3类：N1, N2, N3
        )
        
        # 不确定性估计（用于MC Dropout）
        self.mc_dropout = nn.Dropout(dropout)
        
        # 温度缩放参数（用于校准）
        self.temperature = nn.Parameter(torch.ones(1) * 1.5)
        
    def forward(self, x, num_samples=1, return_uncertainty=False):
        """
        渐进式分类
        Args:
            x: (batch, seq_len, d_model)
            num_samples: MC Dropout采样次数
            return_uncertainty: 是否返回不确定性估计
        """
        batch_size, seq_len, d_model = x.shape
        
        # 取序列的平均特征或最后时刻特征
        x_pooled = x.mean(dim=1)  # (batch, d_model)
        
        # 第一阶段：粗分类
        if num_samples > 1 and self.training:
            # MC Dropout采样
            coarse_preds = []
            for _ in range(num_samples):
                x_drop = self.mc_dropout(x_pooled)
                pred = self.coarse_classifier(x_drop)
                coarse_preds.append(pred)
            coarse_logits = torch.stack(coarse_preds).mean(0)
            coarse_uncertainty = torch.stack(coarse_preds).std(0).mean(-1)
        else:
            coarse_logits = self.coarse_classifier(x_pooled)
            coarse_uncertainty = torch.zeros(batch_size, device=x.device)
        
        # 温度缩放
        coarse_probs = F.softmax(coarse_logits / self.temperature, dim=-1)
        
        # 获取粗分类预测
        coarse_pred = coarse_probs.argmax(dim=-1)  # 0:W, 1:NREM, 2:REM
        
        # 第二阶段：基于粗分类结果的细分类
        # 使用coarse_logits的dtype来确保类型匹配（支持混合精度）
        final_logits = torch.zeros(batch_size, 5, device=x.device, dtype=coarse_logits.dtype)
        
        # Wake (W) -> 类别0
        wake_mask = (coarse_pred == 0)
        if wake_mask.any():
            final_logits[wake_mask, 0] = coarse_logits[wake_mask, 0]
        
        # NREM -> 需要细分为N1(1), N2(2), N3(3)
        nrem_mask = (coarse_pred == 1)
        if nrem_mask.any():
            nrem_features = x_pooled[nrem_mask]
            if num_samples > 1 and self.training:
                nrem_preds = []
                for _ in range(num_samples):
                    nrem_drop = self.mc_dropout(nrem_features)
                    pred = self.nrem_refiner(nrem_drop)
                    nrem_preds.append(pred)
                nrem_logits = torch.stack(nrem_preds).mean(0)
                nrem_uncertainty = torch.stack(nrem_preds).std(0).mean(-1)
                coarse_uncertainty[nrem_mask] += nrem_uncertainty
            else:
                nrem_logits = self.nrem_refiner(nrem_features)
            
            # 映射到最终的5分类（N1:1, N2:2, N3:3）
            final_logits[nrem_mask, 1:4] = nrem_logits
        
        # REM -> 类别4
        rem_mask = (coarse_pred == 2)
        if rem_mask.any():
            final_logits[rem_mask, 4] = coarse_logits[rem_mask, 2]
        
        # 返回最终分类结果
        if return_uncertainty:
            return final_logits, coarse_probs, coarse_uncertainty
        else:
            return final_logits, coarse_probs


# ===================== 混合Mamba-Transformer架构 =====================

class HybridMambaTransformer(nn.Module):
    """混合Mamba-Transformer编码器"""
    def __init__(self, d_model=128, n_heads=8, n_mamba_layers=2, 
                 n_transformer_layers=2, dropout=0.15):
        super().__init__()
        
        # Mamba层（用于长程依赖）
        self.mamba_layers = nn.ModuleList([
            SimplifiedMambaBlock(d_model, d_state=16, d_conv=4, expand_factor=2)
            for _ in range(n_mamba_layers)
        ])
        
        # Transformer层（用于短程模式）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_layers = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=n_transformer_layers
        )
        
        self.norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        """x: (batch, seq_len, d_model)"""
        # 先通过Mamba层处理长程依赖
        for mamba_layer in self.mamba_layers:
            x = mamba_layer(x)
        
        # 再通过Transformer层处理短程模式
        x = self.transformer_layers(x)
        
        return self.norm(x)


# ===================== 完整的Stage 4模型 =====================

class Stage4MambaProgressiveModel(nn.Module):
    """Stage 4: 完整的Mamba-Transformer混合架构 + 渐进式分类"""
    def __init__(self, n_classes=5, d_model=128, n_heads=8, 
                 n_mamba_layers=2, n_transformer_layers=2, 
                 dropout=0.15, seq_len=5, use_improved_classifier=True):
        super().__init__()
        
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 特征提取器（保持与之前Stage一致）
        self.eeg_extractor = nn.Sequential(
            nn.Conv1d(2, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(128, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        self.eog_extractor = nn.Sequential(
            nn.Conv1d(1, 32, kernel_size=50, stride=6),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(32, 64, kernel_size=8, stride=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(64, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        self.emg_extractor = nn.Sequential(
            nn.Conv1d(1, 32, kernel_size=50, stride=6),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(32, 64, kernel_size=8, stride=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(64, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # 模态内特征精炼
        self.modal_refinement = ModalRefinementModule(d_model, dropout)
        
        # EEG中心的跨模态注意力
        self.cross_modal_attention = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        
        # 自适应门控融合
        self.gate_network = nn.Sequential(
            nn.Linear(d_model * 3, d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, 3),
            nn.Softmax(dim=-1)
        )
        
        # 混合Mamba-Transformer编码器
        self.hybrid_encoder = HybridMambaTransformer(
            d_model, n_heads, n_mamba_layers, n_transformer_layers, dropout
        )
        
        # N1增强模块（暂时禁用，避免过度复杂）
        # self.n1_enhancer = N1EnhancementModule(d_model, dropout)
        self.n1_enhancer = None
        
        # 渐进式分类器（根据配置选择版本）
        if use_improved_classifier:
            self.progressive_classifier = ImprovedProgressiveClassifier(d_model, dropout)
            logging.info("使用改进的渐进式分类器（确定组vs过渡组）")
        else:
            self.progressive_classifier = ProgressiveClassifier(d_model, dropout)
            logging.info("使用原始渐进式分类器（W/NREM/REM）")
        
        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # 初始化权重
        self._init_weights()
        
        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建Stage4MambaProgressiveModel: 参数量={total_params:,}")
        
    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def extract_features(self, x):
        """提取多模态特征"""
        # 调整维度
        x = x.transpose(1, 2)  # (batch, channels, time_steps)
        
        # 分离模态
        eeg = x[:, :2, :]  # EEG: 前2个通道
        eog = x[:, 2:3, :]  # EOG: 第3个通道
        emg = x[:, 3:4, :]  # EMG: 第4个通道
        
        # 独立提取特征
        eeg_feat = self.eeg_extractor(eeg)  # (batch, d_model, time')
        eog_feat = self.eog_extractor(eog)
        emg_feat = self.emg_extractor(emg)
        
        # 转换为序列格式
        eeg_feat = eeg_feat.transpose(1, 2)  # (batch, time', d_model)
        eog_feat = eog_feat.transpose(1, 2)
        emg_feat = emg_feat.transpose(1, 2)
        
        return eeg_feat, eog_feat, emg_feat
    
    def forward(self, x, return_uncertainty=False):
        """
        Args:
            x: (batch, seq_len, time_steps, channels)
            return_uncertainty: 是否返回不确定性估计
        """
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 处理每个时间步
        all_features = []
        
        for t in range(seq_len):
            x_t = x[:, t, :, :]  # (batch, time_steps, channels)
            
            # 提取特征
            eeg_feat, eog_feat, emg_feat = self.extract_features(x_t)
            
            # 模态内特征精炼
            eeg_refined, eog_refined, emg_refined = self.modal_refinement(
                eeg_feat, eog_feat, emg_feat
            )
            
            # EEG中心的跨模态注意力
            eog_attended, _ = self.cross_modal_attention(
                eeg_refined, eog_refined, eog_refined
            )
            emg_attended, _ = self.cross_modal_attention(
                eeg_refined, emg_refined, emg_refined
            )
            
            # 自适应门控融合
            # 取每个模态的平均特征
            eeg_pooled = eeg_refined.mean(dim=1)
            eog_pooled = eog_attended.mean(dim=1)
            emg_pooled = emg_attended.mean(dim=1)
            
            concat_feat = torch.cat([eeg_pooled, eog_pooled, emg_pooled], dim=-1)
            gate_weights = self.gate_network(concat_feat)  # (batch, 3)
            
            # 加权融合
            fused_feat = (gate_weights[:, 0:1].unsqueeze(1) * eeg_refined + 
                         gate_weights[:, 1:2].unsqueeze(1) * eog_attended + 
                         gate_weights[:, 2:3].unsqueeze(1) * emg_attended)
            
            # 取平均作为该时间步的特征
            all_features.append(fused_feat.mean(dim=1))
        
        # Stack成序列
        features = torch.stack(all_features, dim=1)  # (batch, seq_len, d_model)
        
        # 位置编码
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # (batch, seq_len, d_model)
        
        # 混合Mamba-Transformer编码
        encoded = self.hybrid_encoder(features)
        
        # N1增强（暂时禁用）
        n1_logits = None
        # if self.n1_enhancer is not None:
        #     encoded, n1_logits = self.n1_enhancer(encoded, return_n1_logits=True)
        
        # 渐进式分类
        if return_uncertainty:
            final_logits, coarse_probs, uncertainty = self.progressive_classifier(
                encoded, num_samples=2 if self.training else 1, 
                return_uncertainty=True
            )
            # 扩展到序列长度
            final_output = final_logits.unsqueeze(1).expand(-1, seq_len, -1)
            return final_output, {
                'coarse_probs': coarse_probs,
                'uncertainty': uncertainty,
                'gate_weights': gate_weights,
                'n1_logits': n1_logits  # 添加N1检测logits
            }
        else:
            final_logits, coarse_probs = self.progressive_classifier(
                encoded, num_samples=1
            )
            # 扩展到序列长度
            final_output = final_logits.unsqueeze(1).expand(-1, seq_len, -1)
            return final_output, {
                'coarse_probs': coarse_probs,
                'gate_weights': gate_weights,
                'n1_logits': n1_logits  # 添加N1检测logits
            }


# ===================== 数据增强 =====================

def mixup_data(x, y, alpha=0.2):
    """Mixup数据增强
    Args:
        x: 输入数据 (batch_size, seq_len, channels, time_steps)
        y: 标签 (batch_size, seq_len)
        alpha: Beta分布参数
    Returns:
        mixed_x: 混合后的输入
        y_a, y_b: 原始标签对
        lam: 混合系数
    """
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1
    
    batch_size = x.size(0)
    index = torch.randperm(batch_size).to(x.device)
    
    mixed_x = lam * x + (1 - lam) * x[index]
    y_a, y_b = y, y[index]
    
    return mixed_x, y_a, y_b, lam, index

def mixup_criterion(criterion, pred, y_a, y_b, lam):
    """Mixup损失计算
    Args:
        criterion: 损失函数
        pred: 模型预测
        y_a, y_b: 混合的两个标签
        lam: 混合系数
    """
    # 分别计算两个标签的损失并加权
    loss_a = criterion((pred, {}), y_a)[0] if isinstance(criterion, (ProgressiveLoss, ImprovedProgressiveLoss)) else criterion(pred, y_a)
    loss_b = criterion((pred, {}), y_b)[0] if isinstance(criterion, (ProgressiveLoss, ImprovedProgressiveLoss)) else criterion(pred, y_b)
    return lam * loss_a + (1 - lam) * loss_b

# ===================== 损失函数 =====================

class LabelSmoothingLoss(nn.Module):
    """Label Smoothing Loss to prevent overfitting"""
    def __init__(self, num_classes=5, smoothing=0.1):
        super().__init__()
        self.num_classes = num_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
        
    def forward(self, pred, target):
        """
        Args:
            pred: (N, C) predictions
            target: (N,) targets
        """
        # Create smoothed label distribution
        with torch.no_grad():
            true_dist = torch.zeros_like(pred)
            true_dist.fill_(self.smoothing / (self.num_classes - 1))
            true_dist.scatter_(1, target.unsqueeze(1), self.confidence)
        
        # Calculate KL divergence loss
        return torch.mean(torch.sum(-true_dist * F.log_softmax(pred, dim=-1), dim=-1))


class FocalLoss(nn.Module):
    """Focal Loss for addressing class imbalance"""
    def __init__(self, alpha=None, gamma=2.0, reduction='mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        """
        Args:
            inputs: (N, C) where N is batch size and C is number of classes
            targets: (N,) where each value is 0 <= targets[i] <= C-1
        """
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if isinstance(self.alpha, (list, np.ndarray, torch.Tensor)):
                # 确保alpha在正确的设备上
                if isinstance(self.alpha, torch.Tensor):
                    self.alpha = self.alpha.to(inputs.device)
                    alpha_t = self.alpha[targets]
                else:
                    alpha_t = torch.tensor(self.alpha, device=inputs.device)[targets]
                focal_loss = alpha_t * focal_loss
            else:
                focal_loss = self.alpha * focal_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class ImprovedProgressiveLoss(nn.Module):
    """改进的渐进式分类损失函数 - 支持新的分组策略和Focal Loss"""
    def __init__(self, coarse_weight=0.3, uncertainty_weight=0.1, use_focal=True, use_label_smoothing=True):
        super().__init__()
        self.coarse_weight = coarse_weight
        self.uncertainty_weight = uncertainty_weight
        self.use_focal = use_focal
        self.use_label_smoothing = use_label_smoothing
        
        if use_focal:
            # 设置类别权重，特别关注N1（类别1）
            # 根据原始数据：W(978), N1(348), N2(2100), N3(419), REM(866)
            # 精细调整N1权重，避免过度
            # W(978), N1(348), N2(2100), N3(419), REM(866)
            # 根据类别分布[6414, 2097, 13775, 4841, 5939]渐进调整
            class_weights = torch.tensor([1.0, 3.5, 0.4, 2.0, 1.2])  # 温和提升N1权重
            self.focal_loss = FocalLoss(alpha=class_weights, gamma=1.5)  # 适度的gamma值
        else:
            self.focal_loss = None
        
        # 添加Label Smoothing
        if use_label_smoothing:
            self.label_smoothing = LabelSmoothingLoss(num_classes=5, smoothing=0.05)  # 减小smoothing
        else:
            self.label_smoothing = None
        
    def forward(self, outputs, labels):
        """
        Args:
            outputs: (main_output, aux_dict)
            labels: (batch, seq_len)
        """
        main_output, aux_dict = outputs
        
        # 主任务损失（5分类）
        if self.use_focal and self.focal_loss is not None:
            # 使用Focal Loss处理类别不平衡
            focal_loss = self.focal_loss(
                main_output.reshape(-1, main_output.size(-1)),
                labels.reshape(-1)
            )
            # 如果启用Label Smoothing，组合两种损失
            if self.use_label_smoothing and self.label_smoothing is not None:
                smooth_loss = self.label_smoothing(
                    main_output.reshape(-1, main_output.size(-1)),
                    labels.reshape(-1)
                )
                main_loss = 0.7 * focal_loss + 0.3 * smooth_loss  # 组合损失
            else:
                main_loss = focal_loss
        else:
            if self.use_label_smoothing and self.label_smoothing is not None:
                main_loss = self.label_smoothing(
                    main_output.reshape(-1, main_output.size(-1)),
                    labels.reshape(-1)
                )
            else:
                main_loss = F.cross_entropy(
                    main_output.reshape(-1, main_output.size(-1)),
                    labels.reshape(-1)
                )
        
        # 粗分类损失（2分类：确定组 vs 过渡组）
        # 将5分类标签映射到2分类
        # 确定组(0): N2(2), N3(3)
        # 过渡组(1): Wake(0), N1(1), REM(4)
        coarse_labels = torch.zeros_like(labels)
        coarse_labels[(labels == 2) | (labels == 3)] = 0  # N2, N3 -> 确定组
        coarse_labels[(labels == 0) | (labels == 1) | (labels == 4)] = 1  # W, N1, REM -> 过渡组
        
        # 只使用第一个时间步的粗分类概率
        coarse_probs = aux_dict['coarse_probs']
        
        # 处理概率维度（新分类器返回2类概率）
        if coarse_probs.shape[-1] == 2:
            coarse_loss = F.cross_entropy(
                torch.log(coarse_probs + 1e-8),  # 使用log因为已经是概率
                coarse_labels[:, 0]  # 只用第一个时间步
            )
        else:
            # 兼容原始3类分类器
            old_coarse_labels = labels.clone()
            old_coarse_labels[labels == 0] = 0  # W -> 0
            old_coarse_labels[(labels >= 1) & (labels <= 3)] = 1  # N1,N2,N3 -> 1 (NREM)
            old_coarse_labels[labels == 4] = 2  # REM -> 2
            coarse_loss = F.cross_entropy(
                torch.log(coarse_probs + 1e-8),
                old_coarse_labels[:, 0]
            )
        
        # 不确定性正则化（鼓励低不确定性）
        uncertainty_loss = 0
        if 'uncertainty' in aux_dict:
            uncertainty_loss = aux_dict['uncertainty'].mean()
        
        # N1辅助损失
        n1_loss = 0
        if 'n1_logits' in aux_dict and aux_dict['n1_logits'] is not None:
            # 创建N1 vs non-N1的二分类标签
            n1_binary_labels = (labels[:, 0] == 1).long()  # N1为1，其他为0
            n1_loss = F.cross_entropy(aux_dict['n1_logits'], n1_binary_labels)
        
        # 组合损失
        total_loss = (main_loss + 
                     self.coarse_weight * coarse_loss + 
                     self.uncertainty_weight * uncertainty_loss +
                     0.2 * n1_loss)  # 添加N1辅助损失
        
        return total_loss, {
            'main': main_loss.item(),
            'coarse': coarse_loss.item(),
            'uncertainty': uncertainty_loss.item() if isinstance(uncertainty_loss, torch.Tensor) else 0,
            'n1': n1_loss.item() if isinstance(n1_loss, torch.Tensor) else 0,
            'total': total_loss.item()
        }


class ProgressiveLoss(nn.Module):
    """渐进式分类的损失函数"""
    def __init__(self, coarse_weight=0.3, uncertainty_weight=0.1, use_focal=False, use_label_smoothing=True):
        super().__init__()
        self.coarse_weight = coarse_weight
        self.uncertainty_weight = uncertainty_weight
        self.use_focal = use_focal
        self.use_label_smoothing = use_label_smoothing
        
        if use_focal:
            # 设置类别权重，特别关注N1（类别1）
            # 精细调整N1权重，避免过度
            # W(978), N1(348), N2(2100), N3(419), REM(866)
            # 根据类别分布[6414, 2097, 13775, 4841, 5939]渐进调整
            class_weights = torch.tensor([1.0, 3.5, 0.4, 2.0, 1.2])  # 温和提升N1权重
            self.focal_loss = FocalLoss(alpha=class_weights, gamma=1.5)  # 适度的gamma值
        else:
            self.focal_loss = None
        
        # 添加Label Smoothing
        if use_label_smoothing:
            self.label_smoothing = LabelSmoothingLoss(num_classes=5, smoothing=0.05)  # 减小smoothing
        else:
            self.label_smoothing = None
        
    def forward(self, outputs, labels):
        """
        Args:
            outputs: (main_output, aux_dict)
            labels: (batch, seq_len)
        """
        main_output, aux_dict = outputs
        
        # 主任务损失（5分类）
        if self.use_focal and self.focal_loss is not None:
            # 使用Focal Loss处理类别不平衡
            focal_loss = self.focal_loss(
                main_output.reshape(-1, main_output.size(-1)),
                labels.reshape(-1)
            )
            # 如果启用Label Smoothing，组合两种损失
            if self.use_label_smoothing and self.label_smoothing is not None:
                smooth_loss = self.label_smoothing(
                    main_output.reshape(-1, main_output.size(-1)),
                    labels.reshape(-1)
                )
                main_loss = 0.7 * focal_loss + 0.3 * smooth_loss  # 组合损失
            else:
                main_loss = focal_loss
        else:
            if self.use_label_smoothing and self.label_smoothing is not None:
                main_loss = self.label_smoothing(
                    main_output.reshape(-1, main_output.size(-1)),
                    labels.reshape(-1)
                )
            else:
                main_loss = F.cross_entropy(
                    main_output.reshape(-1, main_output.size(-1)),
                    labels.reshape(-1)
                )
        
        # 粗分类损失（3分类：W vs NREM vs REM）
        # 将5分类标签映射到3分类
        coarse_labels = labels.clone()
        coarse_labels[labels == 0] = 0  # W -> 0
        coarse_labels[(labels >= 1) & (labels <= 3)] = 1  # N1,N2,N3 -> 1 (NREM)
        coarse_labels[labels == 4] = 2  # REM -> 2
        
        # 只使用第一个时间步的粗分类概率
        coarse_probs = aux_dict['coarse_probs']
        coarse_loss = F.cross_entropy(
            torch.log(coarse_probs + 1e-8),  # 使用log因为已经是概率
            coarse_labels[:, 0]  # 只用第一个时间步
        )
        
        # 不确定性正则化（鼓励低不确定性）
        uncertainty_loss = 0
        if 'uncertainty' in aux_dict:
            uncertainty_loss = aux_dict['uncertainty'].mean()
        
        # N1辅助损失
        n1_loss = 0
        if 'n1_logits' in aux_dict and aux_dict['n1_logits'] is not None:
            # 创建N1 vs non-N1的二分类标签
            n1_binary_labels = (labels[:, 0] == 1).long()  # N1为1，其他为0
            n1_loss = F.cross_entropy(aux_dict['n1_logits'], n1_binary_labels)
        
        # 组合损失
        total_loss = (main_loss + 
                     self.coarse_weight * coarse_loss + 
                     self.uncertainty_weight * uncertainty_loss +
                     0.2 * n1_loss)  # 添加N1辅助损失
        
        return total_loss, {
            'main': main_loss.item(),
            'coarse': coarse_loss.item(),
            'uncertainty': uncertainty_loss.item() if isinstance(uncertainty_loss, torch.Tensor) else 0,
            'n1': n1_loss.item() if isinstance(n1_loss, torch.Tensor) else 0,
            'total': total_loss.item()
        }


# ===================== 训练和评估函数 =====================

def train_epoch(model, dataloader, criterion, optimizer, device, scaler=None, use_mixup=True, mixup_alpha=0.2):
    """训练一个epoch（支持混合精度训练和Mixup增强）"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    coarse_correct = 0
    loss_components = {'main': 0, 'coarse': 0, 'uncertainty': 0}
    
    # 使用混合精度训练
    use_amp = scaler is not None
    
    with tqdm(dataloader, desc="Training") as pbar:
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)
            
            # Mixup数据增强（仅在训练时使用，特别针对N1类别）
            original_labels = labels.clone()  # 保留原始标签用于准确率计算
            if use_mixup and model.training:
                # 对包含N1标签的batch应用更强的mixup
                if (labels == 1).any():
                    data, labels_a, labels_b, lam, index = mixup_data(data, labels, alpha=mixup_alpha * 1.5)
                else:
                    data, labels_a, labels_b, lam, index = mixup_data(data, labels, alpha=mixup_alpha)
            else:
                labels_a = labels_b = labels
                lam = 1.0
            
            optimizer.zero_grad()
            
            # 前向传播（使用autocast进行混合精度）
            with autocast(enabled=use_amp):
                outputs = model(data, return_uncertainty=True)
                
                # 根据是否使用Mixup计算损失
                if use_mixup and model.training:
                    # Mixup损失：分别计算两个标签的损失并加权
                    loss_a, loss_dict_a = criterion(outputs, labels_a)
                    loss_b, loss_dict_b = criterion(outputs, labels_b)
                    loss = lam * loss_a + (1 - lam) * loss_b
                    # 混合loss_dict
                    loss_dict = {k: lam * loss_dict_a[k] + (1 - lam) * loss_dict_b[k] 
                                for k in loss_dict_a.keys()}
                else:
                    loss, loss_dict = criterion(outputs, labels)
            
            # 反向传播
            if use_amp:
                scaler.scale(loss).backward()
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                scaler.step(optimizer)
                scaler.update()
            else:
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
            
            # 统计（使用原始标签计算准确率）
            total_loss += loss.item()
            main_output = outputs[0]
            _, predicted = main_output.max(-1)
            correct += predicted.eq(original_labels).sum().item()
            total += original_labels.numel()
            
            # 粗分类准确率
            coarse_probs = outputs[1]['coarse_probs']
            coarse_pred = coarse_probs.argmax(dim=-1)
            
            # 根据粗分类器类型设置标签（使用原始标签）
            if coarse_probs.shape[-1] == 2:
                # 新的2类分组：确定组(0) vs 过渡组(1)
                coarse_labels = torch.zeros_like(original_labels[:, 0])
                coarse_labels[(original_labels[:, 0] == 2) | (original_labels[:, 0] == 3)] = 0  # N2, N3 -> 确定组
                coarse_labels[(original_labels[:, 0] == 0) | (original_labels[:, 0] == 1) | (original_labels[:, 0] == 4)] = 1  # W, N1, REM -> 过渡组
            else:
                # 原始3类分组
                coarse_labels = original_labels[:, 0].clone()
                coarse_labels[original_labels[:, 0] == 0] = 0  # W
                coarse_labels[(original_labels[:, 0] >= 1) & (original_labels[:, 0] <= 3)] = 1  # NREM
                coarse_labels[original_labels[:, 0] == 4] = 2  # REM
            
            coarse_correct += coarse_pred.eq(coarse_labels).sum().item()
            
            # 累积损失分量
            for key in ['main', 'coarse', 'uncertainty']:
                loss_components[key] += loss_dict[key]
            
            # 更新进度条
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{100.*correct/total:.2f}%',
                'coarse': f'{100.*coarse_correct/total:.2f}%'
            })
    
    # 平均值
    num_batches = len(dataloader)
    for key in loss_components:
        loss_components[key] /= num_batches
    
    return total_loss / num_batches, 100. * correct / total, loss_components


def evaluate(model, dataloader, criterion, device, use_amp=False):
    """评估函数（支持混合精度）"""
    model.eval()
    total_loss = 0
    correct = 0
    total = 0
    coarse_correct = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for data, labels in tqdm(dataloader, desc="Evaluating"):
            data = data.to(device)
            labels = labels.to(device)
            
            # 使用autocast进行混合精度评估
            with autocast(enabled=use_amp):
                outputs = model(data, return_uncertainty=False)
                loss, _ = criterion(outputs, labels)
            
            total_loss += loss.item()
            main_output = outputs[0]
            _, predicted = main_output.max(-1)
            
            correct += predicted.eq(labels).sum().item()
            total += labels.numel()
            
            # 粗分类准确率
            coarse_probs = outputs[1]['coarse_probs']
            coarse_pred = coarse_probs.argmax(dim=-1)
            
            # 根据粗分类器类型设置标签
            if coarse_probs.shape[-1] == 2:
                # 新的2类分组：确定组(0) vs 过渡组(1)
                coarse_labels = torch.zeros_like(labels[:, 0])
                coarse_labels[(labels[:, 0] == 2) | (labels[:, 0] == 3)] = 0  # N2, N3 -> 确定组
                coarse_labels[(labels[:, 0] == 0) | (labels[:, 0] == 1) | (labels[:, 0] == 4)] = 1  # W, N1, REM -> 过渡组
            else:
                # 原始3类分组
                coarse_labels = labels[:, 0].clone()
                coarse_labels[labels[:, 0] == 0] = 0  # W
                coarse_labels[(labels[:, 0] >= 1) & (labels[:, 0] <= 3)] = 1  # NREM
                coarse_labels[labels[:, 0] == 4] = 2  # REM
            
            coarse_correct += coarse_pred.eq(coarse_labels).sum().item()
            
            all_preds.extend(predicted.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
    
    accuracy = 100. * correct / total
    coarse_accuracy = 100. * coarse_correct / len(dataloader.dataset)
    
    # 计算F1分数
    from sklearn.metrics import f1_score, confusion_matrix
    f1 = f1_score(all_labels, all_preds, average='macro')
    cm = confusion_matrix(all_labels, all_preds)
    
    return total_loss / len(dataloader), accuracy, f1, coarse_accuracy, cm


def main():
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_mamba_progressive_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f"{log_dir}/training.log"),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 Stage 4: Mamba-Transformer混合架构 + 渐进式分类策略")
    logging.info("核心创新：")
    logging.info("1. Mamba组件用于长程依赖建模")
    logging.info("2. 模态内特征精炼（EEG局部注意力，EOG/EMG轻量级Mamba）")
    logging.info("3. 渐进式分类策略（粗分类→细分类）")
    logging.info("4. 不确定性估计和置信度决策")
    logging.info("目标: ≥85% accuracy")
    logging.info("="*80)
    
    # 配置
    config = {
        "n_classes": 5,
        "d_model": 128,  # 回退到原始容量
        "n_heads": 8,
        "n_mamba_layers": 1,  # 保持简单
        "n_transformer_layers": 2,  # 恢复原始配置
        "dropout": 0.15,  # 平衡的dropout
        "seq_len": 5,  # 回退到2.5分钟
        "batch_size": 64,  # 恢复原始batch size
        "learning_rate": 3e-4,  # 适中的学习率
        "num_epochs": 20,  # 增加到20个epoch充分训练
        "weight_decay": 1e-4,  # 减少正则化
        "patience": 8,  # 适度的patience
        "coarse_weight": 0.25,  # 略微减少粗分类权重
        "uncertainty_weight": 0.05,  # 减少不确定性权重
        "use_improved_classifier": True,  # 使用改进的确定组vs过渡组分类器
        "use_focal_loss": True,  # 继续使用Focal Loss
        "use_mixup": False,  # 暂时禁用Mixup避免干扰
        "use_label_smoothing": True  # 启用Label Smoothing
    }
    
    logging.info(f"配置: {config}")
    
    # 设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"设备: {device}")
    
    # 数据路径
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 按受试者划分数据集（避免数据泄露）
    # SC4XXYYYY.npz中XX是两位数字的受试者编号
    from collections import defaultdict
    import random
    
    def get_subject_id(filename):
        """从文件名提取受试者ID"""
        basename = os.path.basename(filename)
        subject_id = basename[3:5]  # SC4后面的两位数字
        return subject_id
    
    # 按受试者分组
    subject_files = defaultdict(list)
    for file in all_files:
        subject_id = get_subject_id(file)
        subject_files[subject_id].append(file)
    
    # 获取所有受试者ID并打乱
    random.seed(42)
    subjects = list(subject_files.keys())
    random.shuffle(subjects)
    
    # 划分受试者：测试集2个，验证集2个，其余为训练集
    n_subjects = len(subjects)
    test_subjects = subjects[:2]  # 前2个受试者作为测试集
    val_subjects = subjects[2:4]  # 接下来2个受试者作为验证集
    train_subjects = subjects[4:]  # 其余受试者作为训练集
    
    # 收集文件
    train_files = []
    val_files = []
    test_files = []
    
    for subject in train_subjects:
        train_files.extend(subject_files[subject])
    for subject in val_subjects:
        val_files.extend(subject_files[subject])
    for subject in test_subjects:
        test_files.extend(subject_files[subject])
    
    # 排序确保一致性
    train_files.sort()
    val_files.sort()
    test_files.sort()
    
    logging.info(f"训练集受试者: {sorted(train_subjects)}")
    logging.info(f"验证集受试者: {sorted(val_subjects)}")
    logging.info(f"测试集受试者: {sorted(test_subjects)}")
    
    logging.info("加载数据集...")
    
    # 创建数据集
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        use_channels=4,  # 使用全部4个通道
        max_samples_per_file=None,
        is_training=True,
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        use_channels=4,
        max_samples_per_file=None,
        is_training=False,
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        use_channels=4,
        max_samples_per_file=None,
        is_training=False,
    )
    
    # 创建类别平衡采样器
    # 获取所有训练样本的标签
    train_labels = []
    for i in range(len(train_dataset)):
        _, label = train_dataset[i]
        # 取序列中间位置的标签作为代表
        train_labels.append(label[len(label)//2].item())
    
    # 计算每个类别的权重（频率的倒数）
    class_counts = torch.bincount(torch.tensor(train_labels))
    class_weights = 1.0 / class_counts.float()
    class_weights = class_weights / class_weights.sum() * len(class_weights)  # 归一化
    
    # 为每个样本分配权重
    sample_weights = [class_weights[label] for label in train_labels]
    sample_weights = torch.tensor(sample_weights)
    
    # 暂时禁用 WeightedRandomSampler，使用普通随机采样
    train_sampler = None  # WeightedRandomSampler过于激进，暂时禁用
    
    logging.info(f"类别分布: {class_counts.tolist()}")
    logging.info(f"类别权重: {class_weights.tolist()}")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,  # 使用普通的随机打乱
        num_workers=4,
        pin_memory=True,
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")
    
    # 创建模型
    model = Stage4MambaProgressiveModel(
        n_classes=config["n_classes"],
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_mamba_layers=config["n_mamba_layers"],
        n_transformer_layers=config["n_transformer_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
        use_improved_classifier=config.get("use_improved_classifier", True)
    ).to(device)
    
    # 损失函数（根据分类器版本选择对应的损失函数）
    if config.get("use_improved_classifier", True):
        criterion = ImprovedProgressiveLoss(
            coarse_weight=config["coarse_weight"],
            uncertainty_weight=config["uncertainty_weight"],
            use_focal=config.get("use_focal_loss", True),
            use_label_smoothing=config.get("use_label_smoothing", True)
        )
        logging.info("使用改进的损失函数（含Focal Loss）")
    else:
        criterion = ProgressiveLoss(
            coarse_weight=config["coarse_weight"],
            uncertainty_weight=config["uncertainty_weight"],
            use_focal=config.get("use_focal_loss", True)
        )
        logging.info("使用原始损失函数（含Focal Loss）" if config.get("use_focal_loss", True) else "使用原始损失函数")
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"]
    )
    
    # 学习率调度器 - 添加warmup
    class WarmupCosineScheduler:
        def __init__(self, optimizer, warmup_epochs, total_epochs, eta_min=1e-6):
            self.optimizer = optimizer
            self.warmup_epochs = warmup_epochs
            self.total_epochs = total_epochs
            self.eta_min = eta_min
            self.base_lr = optimizer.param_groups[0]['lr']
            self.current_epoch = 0
            
        def step(self):
            self.current_epoch += 1
            if self.current_epoch <= self.warmup_epochs:
                # Warmup阶段：线性增长
                lr = self.base_lr * (self.current_epoch / self.warmup_epochs)
            else:
                # Cosine退火阶段
                progress = (self.current_epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)
                lr = self.eta_min + (self.base_lr - self.eta_min) * 0.5 * (1 + np.cos(np.pi * progress))
            
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = lr
                
        def get_last_lr(self):
            return [group['lr'] for group in self.optimizer.param_groups]
    
    scheduler = WarmupCosineScheduler(
        optimizer,
        warmup_epochs=3,  # 增加warmup以稳定训练
        total_epochs=config["num_epochs"],
        eta_min=1e-5  # 保持合理的最小学习率
    )
    
    # Early stopping
    early_stopping = EarlyStopping(patience=config["patience"], verbose=True)
    
    # 混合精度训练的GradScaler
    scaler = GradScaler() if device.type == 'cuda' else None
    use_amp = scaler is not None
    logging.info(f"混合精度训练: {'启用' if use_amp else '禁用'}")
    
    # 训练
    best_test_acc = 0
    best_epoch = 0
    
    for epoch in range(1, config["num_epochs"] + 1):
        logging.info("\n" + "="*60)
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        
        # 训练 (根据配置决定是否使用Mixup)
        train_loss, train_acc, loss_components = train_epoch(
            model, train_loader, criterion, optimizer, device, scaler, 
            use_mixup=config.get('use_mixup', False)
        )
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        logging.info(f"  损失分量 - Main: {loss_components['main']:.4f}, "
                    f"Coarse: {loss_components['coarse']:.4f}, "
                    f"Uncertainty: {loss_components['uncertainty']:.4f}")
        
        # 验证
        val_loss, val_acc, val_f1, val_coarse_acc, _ = evaluate(
            model, val_loader, criterion, device, use_amp
        )
        logging.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_f1:.4f}, "
                    f"Coarse Acc: {val_coarse_acc:.2f}%")
        
        # 测试
        test_loss, test_acc, test_f1, test_coarse_acc, test_cm = evaluate(
            model, test_loader, criterion, device, use_amp
        )
        logging.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_f1:.4f}, "
                    f"Coarse Acc: {test_coarse_acc:.2f}%")
        
        # 学习率
        logging.info(f"学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_epoch = epoch
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'test_f1': test_f1,
                'coarse_acc': test_coarse_acc,
                'confusion_matrix': test_cm,
                'config': config
            }, f"{log_dir}/best_model.pth")
            logging.info(f"✅ 保存最佳模型，准确率: {test_acc:.2f}%")
            
            if test_acc >= 85:
                logging.info("🎉 达到目标准确率 85%!")
        
        # Early stopping
        early_stopping(val_loss, model)
        if early_stopping.early_stop:
            logging.info("Early stopping triggered")
            break
        
        scheduler.step()
    
    logging.info("\n" + "="*80)
    logging.info(f"训练完成！")
    logging.info(f"最佳测试准确率: {best_test_acc:.2f}% (Epoch {best_epoch})")
    
    # 加载最佳模型进行最终评估
    if 'best_model_state' in locals():
        model.load_state_dict(best_model_state)
    
    # 详细评估结果
    logging.info("\n" + "="*80)
    logging.info("🎯 Evaluation Results (最终评估结果)")
    logging.info("="*80)
    
    # 收集所有预测结果
    model.eval()
    all_predictions = []
    all_targets = []
    all_coarse_predictions = []
    all_coarse_targets = []
    
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            
            # 处理序列标签
            if len(target.shape) == 2:
                target_single = target[:, 2]  # 取中间epoch
            else:
                target_single = target
            
            # 使用混合精度进行推理
            with autocast(enabled=use_amp):
                outputs, aux_dict = model(data)
            # outputs shape: (batch, seq_len, n_classes)
            # 取中间时间步的预测
            if len(outputs.shape) == 3:
                outputs_single = outputs[:, 2, :]  # 取中间epoch
            else:
                outputs_single = outputs
            
            _, predicted = outputs_single.max(1)
            all_predictions.extend(predicted.cpu().numpy())
            all_targets.extend(target_single.cpu().numpy())
            
            # 粗分类预测 - 从aux_dict中获取
            coarse_probs = aux_dict['coarse_probs']
            _, coarse_pred = coarse_probs.max(1)
            
            # 根据粗分类器类型设置标签
            if coarse_probs.shape[-1] == 2:
                # 新的2类分组：确定组(0) vs 过渡组(1)
                coarse_target = torch.zeros(len(target_single), dtype=torch.long).to(device)
                coarse_target[(target_single == 2) | (target_single == 3)] = 0  # N2, N3 -> 确定组
                coarse_target[(target_single == 0) | (target_single == 1) | (target_single == 4)] = 1  # W, N1, REM -> 过渡组
            else:
                # 原始3类分组：W/NREM/REM
                coarse_target = torch.tensor([
                    0 if t == 0 else (1 if t in [1, 2, 3] else 2) 
                    for t in target_single
                ]).to(device)
            
            all_coarse_predictions.extend(coarse_pred.cpu().numpy())
            all_coarse_targets.extend(coarse_target.cpu().numpy())
    
    # 计算混淆矩阵
    from sklearn.metrics import classification_report, cohen_kappa_score, confusion_matrix
    cm = confusion_matrix(all_targets, all_predictions)
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    
    # 输出混淆矩阵
    logging.info("\n🔄 Confusion Matrix:")
    logging.info("       Wake     N1     N2     N3    REM")
    for i, class_name in enumerate(class_names):
        row = cm[i]
        row_str = f"{class_name:4s}"
        for val in row:
            row_str += f"  {val:5d}"
        logging.info(row_str)
    
    # 输出详细的分类报告
    logging.info("\n📈 Per-Class Metrics:")
    report = classification_report(all_targets, all_predictions, 
                                  target_names=class_names, 
                                  output_dict=True)
    for class_name in class_names:
        metrics = report[class_name]
        logging.info(f"{class_name:5s}: F1={metrics['f1-score']:.3f}, "
                    f"Prec={metrics['precision']:.3f}, "
                    f"Recall={metrics['recall']:.3f}, "
                    f"Support={int(metrics['support'])}")
    
    # 总体指标
    accuracy = report['accuracy']
    macro_f1 = report['macro avg']['f1-score']
    weighted_f1 = report['weighted avg']['f1-score']
    kappa = cohen_kappa_score(all_targets, all_predictions)
    
    logging.info(f"\n📊 Overall Metrics:")
    logging.info(f"Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    logging.info(f"Macro F1: {macro_f1:.4f}")
    logging.info(f"Weighted F1: {weighted_f1:.4f}")
    logging.info(f"Cohen's Kappa: {kappa:.4f}")
    
    # 粗分类评估
    coarse_cm = confusion_matrix(all_coarse_targets, all_coarse_predictions)
    coarse_acc = np.sum(np.array(all_coarse_predictions) == np.array(all_coarse_targets)) / len(all_coarse_targets)
    
    # 根据粗分类器类型设置类名
    if len(np.unique(all_coarse_targets)) == 2:
        coarse_names = ['Stable(N2,N3)', 'Transit(W,N1,REM)']
        header = "       Stable   Transit"
    else:
        coarse_names = ['Wake', 'NREM', 'REM']
        header = "       Wake   NREM    REM"
    
    logging.info(f"\n🎯 Coarse Classification Results:")
    logging.info(f"Coarse Accuracy: {coarse_acc:.4f} ({coarse_acc*100:.2f}%)")
    logging.info("Coarse Confusion Matrix:")
    logging.info(header)
    for i, class_name in enumerate(coarse_names):
        if i < len(coarse_cm):
            row = coarse_cm[i]
            row_str = f"{class_name[:7]:7s}"
            for val in row:
                row_str += f"  {val:7d}"
            logging.info(row_str)
    
    logging.info("\n" + "="*80)
    if best_test_acc >= 85:
        logging.info("✅ Stage 4 成功达到目标!")
    else:
        logging.info(f"⚠️ Stage 4 未达到85%目标 (当前: {best_test_acc:.2f}%)")
    logging.info("="*80)
    
    # 保存训练信息
    with open(f"{log_dir}/training_info.json", 'w') as f:
        json.dump({
            'config': config,
            'best_test_acc': best_test_acc,
            'best_epoch': best_epoch,
            'total_epochs': epoch,
            'target_achieved': best_test_acc >= 85,
            'final_metrics': {
                'accuracy': float(accuracy),
                'macro_f1': float(macro_f1),
                'weighted_f1': float(weighted_f1),
                'kappa': float(kappa),
                'coarse_accuracy': float(coarse_acc)
            }
        }, f, indent=2)


if __name__ == "__main__":
    main()