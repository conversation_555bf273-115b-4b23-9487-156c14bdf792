#!/usr/bin/env python3
"""
Stage 4: Mamba-Transformer混合架构 + 渐进式分类策略
完整实现原始方案的核心创新：
1. Mamba组件用于长程依赖建模
2. 模态内特征精炼（EEG局部注意力，EOG/EMG轻量级Mamba）
3. 渐进式分类策略（粗分类→细分类）
4. 不确定性估计和置信度决策
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

import numpy as np
import os
import glob
import copy
import logging
from datetime import datetime
from pathlib import Path
import json
from tqdm import tqdm
import math
import random
from sklearn.metrics import (
    classification_report,
    confusion_matrix,
    f1_score,
    accuracy_score,
    cohen_kappa_score,
)

# 导入必要的模块
import sys
import os

# 添加依赖路径
current_dir = os.path.dirname(os.path.abspath(__file__))
dependencies_dir = os.path.join(current_dir, "stage4_mp_dependencies")
sys.path.insert(0, dependencies_dir)

from utils.sequence_dataset import SequenceSleepDataset
from models.sequential_mambaformer_v2 import PositionalEncoding
from utils.train_utils import EarlyStopping


def set_seed(seed=42):
    """设置所有随机种子确保可重现性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ["PYTHONHASHSEED"] = str(seed)


# ===================== Mamba组件 =====================


class SimplifiedMambaBlock(nn.Module):
    """简化的Mamba块实现（避免外部依赖）"""

    def __init__(self, d_model, d_state=16, d_conv=4, expand_factor=2):
        super().__init__()
        self.d_model = d_model
        self.d_inner = d_model * expand_factor

        # 输入投影
        self.in_proj = nn.Linear(d_model, self.d_inner * 2)

        # 1D卷积用于局部特征
        self.conv1d = nn.Conv1d(
            self.d_inner,
            self.d_inner,
            kernel_size=d_conv,
            padding=d_conv // 2,
            groups=self.d_inner,
        )

        # SSM参数
        self.x_proj = nn.Linear(self.d_inner, d_state * 2 + 1)
        self.dt_proj = nn.Linear(1, self.d_inner)

        # A矩阵（状态转移）
        A = torch.arange(1, d_state + 1).reshape(1, d_state).repeat(self.d_inner, 1)
        self.A_log = nn.Parameter(torch.log(A))

        # D参数（跳跃连接）
        self.D = nn.Parameter(torch.ones(self.d_inner))

        # 输出投影
        self.out_proj = nn.Linear(self.d_inner, d_model)

        self.activation = nn.SiLU()
        self.norm = nn.LayerNorm(d_model)

    def forward(self, x):
        """x: (batch, seq_len, d_model)"""
        batch_size, seq_len, _ = x.shape
        residual = x

        # 输入投影和激活
        x_proj = self.in_proj(x)
        x_conv, x_gate = x_proj.chunk(2, dim=-1)

        # 卷积处理
        x_conv = x_conv.transpose(1, 2)  # (batch, d_inner, seq_len)
        x_conv = self.conv1d(x_conv)
        # 确保序列长度匹配
        if x_conv.size(2) != seq_len:
            x_conv = x_conv[:, :, :seq_len]
        x_conv = x_conv.transpose(1, 2)  # (batch, seq_len, d_inner)
        x_conv = self.activation(x_conv)

        # 确保x_gate维度匹配
        if x_gate.size(1) != x_conv.size(1):
            # 如果维度不匹配，调整x_gate
            if x_gate.size(1) > x_conv.size(1):
                x_gate = x_gate[:, : x_conv.size(1), :]
            else:
                # 填充x_gate
                padding = x_conv.size(1) - x_gate.size(1)
                x_gate = F.pad(x_gate, (0, 0, 0, padding))

        # SSM处理（简化版）
        ssm_params = self.x_proj(x_conv)
        B, C, delta = torch.split(
            ssm_params, [self.A_log.shape[1], self.A_log.shape[1], 1], dim=-1
        )
        delta = F.softplus(delta)

        # 简化的状态空间计算
        A = -torch.exp(self.A_log)

        # 离散化
        deltaA = torch.exp(delta.unsqueeze(-2) * A.unsqueeze(0).unsqueeze(0))
        deltaB = delta.unsqueeze(-1) * B.unsqueeze(-2)

        # 扫描操作（简化版本）
        y = torch.zeros_like(x_conv)
        h = torch.zeros(batch_size, self.d_inner, self.A_log.shape[1], device=x.device)

        current_seq_len = x_conv.size(1)
        for t in range(current_seq_len):
            h = deltaA[:, t] * h + deltaB[:, t] * x_conv[:, t : t + 1].transpose(-1, -2)
            y[:, t] = (h * C[:, t].unsqueeze(-2)).sum(dim=-1)

        # 添加跳跃连接
        y = y + x_conv * self.D

        # 门控
        y = y * self.activation(x_gate)

        # 输出投影
        output = self.out_proj(y)

        # 确保残差连接的维度匹配
        if output.size(1) != residual.size(1):
            if output.size(1) > residual.size(1):
                output = output[:, : residual.size(1), :]
            else:
                padding = residual.size(1) - output.size(1)
                output = F.pad(output, (0, 0, 0, padding))

        # 残差连接
        return self.norm(output + residual)


class LocalAttention(nn.Module):
    """局部注意力机制（用于EEG）"""

    def __init__(self, d_model, window_size=50, n_heads=4, dropout=0.15):
        super().__init__()
        self.window_size = window_size
        self.attention = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        self.norm = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        """x: (batch, seq_len, d_model)"""
        batch_size, seq_len, d_model = x.shape

        # 如果序列长度小于窗口大小，使用全局注意力
        if seq_len <= self.window_size:
            attn_out, _ = self.attention(x, x, x)
            return self.norm(x + self.dropout(attn_out))

        # 分割成窗口
        num_windows = (seq_len + self.window_size - 1) // self.window_size
        padded_len = num_windows * self.window_size

        # 填充到窗口大小的倍数
        if seq_len < padded_len:
            padding = padded_len - seq_len
            x_padded = F.pad(x, (0, 0, 0, padding))
        else:
            x_padded = x

        # 重塑为窗口
        x_windows = x_padded.reshape(batch_size, num_windows, self.window_size, d_model)
        x_windows = x_windows.reshape(
            batch_size * num_windows, self.window_size, d_model
        )

        # 在每个窗口内应用注意力
        attn_out, _ = self.attention(x_windows, x_windows, x_windows)

        # 重塑回原始形状
        attn_out = attn_out.reshape(batch_size, num_windows, self.window_size, d_model)
        attn_out = attn_out.reshape(batch_size, padded_len, d_model)

        # 移除填充
        if seq_len < padded_len:
            attn_out = attn_out[:, :seq_len]

        return self.norm(x + self.dropout(attn_out))


# ===================== 模态内特征精炼 =====================


class ModalRefinementModule(nn.Module):
    """模态内特征精炼模块"""

    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()

        # EEG: 局部注意力
        self.eeg_refinement = LocalAttention(
            d_model, window_size=50, n_heads=4, dropout=dropout
        )

        # EOG: 轻量级Mamba
        self.eog_refinement = SimplifiedMambaBlock(
            d_model, d_state=8, d_conv=4, expand_factor=1
        )

        # EMG: 轻量级Mamba
        self.emg_refinement = SimplifiedMambaBlock(
            d_model, d_state=8, d_conv=4, expand_factor=1
        )

    def forward(self, eeg_feat, eog_feat, emg_feat):
        """
        分别精炼各模态特征
        Args:
            eeg_feat: (batch, seq_len, d_model)
            eog_feat: (batch, seq_len, d_model)
            emg_feat: (batch, seq_len, d_model)
        """
        eeg_refined = self.eeg_refinement(eeg_feat)
        eog_refined = self.eog_refinement(eog_feat)
        emg_refined = self.emg_refinement(emg_feat)

        return eeg_refined, eog_refined, emg_refined


# ===================== 渐进式分类器 =====================


class ProgressiveClassifier(nn.Module):
    """渐进式分类策略"""

    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()

        # 第一阶段：粗分类器 (W vs NREM vs REM)
        self.coarse_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, 3),  # 3类：W, NREM, REM
        )

        # 第二阶段：NREM细分类器 (N1 vs N2 vs N3)
        self.nrem_refiner = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, 3),  # 3类：N1, N2, N3
        )

        # 不确定性估计（用于MC Dropout）
        self.mc_dropout = nn.Dropout(dropout)

        # 温度缩放参数（用于校准）
        self.temperature = nn.Parameter(torch.ones(1) * 1.5)

    def forward(self, x, num_samples=1, return_uncertainty=False):
        """
        渐进式分类
        Args:
            x: (batch, seq_len, d_model)
            num_samples: MC Dropout采样次数
            return_uncertainty: 是否返回不确定性估计
        """
        batch_size, seq_len, d_model = x.shape

        # 取序列的平均特征或最后时刻特征
        x_pooled = x.mean(dim=1)  # (batch, d_model)

        # 第一阶段：粗分类
        if num_samples > 1 and self.training:
            # MC Dropout采样
            coarse_preds = []
            for _ in range(num_samples):
                x_drop = self.mc_dropout(x_pooled)
                pred = self.coarse_classifier(x_drop)
                coarse_preds.append(pred)
            coarse_logits = torch.stack(coarse_preds).mean(0)
            coarse_uncertainty = torch.stack(coarse_preds).std(0).mean(-1)
        else:
            coarse_logits = self.coarse_classifier(x_pooled)
            coarse_uncertainty = torch.zeros(batch_size, device=x.device)

        # 温度缩放
        coarse_probs = F.softmax(coarse_logits / self.temperature, dim=-1)

        # 第二阶段：软融合策略 - 所有样本都计算NREM细分类
        final_logits = torch.zeros(batch_size, 5, device=x.device)

        # 所有样本都计算NREM细分类（避免硬路由导致的N1机会丧失）
        if num_samples > 1 and self.training:
            nrem_preds = []
            for _ in range(num_samples):
                nrem_drop = self.mc_dropout(x_pooled)
                pred = self.nrem_refiner(nrem_drop)
                nrem_preds.append(pred)
            nrem_logits = torch.stack(nrem_preds).mean(0)
            nrem_uncertainty = torch.stack(nrem_preds).std(0).mean(-1)
            coarse_uncertainty += nrem_uncertainty
        else:
            nrem_logits = self.nrem_refiner(x_pooled)

        # 软融合：用粗分类概率作为权重
        # Wake: 直接使用粗分类的Wake logit
        final_logits[:, 0] = coarse_logits[:, 0]

        # NREM细分类：用NREM概率加权，避免完全依赖硬路由
        nrem_prob = coarse_probs[:, 1]  # NREM概率
        final_logits[:, 1] = torch.log(nrem_prob + 1e-8) + nrem_logits[:, 0]  # N1
        final_logits[:, 2] = torch.log(nrem_prob + 1e-8) + nrem_logits[:, 1]  # N2
        final_logits[:, 3] = torch.log(nrem_prob + 1e-8) + nrem_logits[:, 2]  # N3

        # REM: 直接使用粗分类的REM logit
        final_logits[:, 4] = coarse_logits[:, 2]

        # 返回最终分类结果
        if return_uncertainty:
            return final_logits, coarse_probs, coarse_uncertainty
        else:
            return final_logits, coarse_probs


# ===================== 混合Mamba-Transformer架构 =====================


class HybridMambaTransformer(nn.Module):
    """混合Mamba-Transformer编码器"""

    def __init__(
        self,
        d_model=128,
        n_heads=8,
        n_mamba_layers=2,
        n_transformer_layers=2,
        dropout=0.15,
    ):
        super().__init__()

        # Mamba层（用于长程依赖）
        self.mamba_layers = nn.ModuleList(
            [
                SimplifiedMambaBlock(d_model, d_state=16, d_conv=4, expand_factor=2)
                for _ in range(n_mamba_layers)
            ]
        )

        # Transformer层（用于短程模式）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation="relu",
            batch_first=True,
        )
        self.transformer_layers = nn.TransformerEncoder(
            encoder_layer, num_layers=n_transformer_layers
        )

        self.norm = nn.LayerNorm(d_model)

    def forward(self, x):
        """x: (batch, seq_len, d_model)"""
        # 先通过Mamba层处理长程依赖
        for mamba_layer in self.mamba_layers:
            x = mamba_layer(x)

        # 再通过Transformer层处理短程模式
        x = self.transformer_layers(x)

        return self.norm(x)


# ===================== 完整的Stage 4模型 =====================


class Stage4MambaProgressiveModel(nn.Module):
    """Stage 4: 完整的Mamba-Transformer混合架构 + 渐进式分类"""

    def __init__(
        self,
        n_classes=5,
        d_model=128,
        n_heads=8,
        n_mamba_layers=2,
        n_transformer_layers=2,
        dropout=0.15,
        seq_len=5,
    ):
        super().__init__()

        self.d_model = d_model
        self.seq_len = seq_len

        # 特征提取器（保持与之前Stage一致）
        self.eeg_extractor = nn.Sequential(
            nn.Conv1d(2, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            nn.Conv1d(128, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3),
        )

        self.eog_extractor = nn.Sequential(
            nn.Conv1d(1, 32, kernel_size=50, stride=6),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            nn.Conv1d(32, 64, kernel_size=8, stride=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            nn.Conv1d(64, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3),
        )

        self.emg_extractor = nn.Sequential(
            nn.Conv1d(1, 32, kernel_size=50, stride=6),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            nn.Conv1d(32, 64, kernel_size=8, stride=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            nn.Conv1d(64, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3),
        )

        # 模态内特征精炼
        self.modal_refinement = ModalRefinementModule(d_model, dropout)

        # EEG中心的跨模态注意力
        self.cross_modal_attention = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )

        # 自适应门控融合
        self.gate_network = nn.Sequential(
            nn.Linear(d_model * 3, d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, 3),
            nn.Softmax(dim=-1),
        )

        # 混合Mamba-Transformer编码器
        self.hybrid_encoder = HybridMambaTransformer(
            d_model, n_heads, n_mamba_layers, n_transformer_layers, dropout
        )

        # 渐进式分类器
        self.progressive_classifier = ProgressiveClassifier(d_model, dropout)

        # 位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)

        # 初始化权重
        self._init_weights()

        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建Stage4MambaProgressiveModel: 参数量={total_params:,}")

    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode="fan_out", nonlinearity="relu")
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def extract_features(self, x):
        """提取多模态特征"""
        # 调整维度
        x = x.transpose(1, 2)  # (batch, channels, time_steps)

        # 分离模态
        eeg = x[:, :2, :]  # EEG: 前2个通道
        eog = x[:, 2:3, :]  # EOG: 第3个通道
        emg = x[:, 3:4, :]  # EMG: 第4个通道

        # 独立提取特征
        eeg_feat = self.eeg_extractor(eeg)  # (batch, d_model, time')
        eog_feat = self.eog_extractor(eog)
        emg_feat = self.emg_extractor(emg)

        # 转换为序列格式
        eeg_feat = eeg_feat.transpose(1, 2)  # (batch, time', d_model)
        eog_feat = eog_feat.transpose(1, 2)
        emg_feat = emg_feat.transpose(1, 2)

        return eeg_feat, eog_feat, emg_feat

    def forward(self, x, return_uncertainty=False):
        """
        Args:
            x: (batch, seq_len, time_steps, channels)
            return_uncertainty: 是否返回不确定性估计
        """
        batch_size, seq_len, time_steps, channels = x.shape

        # 处理每个时间步
        all_features = []

        for t in range(seq_len):
            x_t = x[:, t, :, :]  # (batch, time_steps, channels)

            # 提取特征
            eeg_feat, eog_feat, emg_feat = self.extract_features(x_t)

            # 模态内特征精炼
            eeg_refined, eog_refined, emg_refined = self.modal_refinement(
                eeg_feat, eog_feat, emg_feat
            )

            # EEG中心的跨模态注意力
            eog_attended, _ = self.cross_modal_attention(
                eeg_refined, eog_refined, eog_refined
            )
            emg_attended, _ = self.cross_modal_attention(
                eeg_refined, emg_refined, emg_refined
            )

            # 自适应门控融合
            # 取每个模态的平均特征
            eeg_pooled = eeg_refined.mean(dim=1)
            eog_pooled = eog_attended.mean(dim=1)
            emg_pooled = emg_attended.mean(dim=1)

            concat_feat = torch.cat([eeg_pooled, eog_pooled, emg_pooled], dim=-1)
            gate_weights = self.gate_network(concat_feat)  # (batch, 3)

            # 加权融合
            fused_feat = (
                gate_weights[:, 0:1].unsqueeze(1) * eeg_refined
                + gate_weights[:, 1:2].unsqueeze(1) * eog_attended
                + gate_weights[:, 2:3].unsqueeze(1) * emg_attended
            )

            # 取平均作为该时间步的特征
            all_features.append(fused_feat.mean(dim=1))

        # Stack成序列
        features = torch.stack(all_features, dim=1)  # (batch, seq_len, d_model)

        # 位置编码
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # (batch, seq_len, d_model)

        # 混合Mamba-Transformer编码
        encoded = self.hybrid_encoder(features)

        # 渐进式分类
        if return_uncertainty:
            final_logits, coarse_probs, uncertainty = self.progressive_classifier(
                encoded, num_samples=5 if self.training else 1, return_uncertainty=True
            )
            # 扩展到序列长度
            final_output = final_logits.unsqueeze(1).expand(-1, seq_len, -1)
            return final_output, {
                "coarse_probs": coarse_probs,
                "uncertainty": uncertainty,
                "gate_weights": gate_weights,
            }
        else:
            final_logits, coarse_probs = self.progressive_classifier(
                encoded, num_samples=1
            )
            # 扩展到序列长度
            final_output = final_logits.unsqueeze(1).expand(-1, seq_len, -1)
            return final_output, {
                "coarse_probs": coarse_probs,
                "gate_weights": gate_weights,
            }


# ===================== 损失函数 =====================


class ProgressiveLoss(nn.Module):
    """渐进式分类的损失函数"""

    def __init__(self, coarse_weight=0.3, uncertainty_weight=0.1):
        super().__init__()
        self.coarse_weight = coarse_weight
        self.uncertainty_weight = uncertainty_weight

    def forward(self, outputs, labels):
        """
        Args:
            outputs: (main_output, aux_dict)
            labels: (batch, seq_len)
        """
        main_output, aux_dict = outputs

        # 主任务损失（5分类）
        main_loss = F.cross_entropy(
            main_output.reshape(-1, main_output.size(-1)), labels.reshape(-1)
        )

        # 粗分类损失（3分类：W vs NREM vs REM）
        # 将5分类标签映射到3分类
        coarse_labels = labels.clone()
        coarse_labels[labels == 0] = 0  # W -> 0
        coarse_labels[(labels >= 1) & (labels <= 3)] = 1  # N1,N2,N3 -> 1 (NREM)
        coarse_labels[labels == 4] = 2  # REM -> 2

        # 只使用第一个时间步的粗分类概率
        coarse_probs = aux_dict["coarse_probs"]
        coarse_loss = F.cross_entropy(
            torch.log(coarse_probs + 1e-8),  # 使用log因为已经是概率
            coarse_labels[:, 0],  # 只用第一个时间步
        )

        # 不确定性正则化（鼓励低不确定性）
        uncertainty_loss = 0
        if "uncertainty" in aux_dict:
            uncertainty_loss = aux_dict["uncertainty"].mean()

        # 组合损失
        total_loss = (
            main_loss
            + self.coarse_weight * coarse_loss
            + self.uncertainty_weight * uncertainty_loss
        )

        return total_loss, {
            "main": main_loss.item(),
            "coarse": coarse_loss.item(),
            "uncertainty": (
                uncertainty_loss.item()
                if isinstance(uncertainty_loss, torch.Tensor)
                else 0
            ),
            "total": total_loss.item(),
        }


# ===================== 训练和评估函数 =====================


def train_epoch(model, dataloader, criterion, optimizer, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    coarse_correct = 0
    loss_components = {"main": 0, "coarse": 0, "uncertainty": 0}

    with tqdm(dataloader, desc="Training") as pbar:
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)

            optimizer.zero_grad()

            # 前向传播（不使用MC Dropout，与test_soft_fusion_fair.py一致）
            outputs = model(data, return_uncertainty=False)
            loss, loss_dict = criterion(outputs, labels)

            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            # 统计
            total_loss += loss.item()
            main_output = outputs[0]
            _, predicted = main_output.max(-1)
            correct += predicted.eq(labels).sum().item()
            total += labels.numel()

            # 粗分类准确率
            coarse_probs = outputs[1]["coarse_probs"]
            coarse_pred = coarse_probs.argmax(dim=-1)
            coarse_labels = labels[:, 0].clone()
            coarse_labels[labels[:, 0] == 0] = 0  # W
            coarse_labels[(labels[:, 0] >= 1) & (labels[:, 0] <= 3)] = 1  # NREM
            coarse_labels[labels[:, 0] == 4] = 2  # REM
            coarse_correct += coarse_pred.eq(coarse_labels).sum().item()

            # 累积损失分量
            for key in ["main", "coarse", "uncertainty"]:
                loss_components[key] += loss_dict[key]

            # 更新进度条
            pbar.set_postfix(
                {
                    "loss": f"{loss.item():.4f}",
                    "acc": f"{100.*correct/total:.2f}%",
                    "coarse": f"{100.*coarse_correct/len(dataloader.dataset):.2f}%",
                }
            )

    # 平均值
    num_batches = len(dataloader)
    for key in loss_components:
        loss_components[key] /= num_batches

    return total_loss / num_batches, 100.0 * correct / total, loss_components


def evaluate(model, dataloader, criterion, device):
    """评估函数"""
    model.eval()
    total_loss = 0
    correct = 0
    total = 0
    coarse_correct = 0
    all_preds = []
    all_labels = []

    with torch.no_grad():
        for data, labels in tqdm(dataloader, desc="Evaluating"):
            data = data.to(device)
            labels = labels.to(device)

            outputs = model(data, return_uncertainty=False)
            loss, _ = criterion(outputs, labels)

            total_loss += loss.item()
            main_output = outputs[0]
            _, predicted = main_output.max(-1)

            correct += predicted.eq(labels).sum().item()
            total += labels.numel()

            # 粗分类准确率
            coarse_probs = outputs[1]["coarse_probs"]
            coarse_pred = coarse_probs.argmax(dim=-1)
            coarse_labels = labels[:, 0].clone()
            coarse_labels[labels[:, 0] == 0] = 0  # W
            coarse_labels[(labels[:, 0] >= 1) & (labels[:, 0] <= 3)] = 1  # NREM
            coarse_labels[labels[:, 0] == 4] = 2  # REM
            coarse_correct += coarse_pred.eq(coarse_labels).sum().item()

            all_preds.extend(predicted.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())

    accuracy = 100.0 * correct / total
    coarse_accuracy = 100.0 * coarse_correct / len(dataloader.dataset)

    # 计算F1分数
    from sklearn.metrics import f1_score, confusion_matrix

    f1 = f1_score(all_labels, all_preds, average="macro")
    cm = confusion_matrix(all_labels, all_preds)

    return total_loss / len(dataloader), accuracy, f1, coarse_accuracy, cm


def main():
    # 设置随机种子确保可重现性
    set_seed(42)

    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/stage4_mamba_progressive_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(f"{log_dir}/training.log"),
            logging.StreamHandler(),
        ],
    )

    logging.info("=" * 80)
    logging.info("🎯 Stage 4: Mamba-Transformer混合架构 + 渐进式分类策略")
    logging.info("核心创新：")
    logging.info("1. Mamba组件用于长程依赖建模")
    logging.info("2. 模态内特征精炼（EEG局部注意力，EOG/EMG轻量级Mamba）")
    logging.info("3. 渐进式分类策略（粗分类→细分类）")
    logging.info("4. 不确定性估计和置信度决策")
    logging.info("目标: ≥85% accuracy")
    logging.info("=" * 80)

    # 配置
    config = {
        "n_classes": 5,
        "d_model": 128,
        "n_heads": 8,
        "n_mamba_layers": 2,
        "n_transformer_layers": 2,
        "dropout": 0.15,
        "seq_len": 5,
        "batch_size": 32,  # 与纯软融合测试一致
        "learning_rate": 2e-4,  # 与纯软融合测试一致
        "num_epochs": 30,  # 与纯软融合测试一致
        "weight_decay": 1e-4,
        "patience": 10,  # 与纯软融合测试一致
        "coarse_weight": 0.3,
        "uncertainty_weight": 0.1,
    }

    logging.info(f"配置: {config}")

    # 设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"设备: {device}")

    # 数据路径
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))

    # 按受试者划分数据集
    from collections import defaultdict

    subject_files = defaultdict(list)
    for file_path in all_files:
        filename = os.path.basename(file_path)
        if filename.startswith("SC4") and len(filename) >= 7:
            subject_id = filename[3:5]
            subject_files[subject_id].append(file_path)

    # 使用与test_soft_fusion_fair.py完全相同的固定数据分割
    train_subjects = [
        "00",
        "01",
        "02",
        "03",
        "06",
        "07",
        "08",
        "09",
        "10",
        "11",
        "12",
        "13",
        "15",
        "16",
        "17",
        "18",
    ]
    val_subjects = ["04", "14"]
    test_subjects = ["05", "19"]

    # 收集文件
    train_files = []
    val_files = []
    test_files = []

    for subject in train_subjects:
        if subject in subject_files:
            train_files.extend(subject_files[subject])
    for subject in val_subjects:
        if subject in subject_files:
            val_files.extend(subject_files[subject])
    for subject in test_subjects:
        if subject in subject_files:
            test_files.extend(subject_files[subject])

    # 排序确保一致性
    train_files.sort()
    val_files.sort()
    test_files.sort()

    logging.info(f"训练集受试者: {train_subjects}")
    logging.info(f"验证集受试者: {val_subjects}")
    logging.info(f"测试集受试者: {test_subjects}")
    logging.info(f"训练文件数: {len(train_files)}")
    logging.info(f"验证文件数: {len(val_files)}")
    logging.info(f"测试文件数: {len(test_files)}")

    logging.info("加载数据集...")

    # 创建数据集
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,  # 与test_soft_fusion_fair.py一致
        # use_channels和is_training使用默认值
    )

    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
    )

    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
    )

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")

    # 创建模型
    model = Stage4MambaProgressiveModel(
        n_classes=config["n_classes"],
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_mamba_layers=config["n_mamba_layers"],
        n_transformer_layers=config["n_transformer_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)

    # 损失函数
    criterion = ProgressiveLoss(
        coarse_weight=config["coarse_weight"],
        uncertainty_weight=config["uncertainty_weight"],
    )

    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"],
    )

    # 学习率调度器
    scheduler = CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2, eta_min=1e-6)

    # Early stopping
    early_stopping = EarlyStopping(patience=config["patience"], verbose=True)

    # 训练循环
    best_test_acc = 0.0
    best_test_f1 = 0.0
    best_epoch = 0

    for epoch in range(config["num_epochs"]):
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}")

        # 训练
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        for batch_idx, (data, labels) in enumerate(tqdm(train_loader, desc="训练")):
            data, labels = data.to(device), labels.to(device)

            optimizer.zero_grad()

            # 前向传播
            main_output, aux_dict = model(data)

            # 计算损失
            loss, loss_dict = criterion((main_output, aux_dict), labels)

            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()

            # 统计（使用中心时间步）
            train_loss += loss.item()
            if main_output.dim() == 3:
                center_idx = main_output.shape[1] // 2
                main_logits = main_output[:, center_idx, :]
                center_labels = labels[:, center_idx]
            else:
                main_logits = main_output
                center_labels = labels

            _, predicted = main_logits.max(1)
            train_total += center_labels.size(0)
            train_correct += predicted.eq(center_labels).sum().item()

        train_acc = 100.0 * train_correct / train_total
        logging.info(
            f"训练 - Loss: {train_loss/len(train_loader):.4f}, Acc: {train_acc:.2f}%"
        )

        # 验证
        model.eval()
        val_correct = 0
        val_total = 0
        val_preds = []
        val_labels = []

        with torch.no_grad():
            for data, labels in tqdm(val_loader, desc="验证"):
                data, labels = data.to(device), labels.to(device)

                main_output, aux_dict = model(data)

                if main_output.dim() == 3:
                    center_idx = main_output.shape[1] // 2
                    main_logits = main_output[:, center_idx, :]
                    center_labels = labels[:, center_idx]
                else:
                    main_logits = main_output
                    center_labels = labels

                _, predicted = main_logits.max(1)
                val_total += center_labels.size(0)
                val_correct += predicted.eq(center_labels).sum().item()

                val_preds.extend(predicted.cpu().numpy())
                val_labels.extend(center_labels.cpu().numpy())

        val_acc = 100.0 * val_correct / val_total
        val_f1 = f1_score(val_labels, val_preds, average="macro")
        logging.info(f"验证 - Acc: {val_acc:.2f}%, Macro F1: {val_f1:.4f}")

        # 测试
        model.eval()
        test_correct = 0
        test_total = 0
        test_preds = []
        test_labels = []

        with torch.no_grad():
            for data, labels in tqdm(test_loader, desc="测试"):
                data, labels = data.to(device), labels.to(device)

                main_output, aux_dict = model(data)

                if main_output.dim() == 3:
                    center_idx = main_output.shape[1] // 2
                    main_logits = main_output[:, center_idx, :]
                    center_labels = labels[:, center_idx]
                else:
                    main_logits = main_output
                    center_labels = labels

                _, predicted = main_logits.max(1)
                test_total += center_labels.size(0)
                test_correct += predicted.eq(center_labels).sum().item()

                test_preds.extend(predicted.cpu().numpy())
                test_labels.extend(center_labels.cpu().numpy())

        test_acc = 100.0 * test_correct / test_total
        test_f1 = f1_score(test_labels, test_preds, average="macro")
        test_kappa = cohen_kappa_score(test_labels, test_preds)
        logging.info(
            f"测试 - Acc: {test_acc:.2f}%, Macro F1: {test_f1:.4f}, Kappa: {test_kappa:.4f}"
        )

        # 记录最佳结果
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_test_f1 = test_f1
            best_epoch = epoch + 1
            best_model_state = copy.deepcopy(model.state_dict())

            # 保存最佳模型到日志目录
            torch.save(
                {
                    "epoch": best_epoch,
                    "model_state_dict": best_model_state,
                    "test_acc": best_test_acc,
                    "test_f1": best_test_f1,
                    "config": config,
                },
                os.path.join(log_dir, "best_model.pth"),
            )

            logging.info(
                f"✅ 新的最佳结果: Epoch {best_epoch}, Acc={best_test_acc:.2f}%, F1={best_test_f1:.4f}"
            )

            # 详细分类报告
            class_names = ["Wake", "N1", "N2", "N3", "REM"]
            report = classification_report(
                test_labels, test_preds, target_names=class_names, digits=3
            )
            logging.info(f"\n详细分类报告:\n{report}")

            # 混淆矩阵
            cm = confusion_matrix(test_labels, test_preds)
            logging.info("\n混淆矩阵:")
            logging.info("     W   N1   N2   N3  REM")
            for i, row in enumerate(cm):
                logging.info(f"{class_names[i]:3s} {row}")

        scheduler.step()
        logging.info(f"学习率: {scheduler.get_last_lr()[0]:.6f}")

        if early_stopping.early_stop:
            logging.info("早停触发")
            break

    logging.info(f"\n" + "=" * 80)
    logging.info(f"最佳结果: Epoch {best_epoch}")
    logging.info(f"最佳测试准确率: {best_test_acc:.2f}%")
    logging.info(f"最佳测试Macro F1: {best_test_f1:.4f}")
    logging.info("=" * 80)

    # 加载最佳模型进行最终评估
    if "best_model_state" in locals():
        model.load_state_dict(best_model_state)

    # 详细评估结果
    logging.info("\n" + "=" * 80)
    logging.info("🎯 Evaluation Results (最终评估结果)")
    logging.info("=" * 80)

    # 收集所有预测结果
    model.eval()
    all_predictions = []
    all_targets = []
    all_coarse_predictions = []
    all_coarse_targets = []

    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)

            # 处理序列标签
            if len(target.shape) == 2:
                target_single = target[:, 2]  # 取中间epoch
            else:
                target_single = target

            outputs, aux_dict = model(data)
            # outputs shape: (batch, seq_len, n_classes)
            # 取中间时间步的预测
            if len(outputs.shape) == 3:
                outputs_single = outputs[:, 2, :]  # 取中间epoch
            else:
                outputs_single = outputs

            _, predicted = outputs_single.max(1)
            all_predictions.extend(predicted.cpu().numpy())
            all_targets.extend(target_single.cpu().numpy())

            # 粗分类预测 - 从aux_dict中获取
            coarse_probs = aux_dict["coarse_probs"]
            _, coarse_pred = coarse_probs.max(1)
            coarse_target = torch.tensor(
                [0 if t == 0 else (1 if t in [1, 2, 3] else 2) for t in target_single]
            ).to(device)
            all_coarse_predictions.extend(coarse_pred.cpu().numpy())
            all_coarse_targets.extend(coarse_target.cpu().numpy())

    # 计算混淆矩阵（已经在文件开头导入了这些函数）

    cm = confusion_matrix(all_targets, all_predictions)
    class_names = ["Wake", "N1", "N2", "N3", "REM"]

    # 输出混淆矩阵
    logging.info("\n🔄 Confusion Matrix:")
    logging.info("       Wake     N1     N2     N3    REM")
    for i, class_name in enumerate(class_names):
        row = cm[i]
        row_str = f"{class_name:4s}"
        for val in row:
            row_str += f"  {val:5d}"
        logging.info(row_str)

    # 输出详细的分类报告
    logging.info("\n📈 Per-Class Metrics:")
    report = classification_report(
        all_targets, all_predictions, target_names=class_names, output_dict=True
    )
    for class_name in class_names:
        metrics = report[class_name]
        logging.info(
            f"{class_name:5s}: F1={metrics['f1-score']:.3f}, "
            f"Prec={metrics['precision']:.3f}, "
            f"Recall={metrics['recall']:.3f}, "
            f"Support={int(metrics['support'])}"
        )

    # 总体指标
    accuracy = report["accuracy"]
    macro_f1 = report["macro avg"]["f1-score"]
    weighted_f1 = report["weighted avg"]["f1-score"]
    kappa = cohen_kappa_score(all_targets, all_predictions)

    logging.info(f"\n📊 Overall Metrics:")
    logging.info(f"Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    logging.info(f"Macro F1: {macro_f1:.4f}")
    logging.info(f"Weighted F1: {weighted_f1:.4f}")
    logging.info(f"Cohen's Kappa: {kappa:.4f}")

    # 粗分类评估
    coarse_cm = confusion_matrix(all_coarse_targets, all_coarse_predictions)
    coarse_names = ["Wake", "NREM", "REM"]
    coarse_acc = np.sum(
        np.array(all_coarse_predictions) == np.array(all_coarse_targets)
    ) / len(all_coarse_targets)

    logging.info(f"\n🎯 Coarse Classification Results:")
    logging.info(f"Coarse Accuracy: {coarse_acc:.4f} ({coarse_acc*100:.2f}%)")
    logging.info("Coarse Confusion Matrix:")
    logging.info("       Wake   NREM    REM")
    for i, class_name in enumerate(coarse_names):
        row = coarse_cm[i]
        row_str = f"{class_name:4s}"
        for val in row:
            row_str += f"  {val:5d}"
        logging.info(row_str)

    logging.info("\n" + "=" * 80)
    if best_test_acc >= 85:
        logging.info("✅ Stage 4 成功达到目标!")
    else:
        logging.info(f"⚠️ Stage 4 未达到85%目标 (当前: {best_test_acc:.2f}%)")
    logging.info("=" * 80)

    # 保存训练信息
    with open(f"{log_dir}/training_info.json", "w") as f:
        json.dump(
            {
                "config": config,
                "best_test_acc": best_test_acc,
                "best_epoch": best_epoch,
                "total_epochs": epoch,
                "target_achieved": best_test_acc >= 85,
                "final_metrics": {
                    "accuracy": float(accuracy),
                    "macro_f1": float(macro_f1),
                    "weighted_f1": float(weighted_f1),
                    "kappa": float(kappa),
                    "coarse_accuracy": float(coarse_acc),
                },
            },
            f,
            indent=2,
        )


if __name__ == "__main__":
    main()
