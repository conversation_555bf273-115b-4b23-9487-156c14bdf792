"""
Epoch级别的评估函数
实现序列到序列模型的正确评估方法：
1. 收集每个epoch的所有预测
2. 平均概率策略得到最终预测
3. 计算epoch级别的指标
"""

import numpy as np
import torch
from collections import defaultdict
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, cohen_kappa_score
import logging


class EpochLevelEvaluator:
    """
    处理序列重叠的epoch级别评估器
    """
    
    def __init__(self, seq_len=5, n_classes=5):
        self.seq_len = seq_len
        self.n_classes = n_classes
        self.reset()
    
    def reset(self):
        """重置收集的预测"""
        # 每个epoch收集所有预测的概率
        self.epoch_predictions = defaultdict(list)
        self.epoch_labels = {}
        self.file_boundaries = []  # 记录文件边界
    
    def add_file_boundary(self, epoch_idx):
        """添加文件边界，确保序列不跨文件"""
        self.file_boundaries.append(epoch_idx)
    
    def add_batch_predictions(self, seq_preds, seq_labels, start_indices, file_ids=None):
        """
        添加一批序列预测
        
        Args:
            seq_preds: (batch_size, seq_len, n_classes) - softmax概率
            seq_labels: (batch_size, seq_len) - 真实标签
            start_indices: (batch_size,) - 每个序列的起始epoch索引
            file_ids: (batch_size,) - 每个序列所属的文件ID（可选）
        """
        batch_size = seq_preds.shape[0]
        
        for b in range(batch_size):
            start_idx = start_indices[b]
            
            for pos in range(self.seq_len):
                epoch_idx = start_idx + pos
                
                # 存储预测概率
                self.epoch_predictions[epoch_idx].append(seq_preds[b, pos])
                
                # 存储真实标签（只需要存一次）
                if epoch_idx not in self.epoch_labels:
                    self.epoch_labels[epoch_idx] = seq_labels[b, pos]
    
    def get_final_predictions(self):
        """
        使用平均概率策略获取每个epoch的最终预测
        
        Returns:
            final_preds: 最终预测标签数组
            final_labels: 真实标签数组
            avg_probs: 平均概率数组
        """
        # 获取所有epoch索引并排序
        epoch_indices = sorted(self.epoch_labels.keys())
        
        final_preds = []
        final_labels = []
        avg_probs = []
        
        for epoch_idx in epoch_indices:
            # 获取该epoch的所有预测概率
            predictions = self.epoch_predictions[epoch_idx]
            
            if len(predictions) == 0:
                logging.warning(f"Epoch {epoch_idx} has no predictions!")
                continue
            
            # 计算平均概率
            avg_prob = np.mean(predictions, axis=0)
            
            # 选择概率最高的类别
            final_pred = np.argmax(avg_prob)
            
            final_preds.append(final_pred)
            final_labels.append(self.epoch_labels[epoch_idx])
            avg_probs.append(avg_prob)
        
        return np.array(final_preds), np.array(final_labels), np.array(avg_probs)
    
    def evaluate(self):
        """
        计算epoch级别的评估指标
        
        Returns:
            metrics: 包含各种评估指标的字典
        """
        final_preds, final_labels, avg_probs = self.get_final_predictions()
        
        # 基本指标
        accuracy = accuracy_score(final_labels, final_preds)
        macro_f1 = f1_score(final_labels, final_preds, average='macro')
        kappa = cohen_kappa_score(final_labels, final_preds)
        
        # 混淆矩阵
        cm = confusion_matrix(final_labels, final_preds)
        
        # 每类指标
        per_class_metrics = {}
        class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        
        for i, name in enumerate(class_names):
            # 该类的预测和真实标签
            class_mask = final_labels == i
            class_pred_mask = final_preds == i
            
            # 计算指标
            tp = np.sum((final_labels == i) & (final_preds == i))
            fp = np.sum((final_labels != i) & (final_preds == i))
            fn = np.sum((final_labels == i) & (final_preds != i))
            tn = np.sum((final_labels != i) & (final_preds != i))
            
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            per_class_metrics[name] = {
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'support': np.sum(class_mask)
            }
        
        # 汇总结果
        metrics = {
            'accuracy': accuracy,
            'macro_f1': macro_f1,
            'kappa': kappa,
            'confusion_matrix': cm,
            'per_class_metrics': per_class_metrics,
            'total_epochs': len(final_labels),
            'total_predictions_collected': sum(len(preds) for preds in self.epoch_predictions.values()),
            'avg_predictions_per_epoch': np.mean([len(preds) for preds in self.epoch_predictions.values()])
        }
        
        return metrics


def evaluate_with_epoch_aggregation(model, data_loader, device, dataset):
    """
    使用epoch级别聚合的评估函数
    
    Args:
        model: 训练好的模型
        data_loader: 数据加载器
        device: 设备
        dataset: 数据集对象（需要提供epoch索引信息）
    
    Returns:
        metrics: epoch级别的评估指标
    """
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=5, n_classes=5)
    
    with torch.no_grad():
        for batch_idx, (data, labels) in enumerate(data_loader):
            data = data.to(device)
            labels = labels.to(device)
            
            # 获取模型输出
            outputs, _ = model(data)  # (batch_size, seq_len, n_classes)
            probs = torch.softmax(outputs, dim=-1)
            
            # 获取每个序列的起始索引
            # 这需要dataset提供相应的方法
            batch_start = batch_idx * data_loader.batch_size
            batch_end = min(batch_start + data.shape[0], len(dataset))
            
            start_indices = []
            for i in range(batch_start, batch_end):
                # 假设dataset有get_sequence_info方法返回序列信息
                seq_info = dataset.get_sequence_info(i)
                start_indices.append(seq_info['start_epoch_idx'])
            
            # 添加预测
            evaluator.add_batch_predictions(
                probs.cpu().numpy(),
                labels.cpu().numpy(),
                start_indices
            )
    
    # 计算最终指标
    metrics = evaluator.evaluate()
    
    return metrics


def log_epoch_level_metrics(metrics, phase='Test', logger=None):
    """记录epoch级别的详细指标"""
    if logger is None:
        logger = logging
    
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 {phase} Epoch-Level Evaluation Results")
    logger.info(f"{'='*60}")
    
    logger.info(f"Total epochs evaluated: {metrics['total_epochs']}")
    logger.info(f"Average predictions per epoch: {metrics['avg_predictions_per_epoch']:.2f}")
    logger.info(f"Accuracy: {metrics['accuracy']:.4f}")
    logger.info(f"Macro F1: {metrics['macro_f1']:.4f}")
    logger.info(f"Cohen's Kappa: {metrics['kappa']:.4f}")
    
    # 混淆矩阵
    logger.info(f"\n🔄 Confusion Matrix:")
    cm = metrics['confusion_matrix']
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logger.info("     " + " ".join(f"{name:>6}" for name in class_names))
    for i, row in enumerate(cm):
        logger.info(f"{class_names[i]:>4} " + " ".join(f"{val:>6}" for val in row))
    logger.info(f"Total: {cm.sum()}")
    
    # 每类指标
    logger.info(f"\n📈 Per-Class Metrics:")
    for class_name, metrics_dict in metrics['per_class_metrics'].items():
        logger.info(f"{class_name:>5}: "
                   f"F1={metrics_dict['f1']:.3f}, "
                   f"Prec={metrics_dict['precision']:.3f}, "
                   f"Recall={metrics_dict['recall']:.3f}, "
                   f"Support={metrics_dict['support']}")