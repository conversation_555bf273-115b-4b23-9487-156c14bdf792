#!/usr/bin/env python3
"""
Stage 2 独立训练 - 不加载Stage 1权重
目标：从头训练带有跨模态注意力的模型，达到85%以上准确率
架构：基础模型 + 轻量级跨模态注意力模块
"""

import os
import sys
import json
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
from datetime import datetime
from pathlib import Path
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
from tqdm import tqdm

# 添加项目路径
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')

from mambaformer.utils.sequence_dataset import SequenceSleepDataset
from mambaformer.models.progressive_mambaformer_v1_fixed import (
    ProgressiveMAMBAFORMER_V1_Fixed,
    SequentialFocalLoss,
    TemporalConsistencyLoss
)


class LightweightCrossModalAttention(nn.Module):
    """轻量级跨模态注意力模块 - Stage 2特有"""
    def __init__(self, d_model=512, n_heads=4, dropout=0.1):
        super().__init__()
        
        # 使用较少的注意力头以减少计算量
        self.attention = nn.MultiheadAttention(
            d_model, 
            n_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 轻量级前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model * 2),
            nn.GELU(),  # 使用GELU激活
            nn.Dropout(dropout),
            nn.Linear(d_model * 2, d_model)
        )
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 可学习的融合权重，初始化为0.2（20%新特征）
        self.alpha = nn.Parameter(torch.tensor([0.2]))
        
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, d_model)
        Returns:
            enhanced_x: (batch, seq_len, d_model)
        """
        residual = x
        
        # 自注意力
        attn_out, _ = self.attention(x, x, x)
        x = self.norm1(residual + self.dropout(attn_out))
        
        # 前馈网络
        ffn_out = self.ffn(x)
        x = self.norm2(x + self.dropout(ffn_out))
        
        # 可学习的加权融合
        alpha = torch.sigmoid(self.alpha)  # 确保在[0,1]范围内
        enhanced = (1 - alpha) * residual + alpha * x
        
        return enhanced


class Stage2Model(nn.Module):
    """Stage 2独立模型 - 基础架构 + 跨模态注意力"""
    def __init__(self, config):
        super().__init__()
        
        # 基础模型（不加载预训练权重）
        self.base_model = ProgressiveMAMBAFORMER_V1_Fixed(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len'],
        )
        
        # Stage 2特有的跨模态注意力模块
        self.cross_modal_attention = LightweightCrossModalAttention(
            d_model=config['d_model'],
            n_heads=config['attention_heads'],
            dropout=config['dropout']
        )
        
        # 额外的分类头用于多任务学习
        self.auxiliary_classifier = nn.Sequential(
            nn.Linear(config['d_model'], config['d_model'] // 2),
            nn.ReLU(),
            nn.Dropout(config['dropout']),
            nn.Linear(config['d_model'] // 2, 5)
        )
        
    def forward(self, x):
        """前向传播"""
        batch_size, seq_len, channels, time_steps = x.shape
        
        # 重塑数据
        x_reshaped = x.view(batch_size * seq_len, channels, time_steps)
        
        # 特征提取
        features = self.base_model.feature_extractor(x_reshaped)
        features = features.view(batch_size, seq_len, -1)
        
        # 位置编码
        features = features.transpose(0, 1)
        features = self.base_model.pos_encoder(features)
        features = features.transpose(0, 1)
        
        # 通过transformer层
        for layer in self.base_model.transformer_encoder.layers:
            features = layer(features)
        
        # 应用跨模态注意力（Stage 2核心）
        enhanced_features = self.cross_modal_attention(features)
        
        # 主分类输出 - shape: (batch_size, seq_len, n_classes)
        main_output = self.base_model.classifier(enhanced_features)
        
        # 辅助分类输出（用于多任务学习）- shape: (batch_size, seq_len, n_classes)
        aux_output = self.auxiliary_classifier(enhanced_features)
        
        return main_output, aux_output


def train_epoch(model, dataloader, criterion_main, criterion_aux, criterion_temporal, 
                optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    progress_bar = tqdm(dataloader, desc=f"Epoch {epoch}")
    for batch_idx, (data, labels) in enumerate(progress_bar):
        data = data.to(device)
        labels = labels.to(device)
        data = data.permute(0, 1, 3, 2)  # (B, S, C, T)
        
        optimizer.zero_grad()
        
        # 前向传播
        main_output, aux_output = model(data)
        
        # 计算损失 - 需要reshape输出和标签
        # main_output shape: (batch_size, seq_len, n_classes)
        # labels shape: (batch_size, seq_len)
        main_loss = criterion_main(main_output, labels)
        
        # 辅助损失 - 正确处理序列标签
        # aux_output shape: (batch_size, seq_len, n_classes)
        # labels shape: (batch_size, seq_len)
        aux_loss = criterion_aux(aux_output, labels)
        
        temporal_loss = criterion_temporal(main_output)
        
        # 组合损失（主任务权重更高）
        loss = 0.7 * main_loss + 0.2 * aux_loss + 0.1 * temporal_loss
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 记录
        total_loss += loss.item()
        preds = main_output.argmax(dim=-1)
        all_preds.append(preds.cpu().numpy())
        all_labels.append(labels.cpu().numpy())
        
        # 更新进度条
        if batch_idx % 10 == 0:
            alpha = torch.sigmoid(model.cross_modal_attention.alpha).item()
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'alpha': f"{alpha:.3f}"
            })
    
    # 计算指标
    all_preds = np.concatenate(all_preds).flatten()
    all_labels = np.concatenate(all_labels).flatten()
    accuracy = accuracy_score(all_labels, all_preds)
    
    return total_loss / len(dataloader), accuracy


def evaluate(model, dataloader, device):
    """评估模型"""
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for data, labels in tqdm(dataloader, desc="评估"):
            data = data.to(device)
            data = data.permute(0, 1, 3, 2)
            
            main_output, _ = model(data)
            preds = main_output.argmax(dim=-1)
            
            all_preds.append(preds.cpu().numpy())
            all_labels.append(labels.numpy())
    
    all_preds = np.concatenate(all_preds).flatten()
    all_labels = np.concatenate(all_labels).flatten()
    
    accuracy = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='weighted')
    
    return accuracy, f1


def main():
    # 配置
    config = {
        # 模型配置
        'd_model': 512,
        'n_heads': 32,
        'n_layers': 12,
        'dropout': 0.15,
        'seq_len': 7,
        
        # Stage 2特定配置
        'attention_heads': 4,  # 轻量级注意力头
        
        # 训练配置
        'batch_size': 16,
        'learning_rate': 3e-4,  # 较高的学习率因为从头训练
        'num_epochs': 50,      # 更多epoch因为从头训练
        'warmup_epochs': 5,
        'weight_decay': 1e-4,
        'patience': 10,
    }
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = Path(f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_independent_{timestamp}")
    exp_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(exp_dir / 'training.log'),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 Stage 2 独立训练 - 不加载Stage 1权重")
    logging.info("目标：从头训练达到85%以上准确率")
    logging.info("架构：基础模型 + 轻量级跨模态注意力")
    logging.info("="*80)
    logging.info(f"配置: {config}")
    logging.info(f"设备: {device}")
    
    # 数据集路径
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"
    
    # 文件列表
    train_files = [
        "SC4041E0.npz", "SC4042E0.npz", "SC4051E0.npz", "SC4052E0.npz",
        "SC4061E0.npz", "SC4062E0.npz", "SC4081E0.npz", "SC4082E0.npz",
        "SC4091E0.npz", "SC4092E0.npz", "SC4101E0.npz", "SC4102E0.npz",
        "SC4111E0.npz", "SC4112E0.npz", "SC4121E0.npz", "SC4122E0.npz",
        "SC4131E0.npz", "SC4141E0.npz", "SC4142E0.npz", "SC4151E0.npz",
        "SC4152E0.npz", "SC4161E0.npz", "SC4162E0.npz", "SC4171E0.npz",
        "SC4172E0.npz", "SC4181E0.npz", "SC4182E0.npz", "SC4191E0.npz", "SC4192E0.npz"
    ]
    val_files = [
        "SC4021E0.npz", "SC4022E0.npz", "SC4031E0.npz",
        "SC4032E0.npz", "SC4071E0.npz", "SC4072E0.npz",
    ]
    test_files = [
        "SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"
    ]
    
    # 完整路径
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # 加载数据集
    logging.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=True,
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")
    
    # 创建Stage 2模型（从头开始，不加载任何预训练权重）
    model = Stage2Model(config).to(device)
    
    # 统计参数
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logging.info(f"总参数: {total_params:,}")
    logging.info(f"可训练参数: {trainable_params:,}")
    
    # 损失函数
    criterion_main = SequentialFocalLoss(alpha=1, gamma=2)
    criterion_aux = SequentialFocalLoss(alpha=0.5, gamma=1.5)  # 辅助损失也使用序列损失
    criterion_temporal = TemporalConsistencyLoss(weight=0.1)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度器 - 使用余弦退火和热重启
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=10,  # 第一次重启的epoch数
        T_mult=2,  # 每次重启后周期翻倍
        eta_min=1e-6
    )
    
    # 训练循环
    best_acc = 0
    patience_counter = 0
    
    for epoch in range(1, config['num_epochs'] + 1):
        logging.info(f"\n{'='*60}")
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, criterion_main, criterion_aux, 
            criterion_temporal, optimizer, device, epoch
        )
        
        # 评估
        val_acc, val_f1 = evaluate(model, val_loader, device)
        test_acc, test_f1 = evaluate(model, test_loader, device)
        
        # 获取当前alpha值
        alpha = torch.sigmoid(model.cross_modal_attention.alpha).item()
        
        # 记录结果
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc*100:.2f}%")
        logging.info(f"验证 - Acc: {val_acc*100:.2f}%, F1: {val_f1:.4f}")
        logging.info(f"测试 - Acc: {test_acc*100:.2f}%, F1: {test_f1:.4f}")
        logging.info(f"融合权重(alpha): {alpha:.3f}")
        logging.info(f"学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_accuracy': best_acc,
                'alpha': alpha,
                'config': config
            }, exp_dir / 'best_model.pth')
            
            logging.info(f"✅ 保存最佳模型，准确率: {best_acc*100:.2f}%")
        else:
            patience_counter += 1
        
        # 检查是否达到目标
        if test_acc >= 0.85:
            logging.info(f"🎉 达到目标准确率85%！当前: {test_acc*100:.2f}%")
            if epoch >= 10:  # 至少训练10个epoch确保稳定
                break
        
        # 早停
        if patience_counter >= config['patience']:
            logging.info(f"早停：{config['patience']}个epoch没有改进")
            break
        
        # 更新学习率
        scheduler.step()
    
    # 最终结果
    logging.info(f"\n{'='*60}")
    logging.info(f"训练完成! 最佳测试准确率: {best_acc*100:.2f}%")
    
    # 保存结果摘要
    summary = {
        'stage': 'Stage 2 - Independent Training',
        'final_accuracy': float(best_acc),
        'final_alpha': float(alpha),
        'total_epochs': epoch,
        'config': config
    }
    
    with open(exp_dir / 'result_summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    logging.info(f"结果已保存到: {exp_dir}")
    
    return best_acc


if __name__ == "__main__":
    main()