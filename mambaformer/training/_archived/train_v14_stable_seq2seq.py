#!/usr/bin/env python3
"""
V14 Stable Sequence-to-Sequence 实现
解决训练不稳定问题

核心改进：
1. 更保守的学习率 (1e-4)
2. <PERSON><PERSON><PERSON>aling with Warm Restarts
3. 更强的正则化
4. Gradient accumulation
5. EMA (Exponential Moving Average) for stable evaluation
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
from copy import deepcopy

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.sequence_dataset import SequenceSleepDataset


class SequentialFeatureExtractor(nn.Module):
    """特征提取器 - 从V14 FIXED继承"""
    def __init__(self, input_channels=3, d_model=192):
        super().__init__()
        
        self.conv_layers = nn.Sequential(
            # 第一层
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6, padding=24),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=8, stride=8),
            nn.Dropout(0.3),  # 增加dropout
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=8, stride=1, padding=4),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4, stride=4),
            nn.Dropout(0.3),
            
            # 第三层
            nn.Conv1d(128, d_model, kernel_size=4, stride=1, padding=2),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
    def forward(self, x):
        # x: (batch, seq_len, time_steps, channels)
        batch_size, seq_len, time_steps, channels = x.shape
        
        # Reshape for conv processing
        x = x.view(batch_size * seq_len, channels, time_steps)
        
        # Extract features
        x = self.conv_layers(x)
        
        # Global average pooling
        x = F.adaptive_avg_pool1d(x, 1).squeeze(-1)
        
        # Reshape back
        x = x.view(batch_size, seq_len, -1)
        
        return x


class StableSeq2SeqMAMBAFORMER(nn.Module):
    """
    稳定的Sequence-to-Sequence MAMBAFORMER
    增强的正则化和稳定性
    """
    def __init__(self, input_channels=3, n_classes=5, d_model=192, 
                 n_heads=12, n_layers=4, dropout=0.2, seq_len=5):
        super().__init__()
        
        self.seq_len = seq_len
        self.d_model = d_model
        self.n_classes = n_classes
        
        # 特征提取器
        self.feature_extractor = SequentialFeatureExtractor(input_channels, d_model)
        
        # Positional encoding (learnable)
        self.pos_encoder = nn.Parameter(torch.randn(1, seq_len, d_model) * 0.01)  # 更小的初始化
        
        # Layer Normalization before transformer
        self.pre_norm = nn.LayerNorm(d_model)
        
        # Transformer encoder with more regularization
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True,
            norm_first=True  # Pre-LN for stability
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 输出头 - 为序列中每个位置生成预测
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # N1专门分支（从V14继承）
        self.n1_branch = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, n_classes)
        )
        
        # 融合门控
        self.fusion_gate = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.GELU(),
            nn.Linear(d_model // 4, 1),
            nn.Sigmoid()
        )
        
        self._init_weights()
        
    def _init_weights(self):
        """初始化权重 - 更保守的初始化"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p, gain=0.5)  # 更小的gain
    
    def forward(self, x):
        """
        输入: (batch, seq_len, time_steps, channels)
        输出: (batch, seq_len, n_classes) - 整个序列的预测！
        """
        batch_size = x.shape[0]
        
        # 特征提取
        features = self.feature_extractor(x)  # (batch, seq_len, d_model)
        
        # 添加位置编码
        features = features + self.pos_encoder
        
        # Pre-normalization
        features = self.pre_norm(features)
        
        # Transformer处理
        encoded = self.transformer(features)  # (batch, seq_len, d_model)
        
        # 为每个位置生成预测
        main_out = self.classifier(encoded)  # (batch, seq_len, n_classes)
        n1_out = self.n1_branch(encoded)  # (batch, seq_len, n_classes)
        
        # 融合主预测和N1预测
        gate = self.fusion_gate(encoded)  # (batch, seq_len, 1)
        final_out = gate * main_out + (1 - gate) * n1_out
        
        return final_out, n1_out


class StableSeq2SeqLoss(nn.Module):
    """
    稳定的序列级别损失函数
    使用Label Smoothing增加稳定性
    """
    def __init__(self, rem_weight=2.0, n1_weight=3.0, gamma=1.5, label_smoothing=0.1):
        super().__init__()
        self.rem_weight = rem_weight
        self.n1_weight = n1_weight
        self.gamma = gamma
        self.label_smoothing = label_smoothing
        
        # 类别权重 [Wake, N1, N2, N3, REM]
        self.class_weights = torch.tensor([1.5, n1_weight, 1.0, 1.0, rem_weight])
        
    def forward(self, predictions, targets):
        """
        predictions: (batch, seq_len, n_classes)
        targets: (batch, seq_len) - 每个位置都有标签
        """
        batch_size, seq_len, n_classes = predictions.shape
        
        # Reshape for loss calculation
        predictions = predictions.reshape(-1, n_classes)  # (batch*seq_len, n_classes)
        targets = targets.reshape(-1)  # (batch*seq_len)
        
        # 移动class_weights到正确的设备
        if self.class_weights.device != predictions.device:
            self.class_weights = self.class_weights.to(predictions.device)
        
        # Label smoothing cross entropy
        ce_loss = F.cross_entropy(
            predictions, targets, 
            weight=self.class_weights, 
            reduction='none',
            label_smoothing=self.label_smoothing
        )
        
        # Focal loss (more moderate)
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        # 对所有位置取平均
        return focal_loss.mean()


class TemporalConsistencyLoss(nn.Module):
    """时序一致性损失 - 鼓励相邻预测平滑"""
    def __init__(self, weight=0.1):  # 增加权重
        super().__init__()
        self.weight = weight
        
    def forward(self, predictions):
        """
        predictions: (batch, seq_len, n_classes)
        """
        if predictions.shape[1] <= 1:
            return 0.0
        
        # 计算相邻预测的KL散度
        probs = F.softmax(predictions, dim=-1)
        
        # 相邻预测的差异
        kl_div = 0
        for i in range(predictions.shape[1] - 1):
            p = probs[:, i, :]
            q = probs[:, i + 1, :]
            kl_div += F.kl_div(q.log(), p, reduction='batchmean')
        
        return self.weight * kl_div


class EMA:
    """Exponential Moving Average for model parameters"""
    def __init__(self, model, decay=0.999):
        self.model = model
        self.decay = decay
        self.shadow = {}
        self.backup = {}
        self.register()
        
    def register(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                self.shadow[name] = param.data.clone()
                
    def update(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                new_average = (1.0 - self.decay) * param.data + self.decay * self.shadow[name]
                self.shadow[name] = new_average.clone()
                
    def apply_shadow(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                self.backup[name] = param.data
                param.data = self.shadow[name]
                
    def restore(self):
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                param.data = self.backup[name]
        self.backup = {}


def train_epoch(model, train_loader, criterion, temp_loss, optimizer, device, epoch, ema=None, accumulation_steps=2):
    """训练一个epoch - 使用gradient accumulation"""
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
    optimizer.zero_grad()
    
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # target shape: (batch, seq_len) - 每个位置都有标签
        if target.dim() == 1:
            # 如果只有中心标签，复制到所有位置
            target = target.unsqueeze(1).expand(-1, model.seq_len)
        
        # 数据增强 (更温和)
        if np.random.random() < 0.2:
            noise = torch.randn_like(data) * 0.003
            data = data + noise
        
        # 前向传播 - 输出整个序列
        predictions, n1_predictions = model(data)  # (batch, seq_len, n_classes)
        
        # 计算损失 - 使用所有位置
        main_loss = criterion(predictions, target)
        n1_loss = criterion(n1_predictions, target) * 0.2  # 降低辅助损失权重
        consistency_loss = temp_loss(predictions)
        
        loss = main_loss + n1_loss + consistency_loss
        
        # L2正则化 (更强)
        l2_lambda = 5e-5
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        # Gradient accumulation
        loss = loss / accumulation_steps
        loss.backward()
        
        if (batch_idx + 1) % accumulation_steps == 0:
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), 0.5)  # 更严格的裁剪
            
            optimizer.step()
            optimizer.zero_grad()
            
            # Update EMA
            if ema is not None:
                ema.update()
        
        total_loss += loss.item() * accumulation_steps
        
        # 收集中心预测用于监控
        center_idx = model.seq_len // 2
        center_preds = predictions[:, center_idx, :].argmax(dim=1)
        center_targets = target[:, center_idx]
        
        all_preds.extend(center_preds.cpu().numpy())
        all_targets.extend(center_targets.cpu().numpy())
        
        # 更新进度条
        acc = (center_preds == center_targets).float().mean().item()
        pbar.set_postfix({'loss': f'{loss.item()*accumulation_steps:.4f}', 'acc': f'{acc:.4f}'})
    
    # 计算epoch指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1


def evaluate(model, data_loader, device, ema=None):
    """评估模型 - 使用EMA if available"""
    if ema is not None:
        ema.apply_shadow()
    
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for batch_idx, (data, target) in enumerate(tqdm(data_loader, desc='Evaluation')):
            data = data.to(device)
            
            # 获取序列预测
            predictions, _ = model(data)  # (batch, seq_len, n_classes)
            
            # 使用中心位置进行评估
            center_idx = model.seq_len // 2
            center_preds = predictions[:, center_idx, :].argmax(dim=1)
            
            if target.dim() > 1:
                center_targets = target[:, center_idx]
            else:
                center_targets = target
            
            all_preds.extend(center_preds.cpu().numpy())
            all_targets.extend(center_targets.numpy())
    
    if ema is not None:
        ema.restore()
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm


def main():
    # 配置（更保守的超参数）
    config = {
        'd_model': 192,
        'n_heads': 12,
        'n_layers': 4,
        'dropout': 0.2,  # 增加dropout
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 1e-4,  # 更低的学习率
        'weight_decay': 5e-4,  # 更强的weight decay
        'num_epochs': 60,
        'patience': 15,
        'rem_weight': 2.0,  # 更温和的类别权重
        'n1_weight': 3.0,
        'accumulation_steps': 2,
        'ema_decay': 0.999
    }
    
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f'../logs/v14_stable_seq2seq_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 Stable Sequence-to-Sequence Training")
    logging.info("="*80)
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割（与V14 FIXED相同）
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 创建数据集
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,  # 使用全部数据
        is_training=True
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,
        is_training=False
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,
        is_training=False
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'],
        shuffle=True, num_workers=4, pin_memory=True, drop_last=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = StableSeq2SeqMAMBAFORMER(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # EMA
    ema = EMA(model, decay=config['ema_decay'])
    
    # 损失函数
    criterion = StableSeq2SeqLoss(
        rem_weight=config['rem_weight'],
        n1_weight=config['n1_weight'],
        label_smoothing=0.1
    )
    temp_loss = TemporalConsistencyLoss(weight=0.1)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)  # 默认值
    )
    
    # 学习率调度 - Cosine Annealing with Warm Restarts
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    
    # 训练循环
    best_val_acc = 0
    best_test_acc = 0
    patience_counter = 0
    
    logging.info("\n" + "="*80)
    logging.info("Starting Stable Training with Sequence-to-Sequence!")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, temp_loss, optimizer, device, epoch, 
            ema=ema, accumulation_steps=config['accumulation_steps']
        )
        
        # 评估 (使用EMA)
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(
            model, val_loader, device, ema=ema
        )
        
        # 调整学习率
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            
            # 在测试集上评估
            test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(
                model, test_loader, device, ema=ema
            )
            best_test_acc = test_acc
            
            # 保存模型
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'ema_state_dict': ema.shadow,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Val: {val_acc:.4f}, Test: {test_acc:.4f})")
            logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                        f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
            
            if test_acc >= 0.90:
                logging.info("\n" + "="*80)
                logging.info("🎉 ACHIEVED 90% TEST ACCURACY WITH STABLE SEQ2SEQ!")
                logging.info("="*80)
                break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS")
    logging.info("="*80)
    logging.info(f"Best Val Accuracy: {best_val_acc:.4f}")
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    
    gap_to_90 = 0.90 - best_test_acc
    if best_test_acc >= 0.90:
        logging.info(f"✅ TARGET ACHIEVED!")
    else:
        logging.info(f"Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
    
    # 保存结果
    results = {
        'best_val_acc': float(best_val_acc),
        'best_test_acc': float(best_test_acc),
        'config': config
    }
    
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\nResults saved to {log_dir}")


if __name__ == '__main__':
    main()