#!/usr/bin/env python3
"""
监控所有阶段训练进度
"""
import os
import re
import time
import glob
from datetime import datetime

def check_stage_status(stage_num):
    """检查特定阶段的状态"""
    log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/"
    
    # 查找该阶段的最新日志
    pattern = f"{log_dir}stage{stage_num}_*/training.log"
    log_files = glob.glob(pattern)
    
    if not log_files:
        return {
            'status': 'NOT_STARTED',
            'message': f'Stage {stage_num} 未开始'
        }
    
    # 获取最新的日志文件
    latest_log = sorted(log_files)[-1]
    
    try:
        with open(latest_log, 'r') as f:
            lines = f.readlines()
        
        # 查找最新状态
        current_epoch = 0
        train_acc = 0
        val_acc = 0
        test_acc = 0
        completed = False
        
        for line in reversed(lines[-100:]):
            if "达到目标" in line:
                match = re.search(r"测试准确率: ([\d.]+)%", line)
                if match:
                    completed = True
                    test_acc = float(match.group(1))
                    
            if "Epoch " in line and "/" in line:
                match = re.search(r"Epoch (\d+)/(\d+)", line)
                if match:
                    current_epoch = int(match.group(1))
                    
            if "训练 - " in line and "Acc:" in line:
                match = re.search(r"Acc: ([\d.]+)%", line)
                if match:
                    train_acc = float(match.group(1))
                    
            if "验证 - " in line and "Acc:" in line:
                match = re.search(r"Acc: ([\d.]+)%", line)
                if match:
                    val_acc = float(match.group(1))
                    
            if "测试 - " in line and "Acc:" in line:
                match = re.search(r"Acc: ([\d.]+)%", line)
                if match:
                    test_acc = float(match.group(1))
        
        if completed:
            return {
                'status': 'COMPLETED',
                'message': f'Stage {stage_num} ✅ 完成 (测试: {test_acc:.2f}%)',
                'test_acc': test_acc
            }
        elif current_epoch > 0:
            return {
                'status': 'TRAINING',
                'message': f'Stage {stage_num} 🔄 训练中 (Epoch {current_epoch}, 训练: {train_acc:.1f}%, 验证: {val_acc:.1f}%)',
                'epoch': current_epoch,
                'train_acc': train_acc,
                'val_acc': val_acc
            }
        else:
            return {
                'status': 'INITIALIZING',
                'message': f'Stage {stage_num} 🔵 初始化中'
            }
            
    except Exception as e:
        return {
            'status': 'ERROR',
            'message': f'Stage {stage_num} ❌ 错误: {str(e)}'
        }

def main():
    print("="*80)
    print("📊 渐进式融合策略 - 全阶段训练监控")
    print("="*80)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("目标: ICASSP 2026论文 - 创新型睡眠分期模型")
    print("-"*80)
    
    while True:
        os.system('clear')
        print("="*80)
        print("📊 渐进式融合策略 - 全阶段训练监控")
        print("="*80)
        print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-"*80)
        
        # 定义各阶段
        stages_info = {
            1: "基线模型（ProgressiveMAMBAFORMER v1）",
            2: "跨模态注意力增强",
            3: "自适应门控融合",
            4: "Mamba状态空间建模",
            5: "完整渐进式融合模型"
        }
        
        all_completed = True
        results = []
        
        for stage_num, description in stages_info.items():
            status = check_stage_status(stage_num)
            
            if status['status'] != 'COMPLETED':
                all_completed = False
            
            print(f"\n📌 Stage {stage_num}: {description}")
            print(f"   状态: {status['message']}")
            
            if status['status'] == 'COMPLETED':
                results.append(f"Stage {stage_num}: {status.get('test_acc', 0):.2f}%")
        
        # 显示进度条
        completed_stages = sum(1 for i in range(1, 6) if check_stage_status(i)['status'] == 'COMPLETED')
        progress = completed_stages / 5 * 100
        bar_length = 50
        filled = int(bar_length * progress / 100)
        bar = '█' * filled + '░' * (bar_length - filled)
        
        print("\n" + "-"*80)
        print(f"总进度: [{bar}] {progress:.0f}% ({completed_stages}/5 阶段)")
        
        if results:
            print("\n已完成阶段结果:")
            for result in results:
                print(f"  • {result}")
        
        if all_completed:
            print("\n" + "="*80)
            print("🎉🎉🎉 所有阶段训练完成！")
            print("准备生成最终实验报告...")
            break
        
        print("\n按Ctrl+C退出监控")
        time.sleep(30)  # 每30秒更新一次

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n监控已停止")