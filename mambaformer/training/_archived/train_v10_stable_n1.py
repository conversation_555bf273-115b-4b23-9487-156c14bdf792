#!/usr/bin/env python3
"""
V10 STABLE N1 EXPERT - 稳定的N1专家模型
基于V8 Proper Eval的稳定架构 + V9的N1检测分支思想
目标：稳定训练，突破90%
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v10_stable_n1_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 V10 STABLE N1 EXPERT - BALANCED APPROACH")
    logging.info("="*80)
    
    return log_dir

class StableN1Model(nn.Module):
    """稳定的N1专家模型 - 基于V8架构 + N1检测分支"""
    def __init__(self, input_channels, n_classes, d_model, n_heads, n_layers, dropout, seq_len):
        super().__init__()
        
        # 主干网络 - V8的稳定架构
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # 辅助头 - 来自V8
        self.auxiliary_head = nn.Sequential(
            nn.Linear(n_classes, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, n_classes)
        )
        
        # N1专门检测分支 - 改进自V9
        self.n1_detector = nn.Sequential(
            nn.Linear(n_classes, d_model // 2),
            nn.BatchNorm1d(d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model // 4),
            nn.BatchNorm1d(d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 4, 2)  # 二分类：N1 vs 非N1
        )
        
        # N3/N2边界检测 - 解决深睡眠混淆
        self.deep_sleep_detector = nn.Sequential(
            nn.Linear(n_classes, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, 2)  # 二分类：N3 vs 其他
        )
        
        # 融合层 - 更简单的设计
        self.fusion_weight = nn.Parameter(torch.tensor([0.6, 0.2, 0.1, 0.1]))  # main, aux, n1, deep
        
    def forward(self, x):
        # 主干输出
        main_out, _ = self.backbone(x)
        
        # 提取中心时间步
        if main_out.dim() == 3:
            center_out = main_out[:, main_out.shape[1]//2, :]
        else:
            center_out = main_out
        
        # 辅助输出
        aux_out = self.auxiliary_head(center_out)
        
        # N1检测
        n1_detection = self.n1_detector(center_out)
        n1_prob = F.softmax(n1_detection, dim=-1)[:, 1]  # N1概率
        
        # 深睡眠检测
        deep_detection = self.deep_sleep_detector(center_out)
        deep_prob = F.softmax(deep_detection, dim=-1)[:, 1]  # N3概率
        
        # 融合输出 - 加权平均
        weights = F.softmax(self.fusion_weight, dim=0)
        
        # 基础融合
        final_out = weights[0] * center_out + weights[1] * aux_out
        
        # N1增强 - 如果N1检测器认为是N1，增强N1分数
        n1_boost = n1_prob * weights[2] * 3.0  # 温和的增强
        final_out[:, 1] = final_out[:, 1] + n1_boost
        
        # N3增强 - 如果深睡眠检测器认为是N3，增强N3分数
        n3_boost = deep_prob * weights[3] * 2.0
        final_out[:, 3] = final_out[:, 3] + n3_boost
        
        return final_out, aux_out, n1_detection, deep_detection

class StableN1Loss(nn.Module):
    """稳定的损失函数 - 温和递增N1权重"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 初始权重 - 相对温和
        self.base_weights = torch.tensor([2.5, 8.0, 1.0, 1.2, 2.0]).to(device)
        self.gamma = 2.0  # Focal loss gamma
        self.label_smoothing = 0.1
        
    def forward(self, final_out, aux_out, n1_detection, deep_detection, targets, epoch=0):
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        # 动态权重 - 温和递增
        weights = self.base_weights.clone()
        if epoch > 10:
            # N1权重温和递增
            weights[1] = min(15.0, weights[1] + (epoch - 10) * 0.2)
        if epoch > 20:
            # 后期进一步增强
            weights[1] = min(20.0, weights[1] + (epoch - 20) * 0.1)
        
        # 主损失 - Focal Loss
        ce_loss = F.cross_entropy(final_out, targets, weight=weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = ((1 - pt) ** self.gamma) * ce_loss
        
        # 辅助损失
        aux_loss = F.cross_entropy(aux_out, targets, weight=weights)
        
        # N1检测损失
        n1_targets = (targets == 1).long()
        n1_weights = torch.tensor([1.0, 5.0]).to(self.device)  # 温和的N1权重
        n1_loss = F.cross_entropy(n1_detection, n1_targets, weight=n1_weights)
        
        # 深睡眠检测损失
        deep_targets = (targets == 3).long()
        deep_loss = F.cross_entropy(deep_detection, deep_targets)
        
        # N1错误惩罚 - 温和
        n1_mask = (targets == 1).float()
        n1_pred_wrong = n1_mask * (final_out.argmax(dim=1) != 1).float()
        n1_penalty = n1_pred_wrong.mean() * 2.0  # 温和惩罚
        
        # N3→N2混淆惩罚
        n3_mask = (targets == 3).float()
        n3_to_n2 = n3_mask * (final_out.argmax(dim=1) == 2).float()
        n3_penalty = n3_to_n2.mean() * 1.0
        
        # 总损失 - 平衡的权重
        total_loss = (
            focal_loss.mean() +
            0.2 * aux_loss +
            0.3 * n1_loss +  # N1损失重要
            0.1 * deep_loss +
            0.1 * n1_penalty +
            0.05 * n3_penalty
        )
        
        # 标签平滑
        if self.label_smoothing > 0 and self.training:
            n_classes = final_out.size(-1)
            smooth_loss = -F.log_softmax(final_out, dim=-1).mean(dim=-1)
            total_loss = (1 - self.label_smoothing) * total_loss + self.label_smoothing * smooth_loss.mean()
        
        return total_loss

def balanced_augment(x, targets=None, epoch=0):
    """平衡的数据增强"""
    # 温和的增强概率
    if random.random() > 0.5:
        return x
    
    # 对N1样本特殊处理
    if targets is not None:
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        n1_mask = (targets == 1)
        
        # N1样本增强
        for idx in torch.where(n1_mask)[0]:
            if random.random() < 0.6:  # 60%概率
                # 轻微噪声
                x[idx] = x[idx] + torch.randn_like(x[idx]) * 0.015
                
                # 幅度调整
                x[idx] = x[idx] * (0.85 + random.random() * 0.3)
    
    # 一般增强
    if random.random() < 0.3:
        x = x + torch.randn_like(x) * 0.008
    
    if random.random() < 0.2:
        shift = random.randint(-30, 30)
        x = torch.roll(x, shifts=shift, dims=-1)
    
    return x

def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    model.train()
    criterion.training = True
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # 平衡增强
        data = balanced_augment(data, target, epoch)
        
        optimizer.zero_grad()
        
        # 前向传播
        final_out, aux_out, n1_detection, deep_detection = model(data)
        
        # 计算损失
        loss = criterion(final_out, aux_out, n1_detection, deep_detection, target, epoch)
        
        # L2正则化 - 轻微
        l2_lambda = 5e-5
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        preds = final_out.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
        
        pbar.set_postfix({'loss': f'{loss.item():.4f}'})
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate_with_tta(model, data_loader, device, n_tta=5):
    """评估函数 - 带TTA"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            predictions = []
            for i in range(n_tta):
                if i == 0:
                    data_aug = data
                elif i == 1:
                    data_aug = data + torch.randn_like(data) * 0.002
                elif i == 2:
                    shift = random.randint(-20, 20)
                    data_aug = torch.roll(data, shifts=shift, dims=-1)
                elif i == 3:
                    data_aug = data * (0.97 + random.random() * 0.06)
                else:
                    data_aug = data + torch.randn_like(data) * 0.003
                
                final_out, aux_out, n1_detection, deep_detection = model(data_aug)
                
                # 组合输出
                combined = 0.7 * F.softmax(final_out, dim=-1) + 0.3 * F.softmax(aux_out, dim=-1)
                
                # N1概率增强
                n1_prob = F.softmax(n1_detection, dim=-1)[:, 1]
                combined[:, 1] = combined[:, 1] * (1 + n1_prob * 0.2)
                
                # N3概率增强
                deep_prob = F.softmax(deep_detection, dim=-1)[:, 1]
                combined[:, 3] = combined[:, 3] * (1 + deep_prob * 0.1)
                
                predictions.append(combined)
            
            avg_probs = torch.stack(predictions).mean(dim=0)
            preds = avg_probs.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    # 配置 - 基于V8的稳定配置
    config = {
        'd_model': 256,  # V8的大小
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.2,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 2e-4,
        'weight_decay': 1e-4,
        'num_epochs': 100,
        'patience': 20
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    logging.info("Strategy: Stable V8 architecture + N1 detection branch")
    logging.info("Focus: Balanced approach for N1 and deep sleep confusion")
    logging.info("Current best: 84.44% (V8) | Target: 90.00%")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割 - 使用V8的分割
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None  # 不截断
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'],
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = StableN1Model(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = StableN1Loss(device)
    optimizer = optim.AdamW(model.parameters(),
                           lr=config['learning_rate'],
                           weight_decay=config['weight_decay'])
    
    # 学习率调度 - 使用V8的策略
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5, min_lr=1e-7
    )
    
    # 训练
    best_val_acc = 0
    best_val_metrics = {}
    patience_counter = 0
    
    logging.info("STARTING STABLE N1 EXPERT TRAINING!")
    logging.info("Model selection based on VALIDATION SET only")
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch
        )
        
        # 验证集评估
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate_with_tta(
            model, val_loader, device, n_tta=3
        )
        
        # 学习率调度
        scheduler.step(val_acc)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_val_metrics = {
                'epoch': epoch + 1,
                'accuracy': val_acc,
                'f1': val_f1,
                'kappa': val_kappa,
                'class_f1': val_class_f1.tolist(),
                'confusion_matrix': val_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_metrics': best_val_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Val Acc: {val_acc:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 加载最佳模型进行测试
    logging.info("\n" + "="*80)
    logging.info("Loading best model for final test evaluation...")
    checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 测试集评估（只执行一次）
    logging.info("Final evaluation on TEST SET (with 5x TTA)...")
    test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate_with_tta(
        model, test_loader, device, n_tta=5
    )
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS - V10 STABLE N1 EXPERT")
    logging.info("="*80)
    logging.info(f"Best Validation Accuracy: {best_val_acc:.4f}")
    logging.info("-"*40)
    logging.info("FINAL TEST RESULTS:")
    logging.info(f"Test Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    logging.info(f"Test F1: {test_f1:.4f}")
    logging.info(f"Test Kappa: {test_kappa:.4f}")
    logging.info(f"Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
    
    # 混淆矩阵
    logging.info("\n🔄 Confusion Matrix:")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logging.info("       " + "  ".join([f"{name:>6}" for name in class_names]))
    for i, true_class in enumerate(class_names):
        row = test_cm[i]
        row_str = " ".join([f"{val:>6}" for val in row])
        logging.info(f"{true_class:>6} {row_str}")
    
    # 详细分析
    logging.info("\n📊 Detailed Analysis:")
    
    # N1分析
    n1_f1 = test_class_f1[1]
    logging.info(f"  N1 F1 Score: {n1_f1:.3f} (Target: >0.6)")
    if n1_f1 > 0.6:
        logging.info(f"  ✅ N1 target achieved!")
    
    # N3→N2分析
    n3_to_n2 = test_cm[3, 2]
    logging.info(f"  N3→N2 errors: {n3_to_n2} (Target: <200)")
    
    # 与目标对比
    gap_to_90 = 0.90 - test_acc
    if test_acc >= 0.90:
        logging.info(f"\n✅ TARGET ACHIEVED! Accuracy: {test_acc:.4f}")
        logging.info("\n🎉🎉🎉 V10 SUCCESS! ACHIEVED 90% TARGET! 🎉🎉🎉")
    else:
        logging.info(f"\nGap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
    
    # 保存结果
    final_results = {
        'best_val_metrics': best_val_metrics,
        'test_metrics': {
            'accuracy': test_acc,
            'f1': test_f1,
            'kappa': test_kappa,
            'class_f1': test_class_f1.tolist(),
            'confusion_matrix': test_cm.tolist()
        },
        'config': config
    }
    
    with open(os.path.join(log_dir, 'final_results.json'), 'w') as f:
        json.dump(final_results, f, indent=2)
    
    logging.info(f"\nResults saved to {log_dir}")

if __name__ == "__main__":
    main()