#!/usr/bin/env python3
"""
监控V14 Seq2Seq训练进度
"""

import os
import time
import glob

def monitor_training():
    """监控最新的seq2seq训练"""
    
    # 找到最新的日志文件
    log_pattern = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_seq2seq_*/training.log'
    log_files = glob.glob(log_pattern)
    
    if not log_files:
        print("没有找到seq2seq训练日志")
        return
    
    latest_log = max(log_files, key=os.path.getctime)
    print(f"监控日志: {latest_log}")
    print("="*80)
    
    # 持续监控
    last_size = 0
    best_val_acc = 0
    best_test_acc = 0
    
    while True:
        try:
            with open(latest_log, 'r') as f:
                f.seek(last_size)
                new_lines = f.readlines()
                last_size = f.tell()
                
                for line in new_lines:
                    # 打印重要信息
                    if any(keyword in line for keyword in ['Epoch', 'Val', 'Test', 'ACHIEVED', 'Best', 'saved']):
                        print(line.strip())
                        
                        # 提取指标
                        if 'Val (Sliding):' in line:
                            try:
                                acc_str = line.split('Acc=')[1].split(',')[0]
                                val_acc = float(acc_str)
                                if val_acc > best_val_acc:
                                    best_val_acc = val_acc
                                    print(f"  🎯 New Best Val: {best_val_acc:.4f}")
                            except:
                                pass
                        
                        if 'Test SW Acc:' in line or 'Test (Sliding):' in line:
                            try:
                                if 'Test SW Acc:' in line:
                                    acc_str = line.split('Test SW Acc:')[1].split(')')[0]
                                else:
                                    acc_str = line.split('Acc=')[1].split(',')[0]
                                test_acc = float(acc_str)
                                if test_acc > best_test_acc:
                                    best_test_acc = test_acc
                                    print(f"  🎯 New Best Test: {best_test_acc:.4f}")
                                    
                                    if test_acc >= 0.90:
                                        print("\n" + "="*80)
                                        print("🎉🎉🎉 ACHIEVED 90% ACCURACY! 🎉🎉🎉")
                                        print("="*80)
                                        return
                            except:
                                pass
                        
                        if 'REM=' in line and 'Class F1' in line:
                            try:
                                rem_f1 = float(line.split('REM=')[1].split()[0])
                                if rem_f1 < 0.1:
                                    print(f"  ⚠️ WARNING: REM F1 = {rem_f1:.3f}")
                            except:
                                pass
            
            # 每10秒检查一次
            time.sleep(10)
            
        except KeyboardInterrupt:
            print("\n监控停止")
            break
        except Exception as e:
            print(f"错误: {e}")
            time.sleep(5)
    
    print(f"\n最终结果:")
    print(f"  Best Val Acc: {best_val_acc:.4f}")
    print(f"  Best Test Acc: {best_test_acc:.4f}")
    if best_test_acc >= 0.90:
        print("  ✅ 达到90%目标!")
    else:
        print(f"  距离90%还差: {0.90 - best_test_acc:.4f}")


if __name__ == '__main__':
    monitor_training()