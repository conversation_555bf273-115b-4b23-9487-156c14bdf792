#!/usr/bin/env python3
"""
Stage 3: 跨模态交互
- 独立训练，不加载任何前期权重
- EEG中心的Cross-Attention机制
- 自适应门控融合
- 目标: ≥85% accuracy
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

import numpy as np
import os
import glob
import logging
from datetime import datetime
from pathlib import Path
import json
from tqdm import tqdm
import math

# 导入必要的模块
import sys
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')
from utils.sequence_dataset import SequenceSleepDataset
from models.sequential_mambaformer_v2 import PositionalEncoding
from utils.train_utils import EarlyStopping

# ===================== Stage 3 模型组件 =====================

class EEGCentricCrossAttention(nn.Module):
    """EEG中心的跨模态注意力"""
    def __init__(self, d_model=128, n_heads=4, dropout=0.15):
        super().__init__()
        self.d_model = d_model
        self.n_heads = n_heads
        
        # EEG→EOG查询
        self.eeg_to_eog_attention = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=n_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # EEG→EMG查询
        self.eeg_to_emg_attention = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=n_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # Layer norms
        self.norm_eog = nn.LayerNorm(d_model)
        self.norm_emg = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, eeg_feat, eog_feat, emg_feat):
        """
        Args:
            eeg_feat: (batch, seq_len, d_model) - EEG特征作为Query
            eog_feat: (batch, seq_len, d_model) - EOG特征作为Key/Value
            emg_feat: (batch, seq_len, d_model) - EMG特征作为Key/Value
        Returns:
            eog_enhanced: (batch, seq_len, d_model)
            emg_enhanced: (batch, seq_len, d_model)
        """
        # EEG查询EOG
        eog_enhanced, _ = self.eeg_to_eog_attention(
            query=eeg_feat,
            key=eog_feat,
            value=eog_feat
        )
        eog_enhanced = self.dropout(eog_enhanced)
        eog_enhanced = self.norm_eog(eog_enhanced + eog_feat)  # 残差连接
        
        # EEG查询EMG
        emg_enhanced, _ = self.eeg_to_emg_attention(
            query=eeg_feat,
            key=emg_feat,
            value=emg_feat
        )
        emg_enhanced = self.dropout(emg_enhanced)
        emg_enhanced = self.norm_emg(emg_enhanced + emg_feat)  # 残差连接
        
        return eog_enhanced, emg_enhanced


class AdaptiveGatedFusion(nn.Module):
    """自适应门控融合"""
    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()
        
        # 门控网络
        self.gate_network = nn.Sequential(
            nn.Linear(d_model * 3, d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, 3),  # 3个模态的权重
            nn.Softmax(dim=-1)
        )
        
        # 融合投影
        self.fusion_proj = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
    def forward(self, eeg_feat, eog_feat, emg_feat):
        """
        Args:
            eeg_feat: (batch, d_model)
            eog_feat: (batch, d_model)
            emg_feat: (batch, d_model)
        Returns:
            fused: (batch, d_model)
            weights: (batch, 3)
        """
        # 拼接特征
        concat_feat = torch.cat([eeg_feat, eog_feat, emg_feat], dim=-1)
        
        # 计算门控权重
        weights = self.gate_network(concat_feat)  # (batch, 3)
        
        # 加权融合
        weighted_sum = (weights[:, 0:1] * eeg_feat + 
                       weights[:, 1:2] * eog_feat + 
                       weights[:, 2:3] * emg_feat)
        
        # 投影
        fused = self.fusion_proj(weighted_sum)
        
        return fused, weights


class CrossModalFeatureExtractor(nn.Module):
    """跨模态特征提取器"""
    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()
        
        # EEG特征提取器 (2通道)
        self.eeg_extractor = nn.Sequential(
            nn.Conv1d(2, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(128, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # EOG特征提取器 (1通道)
        self.eog_extractor = nn.Sequential(
            nn.Conv1d(1, 32, kernel_size=50, stride=6),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(32, 64, kernel_size=8, stride=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(64, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # EMG特征提取器 (1通道)
        self.emg_extractor = nn.Sequential(
            nn.Conv1d(1, 32, kernel_size=50, stride=6),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            nn.Conv1d(32, 64, kernel_size=8, stride=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            nn.Conv1d(64, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # 时间维度池化
        self.temporal_pool = nn.AdaptiveAvgPool1d(1)
        
        # 跨模态注意力
        self.cross_attention = EEGCentricCrossAttention(d_model, n_heads=4, dropout=dropout)
        
        # 自适应门控融合
        self.gated_fusion = AdaptiveGatedFusion(d_model, dropout)
        
        # 可学习的融合权重 (控制新特征的影响)
        self.alpha = nn.Parameter(torch.tensor(0.2))  # 初始20%新特征
        
    def forward(self, x):
        """
        Args:
            x: (batch, time_steps, channels)
        Returns:
            fused_features: (batch, d_model)
            modal_features: dict
        """
        # 调整维度
        x = x.transpose(1, 2)  # (batch, channels, time_steps)
        
        # 分离模态
        eeg = x[:, :2, :]  # EEG: 前2个通道
        eog = x[:, 2:3, :]  # EOG: 第3个通道
        emg = x[:, 3:4, :]  # EMG: 第4个通道
        
        # 独立提取特征
        eeg_feat = self.eeg_extractor(eeg)  # (batch, d_model, time')
        eog_feat = self.eog_extractor(eog)
        emg_feat = self.emg_extractor(emg)
        
        # 为跨模态注意力准备序列特征
        eeg_seq = eeg_feat.transpose(1, 2)  # (batch, time', d_model)
        eog_seq = eog_feat.transpose(1, 2)
        emg_seq = emg_feat.transpose(1, 2)
        
        # 跨模态注意力增强
        eog_enhanced, emg_enhanced = self.cross_attention(eeg_seq, eog_seq, emg_seq)
        
        # 时间池化得到全局特征
        eeg_global = self.temporal_pool(eeg_feat).squeeze(-1)
        eog_global = self.temporal_pool(eog_enhanced.transpose(1, 2)).squeeze(-1)
        emg_global = self.temporal_pool(emg_enhanced.transpose(1, 2)).squeeze(-1)
        
        # 自适应门控融合
        fused_cross, gate_weights = self.gated_fusion(eeg_global, eog_global, emg_global)
        
        # 控制新特征的影响
        alpha = torch.sigmoid(self.alpha)
        final_fused = (1 - alpha) * eeg_global + alpha * fused_cross
        
        return final_fused, {
            'eeg': eeg_global,
            'eog': eog_global, 
            'emg': emg_global,
            'gate_weights': gate_weights,
            'alpha': alpha.item()
        }


class Stage3Model(nn.Module):
    """Stage 3模型 - 跨模态交互"""
    def __init__(self, n_classes=5, d_model=128, n_heads=8, n_layers=4, 
                 dropout=0.15, seq_len=5):
        super().__init__()
        
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 跨模态特征提取器
        self.feature_extractor = CrossModalFeatureExtractor(d_model, dropout)
        
        # 时序位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=n_layers
        )
        
        # 主分类器
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 模态特定的辅助分类器
        self.eeg_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, n_classes)
        )
        
        self.eog_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, n_classes)
        )
        
        self.emg_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, n_classes)
        )
        
        # 初始化权重
        self._init_weights()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建Stage3Model: 参数量={total_params:,}, d_model={d_model}")
        
    def _init_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, time_steps, channels)
        Returns:
            main_output: (batch, seq_len, n_classes)
            aux_outputs: dict
        """
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 处理每个时间步
        all_features = []
        all_modal_features = {
            'eeg': [],
            'eog': [],
            'emg': [],
            'gate_weights': [],
            'alpha': []
        }
        
        for t in range(seq_len):
            # 提取当前时间步的特征
            x_t = x[:, t, :, :]  # (batch, time_steps, channels)
            fused_feat, modal_dict = self.feature_extractor(x_t)
            
            all_features.append(fused_feat)
            for key in ['eeg', 'eog', 'emg']:
                all_modal_features[key].append(modal_dict[key])
            all_modal_features['gate_weights'].append(modal_dict['gate_weights'])
            all_modal_features['alpha'].append(modal_dict['alpha'])
        
        # Stack成序列
        features = torch.stack(all_features, dim=1)  # (batch, seq_len, d_model)
        eeg_features = torch.stack(all_modal_features['eeg'], dim=1)
        eog_features = torch.stack(all_modal_features['eog'], dim=1)
        emg_features = torch.stack(all_modal_features['emg'], dim=1)
        
        # 位置编码
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # (batch, seq_len, d_model)
        
        # Transformer编码
        encoded = self.transformer_encoder(features)
        
        # 分类
        main_output = self.classifier(encoded)
        eeg_output = self.eeg_classifier(eeg_features)
        eog_output = self.eog_classifier(eog_features)
        emg_output = self.emg_classifier(emg_features)
        
        # 平均alpha和gate_weights
        avg_alpha = np.mean(all_modal_features['alpha'])
        avg_gate_weights = torch.stack(all_modal_features['gate_weights']).mean(0)
        
        return main_output, {
            'eeg': eeg_output,
            'eog': eog_output,
            'emg': emg_output,
            'gate_weights': avg_gate_weights,
            'alpha': avg_alpha
        }


# ===================== 损失函数 =====================

class Stage3Loss(nn.Module):
    """Stage 3的损失函数 - 多任务学习with跨模态正则化"""
    def __init__(self, aux_weight=0.15, consistency_weight=0.05):
        super().__init__()
        self.aux_weight = aux_weight  # 辅助任务权重
        self.consistency_weight = consistency_weight  # 一致性正则化权重
        
    def forward(self, outputs, labels):
        """
        Args:
            outputs: (main_output, aux_dict)
            labels: (batch, seq_len)
        """
        main_output, aux_dict = outputs
        
        # 主任务损失
        main_loss = F.cross_entropy(
            main_output.reshape(-1, main_output.size(-1)),
            labels.reshape(-1)
        )
        
        # 辅助任务损失
        eeg_loss = F.cross_entropy(
            aux_dict['eeg'].reshape(-1, aux_dict['eeg'].size(-1)),
            labels.reshape(-1)
        )
        
        eog_loss = F.cross_entropy(
            aux_dict['eog'].reshape(-1, aux_dict['eog'].size(-1)),
            labels.reshape(-1)
        )
        
        emg_loss = F.cross_entropy(
            aux_dict['emg'].reshape(-1, aux_dict['emg'].size(-1)),
            labels.reshape(-1)
        )
        
        aux_loss = (eeg_loss + eog_loss + emg_loss) / 3
        
        # 一致性正则化 (鼓励不同模态预测一致)
        consistency_loss = 0
        for modal1, modal2 in [('eeg', 'eog'), ('eeg', 'emg'), ('eog', 'emg')]:
            pred1 = F.softmax(aux_dict[modal1], dim=-1)
            pred2 = F.softmax(aux_dict[modal2], dim=-1)
            consistency_loss += F.kl_div(
                pred1.log(), pred2, reduction='batchmean'
            )
        consistency_loss /= 3
        
        # 组合损失
        total_loss = (main_loss + 
                     self.aux_weight * aux_loss + 
                     self.consistency_weight * consistency_loss)
        
        return total_loss, {
            'main': main_loss.item(),
            'eeg': eeg_loss.item(),
            'eog': eog_loss.item(),
            'emg': emg_loss.item(),
            'consistency': consistency_loss.item() if isinstance(consistency_loss, torch.Tensor) else consistency_loss,
            'total': total_loss.item()
        }


# ===================== 训练函数 =====================

def train_epoch(model, dataloader, criterion, optimizer, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    loss_components = {'main': 0, 'eeg': 0, 'eog': 0, 'emg': 0, 'consistency': 0}
    alpha_sum = 0
    gate_weights_sum = torch.zeros(3)  # Keep on CPU for accumulation
    
    with tqdm(dataloader, desc="Training") as pbar:
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(data)
            loss, loss_dict = criterion(outputs, labels)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            # 统计
            total_loss += loss.item()
            main_output = outputs[0]
            _, predicted = main_output.max(-1)
            correct += predicted.eq(labels).sum().item()
            total += labels.numel()
            
            # 累积损失分量
            for key in loss_components:
                loss_components[key] += loss_dict[key]
            
            # 累积alpha和gate_weights
            alpha_sum += outputs[1]['alpha']
            gate_weights_sum = gate_weights_sum + outputs[1]['gate_weights'].mean(0).detach().cpu()
            
            # 更新进度条
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{100.*correct/total:.2f}%',
                'alpha': f'{outputs[1]["alpha"]:.3f}'
            })
    
    # 平均值
    num_batches = len(dataloader)
    for key in loss_components:
        loss_components[key] /= num_batches
    avg_alpha = alpha_sum / num_batches
    avg_gate_weights = gate_weights_sum / num_batches
    
    return total_loss / num_batches, 100. * correct / total, loss_components, avg_alpha, avg_gate_weights


def evaluate(model, dataloader, criterion, device):
    """评估函数"""
    model.eval()
    total_loss = 0
    correct = 0
    total = 0
    all_preds = []
    all_labels = []
    alpha_sum = 0
    gate_weights_sum = torch.zeros(3)  # Keep on CPU for accumulation
    
    with torch.no_grad():
        for data, labels in tqdm(dataloader, desc="Evaluating"):
            data = data.to(device)
            labels = labels.to(device)
            
            outputs = model(data)
            loss, _ = criterion(outputs, labels)
            
            total_loss += loss.item()
            main_output = outputs[0]
            _, predicted = main_output.max(-1)
            
            correct += predicted.eq(labels).sum().item()
            total += labels.numel()
            
            all_preds.extend(predicted.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
            
            alpha_sum += outputs[1]['alpha']
            gate_weights_sum = gate_weights_sum + outputs[1]['gate_weights'].mean(0).detach().cpu()
    
    accuracy = 100. * correct / total
    avg_alpha = alpha_sum / len(dataloader)
    avg_gate_weights = gate_weights_sum / len(dataloader)
    
    # 计算F1分数
    from sklearn.metrics import f1_score, confusion_matrix
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    # 计算混淆矩阵
    cm = confusion_matrix(all_labels, all_preds)
    
    return total_loss / len(dataloader), accuracy, f1, avg_alpha, avg_gate_weights, cm


def main():
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage3_crossmodal_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f"{log_dir}/training.log"),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 Stage 3: 跨模态交互 - 独立训练")
    logging.info("目标: ≥85% accuracy")
    logging.info("策略: EEG中心的Cross-Attention + 自适应门控融合")
    logging.info("="*80)
    
    # 配置
    config = {
        "n_classes": 5,
        "d_model": 128,
        "n_heads": 8,
        "n_layers": 4,
        "dropout": 0.15,
        "seq_len": 5,
        "batch_size": 32,
        "learning_rate": 1e-3,
        "num_epochs": 100,
        "weight_decay": 1e-4,
        "patience": 15,
        "aux_weight": 0.15,
        "consistency_weight": 0.05
    }
    
    logging.info(f"配置: {config}")
    
    # 设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"设备: {device}")
    
    # 数据路径
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 数据划分
    np.random.seed(42)
    np.random.shuffle(all_files)
    
    n_files = len(all_files)
    train_split = int(0.7 * n_files)
    val_split = int(0.85 * n_files)
    
    train_files = all_files[:train_split]
    val_files = all_files[train_split:val_split]
    test_files = all_files[val_split:]
    
    logging.info("加载数据集...")
    
    # 创建数据集
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        use_channels=4,  # 使用全部4个通道
        max_samples_per_file=None,
        is_training=True,
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        use_channels=4,
        max_samples_per_file=None,
        is_training=False,
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        use_channels=4,
        max_samples_per_file=None,
        is_training=False,
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")
    
    # 创建模型 - 独立训练，不加载任何权重
    model = Stage3Model(
        n_classes=config["n_classes"],
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"]
    ).to(device)
    
    # 损失函数
    criterion = Stage3Loss(
        aux_weight=config["aux_weight"],
        consistency_weight=config["consistency_weight"]
    )
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"]
    )
    
    # 学习率调度器
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=10,
        T_mult=2,
        eta_min=1e-5
    )
    
    # Early stopping
    early_stopping = EarlyStopping(patience=config["patience"], verbose=True)
    
    # 训练
    best_test_acc = 0
    best_epoch = 0
    
    for epoch in range(1, config["num_epochs"] + 1):
        logging.info("\n" + "="*60)
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        
        # 训练
        train_loss, train_acc, loss_components, train_alpha, train_gates = train_epoch(
            model, train_loader, criterion, optimizer, device
        )
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        logging.info(f"  损失分量 - Main: {loss_components['main']:.4f}, "
                    f"EEG: {loss_components['eeg']:.4f}, "
                    f"EOG: {loss_components['eog']:.4f}, "
                    f"EMG: {loss_components['emg']:.4f}, "
                    f"Consistency: {loss_components['consistency']:.4f}")
        logging.info(f"  Alpha值: {train_alpha:.4f}")
        logging.info(f"  门控权重 - EEG: {train_gates[0]:.3f}, "
                    f"EOG: {train_gates[1]:.3f}, EMG: {train_gates[2]:.3f}")
        
        # 验证
        val_loss, val_acc, val_f1, val_alpha, val_gates, _ = evaluate(
            model, val_loader, criterion, device
        )
        logging.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_f1:.4f}")
        
        # 测试
        test_loss, test_acc, test_f1, test_alpha, test_gates, test_cm = evaluate(
            model, test_loader, criterion, device
        )
        logging.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_f1:.4f}")
        logging.info(f"  Alpha值: {test_alpha:.4f}")
        logging.info(f"  门控权重 - EEG: {test_gates[0]:.3f}, "
                    f"EOG: {test_gates[1]:.3f}, EMG: {test_gates[2]:.3f}")
        
        # 学习率
        logging.info(f"学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_epoch = epoch
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'test_f1': test_f1,
                'confusion_matrix': test_cm,
                'config': config
            }, f"{log_dir}/best_model.pth")
            logging.info(f"✅ 保存最佳模型，准确率: {test_acc:.2f}%")
            
            # 如果达到目标，记录成功
            if test_acc >= 85:
                logging.info("🎉 达到目标准确率 85%!")
        
        # Early stopping
        early_stopping(val_loss, model)
        if early_stopping.early_stop:
            logging.info("Early stopping triggered")
            break
        
        scheduler.step()
    
    logging.info("\n" + "="*80)
    logging.info(f"训练完成！")
    logging.info(f"最佳测试准确率: {best_test_acc:.2f}% (Epoch {best_epoch})")
    
    if best_test_acc >= 85:
        logging.info("✅ Stage 3 成功达到目标!")
    else:
        logging.info(f"⚠️ Stage 3 未达到85%目标 (当前: {best_test_acc:.2f}%)")
    
    # 保存训练信息
    with open(f"{log_dir}/training_info.json", 'w') as f:
        json.dump({
            'config': config,
            'best_test_acc': best_test_acc,
            'best_epoch': best_epoch,
            'total_epochs': epoch,
            'target_achieved': best_test_acc >= 85
        }, f, indent=2)
    
    # 更新进度报告
    update_progress_report(3, best_test_acc, test_alpha, test_gates)


def update_progress_report(stage, accuracy, alpha, gate_weights):
    """更新进度报告"""
    report_path = "/media/main/ypf/eeg/Cross-Modal-Transformer/scheme/progress_report.md"
    
    with open(report_path, 'a') as f:
        f.write(f"\n## Stage {stage} Results - 独立训练\n")
        f.write(f"- Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n")
        f.write(f"- Accuracy: {accuracy:.2f}%\n")
        f.write(f"- Alpha (fusion weight): {alpha:.4f}\n")
        f.write(f"- Gate weights: EEG={gate_weights[0]:.3f}, EOG={gate_weights[1]:.3f}, EMG={gate_weights[2]:.3f}\n")
        f.write(f"- Status: {'✅ Success' if accuracy >= 85 else '⚠️ Below target'}\n")
        f.write(f"- Training: 完全独立，不加载任何前期权重\n")


if __name__ == "__main__":
    main()