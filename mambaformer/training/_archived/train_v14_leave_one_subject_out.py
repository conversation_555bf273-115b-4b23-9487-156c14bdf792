#!/usr/bin/env python3
"""
V14 with Leave-One-Subject-Out Cross-Validation (20-fold)
每个fold使用19个受试者训练，1个受试者验证
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from sklearn.metrics import (
    accuracy_score,
    f1_score,
    cohen_kappa_score,
    confusion_matrix,
)

# 完全禁用所有tqdm进度条
os.environ["TQDM_DISABLE"] = "1"

# 尝试导入并禁用tqdm（如果存在）
try:
    import tqdm

    tqdm.tqdm.__init__ = lambda self, *args, **kwargs: None
    tqdm.tqdm.__iter__ = lambda self: iter([])
    tqdm.tqdm.update = lambda self, n=1: None
    tqdm.tqdm.close = lambda self: None
except:
    pass

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset


def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v14_leave_one_subject_out_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, "training.log")

    # 清除已有的handlers避免重复
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[logging.FileHandler(log_file), logging.StreamHandler()],
        force=True,
    )

    logging.info("=" * 80)
    logging.info("🚀 V14 with Leave-One-Subject-Out Cross-Validation")
    logging.info("=" * 80)

    return log_dir


class REMFocusedLoss(nn.Module):
    """V14的REM/Wake专注损失函数"""

    def __init__(self, device="cuda"):
        super().__init__()
        self.device = device
        # V14的类权重配置
        self.class_weights = torch.tensor([3.0, 1.0, 1.0, 1.0, 5.0]).to(device)
        # Wake=5.0, N1=1.0, N2=1.0, N3=1.0, REM=3.0

    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            inputs = inputs.reshape(-1, inputs.shape[-1])
            targets = targets.reshape(-1)

        # Focal Loss
        ce_loss = nn.functional.cross_entropy(inputs, targets, reduction="none")
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** 2.0 * ce_loss

        # 应用类权重
        weights = self.class_weights[targets]
        weighted_loss = focal_loss * weights

        return weighted_loss.mean()


def train_epoch(model, train_loader, criterion, optimizer, device):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []

    for data, target in train_loader:
        data, target = data.to(device), target.to(device)

        # 处理序列标签
        if target.dim() > 1:
            target_for_loss = target
            target_for_metric = target[:, target.shape[1] // 2]
        else:
            target_for_loss = target
            target_for_metric = target

        optimizer.zero_grad()
        output, _ = model(data)
        loss = criterion(output, target_for_loss)

        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()

        total_loss += loss.item()

        # 收集预测
        if output.dim() == 3:
            preds = output[:, output.shape[1] // 2, :].argmax(dim=1)
        else:
            preds = output.argmax(dim=1)

        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target_for_metric.cpu().numpy())

    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average="macro")

    return total_loss / len(train_loader), accuracy, f1


def evaluate(model, data_loader, device):
    model.eval()
    all_preds = []
    all_targets = []

    with torch.no_grad():
        for data, target in data_loader:
            data = data.to(device)

            if target.dim() > 1:
                target = target[:, target.shape[1] // 2]

            output, _ = model(data)

            if output.dim() == 3:
                preds = output[:, output.shape[1] // 2, :].argmax(dim=1)
            else:
                preds = output.argmax(dim=1)

            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())

    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average="macro")
    kappa = cohen_kappa_score(all_targets, all_preds)

    # Per-class F1
    class_f1 = f1_score(all_targets, all_preds, average=None)

    return accuracy, f1, kappa, class_f1


def get_subject_files():
    """组织所有文件按受试者分组"""
    # 20个受试者，每个受试者有2个session
    subjects = {}
    
    # 受试者01
    subjects["01"] = ["SC4001E0.npz", "SC4002E0.npz"]
    # 受试者02
    subjects["02"] = ["SC4011E0.npz", "SC4012E0.npz"]
    # 受试者03
    subjects["03"] = ["SC4021E0.npz", "SC4022E0.npz"]
    # 受试者04
    subjects["04"] = ["SC4031E0.npz", "SC4032E0.npz"]
    # 受试者05
    subjects["05"] = ["SC4041E0.npz", "SC4042E0.npz"]
    # 受试者06
    subjects["06"] = ["SC4051E0.npz", "SC4052E0.npz"]
    # 受试者07
    subjects["07"] = ["SC4061E0.npz", "SC4062E0.npz"]
    # 受试者08
    subjects["08"] = ["SC4071E0.npz", "SC4072E0.npz"]
    # 受试者09
    subjects["09"] = ["SC4081E0.npz", "SC4082E0.npz"]
    # 受试者10
    subjects["10"] = ["SC4091E0.npz", "SC4092E0.npz"]
    # 受试者11
    subjects["11"] = ["SC4101E0.npz", "SC4102E0.npz"]
    # 受试者12
    subjects["12"] = ["SC4111E0.npz", "SC4112E0.npz"]
    # 受试者13
    subjects["13"] = ["SC4121E0.npz", "SC4122E0.npz"]
    # 受试者14
    subjects["14"] = ["SC4131E0.npz", "SC4132E0.npz"] if os.path.exists("/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4132E0.npz") else ["SC4131E0.npz"]
    # 受试者15
    subjects["15"] = ["SC4141E0.npz", "SC4142E0.npz"]
    # 受试者16
    subjects["16"] = ["SC4151E0.npz", "SC4152E0.npz"]
    # 受试者17
    subjects["17"] = ["SC4161E0.npz", "SC4162E0.npz"]
    # 受试者18
    subjects["18"] = ["SC4171E0.npz", "SC4172E0.npz"]
    # 受试者19
    subjects["19"] = ["SC4181E0.npz", "SC4182E0.npz"]
    # 受试者20
    subjects["20"] = ["SC4191E0.npz", "SC4192E0.npz"]
    
    return subjects


def train_fold(fold_idx, val_subject_id, train_files, val_files, config, device, log_dir):
    """训练单个fold（留一受试者验证）"""
    logging.info(f"\n{'='*60}")
    logging.info(f"FOLD {fold_idx + 1}/20 - Validating on Subject {val_subject_id}")
    logging.info(f"Train files: {len(train_files)} (19 subjects), Val files: {len(val_files)} (1 subject)")
    logging.info(f"{'='*60}")
    
    # 数据路径
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"
    
    # 加载数据
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config["seq_len"],
        max_samples_per_file=None,
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config["seq_len"],
        max_samples_per_file=None,
    )
    
    logging.info(f"Subject {val_subject_id} - Train samples: {len(train_dataset)}, Val samples: {len(val_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config["batch_size"], shuffle=True, num_workers=4
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config["batch_size"], shuffle=False, num_workers=4
    )
    
    # 创建模型
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)
    
    # 损失函数和优化器
    criterion = REMFocusedLoss(device)
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"],
    )
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode="max", factor=0.5, patience=5
    )
    
    # 训练循环
    best_val_acc = 0
    best_val_f1 = 0
    best_val_kappa = 0
    best_val_class_f1 = None
    patience_counter = 0
    
    for epoch in range(config["num_epochs"]):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device
        )
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1 = evaluate(model, val_loader, device)
        
        # 学习率调度
        scheduler.step(val_acc)
        
        # 简化的日志输出（每5轮或最后一轮输出）
        if epoch % 5 == 0 or epoch == config["num_epochs"] - 1:
            logging.info(
                f"  Epoch {epoch+1}/{config['num_epochs']}: "
                f"Train Loss={train_loss:.4f}, Acc={train_acc:.4f} | "
                f"Val Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}"
            )
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_kappa = val_kappa
            best_val_class_f1 = val_class_f1
            patience_counter = 0
            
            # 保存fold最佳模型
            torch.save(
                {
                    "fold": fold_idx,
                    "val_subject": val_subject_id,
                    "epoch": epoch,
                    "model_state_dict": model.state_dict(),
                    "val_acc": val_acc,
                    "val_f1": val_f1,
                    "val_kappa": val_kappa,
                    "val_class_f1": val_class_f1.tolist(),
                    "config": config,
                },
                os.path.join(log_dir, f"best_model_subject_{val_subject_id}.pth"),
            )
        else:
            patience_counter += 1
            if patience_counter >= config["patience"]:
                logging.info(f"  Early stopping at epoch {epoch+1}")
                break
    
    logging.info(f"Subject {val_subject_id} Best Results: Acc={best_val_acc:.4f}, F1={best_val_f1:.4f}, Kappa={best_val_kappa:.4f}")
    if best_val_class_f1 is not None:
        logging.info(
            f"  Class F1: W={best_val_class_f1[0]:.3f}, N1={best_val_class_f1[1]:.3f}, "
            f"N2={best_val_class_f1[2]:.3f}, N3={best_val_class_f1[3]:.3f}, REM={best_val_class_f1[4]:.3f}"
        )
    
    return best_val_acc, best_val_f1, best_val_kappa, best_val_class_f1


def main():
    # 配置参数（与原始V14相同）
    config = {
        "d_model": 256,
        "n_heads": 16,
        "n_layers": 6,
        "dropout": 0.15,
        "seq_len": 5,
        "batch_size": 32,
        "learning_rate": 2e-4,
        "weight_decay": 1e-4,
        "num_epochs": 30,  # 每个fold的训练轮数
        "patience": 10,
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"Device: {device}")
    
    # 获取按受试者组织的文件
    subjects = get_subject_files()
    subject_ids = sorted(subjects.keys())
    
    logging.info(f"Total subjects: {len(subject_ids)}")
    logging.info(f"Subjects: {', '.join(subject_ids)}")
    
    # 存储所有fold的结果
    all_fold_results = []
    
    # 执行留一受试者交叉验证
    for fold_idx, val_subject_id in enumerate(subject_ids):
        # 验证集：当前受试者的所有文件
        val_files = subjects[val_subject_id]
        
        # 训练集：其他19个受试者的所有文件
        train_files = []
        for subject_id in subject_ids:
            if subject_id != val_subject_id:
                train_files.extend(subjects[subject_id])
        
        # 训练当前fold
        fold_acc, fold_f1, fold_kappa, fold_class_f1 = train_fold(
            fold_idx, val_subject_id, train_files, val_files, config, device, log_dir
        )
        
        all_fold_results.append({
            "fold": fold_idx + 1,
            "val_subject": val_subject_id,
            "accuracy": float(fold_acc),
            "f1": float(fold_f1),
            "kappa": float(fold_kappa),
            "class_f1": fold_class_f1.tolist() if fold_class_f1 is not None else None,
            "train_subjects": [s for s in subject_ids if s != val_subject_id],
            "val_files": val_files,
        })
    
    # 计算平均结果
    avg_accuracy = np.mean([r["accuracy"] for r in all_fold_results])
    avg_f1 = np.mean([r["f1"] for r in all_fold_results])
    avg_kappa = np.mean([r["kappa"] for r in all_fold_results])
    
    std_accuracy = np.std([r["accuracy"] for r in all_fold_results])
    std_f1 = np.std([r["f1"] for r in all_fold_results])
    std_kappa = np.std([r["kappa"] for r in all_fold_results])
    
    # 计算每类的平均F1
    if all([r["class_f1"] is not None for r in all_fold_results]):
        avg_class_f1 = np.mean([r["class_f1"] for r in all_fold_results], axis=0)
        std_class_f1 = np.std([r["class_f1"] for r in all_fold_results], axis=0)
    else:
        avg_class_f1 = None
        std_class_f1 = None
    
    # 最终结果
    logging.info("\n" + "=" * 80)
    logging.info("📊 LEAVE-ONE-SUBJECT-OUT CROSS-VALIDATION RESULTS")
    logging.info("=" * 80)
    logging.info(f"Average Accuracy: {avg_accuracy:.4f} ± {std_accuracy:.4f} ({avg_accuracy*100:.2f}%)")
    logging.info(f"Average F1 Score: {avg_f1:.4f} ± {std_f1:.4f}")
    logging.info(f"Average Kappa: {avg_kappa:.4f} ± {std_kappa:.4f}")
    
    if avg_class_f1 is not None:
        logging.info("\nAverage Per-Class F1 Scores:")
        logging.info(f"  Wake: {avg_class_f1[0]:.4f} ± {std_class_f1[0]:.4f}")
        logging.info(f"  N1:   {avg_class_f1[1]:.4f} ± {std_class_f1[1]:.4f}")
        logging.info(f"  N2:   {avg_class_f1[2]:.4f} ± {std_class_f1[2]:.4f}")
        logging.info(f"  N3:   {avg_class_f1[3]:.4f} ± {std_class_f1[3]:.4f}")
        logging.info(f"  REM:  {avg_class_f1[4]:.4f} ± {std_class_f1[4]:.4f}")
    
    # 打印每个受试者的结果
    logging.info("\nPer-Subject Results:")
    for result in all_fold_results:
        logging.info(
            f"  Subject {result['val_subject']}: Acc={result['accuracy']:.4f}, "
            f"F1={result['f1']:.4f}, Kappa={result['kappa']:.4f}"
        )
    
    # 找出最好和最差的受试者
    best_subject = max(all_fold_results, key=lambda x: x["accuracy"])
    worst_subject = min(all_fold_results, key=lambda x: x["accuracy"])
    
    logging.info(f"\nBest Subject: {best_subject['val_subject']} with Accuracy={best_subject['accuracy']:.4f}")
    logging.info(f"Worst Subject: {worst_subject['val_subject']} with Accuracy={worst_subject['accuracy']:.4f}")
    
    # 保存结果
    final_results = {
        "average_accuracy": float(avg_accuracy),
        "average_f1": float(avg_f1),
        "average_kappa": float(avg_kappa),
        "std_accuracy": float(std_accuracy),
        "std_f1": float(std_f1),
        "std_kappa": float(std_kappa),
        "avg_class_f1": avg_class_f1.tolist() if avg_class_f1 is not None else None,
        "std_class_f1": std_class_f1.tolist() if std_class_f1 is not None else None,
        "subject_results": all_fold_results,
        "best_subject": best_subject,
        "worst_subject": worst_subject,
        "config": config,
    }
    
    with open(os.path.join(log_dir, "loso_cv_results.json"), "w") as f:
        json.dump(final_results, f, indent=2)
    
    logging.info(f"\n✅ Leave-One-Subject-Out Cross-Validation Complete!")
    logging.info(f"Results saved to {log_dir}")


if __name__ == "__main__":
    main()