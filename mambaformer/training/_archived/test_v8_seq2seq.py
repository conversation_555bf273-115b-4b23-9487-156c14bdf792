#!/usr/bin/env python3
"""
基于V8模型测试Seq2Seq改进
V8是一个稳定的模型，用它来验证seq2seq的效果
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset


def sliding_window_inference_simple(model, data_loader, device):
    """
    简单的滑动窗口推理
    通过多次预测+概率平均提升性能
    """
    model.eval()
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Sliding Window Inference'):
            data = data.to(device)
            batch_size = data.shape[0]
            
            # 收集多个预测
            predictions_list = []
            
            # 1. 原始预测
            output, _ = model(data)
            if output.dim() == 3:
                output = output[:, output.shape[1]//2, :]
            predictions_list.append(F.softmax(output, dim=-1))
            
            # 2. 添加轻微噪声的预测（模拟不同窗口）
            for noise_level in [0.001, 0.002, 0.003, 0.004]:
                noise = torch.randn_like(data) * noise_level
                noisy_data = data + noise
                output, _ = model(noisy_data)
                if output.dim() == 3:
                    output = output[:, output.shape[1]//2, :]
                predictions_list.append(F.softmax(output, dim=-1))
            
            # 3. 时间偏移的预测
            for shift in [-50, 50]:  # 轻微的时间偏移
                shifted_data = torch.roll(data, shifts=shift, dims=-1)
                output, _ = model(shifted_data)
                if output.dim() == 3:
                    output = output[:, output.shape[1]//2, :]
                predictions_list.append(F.softmax(output, dim=-1))
            
            # 平均所有预测
            avg_predictions = torch.stack(predictions_list).mean(dim=0)
            final_preds = avg_predictions.argmax(dim=-1)
            
            # 收集结果
            all_predictions.extend(final_preds.cpu().numpy())
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            all_targets.extend(target.numpy())
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_predictions)
    f1 = f1_score(all_targets, all_predictions, average='macro')
    kappa = cohen_kappa_score(all_targets, all_predictions)
    class_f1 = f1_score(all_targets, all_predictions, average=None)
    cm = confusion_matrix(all_targets, all_predictions)
    
    return accuracy, f1, kappa, class_f1, cm


def evaluate_standard(model, data_loader, device):
    """标准评估"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Standard Evaluation'):
            data = data.to(device)
            
            output, _ = model(data)
            if output.dim() == 3:
                output = output[:, output.shape[1]//2, :]
            
            preds = output.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm


def main():
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f'../logs/v8_seq2seq_test_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'test.log')),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🔬 V8 Model Seq2Seq Testing")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # V8模型路径
    v8_checkpoint = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v8_proper_eval_20250812_155847/best_model.pth'
    
    if not os.path.exists(v8_checkpoint):
        logging.error(f"V8 checkpoint not found: {v8_checkpoint}")
        return
    
    # 加载checkpoint
    logging.info(f"Loading V8 model from: {v8_checkpoint}")
    checkpoint = torch.load(v8_checkpoint, map_location=device, weights_only=False)
    
    # 获取配置
    config = checkpoint['config']
    logging.info(f"Model config: {config}")
    
    # 创建模型
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # 加载权重
    model.load_state_dict(checkpoint['model_state_dict'])
    logging.info(f"✅ Model loaded successfully")
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 创建测试数据集
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None  # 使用全部数据
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset,
        batch_size=32,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    logging.info(f"Test dataset: {len(test_dataset)} samples")
    
    # 评估
    logging.info("\n" + "="*60)
    logging.info("Evaluation Results")
    logging.info("="*60)
    
    # 1. 标准评估
    logging.info("\n📊 Standard Evaluation (Single Prediction):")
    std_acc, std_f1, std_kappa, std_class_f1, std_cm = evaluate_standard(model, test_loader, device)
    
    logging.info(f"  Accuracy: {std_acc:.4f} ({std_acc*100:.2f}%)")
    logging.info(f"  Macro F1: {std_f1:.4f}")
    logging.info(f"  Kappa: {std_kappa:.4f}")
    logging.info(f"  Class F1: W={std_class_f1[0]:.3f}, N1={std_class_f1[1]:.3f}, "
                f"N2={std_class_f1[2]:.3f}, N3={std_class_f1[3]:.3f}, REM={std_class_f1[4]:.3f}")
    
    # 2. 滑动窗口评估
    logging.info("\n📊 Sliding Window Evaluation (Multiple Predictions + Averaging):")
    sw_acc, sw_f1, sw_kappa, sw_class_f1, sw_cm = sliding_window_inference_simple(model, test_loader, device)
    
    logging.info(f"  Accuracy: {sw_acc:.4f} ({sw_acc*100:.2f}%)")
    logging.info(f"  Macro F1: {sw_f1:.4f}")
    logging.info(f"  Kappa: {sw_kappa:.4f}")
    logging.info(f"  Class F1: W={sw_class_f1[0]:.3f}, N1={sw_class_f1[1]:.3f}, "
                f"N2={sw_class_f1[2]:.3f}, N3={sw_class_f1[3]:.3f}, REM={sw_class_f1[4]:.3f}")
    
    # 3. 改进分析
    improvement = sw_acc - std_acc
    logging.info(f"\n🎯 Improvement with Sliding Window: {improvement:.4f} ({improvement*100:.2f}%)")
    
    if improvement > 0:
        logging.info("  ✅ Sliding window inference improved accuracy!")
    else:
        logging.info("  ❌ No improvement with sliding window")
    
    # 4. 混淆矩阵
    logging.info("\n📊 Confusion Matrix (Sliding Window):")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logging.info("       " + "  ".join([f"{name:>6}" for name in class_names]))
    for i, true_class in enumerate(class_names):
        row = sw_cm[i]
        row_str = " ".join([f"{val:>6}" for val in row])
        logging.info(f"{true_class:>6} {row_str}")
    
    # 5. 错误分析
    logging.info("\n🔍 Error Analysis:")
    total_samples = sw_cm.sum()
    total_errors = total_samples - np.trace(sw_cm)
    logging.info(f"Total errors: {total_errors} ({total_errors/total_samples*100:.2f}%)")
    
    # 主要混淆对
    confusion_pairs = []
    for i in range(5):
        for j in range(5):
            if i != j and sw_cm[i, j] > 50:
                confusion_pairs.append((class_names[i], class_names[j], sw_cm[i, j]))
    
    confusion_pairs.sort(key=lambda x: x[2], reverse=True)
    logging.info("\nTop confusion pairs:")
    for true_class, pred_class, count in confusion_pairs[:5]:
        logging.info(f"  {true_class} → {pred_class}: {count} errors")
    
    # 6. 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS")
    logging.info("="*80)
    
    gap_to_90 = 0.90 - sw_acc
    if sw_acc >= 0.90:
        logging.info(f"🎉 ACHIEVED 90% ACCURACY: {sw_acc:.4f}")
    else:
        logging.info(f"Current Best Accuracy: {sw_acc:.4f}")
        logging.info(f"Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
        
        # 建议
        logging.info("\n💡 Suggestions for reaching 90%:")
        if sw_class_f1[1] < 0.6:  # N1通常最弱
            logging.info("  - N1 detection needs improvement (F1 < 0.6)")
        if improvement < 0.01:
            logging.info("  - Sliding window didn't help much, need architectural changes")
        logging.info("  - Consider ensemble methods with multiple models")
        logging.info("  - Try more aggressive data augmentation during training")
        logging.info("  - Implement true sequence-to-sequence prediction")
    
    # 保存结果
    results = {
        'model': 'V8 Proper Eval',
        'checkpoint': v8_checkpoint,
        'standard_evaluation': {
            'accuracy': float(std_acc),
            'macro_f1': float(std_f1),
            'kappa': float(std_kappa),
            'class_f1': std_class_f1.tolist()
        },
        'sliding_window_evaluation': {
            'accuracy': float(sw_acc),
            'macro_f1': float(sw_f1),
            'kappa': float(sw_kappa),
            'class_f1': sw_class_f1.tolist()
        },
        'improvement': float(improvement),
        'confusion_matrix': sw_cm.tolist(),
        'gap_to_90': float(gap_to_90)
    }
    
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\nResults saved to {log_dir}")
    
    # 返回结果供进一步分析
    return sw_acc, improvement


if __name__ == '__main__':
    final_acc, improvement = main()
    
    # 如果接近90%，可以尝试更多技巧
    if 0.87 <= final_acc < 0.90:
        print("\n" + "="*80)
        print("🎯 Close to 90%! Additional suggestions:")
        print("="*80)
        print("1. Combine V8 + V14 models in ensemble")
        print("2. Use more TTA variations")
        print("3. Post-process with temporal smoothing")
        print("4. Train a meta-learner on validation set")
        gap = 0.90 - final_acc
        print(f"\nOnly {gap:.2%} to go!")