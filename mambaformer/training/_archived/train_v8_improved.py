#!/usr/bin/env python3
"""
V8 Improved - 轻量高效架构，目标90%
基于V8的成功经验，结合V14的改进策略：
1. 轻量架构（5-6层）
2. REM/Wake优化损失
3. 增强数据增强
4. 标签平滑
5. 渐进式学习率
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v8_improved_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V8 Improved - Lightweight & Efficient")
    logging.info("="*80)
    
    return log_dir

class V8ImprovedLoss(nn.Module):
    """V8改进损失 - 结合Focal Loss、标签平滑和类权重"""
    def __init__(self, device='cuda', gamma=2.0, smoothing=0.1):
        super().__init__()
        self.device = device
        self.gamma = gamma
        self.smoothing = smoothing
        
        # 基于V14经验的权重，但更平衡
        self.class_weights = torch.tensor([3.5, 2.5, 1.0, 1.0, 3.0]).to(device)
        
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            # 只取中间时间步
            inputs = inputs[:, inputs.shape[1]//2, :]
            targets = targets[:, targets.shape[1]//2] if targets.dim() > 1 else targets
        
        n_classes = inputs.size(1)
        
        # 标签平滑
        if self.smoothing > 0:
            with torch.no_grad():
                targets_one_hot = torch.zeros_like(inputs).scatter_(1, targets.unsqueeze(1), 1)
                targets_one_hot = targets_one_hot * (1 - self.smoothing) + self.smoothing / n_classes
            
            log_probs = F.log_softmax(inputs, dim=1)
            ce_loss = -(targets_one_hot * log_probs).sum(dim=1)
        else:
            ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        
        # Focal term
        probs = F.softmax(inputs, dim=1)
        p_t = probs.gather(1, targets.unsqueeze(1)).squeeze(1)
        focal_weight = (1 - p_t) ** self.gamma
        
        # 应用类权重
        weights = self.class_weights[targets]
        
        loss = focal_weight * ce_loss * weights
        
        return loss.mean()

class EnhancedDataAugmentation:
    """增强的数据增强策略"""
    def __init__(self, p=0.5):
        self.p = p
    
    def __call__(self, x):
        """应用数据增强"""
        if random.random() < self.p:
            # 1. 时间偏移
            if random.random() < 0.3:
                shift = random.randint(-50, 50)
                x = torch.roll(x, shifts=shift, dims=2)
            
            # 2. 幅度缩放
            if random.random() < 0.3:
                scale = 0.8 + random.random() * 0.4  # 0.8-1.2
                x = x * scale
            
            # 3. 添加噪声
            if random.random() < 0.2:
                noise = torch.randn_like(x) * 0.01
                x = x + noise
        
        return x

class V8ImprovedModel(nn.Module):
    """V8改进模型 - 轻量但有效"""
    def __init__(self, config):
        super().__init__()
        
        # 主干网络 - 使用适中的深度
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len']
        )
        
        # 辅助分类器输入维度应该是2（backbone的辅助输出是2维的）
        self.aux_classifier = nn.Sequential(
            nn.Linear(2, 32),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, 2)  # 二分类：清醒/睡眠
        )
        
    def forward(self, x):
        # 主干网络输出
        main_output, aux_features = self.backbone(x)
        
        # 如果有中间特征，用于辅助分类
        if self.training and aux_features is not None:
            if aux_features.dim() == 3:
                # 取中间时间步的特征
                batch_size = aux_features.shape[0]
                # 使用池化或取中间位置
                aux_features = aux_features[:, aux_features.shape[1]//2, :]  # (batch, d_model)
            aux_output = self.aux_classifier(aux_features)
        else:
            aux_output = None
        
        return main_output, aux_output

def mixup_data(x, y, alpha=0.2):
    """Mixup数据增强"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).to(x.device)

    mixed_x = lam * x + (1 - lam) * x[index]
    y_a, y_b = y, y[index]
    
    return mixed_x, y_a, y_b, lam

def train_epoch(model, train_loader, criterion, optimizer, device, augmentation, use_mixup=True):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc='Training')
    for batch_idx, (data, target) in enumerate(pbar):
        data, target = data.to(device), target.to(device)
        
        # 数据增强
        data = augmentation(data)
        
        # Mixup增强（概率性）
        if use_mixup and random.random() < 0.3:
            data, target_a, target_b, lam = mixup_data(data, target)
            
            optimizer.zero_grad()
            output, aux_output = model(data)
            
            # Mixup损失
            loss = lam * criterion(output, target_a) + (1 - lam) * criterion(output, target_b)
            
            # 添加辅助损失（如果有）
            if aux_output is not None:
                # 创建辅助标签（清醒vs睡眠）
                aux_target_a = (target_a[:, target_a.shape[1]//2] if target_a.dim() > 1 else target_a) == 0
                aux_target_b = (target_b[:, target_b.shape[1]//2] if target_b.dim() > 1 else target_b) == 0
                aux_loss = lam * F.cross_entropy(aux_output, aux_target_a.long()) + \
                          (1 - lam) * F.cross_entropy(aux_output, aux_target_b.long())
                loss = loss + 0.1 * aux_loss
        else:
            optimizer.zero_grad()
            output, aux_output = model(data)
            loss = criterion(output, target)
            
            # 添加辅助损失
            if aux_output is not None:
                aux_target = (target[:, target.shape[1]//2] if target.dim() > 1 else target) == 0
                aux_loss = F.cross_entropy(aux_output, aux_target.long())
                loss = loss + 0.1 * aux_loss
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测（用于监控）
        if output.dim() == 3:
            output = output[:, output.shape[1]//2, :]
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        preds = output.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy() if not use_mixup else target_a.cpu().numpy())
        
        pbar.set_postfix({'loss': loss.item()})
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate(model, data_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            output, _ = model(data)
            
            # 处理输出
            if output.dim() == 3:
                output = output[:, output.shape[1]//2, :]
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            preds = output.argmax(dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    # V8改进配置 - 轻量但有效
    config = {
        'd_model': 192,  # 中等维度
        'n_heads': 12,   # 适中的注意力头
        'n_layers': 5,   # 5层（不太深）
        'dropout': 0.1,
        'seq_len': 5,
        'batch_size': 48,  # 较大batch
        'learning_rate': 3e-4,  # 较高学习率（因为模型小）
        'weight_decay': 3e-5,
        'num_epochs': 40,
        'patience': 10,
        'label_smoothing': 0.1,
        'mixup_alpha': 0.2
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割（使用标准分割）
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None  # 使用全部数据！
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = V8ImprovedModel(config).to(device)
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = V8ImprovedLoss(device, smoothing=config['label_smoothing'])
    optimizer = optim.AdamW(model.parameters(), 
                           lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'])
    
    # 学习率调度 - OneCycleLR（快速收敛）
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'] * 10,
        epochs=config['num_epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.2,
        anneal_strategy='cos'
    )
    
    # 数据增强
    augmentation = EnhancedDataAugmentation(p=0.5)
    
    # 训练
    best_val_acc = 0
    best_test_acc = 0
    best_test_metrics = {}
    patience_counter = 0
    
    logging.info("Starting V8 Improved training...")
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, 
            augmentation, use_mixup=(epoch > 3)  # 前几个epoch不用mixup
        )
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(model, val_loader, device)
        
        # 测试
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(model, test_loader, device)
        
        # 学习率调度
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Test: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_test_acc = test_acc
            best_test_metrics = {
                'accuracy': test_acc,
                'f1': test_f1,
                'kappa': test_kappa,
                'class_f1': test_class_f1.tolist(),
                'confusion_matrix': test_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_metrics': best_test_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Test Acc: {test_acc:.4f})")
            
            if test_acc >= 0.87:
                logging.info("  🎯 Reached 87% target!")
            
            if test_acc >= 0.90:
                logging.info("  🎉 ACHIEVED 90% TARGET!")
                # Git commit
                os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '✅ V8 Improved achieved 90%: {test_acc:.4f}'")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS (V8 Improved)")
    logging.info("="*80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test F1: {best_test_metrics['f1']:.4f}")
    logging.info(f"Best Test Kappa: {best_test_metrics['kappa']:.4f}")
    
    if best_test_acc >= 0.90:
        logging.info("✅ SUCCESS: Achieved 90% target!")
    else:
        gap = 0.90 - best_test_acc
        logging.info(f"Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
    
    # 保存结果
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(best_test_metrics, f, indent=2)

if __name__ == "__main__":
    main()