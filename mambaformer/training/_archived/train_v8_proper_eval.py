#!/usr/bin/env python3
"""
V8 PROPER EVALUATION - 正确的评估策略
基于验证集选择模型，测试集只在最后评估
修复了之前的测试集过拟合问题
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import random
import math

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v8_proper_eval_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V8 PROPER EVALUATION - CORRECT METHODOLOGY")
    logging.info("="*80)
    
    return log_dir

class ProperEvalModel(nn.Module):
    """基于最佳架构的模型"""
    def __init__(self, input_channels, n_classes, d_model, n_heads, n_layers, dropout, seq_len):
        super().__init__()
        
        # 主干网络
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # 额外的分类头用于正则化 - 基于n_classes输入
        self.auxiliary_head = nn.Sequential(
            nn.Linear(n_classes, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, n_classes)
        )
        
    def forward(self, x):
        # 主输出
        main_out, _ = self.backbone(x)
        
        # 提取中心时间步输出
        if main_out.dim() == 3:
            center_out = main_out[:, main_out.shape[1]//2, :]
        else:
            center_out = main_out
            
        # 辅助输出（用于训练时的正则化）
        aux_out = self.auxiliary_head(center_out)
        
        return center_out, aux_out

class ProperLoss(nn.Module):
    """改进的损失函数"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 基于之前的分析调整权重
        # N1仍然是最弱的，需要最高权重
        self.class_weights = torch.tensor([2.5, 8.0, 1.0, 1.0, 2.0]).to(device)
        self.gamma = 2.0  # Focal loss gamma
        self.label_smoothing = 0.1
        
    def forward(self, main_out, aux_out, targets, epoch=0):
        # 处理维度
        if main_out.dim() == 3:
            main_out = main_out[:, main_out.shape[1]//2, :]
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        # 动态调整权重
        weights = self.class_weights.clone()
        if epoch > 20:
            weights[1] = min(12.0, weights[1] + (epoch - 20) * 0.1)
        
        # 主损失 - Focal Loss
        ce_loss = F.cross_entropy(main_out, targets, weight=weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = ((1 - pt) ** self.gamma) * ce_loss
        
        # 辅助损失
        aux_loss = F.cross_entropy(aux_out, targets, weight=weights)
        
        # 标签平滑
        if self.label_smoothing > 0 and self.training:
            n_classes = main_out.size(-1)
            smooth_loss = -F.log_softmax(main_out, dim=-1).mean(dim=-1)
            total_loss = (1 - self.label_smoothing) * focal_loss.mean() + \
                        self.label_smoothing * smooth_loss.mean() + \
                        0.3 * aux_loss
        else:
            total_loss = focal_loss.mean() + 0.3 * aux_loss
        
        return total_loss

def safe_augment(x, targets=None, epoch=0):
    """安全的数据增强"""
    # 根据epoch调整强度
    if epoch < 10:
        p = 0.3
    elif epoch < 30:
        p = 0.5
    else:
        p = 0.6
    
    if random.random() > p:
        return x
    
    # 1. MixUp/CutMix
    if targets is not None and random.random() < 0.3:
        batch_size = x.size(0)
        if batch_size > 1:
            index = torch.randperm(batch_size)
            lam = np.random.beta(0.4, 0.4)
            
            # 时间维度CutMix
            seq_len = x.shape[-1]
            if seq_len > 10:  # 确保序列足够长
                cut_len = int(seq_len * (1 - lam))
                cut_len = min(cut_len, seq_len - 1)  # 确保不会超出范围
                if cut_len > 0:
                    start = random.randint(0, seq_len - cut_len)
                    x[..., start:start+cut_len] = x[index][..., start:start+cut_len]
    
    # 2. 噪声
    if random.random() < 0.3:
        noise_level = 0.005 + random.random() * 0.015
        noise = torch.randn_like(x) * noise_level
        x = x + noise
    
    # 3. 时间偏移
    if random.random() < 0.3:
        max_shift = min(100, x.shape[-1] // 4)
        shift = random.randint(-max_shift, max_shift)
        x = torch.roll(x, shifts=shift, dims=-1)
    
    # 4. 幅度缩放
    if random.random() < 0.3:
        scale = 0.7 + random.random() * 0.6  # 0.7-1.3
        x = x * scale
    
    # 5. 安全的随机遮挡
    if random.random() < 0.2:
        seq_len = x.shape[-1]
        if seq_len > 100:  # 只在序列足够长时使用
            max_mask = min(80, seq_len // 4)
            mask_len = random.randint(20, max_mask)
            mask_start = random.randint(0, seq_len - mask_len)
            x[..., mask_start:mask_start+mask_len] *= 0.1
    
    return x

def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    model.train()
    criterion.training = True
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # 数据增强
        data = safe_augment(data, target if target.dim() == 1 else target[:, target.shape[1]//2], epoch)
        
        optimizer.zero_grad()
        
        # 前向传播
        main_out, aux_out = model(data)
        
        # 计算损失
        loss = criterion(main_out, aux_out, target, epoch)
        
        # L2正则化
        l2_lambda = 1e-5
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测（用于监控训练进度）
        if main_out.dim() == 3:
            main_out = main_out[:, main_out.shape[1]//2, :]
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        preds = main_out.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
        
        pbar.set_postfix({'loss': f'{loss.item():.4f}'})
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate(model, data_loader, device, use_tta=False, return_predictions=False):
    """评估函数 - 可选TTA"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            if use_tta:
                # Test Time Augmentation
                predictions = []
                for i in range(3):
                    if i == 0:
                        data_aug = data
                    elif i == 1:
                        data_aug = data + torch.randn_like(data) * 0.002
                    else:
                        shift = random.randint(-20, 20)
                        data_aug = torch.roll(data, shifts=shift, dims=-1)
                    
                    main_out, aux_out = model(data_aug)
                    if main_out.dim() == 3:
                        main_out = main_out[:, main_out.shape[1]//2, :]
                    
                    # 组合主输出和辅助输出
                    combined = 0.7 * F.softmax(main_out, dim=-1) + 0.3 * F.softmax(aux_out, dim=-1)
                    predictions.append(combined)
                
                avg_probs = torch.stack(predictions).mean(dim=0)
                preds = avg_probs.argmax(dim=1)
            else:
                main_out, _ = model(data)
                if main_out.dim() == 3:
                    main_out = main_out[:, main_out.shape[1]//2, :]
                preds = main_out.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    if return_predictions:
        return accuracy, f1, kappa, class_f1, cm, all_targets, all_preds
    return accuracy, f1, kappa, class_f1, cm

def main():
    # 配置
    config = {
        'd_model': 256,  # 中等规模，避免过拟合
        'n_heads': 16,   
        'n_layers': 6,
        'dropout': 0.2,  # 更多dropout
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 2e-4,
        'weight_decay': 1e-4,
        'num_epochs': 100,
        'patience': 20
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    logging.info("Strategy: Proper evaluation - Val for selection, Test only at end")
    logging.info("Previous best: 86.76% (but potentially overfit to test set)")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = ProperEvalModel(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = ProperLoss(device)
    optimizer = optim.AdamW(model.parameters(), 
                           lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'])
    
    # 学习率调度
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    
    # 训练
    best_val_acc = 0
    best_val_metrics = {}
    patience_counter = 0
    
    logging.info("STARTING PROPER EVALUATION TRAINING!")
    logging.info("Model selection based on VALIDATION SET only")
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch
        )
        
        # 验证集评估（用于选择最佳模型）
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(
            model, val_loader, device, use_tta=False
        )
        
        # 学习率调度
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # 基于验证集保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_val_metrics = {
                'epoch': epoch + 1,
                'accuracy': val_acc,
                'f1': val_f1,
                'kappa': val_kappa,
                'class_f1': val_class_f1.tolist(),
                'confusion_matrix': val_cm.tolist()
            }
            patience_counter = 0
            
            # 保存模型
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_metrics': best_val_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Val Acc: {val_acc:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 加载最佳模型
    logging.info("\n" + "="*80)
    logging.info("Loading best model for final test evaluation...")
    checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 最终测试集评估（只执行一次）
    logging.info("Final evaluation on TEST SET (with TTA)...")
    test_acc, test_f1, test_kappa, test_class_f1, test_cm, all_targets, all_preds = evaluate(
        model, test_loader, device, use_tta=True, return_predictions=True
    )
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS (PROPER EVALUATION)")
    logging.info("="*80)
    logging.info(f"Best Validation Accuracy: {best_val_acc:.4f} (Epoch {best_val_metrics['epoch']})")
    logging.info(f"Best Validation F1: {best_val_metrics['f1']:.4f}")
    logging.info(f"Best Validation Kappa: {best_val_metrics['kappa']:.4f}")
    logging.info("-"*40)
    logging.info("FINAL TEST RESULTS:")
    logging.info(f"Test Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    logging.info(f"Test F1: {test_f1:.4f}")
    logging.info(f"Test Kappa: {test_kappa:.4f}")
    logging.info(f"Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
    
    # 详细结果分析
    logging.info("\n" + "="*80)
    logging.info("📊 DETAILED EVALUATION RESULTS")
    logging.info("="*80)
    
    # 混淆矩阵
    logging.info("\n🔄 Confusion Matrix:")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logging.info("       " + "  ".join([f"{name:>6}" for name in class_names]))
    for i, true_class in enumerate(class_names):
        row = test_cm[i]
        row_str = " ".join([f"{val:>6}" for val in row])
        logging.info(f"{true_class:>6} {row_str}")
    logging.info(f"Total: {test_cm.sum()}")
    
    # 每类详细指标
    logging.info("\n📈 Per-Class Detailed Metrics:")
    from sklearn.metrics import precision_recall_fscore_support
    precision, recall, _, support = precision_recall_fscore_support(
        all_targets, all_preds, average=None
    )
    
    for i, class_name in enumerate(class_names):
        logging.info(f"{class_name:>6}: F1={test_class_f1[i]:.3f}, "
                    f"Prec={precision[i]:.3f}, Recall={recall[i]:.3f}, "
                    f"Support={support[i]}")
    
    # 错误分析
    logging.info("\n🔍 Error Analysis:")
    total_errors = test_cm.sum() - np.trace(test_cm)
    logging.info(f"Total errors: {total_errors} ({total_errors/test_cm.sum()*100:.2f}%)")
    
    # 主要混淆对
    logging.info("\n📊 Major Confusion Pairs:")
    confusion_pairs = []
    for i in range(5):
        for j in range(5):
            if i != j and test_cm[i, j] > 50:  # 超过50个错误样本
                confusion_pairs.append((class_names[i], class_names[j], test_cm[i, j]))
    
    confusion_pairs.sort(key=lambda x: x[2], reverse=True)
    for true_class, pred_class, count in confusion_pairs[:10]:
        logging.info(f"  {true_class} → {pred_class}: {count} errors")
    
    # 性能总结
    logging.info("\n📋 Performance Summary:")
    logging.info(f"  Strongest class: {class_names[np.argmax(test_class_f1)]} (F1={np.max(test_class_f1):.3f})")
    logging.info(f"  Weakest class: {class_names[np.argmin(test_class_f1)]} (F1={np.min(test_class_f1):.3f})")
    logging.info(f"  F1 variance: {np.std(test_class_f1):.3f}")
    
    # 与目标对比
    logging.info("\n🎯 Target Comparison:")
    gap_to_90 = 0.90 - test_acc
    if test_acc >= 0.90:
        logging.info(f"  ✅ TARGET ACHIEVED! Accuracy: {test_acc:.4f}")
    else:
        logging.info(f"  Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
        logging.info(f"  Required improvement: {gap_to_90/test_acc*100:.2f}% relative increase")
    
    # 建议
    logging.info("\n💡 Improvement Suggestions:")
    if test_class_f1[1] < 0.6:  # N1 is typically weakest
        logging.info("  - N1 stage needs significant improvement (F1 < 0.6)")
        logging.info("  - Consider: More N1 data augmentation, higher class weight, specialized N1 features")
    
    if np.std(test_class_f1) > 0.15:
        logging.info("  - High variance in class performance")
        logging.info("  - Consider: Balanced sampling, adaptive loss weighting")
    
    major_confusion = confusion_pairs[0] if confusion_pairs else None
    if major_confusion:
        logging.info(f"  - Major confusion: {major_confusion[0]} → {major_confusion[1]}")
        logging.info(f"  - Consider: Features to better distinguish these classes")
    
    if test_acc >= 0.90:
        logging.info("\n✅ SUCCESS: ACHIEVED 90% TARGET!")
        os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '✅ ACHIEVED 90% with proper evaluation: {test_acc:.4f}'")
    else:
        gap = 0.90 - test_acc
        logging.info(f"\nGap to 90%: {gap:.4f} ({gap*100:.2f}%)")
    
    # 保存最终结果
    final_results = {
        'best_val_metrics': best_val_metrics,
        'test_metrics': {
            'accuracy': test_acc,
            'f1': test_f1,
            'kappa': test_kappa,
            'class_f1': test_class_f1.tolist(),
            'confusion_matrix': test_cm.tolist()
        },
        'config': config
    }
    
    with open(os.path.join(log_dir, 'final_results.json'), 'w') as f:
        json.dump(final_results, f, indent=2)
    
    logging.info(f"\nResults saved to {log_dir}")

if __name__ == "__main__":
    main()