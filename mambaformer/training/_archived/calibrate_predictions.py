#!/usr/bin/env python3
"""
Probability Calibration for 90% Target
Address high-confidence errors through temperature scaling and isotonic regression
Current: 87.32% with 193 high-confidence errors → Target: 90%
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from sklearn.isotonic import IsotonicRegression
from sklearn.calibration import calibration_curve
from sklearn.metrics import accuracy_score, brier_score_loss
import matplotlib.pyplot as plt
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class TemperatureScaling(nn.Module):
    """Temperature scaling for neural network calibration"""
    
    def __init__(self):
        super().__init__()
        self.temperature = nn.Parameter(torch.ones(1) * 1.5)
    
    def forward(self, logits):
        """Apply temperature scaling to logits"""
        return logits / self.temperature
    
    def fit(self, logits, labels, max_iter=50):
        """Optimize temperature on validation set"""
        self.cuda()
        
        # Convert to tensors
        if not isinstance(logits, torch.Tensor):
            logits = torch.tensor(logits, dtype=torch.float32)
        if not isinstance(labels, torch.Tensor):
            labels = torch.tensor(labels, dtype=torch.long)
        
        logits = logits.cuda()
        labels = labels.cuda()
        
        # Optimize temperature
        optimizer = optim.LBFGS([self.temperature], lr=0.01, max_iter=max_iter)
        
        def eval():
            loss = F.cross_entropy(self(logits), labels)
            loss.backward()
            return loss
        
        optimizer.step(eval)
        
        return self


class EnsembleCalibrator:
    """Calibrate ensemble predictions using multiple methods"""
    
    def __init__(self):
        self.temperature_scaler = None
        self.isotonic_regressors = []
        self.platt_scalers = []
        
    def fit_temperature_scaling(self, probs, labels):
        """Fit temperature scaling on probabilities"""
        # Convert probabilities to logits
        probs = np.clip(probs, 1e-8, 1 - 1e-8)
        logits = np.log(probs / (1 - probs.sum(axis=1, keepdims=True) + probs))
        
        # Fit temperature scaling
        self.temperature_scaler = TemperatureScaling()
        self.temperature_scaler.fit(logits, labels)
        
        return self.temperature_scaler.temperature.item()
    
    def fit_isotonic_regression(self, probs, labels):
        """Fit isotonic regression for each class"""
        self.isotonic_regressors = []
        
        n_classes = probs.shape[1]
        for class_idx in range(n_classes):
            # Binary labels for this class
            binary_labels = (labels == class_idx).astype(int)
            class_probs = probs[:, class_idx]
            
            # Fit isotonic regression
            iso_reg = IsotonicRegression(out_of_bounds='clip')
            iso_reg.fit(class_probs, binary_labels)
            self.isotonic_regressors.append(iso_reg)
        
        return self.isotonic_regressors
    
    def calibrate_temperature(self, probs):
        """Apply temperature scaling calibration"""
        if self.temperature_scaler is None:
            return probs
        
        # Convert to logits
        probs = np.clip(probs, 1e-8, 1 - 1e-8)
        logits = np.log(probs / (1 - probs.sum(axis=1, keepdims=True) + probs))
        
        # Apply temperature scaling
        logits_tensor = torch.tensor(logits, dtype=torch.float32).cuda()
        with torch.no_grad():
            calibrated_logits = self.temperature_scaler(logits_tensor)
            calibrated_probs = F.softmax(calibrated_logits, dim=1).detach().cpu().numpy()
        
        return calibrated_probs
    
    def calibrate_isotonic(self, probs):
        """Apply isotonic regression calibration"""
        if not self.isotonic_regressors:
            return probs
        
        calibrated_probs = np.zeros_like(probs)
        
        for class_idx, iso_reg in enumerate(self.isotonic_regressors):
            calibrated_probs[:, class_idx] = iso_reg.transform(probs[:, class_idx])
        
        # Normalize to ensure probabilities sum to 1
        calibrated_probs = calibrated_probs / calibrated_probs.sum(axis=1, keepdims=True)
        
        return calibrated_probs
    
    def calibrate_ensemble(self, probs, method='temperature'):
        """Apply selected calibration method"""
        if method == 'temperature':
            return self.calibrate_temperature(probs)
        elif method == 'isotonic':
            return self.calibrate_isotonic(probs)
        elif method == 'combined':
            # First apply temperature, then isotonic
            temp_calibrated = self.calibrate_temperature(probs)
            return self.calibrate_isotonic(temp_calibrated)
        else:
            return probs


def analyze_calibration(probs, labels, n_bins=10):
    """Analyze calibration quality"""
    predictions = np.argmax(probs, axis=1)
    confidences = np.max(probs, axis=1)
    
    # Calculate calibration metrics
    accuracies = predictions == labels
    
    # Bin confidences
    bin_boundaries = np.linspace(0, 1, n_bins + 1)
    bin_lowers = bin_boundaries[:-1]
    bin_uppers = bin_boundaries[1:]
    
    bin_accuracies = []
    bin_confidences = []
    bin_counts = []
    
    for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
        in_bin = (confidences > bin_lower) & (confidences <= bin_upper)
        
        if in_bin.sum() > 0:
            bin_accuracies.append(accuracies[in_bin].mean())
            bin_confidences.append(confidences[in_bin].mean())
            bin_counts.append(in_bin.sum())
        else:
            bin_accuracies.append(0)
            bin_confidences.append((bin_lower + bin_upper) / 2)
            bin_counts.append(0)
    
    # Calculate ECE (Expected Calibration Error)
    ece = 0
    for i in range(n_bins):
        if bin_counts[i] > 0:
            ece += (bin_counts[i] / len(labels)) * abs(bin_accuracies[i] - bin_confidences[i])
    
    # Calculate MCE (Maximum Calibration Error)
    mce = max([abs(acc - conf) for acc, conf in zip(bin_accuracies, bin_confidences)])
    
    # Brier score (multiclass)
    # For multiclass, calculate mean squared error between predicted probs and one-hot labels
    n_classes = probs.shape[1]
    one_hot_labels = np.zeros((len(labels), n_classes))
    one_hot_labels[np.arange(len(labels)), labels] = 1
    brier = np.mean((probs - one_hot_labels) ** 2)
    
    return {
        'ece': ece,
        'mce': mce,
        'brier': brier,
        'bin_accuracies': bin_accuracies,
        'bin_confidences': bin_confidences,
        'bin_counts': bin_counts
    }


def evaluate_calibration():
    """Main calibration evaluation function"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/calibration_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'calibration.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 PROBABILITY CALIBRATION")
    logging.info("Addressing 193 high-confidence errors")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Model configurations (best ensemble)
    model_configs = [
        {
            'name': 'V17_Stable',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v17_stable_20250811_004457/best_model.pth',
            'd_model': 288,
            'n_heads': 18,
            'n_layers': 6,
            'seq_len': 5,
            'weight': 0.55
        },
        {
            'name': 'V18_Fixed',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v18_fixed_20250811_014911/best_model.pth',
            'd_model': 384,
            'n_heads': 24,
            'n_layers': 7,
            'seq_len': 6,
            'weight': 0.25
        },
        {
            'name': 'V22_Deep',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v22_deep_20250811_030913/best_model.pth',
            'd_model': 448,
            'n_heads': 28,
            'n_layers': 10,
            'seq_len': 9,
            'weight': 0.20
        }
    ]
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # Use validation set for calibration fitting
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Get validation predictions for calibration fitting
    logging.info("\n📊 Getting validation predictions for calibration...")
    val_probabilities = []
    val_labels = None
    
    for config in model_configs:
        logging.info(f"Processing {config['name']}...")
        
        # Load model
        model = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=0.0,
            seq_len=config['seq_len']
        ).to(device)
        
        checkpoint = torch.load(config['path'], map_location=device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Create dataset
        val_dataset = SequenceSleepDataset(
            val_files,
            seq_len=config['seq_len'],
            use_channels=3,
            max_samples_per_file=None
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=32,
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        # Get predictions
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in val_loader:
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get final predictions
        _, labels, probs = evaluator.get_final_predictions()
        val_probabilities.append(probs * config['weight'])
        
        if val_labels is None:
            val_labels = labels
    
    # Combine ensemble predictions
    val_ensemble_probs = np.sum(val_probabilities, axis=0)
    
    # Fit calibration methods
    logging.info("\n🔧 Fitting calibration methods...")
    calibrator = EnsembleCalibrator()
    
    # 1. Temperature Scaling
    temperature = calibrator.fit_temperature_scaling(val_ensemble_probs, val_labels)
    logging.info(f"Temperature scaling: T={temperature:.3f}")
    
    # 2. Isotonic Regression
    calibrator.fit_isotonic_regression(val_ensemble_probs, val_labels)
    logging.info("Isotonic regression: Fitted for each class")
    
    # Analyze validation calibration
    val_metrics_before = analyze_calibration(val_ensemble_probs, val_labels)
    val_metrics_temp = analyze_calibration(
        calibrator.calibrate_temperature(val_ensemble_probs), val_labels
    )
    val_metrics_iso = analyze_calibration(
        calibrator.calibrate_isotonic(val_ensemble_probs), val_labels
    )
    
    logging.info("\n📊 Validation Calibration Metrics:")
    logging.info(f"Before: ECE={val_metrics_before['ece']:.4f}, MCE={val_metrics_before['mce']:.4f}")
    logging.info(f"Temperature: ECE={val_metrics_temp['ece']:.4f}, MCE={val_metrics_temp['mce']:.4f}")
    logging.info(f"Isotonic: ECE={val_metrics_iso['ece']:.4f}, MCE={val_metrics_iso['mce']:.4f}")
    
    # Get test predictions
    logging.info("\n📊 Getting test predictions...")
    test_probabilities = []
    test_labels = None
    
    for config in model_configs:
        # Load model
        model = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=0.0,
            seq_len=config['seq_len']
        ).to(device)
        
        checkpoint = torch.load(config['path'], map_location=device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Create dataset
        test_dataset = SequenceSleepDataset(
            test_files,
            seq_len=config['seq_len'],
            use_channels=3,
            max_samples_per_file=None
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=32,
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        # Get predictions
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in test_loader:
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get final predictions
        _, labels, probs = evaluator.get_final_predictions()
        test_probabilities.append(probs * config['weight'])
        
        if test_labels is None:
            test_labels = labels
    
    # Combine test ensemble predictions
    test_ensemble_probs = np.sum(test_probabilities, axis=0)
    
    # Evaluate different calibration methods on test set
    logging.info("\n" + "="*80)
    logging.info("TEST RESULTS:")
    logging.info("="*80)
    
    # 1. Original (no calibration)
    test_preds_orig = np.argmax(test_ensemble_probs, axis=1)
    test_acc_orig = accuracy_score(test_labels, test_preds_orig)
    test_metrics_orig = analyze_calibration(test_ensemble_probs, test_labels)
    
    logging.info(f"\n1. Original Ensemble:")
    logging.info(f"   Accuracy: {test_acc_orig:.4f} ({test_acc_orig*100:.2f}%)")
    logging.info(f"   ECE: {test_metrics_orig['ece']:.4f}, MCE: {test_metrics_orig['mce']:.4f}")
    
    # 2. Temperature Scaling
    test_probs_temp = calibrator.calibrate_temperature(test_ensemble_probs)
    test_preds_temp = np.argmax(test_probs_temp, axis=1)
    test_acc_temp = accuracy_score(test_labels, test_preds_temp)
    test_metrics_temp = analyze_calibration(test_probs_temp, test_labels)
    
    logging.info(f"\n2. Temperature Scaling (T={temperature:.3f}):")
    logging.info(f"   Accuracy: {test_acc_temp:.4f} ({test_acc_temp*100:.2f}%)")
    logging.info(f"   ECE: {test_metrics_temp['ece']:.4f}, MCE: {test_metrics_temp['mce']:.4f}")
    
    # 3. Isotonic Regression
    test_probs_iso = calibrator.calibrate_isotonic(test_ensemble_probs)
    test_preds_iso = np.argmax(test_probs_iso, axis=1)
    test_acc_iso = accuracy_score(test_labels, test_preds_iso)
    test_metrics_iso = analyze_calibration(test_probs_iso, test_labels)
    
    logging.info(f"\n3. Isotonic Regression:")
    logging.info(f"   Accuracy: {test_acc_iso:.4f} ({test_acc_iso*100:.2f}%)")
    logging.info(f"   ECE: {test_metrics_iso['ece']:.4f}, MCE: {test_metrics_iso['mce']:.4f}")
    
    # 4. Combined (Temperature + Isotonic)
    test_probs_combined = calibrator.calibrate_ensemble(test_ensemble_probs, method='combined')
    test_preds_combined = np.argmax(test_probs_combined, axis=1)
    test_acc_combined = accuracy_score(test_labels, test_preds_combined)
    test_metrics_combined = analyze_calibration(test_probs_combined, test_labels)
    
    logging.info(f"\n4. Combined (Temp + Isotonic):")
    logging.info(f"   Accuracy: {test_acc_combined:.4f} ({test_acc_combined*100:.2f}%)")
    logging.info(f"   ECE: {test_metrics_combined['ece']:.4f}, MCE: {test_metrics_combined['mce']:.4f}")
    
    # Find best method
    all_accs = [test_acc_orig, test_acc_temp, test_acc_iso, test_acc_combined]
    all_names = ["Original", "Temperature", "Isotonic", "Combined"]
    best_acc = max(all_accs)
    best_method = all_names[all_accs.index(best_acc)]
    
    logging.info("\n" + "="*80)
    logging.info(f"🏆 BEST CALIBRATION: {best_method}")
    logging.info(f"   Test Accuracy: {best_acc:.4f} ({best_acc*100:.2f}%)")
    
    # Check progress to 90%
    if best_acc >= 0.90:
        logging.info("\n🎉🎉🎉 SUCCESS! 90% TARGET ACHIEVED! 🎉🎉🎉")
    else:
        gap = 0.90 - best_acc
        logging.info(f"\n📈 Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
    
    # Save calibrator
    calibrator_data = {
        'temperature': temperature,
        'method': best_method,
        'test_accuracy': float(best_acc),
        'test_ece': float(test_metrics_orig['ece']),
        'calibrated_ece': float(min([test_metrics_temp['ece'], test_metrics_iso['ece'], 
                                     test_metrics_combined['ece']]))
    }
    
    calibrator_file = os.path.join(log_dir, 'calibrator.json')
    with open(calibrator_file, 'w') as f:
        json.dump(calibrator_data, f, indent=2)
    
    logging.info(f"\n💾 Calibrator saved to {calibrator_file}")
    logging.info("="*80)
    
    return calibrator_data


if __name__ == "__main__":
    results = evaluate_calibration()