2025-08-12 02:38:22,182 - INFO - ================================================================================
2025-08-12 02:38:22,182 - INFO - 🚀 V8 Refined - Breakthrough to 90%
2025-08-12 02:38:22,182 - INFO - ================================================================================
2025-08-12 02:38:22,182 - INFO - Configuration: {
  "d_model": 256,
  "n_heads": 16,
  "n_layers": 6,
  "dropout": 0.15,
  "seq_len": 5,
  "batch_size": 32,
  "learning_rate": 0.0002,
  "weight_decay": 5e-05,
  "num_epochs": 80,
  "patience": 15
}
2025-08-12 02:38:22,360 - INFO - Device: cuda
2025-08-12 02:38:22,360 - INFO - Loading datasets...
2025-08-12 02:38:23,986 - INFO - 从 24 个文件加载了 25362 个epochs, 创建了 25266 个序列
2025-08-12 02:38:23,987 - INFO - 创建序列数据集: 25266个序列, 序列长度=5, 通道数=3, 总epochs=25362
2025-08-12 02:38:24,410 - INFO - 从 5 个文件加载了 4951 个epochs, 创建了 4931 个序列
2025-08-12 02:38:24,411 - INFO - 创建序列数据集: 4931个序列, 序列长度=5, 通道数=3, 总epochs=4951
2025-08-12 02:38:25,189 - INFO - 从 10 个文件加载了 11995 个epochs, 创建了 11955 个序列
2025-08-12 02:38:25,189 - INFO - 创建序列数据集: 11955个序列, 序列长度=5, 通道数=3, 总epochs=11995
2025-08-12 02:38:25,189 - INFO - Dataset sizes: Train=25266, Val=4931, Test=11955
2025-08-12 02:38:25,549 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-12 02:38:25,757 - INFO - Model parameters: 4,997,255
2025-08-12 02:38:27,284 - INFO - Starting V8 Refined training...
2025-08-12 02:38:27,285 - INFO - Goal: Break through 85.34% and reach 90%!

Epoch 1:   0%|          | 0/790 [00:00<?, ?it/s]
Epoch 1:   0%|          | 0/790 [00:00<?, ?it/s, loss=3.7677, N1_acc=0.250]
Epoch 1:   0%|          | 1/790 [00:00<11:27,  1.15it/s, loss=3.7677, N1_acc=0.250]
Epoch 1:   0%|          | 1/790 [00:00<11:27,  1.15it/s, loss=3.3362, N1_acc=0.429]
Epoch 1:   0%|          | 1/790 [00:00<11:27,  1.15it/s, loss=3.0080, N1_acc=0.273]
Epoch 1:   0%|          | 1/790 [00:00<11:27,  1.15it/s, loss=2.0581, N1_acc=0.250]
Epoch 1:   0%|          | 1/790 [00:00<11:27,  1.15it/s, loss=2.4749, N1_acc=0.357]
Epoch 1:   1%|          | 5/790 [00:00<02:00,  6.52it/s, loss=2.4749, N1_acc=0.357]
Epoch 1:   1%|          | 5/790 [00:01<02:00,  6.52it/s, loss=2.1747, N1_acc=0.357]
Epoch 1:   1%|          | 6/790 [00:01<02:17,  5.71it/s, loss=2.1747, N1_acc=0.357]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_refined.py", line 495, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_refined.py", line 392, in main
    train_loss, train_acc, train_f1 = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_refined.py", line 178, in train_epoch
    data = augmentation(data)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_refined.py", line 117, in __call__
    mask_len = random.randint(50, min(200, seq_len // 2))
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/random.py", line 338, in randint
    return self.randrange(a, b+1)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/random.py", line 316, in randrange
    raise ValueError("empty range for randrange() (%d, %d, %d)" % (istart, istop, width))
ValueError: empty range for randrange() (50, 2, -48)
