#!/usr/bin/env python3
"""
Check model dimensions from checkpoint files to determine correct d_model values.
"""

import torch
import os
from pathlib import Path

def check_checkpoint_dimensions(checkpoint_path):
    """Load checkpoint and extract model dimensions."""
    try:
        # Check if file exists
        if not os.path.exists(checkpoint_path):
            print(f"❌ File not found: {checkpoint_path}")
            return None
            
        # Load checkpoint
        checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
        
        # Get state dict (handle different checkpoint formats)
        if isinstance(checkpoint, dict):
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            elif 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            else:
                state_dict = checkpoint
        else:
            state_dict = checkpoint
        
        # Look for key layers to determine d_model
        d_model = None
        
        # Try different possible layer names
        key_patterns = [
            'feature_extractor.conv_layers.10.weight',
            'feature_extractor.conv_layers.10.bias',
            'feature_extractor.conv_layers.7.weight',
            'feature_extractor.conv_layers.7.bias',
            'feature_extractor.conv_layers.4.weight',
            'feature_extractor.conv_layers.4.bias',
            'classifier.0.weight',
            'classifier.weight',
        ]
        
        # Find the d_model from available keys
        for pattern in key_patterns:
            if pattern in state_dict:
                tensor_shape = state_dict[pattern].shape
                if 'weight' in pattern:
                    # For conv layers: (out_channels, in_channels, ...)
                    # For linear layers: (out_features, in_features)
                    if 'conv_layers.10' in pattern:
                        d_model = tensor_shape[0]  # out_channels
                    elif 'conv_layers.7' in pattern:
                        d_model = tensor_shape[1] if len(tensor_shape) > 1 else tensor_shape[0]  # in_channels for next layer
                    elif 'conv_layers.4' in pattern:
                        d_model = tensor_shape[1] if len(tensor_shape) > 1 else tensor_shape[0]
                    elif 'classifier' in pattern:
                        d_model = tensor_shape[1] if len(tensor_shape) > 1 else tensor_shape[0]  # in_features
                else:  # bias
                    d_model = tensor_shape[0]
                
                if d_model is not None:
                    print(f"  Found from '{pattern}': shape={tensor_shape}")
                    break
        
        # If still not found, try to find any conv layer
        if d_model is None:
            for key in state_dict.keys():
                if 'feature_extractor.conv_layers' in key and 'weight' in key:
                    tensor_shape = state_dict[key].shape
                    print(f"  Layer '{key}': shape={tensor_shape}")
                    if len(tensor_shape) >= 2:
                        # Take the output channels of the last conv layer we can find
                        layer_num = int(key.split('.')[2])
                        if layer_num >= 7:  # Focus on later layers
                            d_model = tensor_shape[0]
                            break
        
        # Print some sample keys for debugging
        print(f"  Sample keys in checkpoint:")
        sample_keys = [k for k in list(state_dict.keys())[:10] if 'feature_extractor' in k or 'classifier' in k]
        for key in sample_keys[:5]:
            print(f"    - {key}: {state_dict[key].shape}")
        
        return d_model
        
    except Exception as e:
        print(f"❌ Error loading {checkpoint_path}: {e}")
        return None

def main():
    """Check dimensions for all specified checkpoints."""
    
    # Define checkpoints to check
    checkpoints = [
        ("v17_stable", "../logs/v17_stable_20250811_004457/best_model.pth"),
        ("v18_fixed", "../logs/v18_fixed_20250811_014911/best_model.pth"),
        ("v22_deep", "../logs/v22_deep_20250811_030913/best_model.pth"),
        ("n1_specialist", "../logs/n1_specialist_20250811_171630/best_n1_model.pth"),
    ]
    
    print("=" * 70)
    print("Checking Model Dimensions from Checkpoints")
    print("=" * 70)
    
    results = {}
    
    for name, path in checkpoints:
        print(f"\n📦 Checking {name}:")
        print(f"   Path: {path}")
        
        d_model = check_checkpoint_dimensions(path)
        
        if d_model is not None:
            print(f"   ✅ d_model = {d_model}")
            results[name] = d_model
        else:
            print(f"   ⚠️  Could not determine d_model")
            results[name] = None
    
    # Summary
    print("\n" + "=" * 70)
    print("Summary of Model Dimensions:")
    print("=" * 70)
    
    for name, d_model in results.items():
        if d_model is not None:
            print(f"  {name:20s}: d_model = {d_model}")
        else:
            print(f"  {name:20s}: ❌ Unable to determine")
    
    print("\n" + "=" * 70)
    print("Recommended config values:")
    print("=" * 70)
    
    for name, d_model in results.items():
        if d_model is not None:
            print(f"\n# For {name}:")
            print(f"config['d_model'] = {d_model}")

if __name__ == "__main__":
    main()