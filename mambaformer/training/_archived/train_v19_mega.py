#!/usr/bin/env python3
"""
V19 MAMBAFORMER - MEGA Model for 90% accuracy
Massive architecture upgrade with advanced techniques
Target: 90% Accuracy, 82% Macro F1, 0.82 Kappa
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts, ReduceLROnPlateau
from torch.cuda.amp import autocast, GradScaler
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class ExtremeFocalLoss(nn.Module):
    """Extreme focal loss with N1 super focus"""
    def __init__(self, gamma=4.0):
        super().__init__()
        self.gamma = gamma
        # Extreme weights for N1
        self.class_weights = torch.tensor([3.5, 15.0, 1.0, 1.5, 3.0])  # Wake, N1, N2, N3, REM
        
    def forward(self, inputs, targets):
        batch_size, seq_len = targets.shape
        n_classes = inputs.shape[-1]
        
        # Flatten
        inputs_flat = inputs.reshape(-1, n_classes)
        targets_flat = targets.reshape(-1)
        
        # Apply extreme class weights
        ce_loss = F.cross_entropy(inputs_flat, targets_flat, weight=self.class_weights.to(inputs.device), reduction='none')
        
        # Focal loss modification with higher gamma
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        # Triple penalty for N1 misclassification
        n1_mask = (targets_flat == 1).float()
        n1_penalty = focal_loss * n1_mask * 3.0  # Triple penalty
        
        # Additional penalty for confusing N1 with N2
        pred_classes = torch.argmax(inputs_flat, dim=-1)
        n1_n2_confusion = ((targets_flat == 1) & (pred_classes == 2)).float()
        confusion_penalty = n1_n2_confusion * 5.0  # Heavy penalty for N1->N2 confusion
        
        total_loss = focal_loss + n1_penalty + confusion_penalty
        
        return total_loss.mean()


class AdaptiveTemporalConsistency(nn.Module):
    """Adaptive temporal consistency with sleep stage transitions"""
    def __init__(self, weight=0.3):
        super().__init__()
        self.weight = weight
        # Valid sleep transitions (more likely)
        self.valid_transitions = {
            0: [0, 1],      # Wake -> Wake, N1
            1: [0, 1, 2],   # N1 -> Wake, N1, N2
            2: [1, 2, 3, 4], # N2 -> N1, N2, N3, REM
            3: [2, 3],      # N3 -> N2, N3
            4: [2, 4]       # REM -> N2, REM
        }
        
    def forward(self, predictions, targets=None):
        if predictions.shape[1] <= 1:
            return 0
        
        pred_probs = torch.softmax(predictions, dim=-1)
        pred_classes = torch.argmax(predictions, dim=-1)
        
        # Basic temporal consistency
        temporal_diff = torch.abs(pred_probs[:, 1:] - pred_probs[:, :-1])
        consistency_loss = temporal_diff.mean() * self.weight
        
        # Penalize invalid transitions
        if targets is not None:
            transition_penalty = 0
            for i in range(targets.shape[1] - 1):
                current_stage = targets[:, i]
                next_pred = pred_classes[:, i+1]
                
                # Check for invalid transitions
                invalid_mask = torch.zeros_like(current_stage, dtype=torch.float32)
                for stage in range(5):
                    stage_mask = (current_stage == stage)
                    if stage_mask.any():
                        # Penalize predictions outside valid transitions
                        valid_next = torch.tensor(self.valid_transitions[stage], device=targets.device)
                        is_valid = torch.isin(next_pred[stage_mask], valid_next)
                        invalid_mask[stage_mask] = (~is_valid).float()
                
                transition_penalty += invalid_mask.float().mean() * 0.5
            
            return consistency_loss + transition_penalty
        
        return consistency_loss


class MixupAugmentation(nn.Module):
    """Mixup augmentation for better generalization"""
    def __init__(self, alpha=0.2):
        super().__init__()
        self.alpha = alpha
    
    def forward(self, data, labels):
        if self.training and np.random.random() > 0.5:
            batch_size = data.size(0)
            
            # Generate mixup weights
            lam = np.random.beta(self.alpha, self.alpha)
            
            # Random permutation
            index = torch.randperm(batch_size).to(data.device)
            
            # Mixup data and labels
            mixed_data = lam * data + (1 - lam) * data[index]
            
            return mixed_data, labels, labels[index], lam
        
        return data, labels, None, 1.0


class UltraLossV19(nn.Module):
    """Ultra combined loss for V19"""
    def __init__(self):
        super().__init__()
        self.focal = ExtremeFocalLoss(gamma=4.0)
        self.temporal = AdaptiveTemporalConsistency(weight=0.3)
        self.ce = nn.CrossEntropyLoss()
        
    def forward(self, predictions, targets, targets2=None, lam=1.0):
        # Handle mixup
        if targets2 is not None:
            focal_loss = lam * self.focal(predictions, targets) + (1 - lam) * self.focal(predictions, targets2)
            temporal_loss = self.temporal(predictions, targets)
        else:
            focal_loss = self.focal(predictions, targets)
            temporal_loss = self.temporal(predictions, targets)
        
        # Additional standard CE for stability
        pred_flat = predictions.reshape(-1, predictions.shape[-1])
        target_flat = targets.reshape(-1)
        ce_loss = self.ce(pred_flat, target_flat) * 0.05
        
        return focal_loss + temporal_loss + ce_loss


class SuperAugmentedDataset(Dataset):
    """Dataset with extreme augmentation and N1 oversampling"""
    def __init__(self, base_dataset, augment=True, n1_oversample=3.0, mixup_alpha=0.2):
        self.base_dataset = base_dataset
        self.augment = augment
        self.mixup = MixupAugmentation(alpha=mixup_alpha)
        
        # Find N1 samples for extreme oversampling
        self.n1_indices = []
        self.wake_indices = []
        self.other_indices = []
        
        for i in range(len(base_dataset)):
            _, label = base_dataset[i]
            if 1 in label:  # N1 present
                self.n1_indices.append(i)
            elif 0 in label:  # Wake present
                self.wake_indices.append(i)
            else:
                self.other_indices.append(i)
        
        # Create extremely balanced dataset
        self.indices = self.other_indices.copy()
        
        # Extreme N1 oversampling
        n1_samples = int(len(self.n1_indices) * n1_oversample)
        if n1_samples > 0:
            n1_selected = np.random.choice(self.n1_indices, n1_samples, replace=True)
            self.indices.extend(n1_selected.tolist())
        
        # Also oversample Wake
        wake_samples = int(len(self.wake_indices) * 1.5)
        if wake_samples > 0:
            wake_selected = np.random.choice(self.wake_indices, wake_samples, replace=True)
            self.indices.extend(wake_selected.tolist())
        
        np.random.shuffle(self.indices)
        
    def __len__(self):
        return len(self.indices)
    
    def __getitem__(self, idx):
        real_idx = self.indices[idx]
        data, label = self.base_dataset[real_idx]
        
        if self.augment and np.random.random() > 0.3:
            # Aggressive augmentation
            aug_type = np.random.choice(['noise', 'scale', 'shift', 'dropout', 'all'])
            
            if aug_type in ['noise', 'all']:
                # Gaussian noise
                noise_level = np.random.uniform(0.02, 0.1)
                noise = torch.randn_like(data) * noise_level
                data = data + noise
            
            if aug_type in ['scale', 'all']:
                # Random amplitude scaling
                scale = np.random.uniform(0.85, 1.15)
                data = data * scale
            
            if aug_type in ['shift', 'all']:
                # Random time shift
                shift = np.random.randint(-2, 3)
                if shift != 0:
                    data = torch.roll(data, shift, dims=-1)
            
            if aug_type in ['dropout', 'all']:
                # Channel dropout
                if np.random.random() > 0.7:
                    channel_idx = np.random.randint(0, data.shape[0])
                    data[channel_idx] = data[channel_idx] * np.random.uniform(0.3, 0.7)
        
        return data, label


def train_v19():
    """Main training function for V19 MEGA model"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/v19_mega_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V19 MEGA - FINAL PUSH TO 90%")
    logging.info("="*80)
    logging.info("🎯 Targets: Accuracy ≥90%, Macro F1 ≥82%, Kappa ≥0.82")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # V19 MEGA Configuration
    config = {
        'd_model': 512,      # MEGA size
        'n_heads': 32,       # More heads
        'n_layers': 8,       # Deep architecture
        'dropout': 0.22,     # Higher dropout
        'seq_len': 8,        # Longer context
        'batch_size': 16,    # Smaller batch for large model
        'learning_rate': 5e-5,
        'num_epochs': 60,    
        'patience': 25,      # More patience
        'gradient_clip': 0.4,
        'weight_decay': 0.025,
        'n1_oversample': 3.0,  # Extreme N1 oversampling
        'warmup_epochs': 4,
        'mixed_precision': True,
        'mixup_alpha': 0.2
    }
    
    logging.info("\n📋 V19 MEGA Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    logging.info(f"\n📂 Data Split:")
    logging.info(f"  Train: {len(train_files)} files")
    logging.info(f"  Val: {len(val_files)} files")
    logging.info(f"  Test: {len(test_files)} files")
    
    # Create datasets
    logging.info("\n📊 Loading datasets with extreme augmentation...")
    
    base_train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    # Super augmented training with extreme N1 oversampling
    train_dataset = SuperAugmentedDataset(
        base_train_dataset, 
        augment=True,
        n1_oversample=config['n1_oversample'],
        mixup_alpha=config['mixup_alpha']
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    logging.info(f"Train samples: {len(train_dataset)} (with extreme oversampling)")
    logging.info(f"Val samples: {val_dataset.total_epochs} epochs")
    logging.info(f"Test samples: {test_dataset.total_epochs} epochs")
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create MEGA model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"\nModel Parameters: {total_params:,}")
    logging.info(f"Model Size: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # Loss and optimizer
    criterion = UltraLossV19()
    mixup_fn = MixupAugmentation(alpha=config['mixup_alpha'])
    
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # Dual schedulers
    scheduler1 = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=15,
        T_mult=2,
        eta_min=1e-7
    )
    
    scheduler2 = ReduceLROnPlateau(
        optimizer,
        mode='max',
        factor=0.5,
        patience=5
    )
    
    # Mixed precision
    scaler = GradScaler() if config['mixed_precision'] else None
    
    # Training variables
    best_val_acc = 0
    best_val_f1 = 0
    best_val_kappa = 0
    best_model_state = None
    patience_counter = 0
    training_history = []
    
    logging.info("\n🏋️ Starting Training...")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        epoch_start = datetime.now()
        
        # Warmup
        if epoch < config['warmup_epochs']:
            warmup_lr = config['learning_rate'] * (epoch + 1) / config['warmup_epochs']
            for param_group in optimizer.param_groups:
                param_group['lr'] = warmup_lr
            logging.info(f"Warmup LR: {warmup_lr:.2e}")
        
        # Training phase
        model.train()
        train_loss = 0
        train_steps = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config["num_epochs"]}')
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)
            
            # Apply mixup
            data, labels, labels2, lam = mixup_fn(data, labels)
            
            optimizer.zero_grad()
            
            if config['mixed_precision'] and scaler is not None:
                with autocast():
                    outputs, _ = model(data)
                    loss = criterion(outputs, labels, labels2, lam)
                
                scaler.scale(loss).backward()
                scaler.unscale_(optimizer)
                torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
                scaler.step(optimizer)
                scaler.update()
            else:
                outputs, _ = model(data)
                loss = criterion(outputs, labels, labels2, lam)
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
                optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            current_lr = optimizer.param_groups[0]['lr']
            pbar.set_postfix({'loss': f'{loss.item():.4f}', 'lr': f'{current_lr:.2e}'})
        
        avg_train_loss = train_loss / train_steps
        
        # Step scheduler
        if epoch >= config['warmup_epochs']:
            scheduler1.step()
        
        # Validation phase
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(val_loader, desc='Validating', leave=False):
                data = data.to(device)
                
                if config['mixed_precision']:
                    with autocast():
                        outputs, _ = model(data)
                else:
                    outputs, _ = model(data)
                
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Calculate metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics['accuracy']
        val_f1 = val_metrics['macro_f1']
        val_kappa = val_metrics['kappa']
        
        # Step scheduler2
        scheduler2.step(val_acc)
        
        # Get per-class performance
        n1_f1 = val_metrics['per_class_metrics']['N1']['f1']
        rem_f1 = val_metrics['per_class_metrics']['REM']['f1']
        
        epoch_time = (datetime.now() - epoch_start).total_seconds()
        
        # Log results
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Loss: {avg_train_loss:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f} ({val_acc*100:.2f}%)")
        logging.info(f"  Val F1: {val_f1:.4f} ({val_f1*100:.2f}%)")
        logging.info(f"  Val Kappa: {val_kappa:.4f}")
        logging.info(f"  N1 F1: {n1_f1:.4f} | REM F1: {rem_f1:.4f}")
        logging.info(f"  LR: {current_lr:.2e}")
        logging.info(f"  Time: {epoch_time:.1f}s")
        
        # Check targets
        targets_met = []
        if val_acc >= 0.90:
            targets_met.append("ACC")
        if val_f1 >= 0.82:
            targets_met.append("F1")
        if val_kappa >= 0.82:
            targets_met.append("KAPPA")
        
        if targets_met:
            logging.info(f"  🎯 Targets met: {', '.join(targets_met)}")
        
        training_history.append({
            'epoch': epoch + 1,
            'train_loss': avg_train_loss,
            'val_acc': val_acc,
            'val_f1': val_f1,
            'val_kappa': val_kappa,
            'n1_f1': n1_f1,
            'rem_f1': rem_f1,
            'lr': current_lr,
            'time': epoch_time
        })
        
        # Save best model
        improved = False
        if val_acc > best_val_acc:
            improved = True
        elif val_acc == best_val_acc and val_f1 > best_val_f1:
            improved = True
        
        if improved:
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_kappa = val_kappa
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': best_model_state,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_kappa': val_kappa,
                'config': config
            }
            
            checkpoint_path = os.path.join(log_dir, 'best_model.pth')
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"  💾 Saved best model")
            
            # Save if all targets met
            if len(targets_met) == 3:
                success_path = os.path.join(log_dir, f'SUCCESS_epoch{epoch+1}.pth')
                torch.save(checkpoint, success_path)
                logging.info(f"  🎉 SUCCESS! All targets achieved!")
                
                if epoch >= 20:  # At least 20 epochs
                    logging.info("  ✅ Stopping - targets achieved!")
                    break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"\n⏹️ Early stopping triggered")
                break
    
    # Final test evaluation
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        logging.info("\n" + "="*80)
        logging.info("📊 FINAL TEST EVALUATION")
        logging.info("="*80)
        
        model.eval()
        test_evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        test_evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc='Testing'):
                data = data.to(device)
                
                if config['mixed_precision']:
                    with autocast():
                        outputs, _ = model(data)
                else:
                    outputs, _ = model(data)
                
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    test_evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get test metrics
        test_metrics = test_evaluator.evaluate()
        test_acc = test_metrics['accuracy']
        test_f1 = test_metrics['macro_f1']
        test_kappa = test_metrics['kappa']
        
        # Get confusion matrix
        final_preds, final_labels, _ = test_evaluator.get_final_predictions()
        cm = confusion_matrix(final_labels, final_preds)
        
        # Results
        logging.info("\n🎯 FINAL TEST RESULTS:")
        logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
        logging.info(f"  Macro F1: {test_f1:.4f} ({test_f1*100:.2f}%)")
        logging.info(f"  Kappa: {test_kappa:.4f}")
        
        # Target achievement
        logging.info("\n🎯 Target Achievement (90% / 82% / 0.82):")
        all_achieved = True
        
        if test_acc >= 0.90:
            logging.info(f"  ✅ Accuracy: {test_acc:.4f} ≥ 0.90")
        else:
            logging.info(f"  ❌ Accuracy: {test_acc:.4f} < 0.90 (gap: {0.90-test_acc:.4f})")
            all_achieved = False
        
        if test_f1 >= 0.82:
            logging.info(f"  ✅ Macro F1: {test_f1:.4f} ≥ 0.82")
        else:
            logging.info(f"  ❌ Macro F1: {test_f1:.4f} < 0.82 (gap: {0.82-test_f1:.4f})")
            all_achieved = False
        
        if test_kappa >= 0.82:
            logging.info(f"  ✅ Kappa: {test_kappa:.4f} ≥ 0.82")
        else:
            logging.info(f"  ❌ Kappa: {test_kappa:.4f} < 0.82 (gap: {0.82-test_kappa:.4f})")
            all_achieved = False
        
        # Per-class metrics
        logging.info("\n📊 Per-Class Performance:")
        class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        for class_name in class_names:
            metrics = test_metrics['per_class_metrics'][class_name]
            logging.info(f"{class_name}: P={metrics['precision']*100:.1f}%, R={metrics['recall']*100:.1f}%, F1={metrics['f1']*100:.1f}%")
        
        # Save results
        final_results = {
            'timestamp': timestamp,
            'config': config,
            'best_val_acc': float(best_val_acc),
            'best_val_f1': float(best_val_f1),
            'best_val_kappa': float(best_val_kappa),
            'test_acc': float(test_acc),
            'test_f1': float(test_f1),
            'test_kappa': float(test_kappa),
            'confusion_matrix': cm.tolist(),
            'per_class_metrics': test_metrics['per_class_metrics'],
            'training_history': training_history,
            'targets_achieved': all_achieved
        }
        
        # Convert numpy types
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            return obj
        
        final_results = convert_numpy(final_results)
        
        results_file = os.path.join(log_dir, 'final_results.json')
        with open(results_file, 'w') as f:
            json.dump(final_results, f, indent=2)
        
        logging.info(f"\n💾 Results saved to {results_file}")
        
        if all_achieved:
            logging.info("\n" + "="*80)
            logging.info("🎉🎉🎉 SUCCESS! ALL TARGETS ACHIEVED! 🎉🎉🎉")
            logging.info("V19 MEGA Model Ready for ICASSP 2026!")
            logging.info("="*80)
        else:
            logging.info("\n📈 Continue optimization with ensemble methods...")
    
    return final_results


if __name__ == "__main__":
    results = train_v19()