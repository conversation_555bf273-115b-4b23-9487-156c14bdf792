#!/usr/bin/env python3
"""
Stage 2: 继续训练 - 从检查点恢复
目标：达到85%以上准确率
"""

import os
import sys
import json
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR
from datetime import datetime
from pathlib import Path
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
from tqdm import tqdm
import traceback

# 添加项目路径
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')

from mambaformer.utils.sequence_dataset import SequenceSleepDataset
from mambaformer.models.progressive_mambaformer_v1_fixed import (
    ProgressiveMAMBAFORMER_V1_Fixed,
    SequentialFocalLoss,
    TemporalConsistencyLoss
)


class CrossModalAttentionModule(nn.Module):
    """跨模态注意力模块 - Stage 2的核心组件"""
    def __init__(self, d_model=512, n_heads=8, dropout=0.1):
        super().__init__()
        
        # 多头自注意力层
        self.attention = nn.MultiheadAttention(
            d_model, 
            n_heads,
            dropout=dropout,
            batch_first=True
        )
        
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 2, d_model)
        )
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 可学习的融合权重
        self.alpha = nn.Parameter(torch.zeros(1))
        
    def forward(self, x):
        # 保存原始输入
        residual = x
        
        # 自注意力
        attn_out, _ = self.attention(x, x, x)
        x = self.norm1(x + self.dropout(attn_out))
        
        # 前馈网络
        ffn_out = self.ffn(x)
        x = self.norm2(x + self.dropout(ffn_out))
        
        # 通过可学习权重融合原始特征和增强特征
        alpha = torch.sigmoid(self.alpha)
        enhanced = (1 - alpha) * residual + alpha * x
        
        return enhanced


def train_stage2_continue(config):
    """继续Stage 2训练"""
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = Path(f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_continue_{timestamp}")
    exp_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(exp_dir / 'training.log'),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 Stage 2: 继续训练 - 目标85%准确率")
    logging.info("="*80)
    logging.info(f"配置: {config}")
    logging.info(f"Device: {device}")
    
    # 数据集路径
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"
    
    # 文件列表（与Stage 1相同的划分）
    train_files = [
        "SC4041E0.npz", "SC4042E0.npz", "SC4051E0.npz", "SC4052E0.npz",
        "SC4061E0.npz", "SC4062E0.npz", "SC4081E0.npz", "SC4082E0.npz",
        "SC4091E0.npz", "SC4092E0.npz", "SC4101E0.npz", "SC4102E0.npz",
        "SC4111E0.npz", "SC4112E0.npz", "SC4121E0.npz", "SC4122E0.npz",
        "SC4131E0.npz", "SC4141E0.npz", "SC4142E0.npz", "SC4151E0.npz",
        "SC4152E0.npz", "SC4161E0.npz", "SC4162E0.npz", "SC4171E0.npz",
        "SC4172E0.npz", "SC4181E0.npz", "SC4182E0.npz", "SC4191E0.npz", "SC4192E0.npz"
    ]
    val_files = [
        "SC4021E0.npz", "SC4022E0.npz", "SC4031E0.npz",
        "SC4032E0.npz", "SC4071E0.npz", "SC4072E0.npz",
    ]
    test_files = [
        "SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"
    ]
    
    # 完整路径
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # 加载数据集
    logging.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=True,
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=2,  # 减少worker数量避免内存问题
        pin_memory=True,
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=2,
        pin_memory=True,
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=2,
        pin_memory=True,
    )
    
    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")
    
    # 创建基础模型（Stage 1）
    base_model = ProgressiveMAMBAFORMER_V1_Fixed(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)
    
    # 创建跨模态注意力模块
    attention_module = CrossModalAttentionModule(
        d_model=config["d_model"],
        n_heads=config["attention_heads"],
        dropout=config["dropout"]
    ).to(device)
    
    # 尝试加载之前保存的最佳模型
    previous_checkpoint = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_stable_20250815_223349/best_model.pth"
    start_epoch = 0
    best_acc = 0.0
    
    if os.path.exists(previous_checkpoint):
        logging.info(f"加载之前的检查点: {previous_checkpoint}")
        checkpoint = torch.load(previous_checkpoint, map_location=device)
        base_model.load_state_dict(checkpoint['base_model_state_dict'])
        attention_module.load_state_dict(checkpoint['attention_module_state_dict'])
        start_epoch = checkpoint['epoch'] + 1
        best_acc = checkpoint['test_accuracy']
        logging.info(f"✅ 从epoch {start_epoch}继续训练，当前最佳准确率: {best_acc*100:.2f}%")
    else:
        # 加载Stage 1的最佳模型
        stage1_model_path = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_simple_20250815_042107/best_model.pth"
        if os.path.exists(stage1_model_path):
            logging.info(f"加载Stage 1模型: {stage1_model_path}")
            checkpoint = torch.load(stage1_model_path, map_location=device)
            base_model.load_state_dict(checkpoint['model_state_dict'])
            logging.info("✅ 成功加载Stage 1模型")
        else:
            logging.error(f"Stage 1模型不存在: {stage1_model_path}")
            return
    
    # 冻结基础模型的大部分参数
    for name, param in base_model.named_parameters():
        if "transformer_encoder.layers.10" in name or \
           "transformer_encoder.layers.11" in name or \
           "classifier" in name or \
           "auxiliary_head" in name:
            param.requires_grad = True
        else:
            param.requires_grad = False
    
    # 统计参数
    base_trainable = sum(p.numel() for p in base_model.parameters() if p.requires_grad)
    attention_params = sum(p.numel() for p in attention_module.parameters())
    total_params = sum(p.numel() for p in base_model.parameters()) + attention_params
    
    logging.info(f"基础模型可训练参数: {base_trainable:,}")
    logging.info(f"注意力模块参数: {attention_params:,}")
    logging.info(f"总参数: {total_params:,}")
    logging.info(f"可训练比例: {(base_trainable + attention_params) / total_params * 100:.2f}%")
    
    # 定义前向传播函数
    def forward_pass(data, model, attention_module):
        """组合前向传播"""
        batch_size, seq_len, channels, time_steps = data.shape
        
        # 重塑数据
        x_reshaped = data.view(batch_size * seq_len, channels, time_steps)
        
        # 特征提取
        features = model.feature_extractor(x_reshaped)
        features = features.view(batch_size, seq_len, config["d_model"])
        
        # 位置编码
        features = features.transpose(0, 1)
        features = model.pos_encoder(features)
        features = features.transpose(0, 1)
        
        # 通过前10层transformer
        for i in range(10):
            features = model.transformer_encoder.layers[i](features)
        
        # 应用跨模态注意力增强（Stage 2的核心）
        features = attention_module(features)
        
        # 通过最后2层transformer
        for i in range(10, 12):
            features = model.transformer_encoder.layers[i](features)
        
        # 分类
        main_output = model.classifier(features)
        aux_output = model.auxiliary_head(features)
        
        return main_output, aux_output
    
    # 评估函数
    def evaluate(model, attention_module, dataloader):
        model.eval()
        attention_module.eval()
        
        all_preds = []
        all_labels = []
        
        with torch.no_grad():
            for data, labels in tqdm(dataloader, desc="评估"):
                data = data.to(device)
                data = data.permute(0, 1, 3, 2)  # (B, S, C, T)
                
                main_output, _ = forward_pass(data, model, attention_module)
                
                preds = main_output.argmax(dim=-1)
                
                all_preds.append(preds.cpu().numpy())
                all_labels.append(labels.numpy())
        
        all_preds = np.concatenate(all_preds).flatten()
        all_labels = np.concatenate(all_labels).flatten()
        
        accuracy = accuracy_score(all_labels, all_preds)
        f1 = f1_score(all_labels, all_preds, average='weighted')
        
        return {'accuracy': accuracy, 'f1': f1}
    
    # 设置优化器
    optimizer_groups = [
        {'params': base_model.parameters(), 'lr': config["base_lr"]},
        {'params': attention_module.parameters(), 'lr': config["attention_lr"]}
    ]
    optimizer = optim.AdamW(optimizer_groups, weight_decay=config["weight_decay"])
    
    # 学习率调度器
    scheduler = CosineAnnealingLR(
        optimizer, 
        T_max=config["num_epochs"] - start_epoch,
        eta_min=1e-6
    )
    
    # 损失函数
    criterion_main = SequentialFocalLoss(alpha=1, gamma=2)
    criterion_temporal = TemporalConsistencyLoss(weight=0.1)
    
    # 训练循环
    patience_counter = 0
    
    try:
        for epoch in range(start_epoch, config["num_epochs"]):
            logging.info(f"\n{'='*60}")
            logging.info(f"Epoch {epoch+1}/{config['num_epochs']}")
            
            # 训练模式
            base_model.train()
            attention_module.train()
            
            train_loss = 0
            train_preds = []
            train_labels = []
            
            progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}")
            for batch_idx, (data, labels) in enumerate(progress_bar):
                try:
                    data = data.to(device)
                    labels = labels.to(device)
                    data = data.permute(0, 1, 3, 2)
                    
                    optimizer.zero_grad()
                    
                    # 前向传播
                    main_output, aux_output = forward_pass(data, base_model, attention_module)
                    
                    # 计算损失
                    main_loss = criterion_main(main_output, labels)
                    temporal_loss = criterion_temporal(main_output)
                    
                    # 辅助损失
                    aux_target = (labels[:, 1:] != labels[:, :-1]).long()
                    aux_target = F.pad(aux_target, (0, 1), value=0)
                    aux_loss = F.cross_entropy(
                        aux_output.reshape(-1, 2),
                        aux_target.reshape(-1),
                        weight=torch.tensor([1.0, 3.0]).to(device)
                    )
                    
                    # 总损失
                    loss = main_loss + 0.05 * temporal_loss + 0.1 * aux_loss
                    
                    # 反向传播
                    loss.backward()
                    
                    # 梯度裁剪
                    torch.nn.utils.clip_grad_norm_(
                        list(base_model.parameters()) + list(attention_module.parameters()),
                        config["gradient_clip"]
                    )
                    
                    optimizer.step()
                    
                    # 记录
                    train_loss += loss.item()
                    preds = main_output.argmax(dim=-1)
                    train_preds.append(preds.cpu().numpy())
                    train_labels.append(labels.cpu().numpy())
                    
                    # 更新进度条
                    if batch_idx % 10 == 0:
                        alpha = torch.sigmoid(attention_module.alpha).item()
                        progress_bar.set_postfix({
                            'loss': f"{loss.item():.4f}",
                            'alpha': f"{alpha:.4f}"
                        })
                    
                    # 定期清理缓存避免内存问题
                    if batch_idx % 100 == 0:
                        torch.cuda.empty_cache()
                        
                except Exception as e:
                    logging.error(f"批次{batch_idx}训练出错: {str(e)}")
                    logging.error(traceback.format_exc())
                    continue
            
            # 计算训练指标
            train_preds = np.concatenate(train_preds).flatten()
            train_labels = np.concatenate(train_labels).flatten()
            train_acc = accuracy_score(train_labels, train_preds)
            avg_loss = train_loss / len(train_loader)
            
            # 验证
            val_metrics = evaluate(base_model, attention_module, val_loader)
            test_metrics = evaluate(base_model, attention_module, test_loader)
            
            # 记录结果
            logging.info(f"训练 - Loss: {avg_loss:.4f}, Acc: {train_acc*100:.2f}%")
            logging.info(f"验证 - Acc: {val_metrics['accuracy']*100:.2f}%, F1: {val_metrics['f1']:.4f}")
            logging.info(f"测试 - Acc: {test_metrics['accuracy']*100:.2f}%, F1: {test_metrics['f1']:.4f}")
            logging.info(f"注意力权重(alpha): {torch.sigmoid(attention_module.alpha).item():.4f}")
            
            # 保存最佳模型
            if test_metrics['accuracy'] > best_acc:
                best_acc = test_metrics['accuracy']
                patience_counter = 0
                
                torch.save({
                    'epoch': epoch,
                    'base_model_state_dict': base_model.state_dict(),
                    'attention_module_state_dict': attention_module.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'test_accuracy': best_acc,
                    'config': config
                }, exp_dir / 'best_model.pth')
                
                logging.info(f"✅ 保存最佳模型，准确率: {best_acc*100:.2f}%")
                
                # 如果达到目标
                if best_acc >= 0.85:
                    logging.info(f"🎉🎉🎉 达到85%目标！测试准确率: {best_acc*100:.2f}%")
                    
                    # 保存结果摘要
                    summary = {
                        'stage': 'Stage 2 - Cross-Modal Attention',
                        'final_accuracy': best_acc,
                        'final_f1': test_metrics['f1'],
                        'epochs_trained': epoch + 1,
                        'config': config
                    }
                    
                    with open(exp_dir / 'result_summary.json', 'w') as f:
                        json.dump(summary, f, indent=2)
                    
                    return best_acc
            else:
                patience_counter += 1
            
            # 早停
            if patience_counter >= config["patience"]:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
            
            # 更新学习率
            scheduler.step()
            
            # 清理GPU缓存
            torch.cuda.empty_cache()
            
    except Exception as e:
        logging.error(f"训练过程出错: {str(e)}")
        logging.error(traceback.format_exc())
        return best_acc
    
    # 最终结果
    logging.info(f"\n{'='*60}")
    logging.info(f"训练完成! 最佳测试准确率: {best_acc*100:.2f}%")
    logging.info(f"相对Stage 1的改进: {(best_acc - 0.88)*100:+.2f}%")
    
    return best_acc


def main():
    # 配置
    config = {
        # 模型配置
        'd_model': 512,
        'n_heads': 32,
        'n_layers': 12,
        'dropout': 0.15,
        'seq_len': 7,
        
        # Stage 2特定配置
        'attention_heads': 8,
        
        # 训练配置（调整以提高稳定性）
        'batch_size': 12,  # 减小批次大小
        'base_lr': 5e-6,   # 更保守的学习率
        'attention_lr': 2e-5,
        'num_epochs': 30,  # 更多epochs
        'gradient_clip': 0.5,
        'weight_decay': 1e-5,
        'patience': 7,
    }
    
    # 开始训练
    final_acc = train_stage2_continue(config)
    
    return final_acc


if __name__ == "__main__":
    main()