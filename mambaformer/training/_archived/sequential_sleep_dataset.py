"""
简化的睡眠数据集类，用于快速测试
"""
import torch
from torch.utils.data import Dataset
import numpy as np
import os
import pickle


class SequentialSleepDataset(Dataset):
    """序列化睡眠数据集"""
    
    def __init__(self, data_dir, split='train', seq_len=7, max_samples=None):
        self.data_dir = data_dir
        self.split = split
        self.seq_len = seq_len
        
        # 加载预处理的数据
        self.data = []
        self.labels = []
        
        # 根据split确定文件
        if split == 'train':
            files = ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09',
                    '10', '11', '12', '13', '14', '15', '16', '17', '18', '19',
                    '20', '21', '22', '23', '24', '25', '26', '27', '28']
        elif split == 'val':
            files = ['29', '30', '31', '32', '33', '34']
        else:  # test
            files = ['35', '36', '37', '38']
        
        # 加载数据
        for file_idx in files:
            data_path = os.path.join(data_dir, f"SC4{file_idx}1E0_data.pkl")
            label_path = os.path.join(data_dir, f"SC4{file_idx}1E0_labels.pkl")
            
            if os.path.exists(data_path) and os.path.exists(label_path):
                with open(data_path, 'rb') as f:
                    data = pickle.load(f)
                with open(label_path, 'rb') as f:
                    labels = pickle.load(f)
                
                # 简单处理
                if len(data) > 0:
                    # 确保数据维度正确
                    if isinstance(data, list):
                        data = np.array(data)
                    if isinstance(labels, list):
                        labels = np.array(labels)
                    
                    # 创建序列
                    for i in range(len(data) - seq_len + 1):
                        if max_samples and len(self.data) >= max_samples:
                            break
                        
                        seq_data = data[i:i+seq_len]
                        seq_labels = labels[i:i+seq_len]
                        
                        # 检查形状
                        if seq_data.shape[0] == seq_len:
                            self.data.append(seq_data)
                            self.labels.append(seq_labels)
                
                if max_samples and len(self.data) >= max_samples:
                    break
        
        # 如果没有加载到数据，创建随机数据用于测试
        if len(self.data) == 0:
            print(f"Warning: No data loaded for {split}, creating random data for testing")
            n_samples = 1000 if split == 'train' else 200
            for i in range(n_samples):
                # 创建随机数据 (seq_len, time_steps, channels)
                seq_data = np.random.randn(seq_len, 3000, 3).astype(np.float32)
                seq_labels = np.random.randint(0, 5, seq_len)
                self.data.append(seq_data)
                self.labels.append(seq_labels)
        
        self.data = np.array(self.data)
        self.labels = np.array(self.labels)
        
        print(f"Loaded {len(self.data)} sequences for {split}")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        # 返回 (seq_len, time_steps, channels), (seq_len,)
        data = torch.FloatTensor(self.data[idx])
        labels = torch.LongTensor(self.labels[idx])
        return data, labels