"""
Quick evaluation of existing models
"""

import os
import sys
import json
import torch
import numpy as np
from tqdm import tqdm

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator

def main():
    print("🚀 Quick Model Evaluation")
    print("=" * 80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    # Load test data
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    # Create test dataset
    test_dataset = SequenceSleepDataset(test_files, seq_len=5, use_channels=3)
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=32, shuffle=False,
        num_workers=4, pin_memory=True
    )
    
    print(f"Test dataset size: {len(test_dataset)}")
    
    # Models to evaluate
    models_to_eval = {
        'V7': {
            'path': '../../checkpoints/sequential_v7_balanced.pth',
            'config': {'d_model': 128, 'n_heads': 8, 'n_layers': 4, 'dropout': 0.1, 'seq_len': 5}
        },
        'V8': {
            'path': '../../checkpoints/sequential_v8_enhanced.pth', 
            'config': {'d_model': 128, 'n_heads': 8, 'n_layers': 4, 'dropout': 0.1, 'seq_len': 5}
        }
    }
    
    # Evaluate each model
    for model_name, model_info in models_to_eval.items():
        if not os.path.exists(model_info['path']):
            print(f"\n❌ {model_name} not found at {model_info['path']}")
            continue
            
        print(f"\n📊 Evaluating {model_name}...")
        
        # Load model
        model = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            **model_info['config']
        ).to(device)
        
        model.load_state_dict(torch.load(model_info['path'], map_location=device))
        model.eval()
        
        # Evaluate
        evaluator = EpochLevelEvaluator(seq_len=5, n_classes=5)
        
        with torch.no_grad():
            batch_start_idx = 0
            
            for data, labels in tqdm(test_loader):
                data = data.to(device)
                labels = labels.to(device)
                
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                        else:
                            start_indices.append(seq_idx)
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get metrics
        metrics = evaluator.evaluate()
        
        print(f"\n{model_name} Results:")
        print(f"  Accuracy: {metrics['accuracy']:.4f}")
        print(f"  Macro F1: {metrics['macro_f1']:.4f}")
        print(f"  Kappa: {metrics['kappa']:.4f}")
        print(f"  Per-class F1:")
        for class_name, class_metrics in metrics['per_class_metrics'].items():
            print(f"    {class_name}: {class_metrics['f1']:.4f}")
    
    print("\n🎯 Target Performance:")
    print("  Accuracy: 0.87")
    print("  Macro F1: 0.80")
    print("  Kappa: 0.80")
    
    print("\n✅ Evaluation complete!")

if __name__ == "__main__":
    main()