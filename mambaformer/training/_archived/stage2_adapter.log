Warning: No data loaded for train, creating random data for testing
Loaded 1000 sequences for train
Warning: No data loaded for val, creating random data for testing
Loaded 200 sequences for val
Warning: No data loaded for test, creating random data for testing
Loaded 200 sequences for test

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:06,  1.76it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 13.39it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 15.76it/s]
WARNING:__main__:⚠️ 初始准确率偏离Stage 1基线！

Training:   0%|          | 0/63 [00:00<?, ?it/s]
Training:   0%|          | 0/63 [00:00<?, ?it/s, loss=5.1120, scale=0.0000, lr=0.000050]
Training:   2%|▏         | 1/63 [00:00<00:22,  2.77it/s, loss=5.1120, scale=0.0000, lr=0.000050]
Training:  10%|▉         | 6/63 [00:00<00:03, 15.08it/s, loss=5.1120, scale=0.0000, lr=0.000050]
Training:  16%|█▌        | 10/63 [00:00<00:02, 21.80it/s, loss=5.1120, scale=0.0000, lr=0.000050]
Training:  16%|█▌        | 10/63 [00:00<00:02, 21.80it/s, loss=4.9055, scale=0.0000, lr=0.000050]
Training:  22%|██▏       | 14/63 [00:00<00:01, 26.78it/s, loss=4.9055, scale=0.0000, lr=0.000050]
Training:  30%|███       | 19/63 [00:00<00:01, 31.28it/s, loss=4.9055, scale=0.0000, lr=0.000050]
Training:  30%|███       | 19/63 [00:00<00:01, 31.28it/s, loss=4.7164, scale=0.0000, lr=0.000050]
Training:  38%|███▊      | 24/63 [00:00<00:01, 34.29it/s, loss=4.7164, scale=0.0000, lr=0.000050]
Training:  46%|████▌     | 29/63 [00:01<00:00, 36.34it/s, loss=4.7164, scale=0.0000, lr=0.000050]
Training:  46%|████▌     | 29/63 [00:01<00:00, 36.34it/s, loss=4.5774, scale=0.0000, lr=0.000050]
Training:  54%|█████▍    | 34/63 [00:01<00:00, 37.64it/s, loss=4.5774, scale=0.0000, lr=0.000050]
Training:  62%|██████▏   | 39/63 [00:01<00:00, 38.50it/s, loss=4.5774, scale=0.0000, lr=0.000050]
Training:  62%|██████▏   | 39/63 [00:01<00:00, 38.50it/s, loss=4.7415, scale=0.0000, lr=0.000050]
Training:  68%|██████▊   | 43/63 [00:01<00:00, 38.60it/s, loss=4.7415, scale=0.0000, lr=0.000050]
Training:  75%|███████▍  | 47/63 [00:01<00:00, 38.77it/s, loss=4.7415, scale=0.0000, lr=0.000050]
Training:  75%|███████▍  | 47/63 [00:01<00:00, 38.77it/s, loss=4.7541, scale=0.0000, lr=0.000050]
Training:  81%|████████  | 51/63 [00:01<00:00, 39.06it/s, loss=4.7541, scale=0.0000, lr=0.000050]
Training:  87%|████████▋ | 55/63 [00:01<00:00, 39.22it/s, loss=4.7541, scale=0.0000, lr=0.000050]
Training:  95%|█████████▌| 60/63 [00:01<00:00, 40.88it/s, loss=4.7541, scale=0.0000, lr=0.000050]
Training:  95%|█████████▌| 60/63 [00:01<00:00, 40.88it/s, loss=4.5812, scale=0.0000, lr=0.000050]
Training: 100%|██████████| 63/63 [00:01<00:00, 32.23it/s, loss=4.5812, scale=0.0000, lr=0.000050]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.74it/s]
Evaluating:  62%|██████▏   | 8/13 [00:00<00:00, 30.38it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 29.61it/s]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.77it/s]
Evaluating:  62%|██████▏   | 8/13 [00:00<00:00, 30.43it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 29.37it/s]

Training:   0%|          | 0/63 [00:00<?, ?it/s]
Training:   0%|          | 0/63 [00:00<?, ?it/s, loss=5.1159, scale=0.0000, lr=0.000050]
Training:   2%|▏         | 1/63 [00:00<00:14,  4.40it/s, loss=5.1159, scale=0.0000, lr=0.000050]
Training:   8%|▊         | 5/63 [00:00<00:03, 17.63it/s, loss=5.1159, scale=0.0000, lr=0.000050]
Training:  14%|█▍        | 9/63 [00:00<00:02, 25.35it/s, loss=5.1159, scale=0.0000, lr=0.000050]
Training:  14%|█▍        | 9/63 [00:00<00:02, 25.35it/s, loss=5.1238, scale=0.0000, lr=0.000050]
Training:  22%|██▏       | 14/63 [00:00<00:01, 31.18it/s, loss=5.1238, scale=0.0000, lr=0.000050]
Training:  30%|███       | 19/63 [00:00<00:01, 34.43it/s, loss=5.1238, scale=0.0000, lr=0.000050]
Training:  30%|███       | 19/63 [00:00<00:01, 34.43it/s, loss=4.7244, scale=0.0000, lr=0.000050]
Training:  37%|███▋      | 23/63 [00:00<00:01, 35.78it/s, loss=4.7244, scale=0.0000, lr=0.000050]
Training:  43%|████▎     | 27/63 [00:00<00:00, 36.90it/s, loss=4.7244, scale=0.0000, lr=0.000050]
Training:  43%|████▎     | 27/63 [00:00<00:00, 36.90it/s, loss=5.1135, scale=0.0000, lr=0.000050]
Training:  51%|█████     | 32/63 [00:00<00:00, 39.42it/s, loss=5.1135, scale=0.0000, lr=0.000050]
Training:  59%|█████▊    | 37/63 [00:01<00:00, 41.19it/s, loss=5.1135, scale=0.0000, lr=0.000050]
Training:  59%|█████▊    | 37/63 [00:01<00:00, 41.19it/s, loss=5.0840, scale=0.0000, lr=0.000050]
Training:  67%|██████▋   | 42/63 [00:01<00:00, 41.43it/s, loss=5.0840, scale=0.0000, lr=0.000050]
Training:  75%|███████▍  | 47/63 [00:01<00:00, 41.56it/s, loss=5.0840, scale=0.0000, lr=0.000050]
Training:  75%|███████▍  | 47/63 [00:01<00:00, 41.56it/s, loss=4.6180, scale=0.0000, lr=0.000050]
Training:  83%|████████▎ | 52/63 [00:01<00:00, 42.01it/s, loss=4.6180, scale=0.0000, lr=0.000050]
Training:  90%|█████████ | 57/63 [00:01<00:00, 42.99it/s, loss=4.6180, scale=0.0000, lr=0.000050]
Training:  90%|█████████ | 57/63 [00:01<00:00, 42.99it/s, loss=4.6720, scale=0.0000, lr=0.000050]
Training:  98%|█████████▊| 62/63 [00:01<00:00, 44.66it/s, loss=4.6720, scale=0.0000, lr=0.000050]
Training: 100%|██████████| 63/63 [00:01<00:00, 35.71it/s, loss=4.6720, scale=0.0000, lr=0.000050]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.63it/s]
Evaluating:  62%|██████▏   | 8/13 [00:00<00:00, 29.49it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 28.79it/s]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.42it/s]
Evaluating:  62%|██████▏   | 8/13 [00:00<00:00, 28.47it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 27.73it/s]

Training:   0%|          | 0/63 [00:00<?, ?it/s]
Training:   0%|          | 0/63 [00:00<?, ?it/s, loss=5.1508, scale=0.0000, lr=0.000049]
Training:   2%|▏         | 1/63 [00:00<00:13,  4.50it/s, loss=5.1508, scale=0.0000, lr=0.000049]
Training:   8%|▊         | 5/63 [00:00<00:03, 18.00it/s, loss=5.1508, scale=0.0000, lr=0.000049]
Training:  14%|█▍        | 9/63 [00:00<00:02, 25.75it/s, loss=5.1508, scale=0.0000, lr=0.000049]
Training:  14%|█▍        | 9/63 [00:00<00:02, 25.75it/s, loss=4.6416, scale=0.0000, lr=0.000049]
Training:  21%|██        | 13/63 [00:00<00:01, 30.38it/s, loss=4.6416, scale=0.0000, lr=0.000049]
Training:  27%|██▋       | 17/63 [00:00<00:01, 33.32it/s, loss=4.6416, scale=0.0000, lr=0.000049]
Training:  27%|██▋       | 17/63 [00:00<00:01, 33.32it/s, loss=5.2606, scale=0.0000, lr=0.000049]
Training:  33%|███▎      | 21/63 [00:00<00:01, 35.32it/s, loss=5.2606, scale=0.0000, lr=0.000049]
Training:  41%|████▏     | 26/63 [00:00<00:00, 37.13it/s, loss=5.2606, scale=0.0000, lr=0.000049]
Training:  41%|████▏     | 26/63 [00:00<00:00, 37.13it/s, loss=4.6043, scale=0.0000, lr=0.000049]
Training:  49%|████▉     | 31/63 [00:00<00:00, 38.26it/s, loss=4.6043, scale=0.0000, lr=0.000049]
Training:  56%|█████▌    | 35/63 [00:01<00:00, 38.70it/s, loss=4.6043, scale=0.0000, lr=0.000049]
Training:  62%|██████▏   | 39/63 [00:01<00:00, 38.91it/s, loss=4.6043, scale=0.0000, lr=0.000049]
Training:  62%|██████▏   | 39/63 [00:01<00:00, 38.91it/s, loss=5.1340, scale=0.0000, lr=0.000049]
Training:  68%|██████▊   | 43/63 [00:01<00:00, 39.02it/s, loss=5.1340, scale=0.0000, lr=0.000049]
Training:  75%|███████▍  | 47/63 [00:01<00:00, 39.23it/s, loss=5.1340, scale=0.0000, lr=0.000049]
Training:  75%|███████▍  | 47/63 [00:01<00:00, 39.23it/s, loss=4.6984, scale=0.0000, lr=0.000049]
Training:  83%|████████▎ | 52/63 [00:01<00:00, 39.43it/s, loss=4.6984, scale=0.0000, lr=0.000049]
Training:  89%|████████▉ | 56/63 [00:01<00:00, 39.13it/s, loss=4.6984, scale=0.0000, lr=0.000049]
Training:  95%|█████████▌| 60/63 [00:01<00:00, 38.94it/s, loss=4.6984, scale=0.0000, lr=0.000049]
Training:  95%|█████████▌| 60/63 [00:01<00:00, 38.94it/s, loss=4.6286, scale=0.0000, lr=0.000049]
Training: 100%|██████████| 63/63 [00:01<00:00, 33.51it/s, loss=4.6286, scale=0.0000, lr=0.000049]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:03,  3.64it/s]
Evaluating:  46%|████▌     | 6/13 [00:00<00:00, 18.71it/s]
Evaluating:  92%|█████████▏| 12/13 [00:00<00:00, 31.32it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 22.10it/s]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.02it/s]
Evaluating:  62%|██████▏   | 8/13 [00:00<00:00, 26.80it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 25.49it/s]

Training:   0%|          | 0/63 [00:00<?, ?it/s]
Training:   0%|          | 0/63 [00:00<?, ?it/s, loss=4.8613, scale=0.0000, lr=0.000047]
Training:   2%|▏         | 1/63 [00:00<00:18,  3.42it/s, loss=4.8613, scale=0.0000, lr=0.000047]
Training:   8%|▊         | 5/63 [00:00<00:03, 15.27it/s, loss=4.8613, scale=0.0000, lr=0.000047]
Training:  16%|█▌        | 10/63 [00:00<00:02, 24.58it/s, loss=4.8613, scale=0.0000, lr=0.000047]
Training:  16%|█▌        | 10/63 [00:00<00:02, 24.58it/s, loss=5.1433, scale=0.0000, lr=0.000047]
Training:  24%|██▍       | 15/63 [00:00<00:01, 30.41it/s, loss=5.1433, scale=0.0000, lr=0.000047]
Training:  32%|███▏      | 20/63 [00:00<00:01, 33.71it/s, loss=5.1433, scale=0.0000, lr=0.000047]
Training:  32%|███▏      | 20/63 [00:00<00:01, 33.71it/s, loss=4.9427, scale=0.0000, lr=0.000047]
Training:  40%|███▉      | 25/63 [00:00<00:01, 35.93it/s, loss=4.9427, scale=0.0000, lr=0.000047]
Training:  48%|████▊     | 30/63 [00:01<00:00, 37.51it/s, loss=4.9427, scale=0.0000, lr=0.000047]
Training:  48%|████▊     | 30/63 [00:01<00:00, 37.51it/s, loss=4.7076, scale=0.0000, lr=0.000047]
Training:  56%|█████▌    | 35/63 [00:01<00:00, 38.55it/s, loss=4.7076, scale=0.0000, lr=0.000047]
Training:  63%|██████▎   | 40/63 [00:01<00:00, 39.57it/s, loss=4.7076, scale=0.0000, lr=0.000047]
Training:  63%|██████▎   | 40/63 [00:01<00:00, 39.57it/s, loss=5.0494, scale=0.0000, lr=0.000047]
Training:  71%|███████▏  | 45/63 [00:01<00:00, 39.88it/s, loss=5.0494, scale=0.0000, lr=0.000047]
Training:  79%|███████▉  | 50/63 [00:01<00:00, 40.21it/s, loss=5.0494, scale=0.0000, lr=0.000047]
Training:  79%|███████▉  | 50/63 [00:01<00:00, 40.21it/s, loss=4.7028, scale=0.0000, lr=0.000047]
Training:  87%|████████▋ | 55/63 [00:01<00:00, 40.46it/s, loss=4.7028, scale=0.0000, lr=0.000047]
Training:  95%|█████████▌| 60/63 [00:01<00:00, 41.58it/s, loss=4.7028, scale=0.0000, lr=0.000047]
Training:  95%|█████████▌| 60/63 [00:01<00:00, 41.58it/s, loss=5.0222, scale=0.0000, lr=0.000047]
Training: 100%|██████████| 63/63 [00:01<00:00, 33.55it/s, loss=5.0222, scale=0.0000, lr=0.000047]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.13it/s]
Evaluating:  62%|██████▏   | 8/13 [00:00<00:00, 27.55it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 26.18it/s]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.24it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 24.99it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 26.03it/s]

Training:   0%|          | 0/63 [00:00<?, ?it/s]
Training:   0%|          | 0/63 [00:00<?, ?it/s, loss=4.4862, scale=0.0000, lr=0.000045]
Training:   2%|▏         | 1/63 [00:00<00:17,  3.55it/s, loss=4.4862, scale=0.0000, lr=0.000045]
Training:   8%|▊         | 5/63 [00:00<00:03, 15.36it/s, loss=4.4862, scale=0.0000, lr=0.000045]
Training:  14%|█▍        | 9/63 [00:00<00:02, 22.98it/s, loss=4.4862, scale=0.0000, lr=0.000045]
Training:  14%|█▍        | 9/63 [00:00<00:02, 22.98it/s, loss=4.5853, scale=0.0000, lr=0.000045]
Training:  21%|██        | 13/63 [00:00<00:01, 28.22it/s, loss=4.5853, scale=0.0000, lr=0.000045]
Training:  29%|██▊       | 18/63 [00:00<00:01, 32.49it/s, loss=4.5853, scale=0.0000, lr=0.000045]
Training:  29%|██▊       | 18/63 [00:00<00:01, 32.49it/s, loss=4.5173, scale=0.0000, lr=0.000045]
Training:  35%|███▍      | 22/63 [00:00<00:01, 34.49it/s, loss=4.5173, scale=0.0000, lr=0.000045]
Training:  43%|████▎     | 27/63 [00:00<00:00, 37.34it/s, loss=4.5173, scale=0.0000, lr=0.000045]
Training:  43%|████▎     | 27/63 [00:01<00:00, 37.34it/s, loss=5.1922, scale=0.0000, lr=0.000045]
Training:  51%|█████     | 32/63 [00:01<00:00, 39.01it/s, loss=5.1922, scale=0.0000, lr=0.000045]
Training:  59%|█████▊    | 37/63 [00:01<00:00, 39.68it/s, loss=5.1922, scale=0.0000, lr=0.000045]
Training:  59%|█████▊    | 37/63 [00:01<00:00, 39.68it/s, loss=5.1532, scale=0.0000, lr=0.000045]
Training:  67%|██████▋   | 42/63 [00:01<00:00, 40.58it/s, loss=5.1532, scale=0.0000, lr=0.000045]
Training:  75%|███████▍  | 47/63 [00:01<00:00, 40.89it/s, loss=5.1532, scale=0.0000, lr=0.000045]
Training:  75%|███████▍  | 47/63 [00:01<00:00, 40.89it/s, loss=4.7026, scale=0.0000, lr=0.000045]
Training:  83%|████████▎ | 52/63 [00:01<00:00, 40.76it/s, loss=4.7026, scale=0.0000, lr=0.000045]
Training:  90%|█████████ | 57/63 [00:01<00:00, 41.36it/s, loss=4.7026, scale=0.0000, lr=0.000045]
Training:  90%|█████████ | 57/63 [00:01<00:00, 41.36it/s, loss=4.2744, scale=0.0000, lr=0.000045]
Training:  98%|█████████▊| 62/63 [00:01<00:00, 42.67it/s, loss=4.2744, scale=0.0000, lr=0.000045]
Training: 100%|██████████| 63/63 [00:01<00:00, 33.82it/s, loss=4.2744, scale=0.0000, lr=0.000045]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:03,  3.87it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 23.04it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 24.43it/s]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:03,  3.85it/s]
Evaluating:  62%|██████▏   | 8/13 [00:00<00:00, 26.39it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 24.73it/s]
