"""
MAMBAFORMER训练脚本 - 兼容版本
适配Sleep-EDF数据格式
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
import os
import glob
import json
import logging
from pathlib import Path
from datetime import datetime
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from tqdm import tqdm
import time
import warnings
warnings.filterwarnings('ignore')

# 导入模型
from models.mambaformer_net import MambaFormerSleepNet
from models.epoch_cmt import Epoch_Cross_Transformer_Network

class SleepEDFDataset(Dataset):
    """Sleep-EDF数据集加载器"""
    
    def __init__(self, data_path, subject_list):
        self.data_path = data_path
        self.subject_list = subject_list
        
        # 加载所有数据
        self.eeg_data = []
        self.eog_data = []
        self.labels = []
        
        for subject_id in subject_list:
            # 加载EEG数据
            eeg_file = os.path.join(data_path, f'x{int(subject_id):02d}.h5')
            if os.path.exists(eeg_file):
                with h5py.File(eeg_file, 'r') as f:
                    eeg = f['data'][:]
                    self.eeg_data.append(eeg)
                    
                # 加载EOG数据
                eog_file = os.path.join(data_path, f'eog{int(subject_id):02d}.h5')
                with h5py.File(eog_file, 'r') as f:
                    eog = f['data'][:]
                    self.eog_data.append(eog)
                    
                # 加载标签
                label_file = os.path.join(data_path, f'y{int(subject_id):02d}.h5')
                with h5py.File(label_file, 'r') as f:
                    labels = f['data'][:]
                    self.labels.append(labels)
        
        # 合并所有数据
        if len(self.eeg_data) > 0:
            self.eeg_data = np.concatenate(self.eeg_data, axis=0)
            self.eog_data = np.concatenate(self.eog_data, axis=0)
            self.labels = np.concatenate(self.labels, axis=0)
            
            # 转换为张量
            self.eeg_data = torch.tensor(self.eeg_data, dtype=torch.float32)
            self.eog_data = torch.tensor(self.eog_data, dtype=torch.float32)
            self.labels = torch.tensor(self.labels, dtype=torch.long)
            
            # 标准化
            self.eeg_data = (self.eeg_data - self.eeg_data.mean(dim=1, keepdim=True)) / (self.eeg_data.std(dim=1, keepdim=True) + 1e-8)
            self.eog_data = (self.eog_data - self.eog_data.mean(dim=1, keepdim=True)) / (self.eog_data.std(dim=1, keepdim=True) + 1e-8)
            
            print(f"加载数据集: {len(subject_list)} subjects, {len(self.labels)} epochs")
            print(f"EEG形状: {self.eeg_data.shape}, EOG形状: {self.eog_data.shape}")
            print(f"标签分布: {torch.bincount(self.labels)}")
        else:
            raise ValueError("没有找到数据文件")
    
    def __len__(self):
        return len(self.labels)
    
    def __getitem__(self, idx):
        # 添加通道维度 [1, seq_len]
        eeg = self.eeg_data[idx].unsqueeze(0)
        eog = self.eog_data[idx].unsqueeze(0)
        label = self.labels[idx]
        
        return eeg, eog, label

def setup_logging(log_dir, experiment_name):
    """设置日志系统"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"{experiment_name}_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info(f"日志文件: {log_file}")
    
    return logger

def train_epoch(model, train_loader, optimizer, device, epoch, logger):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    pbar = tqdm(train_loader, desc=f"Epoch {epoch+1} [Train]")
    
    for batch_idx, (eeg_data, eog_data, labels) in enumerate(pbar):
        eeg_data = eeg_data.to(device)
        eog_data = eog_data.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        outputs = model(eeg_data, eog_data, stage="both")
        losses = model.compute_loss(outputs, labels, stage="both")
        loss = losses['total_loss']
        
        # 反向传播
        loss.backward()
        optimizer.step()
        
        # 统计
        total_loss += loss.item()
        
        # 获取预测结果
        if 'fine_probs' in outputs:
            pred = outputs['fine_probs'].argmax(dim=1)
        elif 'logits' in outputs:
            pred = outputs['logits'].argmax(dim=1)
        else:
            pred = outputs['fine_logits'].argmax(dim=1)
            
        correct += (pred == labels).sum().item()
        total += labels.size(0)
        
        # 更新进度条
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })
    
    avg_loss = total_loss / len(train_loader)
    avg_acc = correct / total
    
    return avg_loss, avg_acc

def validate(model, val_loader, device, logger):
    """验证模型"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for eeg_data, eog_data, labels in tqdm(val_loader, desc="Validating"):
            eeg_data = eeg_data.to(device)
            eog_data = eog_data.to(device)
            labels = labels.to(device)
            
            # 前向传播
            outputs = model(eeg_data, eog_data, stage="fine")
            losses = model.compute_loss(outputs, labels, stage="fine")
            total_loss += losses['total_loss'].item()
            
            # 获取预测
            if 'fine_probs' in outputs:
                pred = outputs['fine_probs'].argmax(dim=1)
            elif 'logits' in outputs:
                pred = outputs['logits'].argmax(dim=1)
            else:
                pred = outputs['fine_logits'].argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    avg_loss = total_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    
    return avg_loss, accuracy, all_preds, all_labels

def main():
    # 配置
    config = {
        'data_path': './processed_data_fixed',  # 使用修复后的数据
        'train_subjects': ['1', '2', '3'],
        'val_subjects': ['4'],
        'test_subjects': ['5'],
        'batch_size': 64,
        'epochs': 50,  # 增加训练轮数
        'lr': 5e-4,   # 增加学习率
        'patience': 10,
        'experiment_name': 'mambaformer_sleepEDF_fixed',
        'log_dir': './log',
        'save_dir': './checkpoints'
    }
    
    # 创建目录
    os.makedirs(config['log_dir'], exist_ok=True)
    os.makedirs(config['save_dir'], exist_ok=True)
    
    # 设置日志
    logger = setup_logging(config['log_dir'], config['experiment_name'])
    logger.info("🧪 MAMBAFORMER Sleep Stage Classification")
    logger.info(f"配置: {json.dumps(config, indent=2)}")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 创建数据集
    train_dataset = SleepEDFDataset(config['data_path'], config['train_subjects'])
    val_dataset = SleepEDFDataset(config['data_path'], config['val_subjects'])
    test_dataset = SleepEDFDataset(config['data_path'], config['test_subjects'])
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=2)
    test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=2)
    
    logger.info(f"训练集: {len(train_dataset)} 样本, {len(train_loader)} 批次")
    logger.info(f"验证集: {len(val_dataset)} 样本, {len(val_loader)} 批次")
    logger.info(f"测试集: {len(test_dataset)} 样本, {len(test_loader)} 批次")
    
    # 创建模型
    model = MambaFormerSleepNet(
        d_model=64,
        num_mambaformer_layers=4,
        nhead=8,
        window_size=50,
        use_progressive=True,
        dropout=0.1
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"模型参数量: {total_params:,}")
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=config['lr'], weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=3)
    
    # 训练
    best_val_acc = 0
    patience_counter = 0
    history = {'train_loss': [], 'train_acc': [], 'val_loss': [], 'val_acc': []}
    
    start_time = time.time()
    
    for epoch in range(config['epochs']):
        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, device, epoch, logger)
        
        # 验证
        val_loss, val_acc, _, _ = validate(model, val_loader, device, logger)
        
        # 更新历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        
        # 学习率调度
        scheduler.step(val_loss)
        
        # 记录
        logger.info(f"Epoch {epoch+1}/{config['epochs']}: "
                   f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} | "
                   f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'val_acc': val_acc
            }, os.path.join(config['save_dir'], f"{config['experiment_name']}_best.pth"))
            logger.info(f"✓ 新的最佳验证准确率: {best_val_acc:.4f}")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logger.info("早停")
                break
    
    training_time = time.time() - start_time
    logger.info(f"训练完成，用时: {training_time/60:.2f}分钟")
    
    # 测试
    logger.info("\n📊 测试集评估")
    test_loss, test_acc, test_preds, test_labels = validate(model, test_loader, device, logger)
    
    # 分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(test_labels, test_preds, target_names=class_names, digits=4)
    
    logger.info(f"测试准确率: {test_acc:.4f}")
    logger.info(f"\n分类报告:\n{report}")
    
    # 保存结果
    results = {
        'config': config,
        'best_val_acc': best_val_acc,
        'test_acc': test_acc,
        'history': history,
        'classification_report': report
    }
    
    results_file = os.path.join(config['log_dir'], f"{config['experiment_name']}_results.json")
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ 实验完成！结果保存在: {results_file}")

if __name__ == '__main__':
    main()