#!/usr/bin/env python3
"""
V18 MAMBAFORMER - Fixed version for 90% accuracy
Simplified architecture with aggressive N1 handling
Target: 90% Accuracy, 82% Macro F1, 0.82 Kappa
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class FocalLossN1(nn.Module):
    """Focal loss with extreme N1 focus"""
    def __init__(self, gamma=3.0):
        super().__init__()
        self.gamma = gamma
        # Extreme class weights - focus on N1
        self.class_weights = torch.tensor([3.0, 10.0, 1.0, 1.5, 2.5])  # Wake, N1, N2, N3, REM
        
    def forward(self, inputs, targets):
        batch_size, seq_len = targets.shape
        n_classes = inputs.shape[-1]
        
        # Flatten
        inputs_flat = inputs.reshape(-1, n_classes)
        targets_flat = targets.reshape(-1)
        
        # Apply class weights
        ce_loss = F.cross_entropy(inputs_flat, targets_flat, weight=self.class_weights.to(inputs.device), reduction='none')
        
        # Focal loss modification
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        # Extra penalty for N1 misclassification
        n1_mask = (targets_flat == 1).float()
        n1_penalty = focal_loss * n1_mask * 2.0  # Double penalty for N1 errors
        
        total_loss = focal_loss + n1_penalty
        
        return total_loss.mean()


class TemporalConsistencyLoss(nn.Module):
    """Temporal consistency with transition awareness"""
    def __init__(self, weight=0.25):
        super().__init__()
        self.weight = weight
        
    def forward(self, predictions):
        if predictions.shape[1] <= 1:
            return 0
        
        pred_probs = torch.softmax(predictions, dim=-1)
        
        # Penalize large changes between consecutive predictions
        temporal_diff = torch.abs(pred_probs[:, 1:] - pred_probs[:, :-1])
        
        # Focus on reducing rapid transitions
        max_diff = temporal_diff.max(dim=-1)[0]  # Maximum change per timestep
        
        loss = max_diff.mean() * self.weight
        
        return loss


class LabelSmoothingCrossEntropy(nn.Module):
    """Label smoothing for better generalization"""
    def __init__(self, smoothing=0.1):
        super().__init__()
        self.smoothing = smoothing
        
    def forward(self, pred, target):
        n_classes = pred.shape[-1]
        
        # One-hot encode target
        one_hot = torch.zeros_like(pred).scatter(-1, target.unsqueeze(-1), 1)
        
        # Apply label smoothing
        smoothed = one_hot * (1 - self.smoothing) + self.smoothing / n_classes
        
        # Calculate loss
        log_probs = F.log_softmax(pred, dim=-1)
        loss = -(smoothed * log_probs).sum(dim=-1).mean()
        
        return loss


class CombinedLossV18(nn.Module):
    """Combined loss for V18"""
    def __init__(self):
        super().__init__()
        self.focal = FocalLossN1(gamma=3.0)
        self.temporal = TemporalConsistencyLoss(weight=0.25)
        self.label_smooth = LabelSmoothingCrossEntropy(smoothing=0.05)
        
    def forward(self, predictions, targets):
        focal_loss = self.focal(predictions, targets)
        temporal_loss = self.temporal(predictions)
        smooth_loss = self.label_smooth(predictions, targets) * 0.1
        
        return focal_loss + temporal_loss + smooth_loss


class AugmentedDataset(Dataset):
    """Dataset with data augmentation and N1 oversampling"""
    def __init__(self, base_dataset, augment=True, n1_oversample=2.5):
        self.base_dataset = base_dataset
        self.augment = augment
        
        # Find N1 samples for oversampling
        self.n1_indices = []
        self.other_indices = []
        
        for i in range(len(base_dataset)):
            _, label = base_dataset[i]
            if 1 in label:  # N1 present
                self.n1_indices.append(i)
            else:
                self.other_indices.append(i)
        
        # Create balanced dataset with N1 oversampling
        n1_samples = int(len(self.n1_indices) * n1_oversample)
        self.indices = self.other_indices.copy()
        
        # Add oversampled N1
        if n1_samples > 0:
            n1_selected = np.random.choice(self.n1_indices, n1_samples, replace=True)
            self.indices.extend(n1_selected.tolist())
        
        np.random.shuffle(self.indices)
        
    def __len__(self):
        return len(self.indices)
    
    def __getitem__(self, idx):
        real_idx = self.indices[idx]
        data, label = self.base_dataset[real_idx]
        
        if self.augment and np.random.random() > 0.5:
            # Gaussian noise
            noise_level = np.random.uniform(0.02, 0.06)
            noise = torch.randn_like(data) * noise_level
            data = data + noise
            
            # Random amplitude scaling
            scale = np.random.uniform(0.95, 1.05)
            data = data * scale
        
        return data, label


def train_v18():
    """Main training function for V18"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/v18_fixed_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V18 FIXED - PUSH TO 90%")
    logging.info("="*80)
    logging.info("🎯 Targets: Accuracy ≥90%, Macro F1 ≥82%, Kappa ≥0.82")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # V18 Configuration
    config = {
        'd_model': 384,      # Large model
        'n_heads': 24,       
        'n_layers': 7,       # 7 layers
        'dropout': 0.18,     
        'seq_len': 6,        # Moderate context
        'batch_size': 24,    
        'learning_rate': 6e-5,
        'num_epochs': 50,    
        'patience': 15,      
        'gradient_clip': 0.7,
        'weight_decay': 0.015,
        'n1_oversample': 2.5,
        'warmup_epochs': 2
    }
    
    logging.info("\n📋 V18 Fixed Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    logging.info(f"\n📂 Data Split:")
    logging.info(f"  Train: {len(train_files)} files")
    logging.info(f"  Val: {len(val_files)} files")
    logging.info(f"  Test: {len(test_files)} files")
    
    # Create datasets
    logging.info("\n📊 Loading datasets with N1 oversampling...")
    
    base_train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    # Augmented training with N1 oversampling
    train_dataset = AugmentedDataset(
        base_train_dataset, 
        augment=True,
        n1_oversample=config['n1_oversample']
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    logging.info(f"Train samples: {len(train_dataset)} (with N1 oversampling)")
    logging.info(f"Val samples: {val_dataset.total_epochs} epochs")
    logging.info(f"Test samples: {test_dataset.total_epochs} epochs")
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"\nModel Parameters: {total_params:,}")
    logging.info(f"Model Size: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # Loss and optimizer
    criterion = CombinedLossV18()
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # Scheduler
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=10,
        T_mult=2,
        eta_min=1e-6
    )
    
    # Training variables
    best_val_acc = 0
    best_val_f1 = 0
    best_val_kappa = 0
    best_model_state = None
    patience_counter = 0
    training_history = []
    
    logging.info("\n🏋️ Starting Training...")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        epoch_start = datetime.now()
        
        # Warmup
        if epoch < config['warmup_epochs']:
            warmup_lr = config['learning_rate'] * (epoch + 1) / config['warmup_epochs']
            for param_group in optimizer.param_groups:
                param_group['lr'] = warmup_lr
            logging.info(f"Warmup LR: {warmup_lr:.2e}")
        
        # Training phase
        model.train()
        train_loss = 0
        train_steps = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config["num_epochs"]}')
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            outputs, _ = model(data)
            loss = criterion(outputs, labels)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
            optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / train_steps
        
        # Step scheduler
        if epoch >= config['warmup_epochs']:
            scheduler.step()
        
        # Validation phase
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(val_loader, desc='Validating', leave=False):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Calculate metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics['accuracy']
        val_f1 = val_metrics['macro_f1']
        val_kappa = val_metrics['kappa']
        
        # Get per-class performance
        n1_f1 = val_metrics['per_class_metrics']['N1']['f1']
        rem_f1 = val_metrics['per_class_metrics']['REM']['f1']
        
        epoch_time = (datetime.now() - epoch_start).total_seconds()
        
        # Log results
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Loss: {avg_train_loss:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f} ({val_acc*100:.2f}%)")
        logging.info(f"  Val F1: {val_f1:.4f} ({val_f1*100:.2f}%)")
        logging.info(f"  Val Kappa: {val_kappa:.4f}")
        logging.info(f"  N1 F1: {n1_f1:.4f} | REM F1: {rem_f1:.4f}")
        logging.info(f"  LR: {optimizer.param_groups[0]['lr']:.2e}")
        logging.info(f"  Time: {epoch_time:.1f}s")
        
        # Check targets
        targets_met = []
        if val_acc >= 0.90:
            targets_met.append("ACC")
        if val_f1 >= 0.82:
            targets_met.append("F1")
        if val_kappa >= 0.82:
            targets_met.append("KAPPA")
        
        if targets_met:
            logging.info(f"  🎯 Targets met: {', '.join(targets_met)}")
        
        training_history.append({
            'epoch': epoch + 1,
            'train_loss': avg_train_loss,
            'val_acc': val_acc,
            'val_f1': val_f1,
            'val_kappa': val_kappa,
            'n1_f1': n1_f1,
            'rem_f1': rem_f1,
            'lr': optimizer.param_groups[0]['lr'],
            'time': epoch_time
        })
        
        # Save best model
        improved = False
        if val_acc > best_val_acc:
            improved = True
        elif val_acc == best_val_acc and val_f1 > best_val_f1:
            improved = True
        
        if improved:
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_kappa = val_kappa
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': best_model_state,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_kappa': val_kappa,
                'config': config
            }
            
            checkpoint_path = os.path.join(log_dir, 'best_model.pth')
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"  💾 Saved best model")
            
            # Save if all targets met
            if len(targets_met) == 3:
                success_path = os.path.join(log_dir, f'SUCCESS_epoch{epoch+1}.pth')
                torch.save(checkpoint, success_path)
                logging.info(f"  🎉 SUCCESS! All targets achieved!")
                
                if epoch >= 10:  # At least 10 epochs
                    logging.info("  ✅ Stopping - targets achieved!")
                    break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"\n⏹️ Early stopping triggered")
                break
    
    # Final test evaluation
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        logging.info("\n" + "="*80)
        logging.info("📊 FINAL TEST EVALUATION")
        logging.info("="*80)
        
        model.eval()
        test_evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        test_evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc='Testing'):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    test_evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get test metrics
        test_metrics = test_evaluator.evaluate()
        test_acc = test_metrics['accuracy']
        test_f1 = test_metrics['macro_f1']
        test_kappa = test_metrics['kappa']
        
        # Get confusion matrix
        final_preds, final_labels, _ = test_evaluator.get_final_predictions()
        cm = confusion_matrix(final_labels, final_preds)
        
        # Results
        logging.info("\n🎯 FINAL TEST RESULTS:")
        logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
        logging.info(f"  Macro F1: {test_f1:.4f} ({test_f1*100:.2f}%)")
        logging.info(f"  Kappa: {test_kappa:.4f}")
        
        # Target achievement
        logging.info("\n🎯 Target Achievement (90% / 82% / 0.82):")
        all_achieved = True
        
        if test_acc >= 0.90:
            logging.info(f"  ✅ Accuracy: {test_acc:.4f} ≥ 0.90")
        else:
            logging.info(f"  ❌ Accuracy: {test_acc:.4f} < 0.90 (gap: {0.90-test_acc:.4f})")
            all_achieved = False
        
        if test_f1 >= 0.82:
            logging.info(f"  ✅ Macro F1: {test_f1:.4f} ≥ 0.82")
        else:
            logging.info(f"  ❌ Macro F1: {test_f1:.4f} < 0.82 (gap: {0.82-test_f1:.4f})")
            all_achieved = False
        
        if test_kappa >= 0.82:
            logging.info(f"  ✅ Kappa: {test_kappa:.4f} ≥ 0.82")
        else:
            logging.info(f"  ❌ Kappa: {test_kappa:.4f} < 0.82 (gap: {0.82-test_kappa:.4f})")
            all_achieved = False
        
        # Per-class metrics
        logging.info("\n📊 Per-Class Performance:")
        class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        for class_name in class_names:
            metrics = test_metrics['per_class_metrics'][class_name]
            logging.info(f"{class_name}: P={metrics['precision']*100:.1f}%, R={metrics['recall']*100:.1f}%, F1={metrics['f1']*100:.1f}%")
        
        # Save results
        final_results = {
            'timestamp': timestamp,
            'config': config,
            'best_val_acc': float(best_val_acc),
            'best_val_f1': float(best_val_f1),
            'best_val_kappa': float(best_val_kappa),
            'test_acc': float(test_acc),
            'test_f1': float(test_f1),
            'test_kappa': float(test_kappa),
            'confusion_matrix': cm.tolist(),
            'per_class_metrics': test_metrics['per_class_metrics'],
            'training_history': training_history,
            'targets_achieved': all_achieved
        }
        
        # Convert numpy types
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            return obj
        
        final_results = convert_numpy(final_results)
        
        results_file = os.path.join(log_dir, 'final_results.json')
        with open(results_file, 'w') as f:
            json.dump(final_results, f, indent=2)
        
        logging.info(f"\n💾 Results saved to {results_file}")
        
        if all_achieved:
            logging.info("\n" + "="*80)
            logging.info("🎉🎉🎉 SUCCESS! ALL TARGETS ACHIEVED! 🎉🎉🎉")
            logging.info("V18 Model Ready for ICASSP 2026!")
            logging.info("="*80)
        else:
            logging.info("\n📈 Continue optimization...")
    
    return final_results


if __name__ == "__main__":
    results = train_v18()