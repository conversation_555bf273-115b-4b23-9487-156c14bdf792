#!/usr/bin/env python3
"""
V14 True Sequence-to-Sequence 实现
基于V14 FIXED，实现真正的序列到序列预测

核心改进：
1. 模型输出整个序列的预测 (batch, seq_len, n_classes)
2. 损失计算使用所有5个位置的预测vs标签
3. 推理时使用滑动窗口+概率平均
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.sequence_dataset import SequenceSleepDataset


class SequentialFeatureExtractor(nn.Module):
    """特征提取器 - 从V14 FIXED继承"""
    def __init__(self, input_channels=3, d_model=192):
        super().__init__()
        
        self.conv_layers = nn.Sequential(
            # 第一层
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6, padding=24),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=8, stride=8),
            nn.Dropout(0.2),
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=8, stride=1, padding=4),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4, stride=4),
            nn.Dropout(0.2),
            
            # 第三层
            nn.Conv1d(128, d_model, kernel_size=4, stride=1, padding=2),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
    def forward(self, x):
        # x: (batch, seq_len, time_steps, channels)
        batch_size, seq_len, time_steps, channels = x.shape
        
        # Reshape for conv processing
        x = x.view(batch_size * seq_len, channels, time_steps)
        
        # Extract features
        x = self.conv_layers(x)
        
        # Global average pooling
        x = F.adaptive_avg_pool1d(x, 1).squeeze(-1)
        
        # Reshape back
        x = x.view(batch_size, seq_len, -1)
        
        return x


class TrueSeq2SeqMAMBAFORMER(nn.Module):
    """
    真正的Sequence-to-Sequence MAMBAFORMER
    输出整个序列的预测，不只是中心点
    """
    def __init__(self, input_channels=3, n_classes=5, d_model=192, 
                 n_heads=12, n_layers=4, dropout=0.15, seq_len=5):
        super().__init__()
        
        self.seq_len = seq_len
        self.d_model = d_model
        self.n_classes = n_classes
        
        # 特征提取器
        self.feature_extractor = SequentialFeatureExtractor(input_channels, d_model)
        
        # Positional encoding
        self.pos_encoder = nn.Parameter(torch.randn(1, seq_len, d_model) * 0.02)
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 输出头 - 为序列中每个位置生成预测
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # N1专门分支（从V14继承）
        self.n1_branch = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, n_classes)
        )
        
        # 融合门控
        self.fusion_gate = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Linear(d_model // 4, 1),
            nn.Sigmoid()
        )
        
        self._init_weights()
        
    def _init_weights(self):
        """初始化权重"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(self, x):
        """
        输入: (batch, seq_len, time_steps, channels)
        输出: (batch, seq_len, n_classes) - 整个序列的预测！
        """
        batch_size = x.shape[0]
        
        # 特征提取
        features = self.feature_extractor(x)  # (batch, seq_len, d_model)
        
        # 添加位置编码
        features = features + self.pos_encoder
        
        # Transformer处理
        encoded = self.transformer(features)  # (batch, seq_len, d_model)
        
        # 为每个位置生成预测
        main_out = self.classifier(encoded)  # (batch, seq_len, n_classes)
        n1_out = self.n1_branch(encoded)  # (batch, seq_len, n_classes)
        
        # 融合主预测和N1预测
        gate = self.fusion_gate(encoded)  # (batch, seq_len, 1)
        final_out = gate * main_out + (1 - gate) * n1_out
        
        return final_out, n1_out


class Seq2SeqLoss(nn.Module):
    """
    序列级别的损失函数
    对所有5个位置计算损失
    """
    def __init__(self, rem_weight=2.5, n1_weight=3.5, gamma=2.0):
        super().__init__()
        self.rem_weight = rem_weight
        self.n1_weight = n1_weight
        self.gamma = gamma
        
        # 类别权重 [Wake, N1, N2, N3, REM]
        self.class_weights = torch.tensor([2.0, n1_weight, 1.0, 1.0, rem_weight])
        
    def forward(self, predictions, targets):
        """
        predictions: (batch, seq_len, n_classes)
        targets: (batch, seq_len) - 每个位置都有标签
        """
        batch_size, seq_len, n_classes = predictions.shape
        
        # Reshape for loss calculation
        predictions = predictions.reshape(-1, n_classes)  # (batch*seq_len, n_classes)
        targets = targets.reshape(-1)  # (batch*seq_len)
        
        # 移动class_weights到正确的设备
        if self.class_weights.device != predictions.device:
            self.class_weights = self.class_weights.to(predictions.device)
        
        # Focal loss计算
        ce_loss = F.cross_entropy(predictions, targets, weight=self.class_weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        # 对所有位置取平均
        return focal_loss.mean()


class TemporalConsistencyLoss(nn.Module):
    """时序一致性损失 - 鼓励相邻预测平滑"""
    def __init__(self, weight=0.05):
        super().__init__()
        self.weight = weight
        
    def forward(self, predictions):
        """
        predictions: (batch, seq_len, n_classes)
        """
        if predictions.shape[1] <= 1:
            return 0.0
        
        # 计算相邻预测的KL散度
        probs = F.softmax(predictions, dim=-1)
        
        # 相邻预测的差异
        kl_div = 0
        for i in range(predictions.shape[1] - 1):
            p = probs[:, i, :]
            q = probs[:, i + 1, :]
            kl_div += F.kl_div(q.log(), p, reduction='batchmean')
        
        return self.weight * kl_div


def train_epoch(model, train_loader, criterion, temp_loss, optimizer, device, epoch):
    """训练一个epoch - 使用完整序列损失"""
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # target shape: (batch, seq_len) - 每个位置都有标签
        if target.dim() == 1:
            # 如果只有中心标签，复制到所有位置
            target = target.unsqueeze(1).expand(-1, model.seq_len)
        
        # 数据增强
        if np.random.random() < 0.3:
            noise = torch.randn_like(data) * 0.005
            data = data + noise
        
        optimizer.zero_grad()
        
        # 前向传播 - 输出整个序列
        predictions, n1_predictions = model(data)  # (batch, seq_len, n_classes)
        
        # 计算损失 - 使用所有位置
        main_loss = criterion(predictions, target)
        n1_loss = criterion(n1_predictions, target) * 0.3
        consistency_loss = temp_loss(predictions)
        
        loss = main_loss + n1_loss + consistency_loss
        
        # L2正则化
        l2_lambda = 1e-5
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集中心预测用于监控
        center_idx = model.seq_len // 2
        center_preds = predictions[:, center_idx, :].argmax(dim=1)
        center_targets = target[:, center_idx]
        
        all_preds.extend(center_preds.cpu().numpy())
        all_targets.extend(center_targets.cpu().numpy())
        
        # 更新进度条
        acc = (center_preds == center_targets).float().mean().item()
        pbar.set_postfix({'loss': f'{loss.item():.4f}', 'acc': f'{acc:.4f}'})
    
    # 计算epoch指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1


def evaluate_with_sliding_window(model, data_loader, device):
    """
    使用滑动窗口评估
    每个epoch收集多个窗口的预测，然后平均概率
    """
    model.eval()
    seq_len = model.seq_len
    
    # 存储所有epoch的预测
    epoch_predictions = {}  # {file_idx: {epoch_idx: [predictions]}}
    epoch_targets = {}  # {file_idx: {epoch_idx: target}}
    
    with torch.no_grad():
        for batch_idx, (data, target) in enumerate(tqdm(data_loader, desc='Evaluation')):
            data = data.to(device)
            batch_size = data.shape[0]
            
            # 获取序列预测
            predictions, _ = model(data)  # (batch, seq_len, n_classes)
            probs = F.softmax(predictions, dim=-1)
            
            # 处理每个batch中的样本
            for b in range(batch_size):
                file_idx = batch_idx * data_loader.batch_size + b
                
                if file_idx not in epoch_predictions:
                    epoch_predictions[file_idx] = {}
                    epoch_targets[file_idx] = {}
                
                # 对于序列中的每个位置
                for s in range(seq_len):
                    epoch_idx = s  # 相对位置
                    
                    if epoch_idx not in epoch_predictions[file_idx]:
                        epoch_predictions[file_idx][epoch_idx] = []
                    
                    # 存储该位置的预测
                    epoch_predictions[file_idx][epoch_idx].append(probs[b, s].cpu().numpy())
                    
                    # 存储目标（只需要存一次）
                    if epoch_idx not in epoch_targets[file_idx]:
                        if target.dim() > 1:
                            epoch_targets[file_idx][epoch_idx] = target[b, s].item()
                        else:
                            # 如果只有中心标签
                            epoch_targets[file_idx][epoch_idx] = target[b].item() if s == seq_len // 2 else -1
    
    # 聚合预测并计算最终结果
    all_preds = []
    all_targets = []
    
    for file_idx in sorted(epoch_predictions.keys()):
        # 只使用中心位置进行评估（与训练保持一致）
        center_idx = seq_len // 2
        if center_idx in epoch_predictions[file_idx]:
            # 平均该epoch的所有预测
            avg_prob = np.mean(epoch_predictions[file_idx][center_idx], axis=0)
            pred = np.argmax(avg_prob)
            
            all_preds.append(pred)
            
            target_val = epoch_targets[file_idx][center_idx]
            if target_val != -1:  # 有效标签
                all_targets.append(target_val)
    
    # 确保预测和标签数量匹配
    min_len = min(len(all_preds), len(all_targets))
    all_preds = all_preds[:min_len]
    all_targets = all_targets[:min_len]
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm


def main():
    # 配置（基于V14 FIXED）
    config = {
        'd_model': 192,
        'n_heads': 12,
        'n_layers': 4,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 2e-4,
        'weight_decay': 1e-4,
        'num_epochs': 50,
        'patience': 10,
        'rem_weight': 2.5,
        'n1_weight': 3.5,
    }
    
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f'../logs/v14_true_seq2seq_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 True Sequence-to-Sequence Training")
    logging.info("="*80)
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割（与V14 FIXED相同）
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 创建数据集
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,  # 使用全部数据
        is_training=True
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,
        is_training=False
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,
        is_training=False
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'],
        shuffle=True, num_workers=4, pin_memory=True, drop_last=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = TrueSeq2SeqMAMBAFORMER(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数
    criterion = Seq2SeqLoss(
        rem_weight=config['rem_weight'],
        n1_weight=config['n1_weight']
    )
    temp_loss = TemporalConsistencyLoss(weight=0.05)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5, min_lr=1e-6
    )
    
    # 训练循环
    best_val_acc = 0
    best_test_acc = 0
    patience_counter = 0
    
    logging.info("\n" + "="*80)
    logging.info("Starting Training with True Sequence-to-Sequence!")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, temp_loss, optimizer, device, epoch
        )
        
        # 评估
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate_with_sliding_window(
            model, val_loader, device
        )
        
        # 调整学习率
        scheduler.step(val_acc)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            
            # 在测试集上评估
            test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate_with_sliding_window(
                model, test_loader, device
            )
            best_test_acc = test_acc
            
            # 保存模型
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Val: {val_acc:.4f}, Test: {test_acc:.4f})")
            logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                        f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
            
            if test_acc >= 0.90:
                logging.info("\n" + "="*80)
                logging.info("🎉 ACHIEVED 90% TEST ACCURACY WITH TRUE SEQ2SEQ!")
                logging.info("="*80)
                break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS")
    logging.info("="*80)
    logging.info(f"Best Val Accuracy: {best_val_acc:.4f}")
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    
    gap_to_90 = 0.90 - best_test_acc
    if best_test_acc >= 0.90:
        logging.info(f"✅ TARGET ACHIEVED!")
    else:
        logging.info(f"Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
    
    # 保存结果
    results = {
        'best_val_acc': float(best_val_acc),
        'best_test_acc': float(best_test_acc),
        'config': config
    }
    
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\nResults saved to {log_dir}")


if __name__ == '__main__':
    main()