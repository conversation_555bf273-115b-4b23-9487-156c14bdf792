#!/usr/bin/env python3
"""
V16 MAMBAFORMER - Final Push to 87% Accuracy
Building on V15 results (86.67% acc) to achieve final targets
Target: 87% Accuracy, 80% Macro F1, 0.80 Kappa
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torch.optim.lr_scheduler import OneCycleLR
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2, SequentialFocalLoss, TemporalConsistencyLoss
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class AugmentedDataset(Dataset):
    """Dataset with data augmentation for better generalization"""
    def __init__(self, base_dataset, augment=True):
        self.base_dataset = base_dataset
        self.augment = augment
        
    def __len__(self):
        return len(self.base_dataset)
    
    def __getitem__(self, idx):
        data, label = self.base_dataset[idx]
        
        if self.augment and np.random.random() > 0.5:
            # Add Gaussian noise
            noise_level = np.random.uniform(0.01, 0.05)
            noise = torch.randn_like(data) * noise_level
            data = data + noise
            
            # Random amplitude scaling
            scale = np.random.uniform(0.95, 1.05)
            data = data * scale
        
        return data, label


class ImprovedN1Loss(nn.Module):
    """Even more aggressive N1 focus with boundary detection"""
    def __init__(self):
        super().__init__()
        self.focal = SequentialFocalLoss(gamma=2.5)
        self.ce_loss = nn.CrossEntropyLoss(reduction='none')
        
    def forward(self, predictions, targets):
        batch_size, seq_len = targets.shape
        
        # Base focal loss
        focal_loss = self.focal(predictions, targets)
        
        # Calculate per-sample CE loss
        pred_flat = predictions.reshape(-1, predictions.shape[-1])
        target_flat = targets.reshape(-1)
        ce_losses = self.ce_loss(pred_flat, target_flat).reshape(batch_size, seq_len)
        
        # Heavy penalty for N1 errors
        n1_mask = (targets == 1).float()
        n1_penalty = (ce_losses * n1_mask).sum() / (n1_mask.sum() + 1e-6) * 6.0
        
        # Penalty for false N1 predictions
        pred_classes = torch.argmax(predictions, dim=-1)
        false_n1_mask = ((pred_classes == 1) & (targets != 1)).float()
        false_n1_penalty = (ce_losses * false_n1_mask).sum() / (false_n1_mask.sum() + 1e-6) * 2.0
        
        # Boundary penalty - N1 often occurs at transitions
        transition_mask = torch.zeros_like(targets, dtype=torch.float32)
        for i in range(1, seq_len):
            transition_mask[:, i] = (targets[:, i] != targets[:, i-1]).float()
        
        boundary_n1_mask = transition_mask * ((targets == 1) | (pred_classes == 1)).float()
        boundary_penalty = (ce_losses * boundary_n1_mask).sum() / (boundary_n1_mask.sum() + 1e-6) * 1.5
        
        return focal_loss + n1_penalty + false_n1_penalty + boundary_penalty


class FinalLoss(nn.Module):
    """Final loss function combining all improvements"""
    def __init__(self):
        super().__init__()
        self.n1_loss = ImprovedN1Loss()
        self.temporal_loss = TemporalConsistencyLoss(weight=0.35)
        # Strong class weights for N1
        self.class_weights = torch.tensor([2.5, 6.0, 1.0, 1.5, 2.5])
        
    def forward(self, predictions, targets):
        # N1-focused loss
        n1_loss = self.n1_loss(predictions, targets)
        
        # Temporal consistency
        temporal = self.temporal_loss(predictions)
        
        # Weighted CE
        ce_loss = nn.CrossEntropyLoss(weight=self.class_weights.to(predictions.device))
        pred_flat = predictions.reshape(-1, predictions.shape[-1])
        target_flat = targets.reshape(-1)
        ce = ce_loss(pred_flat, target_flat) * 0.15
        
        return n1_loss + temporal + ce


def mixup_data(x, y, alpha=0.2):
    """Mixup augmentation"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1
    
    batch_size = x.size()[0]
    index = torch.randperm(batch_size).to(x.device)
    
    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]
    
    return mixed_x, y_a, y_b, lam


def train_v16():
    """Main training function for V16"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/v16_final_push_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V16 - FINAL PUSH TO 87%")
    logging.info("="*80)
    logging.info("🎯 Targets: Accuracy ≥87%, Macro F1 ≥80%, Kappa ≥0.80")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # V16 Configuration - optimized based on V15 results
    config = {
        'd_model': 336,      # Slightly larger than V15
        'n_heads': 21,       # 336/16 = 21
        'n_layers': 7,       
        'dropout': 0.2,      
        'seq_len': 6,        
        'batch_size': 24,    
        'learning_rate': 4e-5,
        'num_epochs': 35,
        'patience': 10,
        'gradient_clip': 0.5,
        'weight_decay': 0.04,
        'mixup_alpha': 0.2,
        'label_smoothing': 0.05
    }
    
    logging.info("\n📋 V16 Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # Use same split as V14/V15
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Create datasets
    logging.info("\n📊 Loading datasets...")
    
    base_train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    # Wrap with augmentation
    train_dataset = AugmentedDataset(base_train_dataset, augment=True)
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    logging.info(f"Train samples: {base_train_dataset.total_epochs} epochs")
    logging.info(f"Val samples: {val_dataset.total_epochs} epochs")
    logging.info(f"Test samples: {test_dataset.total_epochs} epochs")
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"\nModel Parameters: {total_params:,}")
    logging.info(f"Model Size: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # Load V15 best model as initialization if available
    v15_path = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v15_targeted_20250810_230143/best_model.pth'
    if os.path.exists(v15_path):
        try:
            checkpoint = torch.load(v15_path)
            # Try to load what we can
            model_dict = model.state_dict()
            pretrained_dict = {k: v for k, v in checkpoint['model_state_dict'].items() 
                             if k in model_dict and model_dict[k].shape == v.shape}
            model_dict.update(pretrained_dict)
            model.load_state_dict(model_dict)
            logging.info(f"✅ Loaded {len(pretrained_dict)}/{len(model_dict)} weights from V15")
        except:
            logging.info("⚠️ Could not load V15 weights, starting fresh")
    
    # Loss and optimizer
    criterion = FinalLoss()
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # OneCycle scheduler for better convergence
    total_steps = len(train_loader) * config['num_epochs']
    scheduler = OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'] * 10,
        total_steps=total_steps,
        pct_start=0.3,
        anneal_strategy='cos'
    )
    
    # Training variables
    best_val_acc = 0
    best_val_f1 = 0
    best_val_kappa = 0
    best_model_state = None
    patience_counter = 0
    training_history = []
    
    logging.info("\n🏋️ Starting Training...")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        epoch_start = datetime.now()
        
        # Training phase
        model.train()
        train_loss = 0
        train_steps = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config["num_epochs"]}')
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)
            
            # Mixup augmentation
            if config['mixup_alpha'] > 0 and np.random.random() > 0.5:
                data, labels_a, labels_b, lam = mixup_data(data, labels, config['mixup_alpha'])
                
                optimizer.zero_grad()
                outputs, _ = model(data)
                
                loss = lam * criterion(outputs, labels_a) + (1 - lam) * criterion(outputs, labels_b)
            else:
                optimizer.zero_grad()
                outputs, _ = model(data)
                loss = criterion(outputs, labels)
            
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
            
            optimizer.step()
            scheduler.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            current_lr = scheduler.get_last_lr()[0]
            pbar.set_postfix({'loss': f'{loss.item():.4f}', 'lr': f'{current_lr:.2e}'})
        
        avg_train_loss = train_loss / train_steps
        
        # Validation phase
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(val_loader, desc='Validating', leave=False):
                data = data.to(device)
                outputs, _ = model(data)
                
                # Add temperature scaling for better calibration
                temperature = 1.2
                probs = torch.softmax(outputs / temperature, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Calculate metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics['accuracy']
        val_f1 = val_metrics['macro_f1']
        val_kappa = val_metrics['kappa']
        
        # Get N1 performance
        n1_f1 = val_metrics['per_class_metrics']['N1']['f1']
        
        epoch_time = (datetime.now() - epoch_start).total_seconds()
        
        # Log results
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Loss: {avg_train_loss:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f} ({val_acc*100:.2f}%)")
        logging.info(f"  Val F1: {val_f1:.4f} ({val_f1*100:.2f}%)")
        logging.info(f"  Val Kappa: {val_kappa:.4f}")
        logging.info(f"  N1 F1: {n1_f1:.4f} ({n1_f1*100:.2f}%)")
        logging.info(f"  LR: {scheduler.get_last_lr()[0]:.2e}")
        logging.info(f"  Time: {epoch_time:.1f}s")
        
        # Check targets
        targets_met = []
        if val_acc >= 0.87:
            targets_met.append("ACC")
        if val_f1 >= 0.80:
            targets_met.append("F1")
        if val_kappa >= 0.80:
            targets_met.append("KAPPA")
        
        if targets_met:
            logging.info(f"  🎯 Targets met: {', '.join(targets_met)}")
        
        training_history.append({
            'epoch': epoch + 1,
            'train_loss': avg_train_loss,
            'val_acc': val_acc,
            'val_f1': val_f1,
            'val_kappa': val_kappa,
            'n1_f1': n1_f1,
            'lr': scheduler.get_last_lr()[0],
            'time': epoch_time
        })
        
        # Save best model
        if val_acc > best_val_acc or (val_acc == best_val_acc and val_f1 > best_val_f1):
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_kappa = val_kappa
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': best_model_state,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_kappa': val_kappa,
                'config': config
            }
            
            checkpoint_path = os.path.join(log_dir, 'best_model.pth')
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"  💾 Saved best model")
            
            # Save if all targets met
            if len(targets_met) == 3:
                success_path = os.path.join(log_dir, f'SUCCESS_epoch{epoch+1}.pth')
                torch.save(checkpoint, success_path)
                logging.info(f"  🎉 SUCCESS! All targets achieved!")
                
                # Test immediately
                if epoch >= 5:  # At least 5 epochs
                    break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"\n⏹️ Early stopping triggered")
                break
    
    # Final test evaluation
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        logging.info("\n" + "="*80)
        logging.info("📊 FINAL TEST EVALUATION")
        logging.info("="*80)
        
        model.eval()
        test_evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        test_evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc='Testing'):
                data = data.to(device)
                outputs, _ = model(data)
                
                # Temperature scaling
                temperature = 1.2
                probs = torch.softmax(outputs / temperature, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    test_evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get test metrics
        test_metrics = test_evaluator.evaluate()
        test_acc = test_metrics['accuracy']
        test_f1 = test_metrics['macro_f1']
        test_kappa = test_metrics['kappa']
        
        # Get confusion matrix
        final_preds, final_labels, _ = test_evaluator.get_final_predictions()
        cm = confusion_matrix(final_labels, final_preds)
        
        # Results
        logging.info("\n🎯 FINAL TEST RESULTS:")
        logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
        logging.info(f"  Macro F1: {test_f1:.4f} ({test_f1*100:.2f}%)")
        logging.info(f"  Kappa: {test_kappa:.4f}")
        
        # Target achievement
        logging.info("\n🎯 Target Achievement (87% / 80% / 0.80):")
        all_achieved = True
        
        if test_acc >= 0.87:
            logging.info(f"  ✅ Accuracy: {test_acc:.4f} ≥ 0.87")
        else:
            logging.info(f"  ❌ Accuracy: {test_acc:.4f} < 0.87 (gap: {0.87-test_acc:.4f})")
            all_achieved = False
        
        if test_f1 >= 0.80:
            logging.info(f"  ✅ Macro F1: {test_f1:.4f} ≥ 0.80")
        else:
            logging.info(f"  ❌ Macro F1: {test_f1:.4f} < 0.80 (gap: {0.80-test_f1:.4f})")
            all_achieved = False
        
        if test_kappa >= 0.80:
            logging.info(f"  ✅ Kappa: {test_kappa:.4f} ≥ 0.80")
        else:
            logging.info(f"  ❌ Kappa: {test_kappa:.4f} < 0.80 (gap: {0.80-test_kappa:.4f})")
            all_achieved = False
        
        # Save results
        final_results = {
            'timestamp': timestamp,
            'config': config,
            'best_val_acc': float(best_val_acc),
            'best_val_f1': float(best_val_f1),
            'best_val_kappa': float(best_val_kappa),
            'test_acc': float(test_acc),
            'test_f1': float(test_f1),
            'test_kappa': float(test_kappa),
            'confusion_matrix': cm.tolist(),
            'per_class_metrics': test_metrics['per_class_metrics'],
            'training_history': training_history,
            'targets_achieved': all_achieved
        }
        
        results_file = os.path.join(log_dir, 'final_results.json')
        with open(results_file, 'w') as f:
            json.dump(final_results, f, indent=2)
        
        logging.info(f"\n💾 Results saved to {results_file}")
        
        if all_achieved:
            logging.info("\n" + "="*80)
            logging.info("🎉🎉🎉 SUCCESS! ALL TARGETS ACHIEVED! 🎉🎉🎉")
            logging.info("V16 Model Ready for ICASSP 2026!")
            logging.info("="*80)
    
    return final_results


if __name__ == "__main__":
    results = train_v16()