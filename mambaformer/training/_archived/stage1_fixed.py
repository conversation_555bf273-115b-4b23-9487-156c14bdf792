#!/usr/bin/env python3
"""
🎯 第1阶段实验（修复版）：简化的多模态特征提取
目标：确保基础功能正常，性能达到85%以上
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import OneCycleLR
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import (
    confusion_matrix,
    classification_report,
    accuracy_score,
    f1_score,
    cohen_kappa_score,
)
import warnings

warnings.filterwarnings("ignore")

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.progressive_mambaformer_v1_fixed import (
    ProgressiveMAMBAFORMER_V1_Fixed,
    SequentialFocalLoss,
    TemporalConsistencyLoss
)
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class FocalLoss(nn.Module):
    """Focal loss for handling class imbalance"""
    def __init__(self, alpha=None, gamma=2.0, device="cuda"):
        super().__init__()
        self.gamma = gamma
        if alpha is not None:
            self.alpha = torch.tensor(alpha).to(device)
        else:
            self.alpha = None
        self.device = device

    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)

        ce_loss = F.cross_entropy(inputs, targets, reduction="none")
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** self.gamma * ce_loss

        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss

        return focal_loss.mean()


class LabelSmoothingLoss(nn.Module):
    """Label smoothing for better generalization"""
    def __init__(self, n_classes=5, smoothing=0.1):
        super().__init__()
        self.n_classes = n_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing

    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)

        with torch.no_grad():
            true_dist = torch.zeros_like(inputs)
            true_dist.fill_(self.smoothing / (self.n_classes - 1))
            true_dist.scatter_(1, targets.unsqueeze(1), self.confidence)

        return torch.mean(torch.sum(-true_dist * F.log_softmax(inputs, dim=-1), dim=-1))


def validate_model_output(model, data_loader, device):
    """验证模型输出是否正常"""
    model.eval()
    with torch.no_grad():
        for data, target in data_loader:
            data = data.to(device)
            main_output, aux_output = model(data)
            
            # 检查输出维度
            print(f"输入形状: {data.shape}")
            print(f"主输出形状: {main_output.shape}")
            print(f"辅助输出形状: {aux_output.shape}")
            
            # 检查输出值范围
            print(f"主输出范围: [{main_output.min():.3f}, {main_output.max():.3f}]")
            
            # 获取预测
            predictions = torch.argmax(main_output[:, main_output.size(1)//2, :], dim=-1)
            print(f"预测类别分布: {torch.bincount(predictions)}")
            print(f"目标类别分布: {torch.bincount(target[:, target.size(1)//2])}")
            
            break
    
    return True


def train_one_epoch(model, data_loader, focal_loss, ls_loss, optimizer, scheduler, device, epoch, config):
    """Train for one epoch"""
    model.train()
    running_loss = 0.0
    correct_predictions = 0
    total_predictions = 0
    
    progress_bar = tqdm(data_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']}")
    
    for batch_idx, (data, target) in enumerate(progress_bar):
        data, target = data.to(device), target.to(device)
        
        # 轻微的数据增强
        if torch.rand(1).item() < 0.2:  # 降低增强概率
            noise_std = data.std() * 0.01  # 降低噪声强度
            noise = torch.randn_like(data) * noise_std
            data = data + noise
        
        # Forward pass
        optimizer.zero_grad()
        main_output, aux_output = model(data)
        
        # Calculate losses
        loss1 = focal_loss(main_output, target)
        loss2 = ls_loss(main_output, target)
        
        # Combined loss
        loss = 0.6 * loss1 + 0.4 * loss2
        
        # Add temporal consistency loss (降低权重)
        temp_loss = TemporalConsistencyLoss(weight=0.02)
        loss += temp_loss(main_output)
        
        # Backward pass
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
        
        optimizer.step()
        scheduler.step()
        
        # Calculate accuracy
        _, predicted = torch.max(main_output[:, main_output.size(1)//2, :], dim=-1)
        true_labels = target[:, target.size(1)//2]
        correct_predictions += (predicted == true_labels).sum().item()
        total_predictions += true_labels.size(0)
        
        running_loss += loss.item()
        
        # Update progress bar
        current_acc = 100 * correct_predictions / total_predictions
        progress_bar.set_postfix({
            'loss': f'{running_loss/(batch_idx+1):.4f}',
            'acc': f'{current_acc:.2f}%',
            'lr': f'{scheduler.get_last_lr()[0]:.6f}'
        })
        
        # 每100个batch打印一次详细信息
        if batch_idx % 100 == 0 and batch_idx > 0:
            logging.info(f"Batch {batch_idx}: Loss={running_loss/(batch_idx+1):.4f}, "
                        f"Acc={current_acc:.2f}%, LR={scheduler.get_last_lr()[0]:.6f}")
    
    epoch_loss = running_loss / len(data_loader)
    epoch_acc = 100 * correct_predictions / total_predictions
    
    return epoch_loss, epoch_acc


def evaluate(model, data_loader, device):
    """Evaluate the model"""
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc="Evaluating"):
            data = data.to(device)
            main_output, _ = model(data)
            
            # Get center predictions
            predictions = torch.argmax(main_output[:, main_output.size(1)//2, :], dim=-1)
            targets = target[:, target.size(1)//2]
            
            all_predictions.extend(predictions.cpu().numpy())
            all_targets.extend(targets.numpy())
    
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    
    # Calculate metrics
    accuracy = accuracy_score(all_targets, all_predictions)
    f1_macro = f1_score(all_targets, all_predictions, average='macro')
    kappa = cohen_kappa_score(all_targets, all_predictions)
    
    # Class-wise metrics
    report = classification_report(all_targets, all_predictions, 
                                 target_names=['W', 'N1', 'N2', 'N3', 'REM'],
                                 output_dict=True)
    
    # Confusion matrix
    cm = confusion_matrix(all_targets, all_predictions)
    
    return {
        'accuracy': accuracy,
        'f1_macro': f1_macro,
        'kappa': kappa,
        'classification_report': report,
        'confusion_matrix': cm
    }


def main():
    # Configuration - 调整后的配置
    config = {
        "d_model": 512,
        "n_heads": 32,
        "n_layers": 12,
        "dropout": 0.15,  # 降低dropout
        "seq_len": 7,
        "batch_size": 16,
        "learning_rate": 3e-4,  # 提高学习率
        "num_epochs": 20,  # 增加epochs
        "gradient_clip": 1.0,
        "weight_decay": 0.02,  # 降低weight decay
        "label_smoothing": 0.1,
    }

    # Setup logging
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_fixed_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(os.path.join(log_dir, "training.log")),
            logging.StreamHandler(),
        ],
    )

    logging.info("=" * 80)
    logging.info("🎯 第1阶段实验（修复版）：简化的多模态特征提取")
    logging.info("=" * 80)
    logging.info(f"配置: {config}")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"Device: {device}")

    # Data paths
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"

    # 先用少量数据测试
    test_mode = True  # 设为False进行完整训练
    
    if test_mode:
        logging.info("⚠️ 测试模式：使用少量数据验证模型")
        train_files = ["SC4001E0.npz", "SC4002E0.npz"]
        val_files = ["SC4011E0.npz"]
        test_files = ["SC4012E0.npz"]
    else:
        # 完整数据集
        train_files = [
            "SC4181E0.npz", "SC4182E0.npz", "SC4161E0.npz", "SC4162E0.npz",
            "SC4131E0.npz", "SC4101E0.npz", "SC4102E0.npz", "SC4111E0.npz",
            "SC4112E0.npz", "SC4041E0.npz", "SC4042E0.npz", "SC4191E0.npz",
            "SC4192E0.npz", "SC4061E0.npz", "SC4062E0.npz", "SC4091E0.npz",
            "SC4092E0.npz", "SC4121E0.npz", "SC4122E0.npz", "SC4141E0.npz",
            "SC4142E0.npz", "SC4051E0.npz", "SC4052E0.npz", "SC4081E0.npz",
            "SC4082E0.npz", "SC4151E0.npz", "SC4152E0.npz", "SC4171E0.npz",
            "SC4172E0.npz",
        ]
        val_files = [
            "SC4021E0.npz", "SC4022E0.npz", "SC4031E0.npz",
            "SC4032E0.npz", "SC4071E0.npz", "SC4072E0.npz",
        ]
        test_files = [
            "SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"
        ]

    # Prepare file paths
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]

    # Create datasets
    logging.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=True,
    )

    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )

    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )

    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")

    # Create model
    model = ProgressiveMAMBAFORMER_V1_Fixed(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)

    # 验证模型输出
    logging.info("验证模型输出...")
    validate_model_output(model, val_loader, device)

    # Loss functions - 调整类权重
    class_weights = torch.tensor([2.0, 5.0, 1.0, 1.5, 2.0]).to(device)  # 降低N1权重
    focal_loss = FocalLoss(alpha=class_weights, gamma=2.0, device=device)
    ls_loss = LabelSmoothingLoss(n_classes=5, smoothing=config["label_smoothing"])

    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"],
    )

    # Scheduler
    total_steps = len(train_loader) * config["num_epochs"]
    scheduler = OneCycleLR(
        optimizer,
        max_lr=config["learning_rate"],
        total_steps=total_steps,
        pct_start=0.3,
        anneal_strategy='cos',
    )

    # Training loop
    best_val_acc = 0
    best_epoch = 0
    patience = 5
    patience_counter = 0
    
    for epoch in range(config["num_epochs"]):
        logging.info(f"\n{'='*60}")
        logging.info(f"Epoch {epoch+1}/{config['num_epochs']}")
        
        # Train
        train_loss, train_acc = train_one_epoch(
            model, train_loader, focal_loss, ls_loss, 
            optimizer, scheduler, device, epoch, config
        )
        
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        
        # Validate
        val_metrics = evaluate(model, val_loader, device)
        val_acc = val_metrics['accuracy'] * 100
        
        logging.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_metrics['f1_macro']:.4f}")
        
        # Log class-wise performance
        logging.info("\n验证集各类别性能:")
        for class_name in ['W', 'N1', 'N2', 'N3', 'REM']:
            class_metrics = val_metrics['classification_report'][class_name]
            logging.info(f"{class_name:>4}: Precision={class_metrics['precision']:.3f}, "
                        f"Recall={class_metrics['recall']:.3f}, "
                        f"F1={class_metrics['f1-score']:.3f}")
        
        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_epoch = epoch
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'config': config,
            }, os.path.join(log_dir, 'best_model.pth'))
            logging.info(f"✅ 保存最佳模型 (Val Acc: {val_acc:.2f}%)")
        else:
            patience_counter += 1
            if patience_counter >= patience and not test_mode:
                logging.info(f"Early stopping triggered at epoch {epoch+1}")
                break
        
        # 在测试模式下，只训练3个epoch
        if test_mode and epoch >= 2:
            break
    
    logging.info(f"\n{'='*60}")
    logging.info(f"训练完成! 最佳验证准确率: {best_val_acc:.2f}% (Epoch {best_epoch+1})")
    
    # Test evaluation
    logging.info("\n测试集评估...")
    checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    
    test_metrics = evaluate(model, test_loader, device)
    test_acc = test_metrics['accuracy'] * 100
    
    logging.info(f"\n{'='*60}")
    logging.info("📊 最终测试结果:")
    logging.info(f"准确率: {test_acc:.2f}%")
    logging.info(f"F1 Score: {test_metrics['f1_macro']:.4f}")
    logging.info(f"Cohen's Kappa: {test_metrics['kappa']:.4f}")
    
    logging.info("\n各类别性能:")
    for class_name in ['W', 'N1', 'N2', 'N3', 'REM']:
        class_metrics = test_metrics['classification_report'][class_name]
        logging.info(f"{class_name:>4}: Precision={class_metrics['precision']:.3f}, "
                    f"Recall={class_metrics['recall']:.3f}, "
                    f"F1={class_metrics['f1-score']:.3f}")
    
    logging.info("\n混淆矩阵:")
    logging.info(test_metrics['confusion_matrix'])
    
    # 保存实验结果
    result_summary = {
        'stage': '第1阶段（修复版）：简化的多模态特征提取',
        'test_mode': test_mode,
        'test_accuracy': test_acc,
        'test_f1_macro': test_metrics['f1_macro'],
        'test_kappa': test_metrics['kappa'],
        'n1_recall': test_metrics['classification_report']['N1']['recall'],
        'n1_f1': test_metrics['classification_report']['N1']['f1-score'],
        'best_val_acc': best_val_acc,
        'config': config,
        'timestamp': timestamp
    }
    
    with open(os.path.join(log_dir, 'result_summary.json'), 'w') as f:
        json.dump(result_summary, f, indent=2)
    
    # 决策判断
    logging.info(f"\n{'='*60}")
    logging.info("🔍 第1阶段实验结论:")
    if test_acc >= 85.0 or (test_mode and test_acc >= 70.0):
        logging.info(f"✅ 性能达标，可以进行完整训练或继续第2阶段")
        if test_metrics['classification_report']['N1']['recall'] > 0.3:
            logging.info("✅ N1召回率正常")
    else:
        logging.info(f"⚠️ 性能未达标，需要进一步调整")
    
    return test_acc, test_metrics


if __name__ == "__main__":
    test_acc, metrics = main()
    
    # 更新方案文档
    with open("/media/main/ypf/eeg/Cross-Modal-Transformer/scheme/stage1_fixed_result.md", "w") as f:
        f.write(f"# 第1阶段修复版实验结果\n\n")
        f.write(f"- 测试准确率: {test_acc:.2f}%\n")
        f.write(f"- N1召回率: {metrics['classification_report']['N1']['recall']:.3f}\n")
        f.write(f"- N1 F1: {metrics['classification_report']['N1']['f1-score']:.3f}\n")
        f.write(f"- 决策: {'继续' if test_acc >= 85 else '需要调整'}\n")