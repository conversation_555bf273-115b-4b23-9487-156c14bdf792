#!/usr/bin/env python3
"""
V13 NO TRUNCATION - 不截断数据的快速模型
基于V12架构但使用完整数据，确保REM期不丢失
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v13_no_truncation_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("⚡ V13 NO TRUNCATION - FULL DATA")
    logging.info("="*80)
    
    return log_dir

class LightweightModel(nn.Module):
    """轻量级模型 - 快速训练"""
    def __init__(self, input_channels, n_classes, d_model, n_heads, n_layers, dropout, seq_len):
        super().__init__()
        
        # 小型主干网络
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # 简单的辅助头
        self.auxiliary_head = nn.Sequential(
            nn.Linear(n_classes, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, n_classes)
        )
        
        # N1增强器
        self.n1_enhancer = nn.Sequential(
            nn.Linear(n_classes, d_model // 8),
            nn.ReLU(),
            nn.Linear(d_model // 8, 1)
        )
        
        # 融合权重
        self.main_weight = nn.Parameter(torch.tensor(0.8))
        self.aux_weight = nn.Parameter(torch.tensor(0.2))
        
    def forward(self, x):
        # 主干输出
        main_out, _ = self.backbone(x)
        
        # 提取中心时间步
        if main_out.dim() == 3:
            center_out = main_out[:, main_out.shape[1]//2, :]
        else:
            center_out = main_out
        
        # 辅助输出
        aux_out = self.auxiliary_head(center_out)
        
        # N1增强
        n1_score = self.n1_enhancer(center_out).squeeze(-1)
        n1_boost = torch.sigmoid(n1_score) * 0.3
        
        # 融合
        final_out = self.main_weight * center_out + self.aux_weight * aux_out
        
        # N1增强
        final_out[:, 1] = final_out[:, 1] + n1_boost
        
        return final_out, aux_out

class EfficientLoss(nn.Module):
    """高效的损失函数"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 平衡的权重 - 特别关注REM
        self.class_weights = torch.tensor([2.5, 5.0, 1.0, 1.2, 2.5]).to(device)  # 增加REM权重
        self.gamma = 2.0
        self.label_smoothing = 0.1
        
    def forward(self, final_out, aux_out, targets, epoch=0):
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        # 动态权重
        weights = self.class_weights.clone()
        if epoch > 5:
            weights[1] = min(8.0, weights[1] + (epoch - 5) * 0.15)
        
        # Focal loss
        ce_loss = F.cross_entropy(final_out, targets, weight=weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = ((1 - pt) ** self.gamma) * ce_loss
        
        # 辅助损失
        aux_loss = F.cross_entropy(aux_out, targets, weight=weights)
        
        # 总损失
        total_loss = focal_loss.mean() + 0.2 * aux_loss
        
        # 标签平滑
        if self.label_smoothing > 0 and self.training:
            n_classes = final_out.size(-1)
            smooth_loss = -F.log_softmax(final_out, dim=-1).mean(dim=-1)
            total_loss = (1 - self.label_smoothing) * total_loss + self.label_smoothing * smooth_loss.mean()
        
        return total_loss

def simple_augment(x, targets=None):
    """简单数据增强"""
    if random.random() > 0.5:
        return x
    
    # 轻微噪声
    if random.random() < 0.3:
        x = x + torch.randn_like(x) * 0.01
    
    # 时间偏移
    if random.random() < 0.2:
        shift = random.randint(-30, 30)
        x = torch.roll(x, shifts=shift, dims=-1)
    
    return x

def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    model.train()
    criterion.training = True
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # 简单增强
        data = simple_augment(data, target)
        
        optimizer.zero_grad()
        
        # 前向传播
        final_out, aux_out = model(data)
        
        # 计算损失
        loss = criterion(final_out, aux_out, target, epoch)
        
        # L2正则化
        l2_lambda = 1e-5
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        preds = final_out.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
        
        pbar.set_postfix({'loss': f'{loss.item():.4f}'})
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    # 计算每类F1
    class_f1 = f1_score(all_targets, all_preds, average=None)
    
    return total_loss / len(train_loader), accuracy, f1, class_f1

def evaluate(model, data_loader, device):
    """快速评估"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            final_out, aux_out = model(data)
            
            # 组合输出
            combined = 0.8 * F.softmax(final_out, dim=-1) + 0.2 * F.softmax(aux_out, dim=-1)
            preds = combined.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    # 配置 - 小模型但完整数据
    config = {
        'd_model': 192,
        'n_heads': 12,
        'n_layers': 4,
        'dropout': 0.2,
        'seq_len': 5,
        'batch_size': 32,  # 减小batch size因为数据更多
        'learning_rate': 3e-4,
        'weight_decay': 1e-4,
        'num_epochs': 40,  # 稍多的epochs
        'patience': 10
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    logging.info("Strategy: Lightweight model with FULL DATA (no truncation)")
    logging.info("Focus: Preserve REM data, achieve balanced performance")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据 - 不截断！
    logging.info("Loading FULL datasets (no truncation)...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None  # 不截断
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'],
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = LightweightModel(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # 尝试加载V8的主干权重
    v8_path = '../logs/v8_proper_eval_20250812_174838/best_model.pth'
    if os.path.exists(v8_path):
        logging.info(f"Loading V8 backbone weights from {v8_path}")
        checkpoint = torch.load(v8_path, map_location=device, weights_only=False)
        model_dict = model.state_dict()
        pretrained_dict = {k: v for k, v in checkpoint['model_state_dict'].items() 
                          if 'backbone' in k and k in model_dict and v.shape == model_dict[k].shape}
        model_dict.update(pretrained_dict)
        model.load_state_dict(model_dict, strict=False)
        logging.info(f"Loaded {len(pretrained_dict)} weights from V8")
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = EfficientLoss(device)
    optimizer = optim.AdamW(model.parameters(),
                           lr=config['learning_rate'],
                           weight_decay=config['weight_decay'])
    
    # 学习率调度
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5, min_lr=1e-6
    )
    
    # 训练
    best_val_acc = 0
    best_val_metrics = {}
    patience_counter = 0
    
    logging.info("STARTING TRAINING WITH FULL DATA!")
    logging.info("Ensuring REM data is preserved...")
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1, train_class_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch
        )
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(
            model, val_loader, device
        )
        
        # 学习率调度
        scheduler.step(val_acc)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Train Class F1: W={train_class_f1[0]:.3f}, N1={train_class_f1[1]:.3f}, "
                    f"N2={train_class_f1[2]:.3f}, N3={train_class_f1[3]:.3f}, REM={train_class_f1[4]:.3f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # 检查REM性能
        if val_class_f1[4] < 0.5:
            logging.warning(f"  ⚠️ REM F1 too low: {val_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_val_metrics = {
                'epoch': epoch + 1,
                'accuracy': val_acc,
                'f1': val_f1,
                'kappa': val_kappa,
                'class_f1': val_class_f1.tolist(),
                'confusion_matrix': val_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_metrics': best_val_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Val Acc: {val_acc:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 加载最佳模型进行测试
    logging.info("\n" + "="*80)
    logging.info("Loading best model for final test...")
    checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 测试
    test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(
        model, test_loader, device
    )
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS - V13 NO TRUNCATION")
    logging.info("="*80)
    logging.info(f"Best Validation Accuracy: {best_val_acc:.4f}")
    logging.info("-"*40)
    logging.info("FINAL TEST RESULTS:")
    logging.info(f"Test Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    logging.info(f"Test F1: {test_f1:.4f}")
    logging.info(f"Test Kappa: {test_kappa:.4f}")
    logging.info(f"Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
    
    # 检查REM性能
    if test_class_f1[4] < 0.6:
        logging.warning(f"⚠️ REM F1 is low: {test_class_f1[4]:.3f} - may indicate data issue")
    
    # N3→N2分析
    n3_to_n2 = test_cm[3, 2]
    logging.info(f"N3→N2 errors: {n3_to_n2}")
    
    # 混淆矩阵
    logging.info("\n🔄 Confusion Matrix:")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logging.info("       " + "  ".join([f"{name:>6}" for name in class_names]))
    for i, true_class in enumerate(class_names):
        row = test_cm[i]
        row_str = " ".join([f"{val:>6}" for val in row])
        logging.info(f"{true_class:>6} {row_str}")
    
    # 保存结果
    final_results = {
        'best_val_metrics': best_val_metrics,
        'test_metrics': {
            'accuracy': test_acc,
            'f1': test_f1,
            'kappa': test_kappa,
            'class_f1': test_class_f1.tolist(),
            'confusion_matrix': test_cm.tolist()
        },
        'config': config
    }
    
    with open(os.path.join(log_dir, 'final_results.json'), 'w') as f:
        json.dump(final_results, f, indent=2)
    
    logging.info(f"\nResults saved to {log_dir}")

if __name__ == "__main__":
    main()