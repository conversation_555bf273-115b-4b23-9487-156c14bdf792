#!/usr/bin/env python3
"""
测试伪标签数据集修复
"""

import sys
import os
import torch
import numpy as np
from torch.utils.data import DataLoader

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入修复后的PseudoLabelDataset
from training.pseudo_labeling_v21 import PseudoLabelDataset

def test_pseudo_dataset():
    """测试PseudoLabelDataset的数据类型"""
    print("Testing PseudoLabelDataset...")
    
    # 创建模拟数据
    n_samples = 10
    seq_len = 7
    n_features = 3000
    n_channels = 3
    n_classes = 5
    
    # 模拟生成的伪标签数据（混合类型）
    pseudo_data = []
    pseudo_probs = []
    
    for i in range(n_samples):
        # 模拟从GPU来的tensor数据（已经.cpu()）
        data = torch.randn(seq_len, n_features, n_channels)
        # 模拟概率（numpy数组）
        probs = np.random.rand(seq_len, n_classes)
        probs = probs / probs.sum(axis=-1, keepdims=True)  # 归一化
        
        pseudo_data.append(data)
        pseudo_probs.append(probs)
    
    # 创建PseudoLabelDataset
    dataset = PseudoLabelDataset(pseudo_data, pseudo_probs, confidence_threshold=0.5)
    
    print(f"Dataset size: {len(dataset)}")
    
    # 测试单个样本
    if len(dataset) > 0:
        data_item, label_item = dataset[0]
        print(f"Data type: {type(data_item)}, shape: {data_item.shape}")
        print(f"Label type: {type(label_item)}, shape: {label_item.shape}")
        
        # 验证是否为tensor
        assert isinstance(data_item, torch.Tensor), "Data should be torch.Tensor"
        assert isinstance(label_item, torch.Tensor), "Label should be torch.Tensor"
        print("✓ Single item test passed")
    
    # 测试DataLoader
    if len(dataset) > 0:
        dataloader = DataLoader(dataset, batch_size=2, shuffle=False, num_workers=0)
        
        try:
            for batch_idx, (batch_data, batch_labels) in enumerate(dataloader):
                print(f"Batch {batch_idx}: data shape {batch_data.shape}, labels shape {batch_labels.shape}")
                assert isinstance(batch_data, torch.Tensor), "Batch data should be torch.Tensor"
                assert isinstance(batch_labels, torch.Tensor), "Batch labels should be torch.Tensor"
                
                if batch_idx >= 2:  # 只测试前几个批次
                    break
            
            print("✓ DataLoader test passed")
            
        except Exception as e:
            print(f"✗ DataLoader test failed: {e}")
            return False
    
    # 测试多进程DataLoader
    if len(dataset) > 2:
        dataloader_mp = DataLoader(dataset, batch_size=2, shuffle=False, num_workers=2)
        
        try:
            for batch_idx, (batch_data, batch_labels) in enumerate(dataloader_mp):
                print(f"Multi-process batch {batch_idx}: data shape {batch_data.shape}, labels shape {batch_labels.shape}")
                assert isinstance(batch_data, torch.Tensor), "Batch data should be torch.Tensor"
                assert isinstance(batch_labels, torch.Tensor), "Batch labels should be torch.Tensor"
                
                if batch_idx >= 1:  # 只测试前几个批次
                    break
            
            print("✓ Multi-process DataLoader test passed")
            
        except Exception as e:
            print(f"✗ Multi-process DataLoader test failed: {e}")
            return False
    
    print("\n✅ All tests passed! The fix is working correctly.")
    return True

if __name__ == "__main__":
    success = test_pseudo_dataset()
    sys.exit(0 if success else 1)