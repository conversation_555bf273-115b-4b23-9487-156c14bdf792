#!/bin/bash
# 渐进式训练执行脚本

echo "====================================="
echo "V14 Progressive Training Pipeline"
echo "====================================="

# Step 1: 基准验证 (如果还没完成)
if [ ! -f "best_model_baseline.pth" ]; then
    echo "[Step 1] Starting baseline training..."
    python train_v14_progressive_step1.py > v14_step1_training.log 2>&1
    
    # 检查是否成功
    if [ $? -eq 0 ]; then
        echo "[Step 1] ✅ Baseline training completed"
    else
        echo "[Step 1] ❌ Baseline training failed"
        exit 1
    fi
else
    echo "[Step 1] ✅ Baseline model already exists"
fi

# Step 2: 渐进式分类
echo ""
echo "[Step 2] Starting progressive classification training..."
python train_v14_progressive_step2.py > v14_step2_training.log 2>&1 &
STEP2_PID=$!

echo "[Step 2] Training started with PID: $STEP2_PID"
echo "Monitoring training progress..."

# 监控Step 2训练
while kill -0 $STEP2_PID 2>/dev/null; do
    # 检查最新的准确率
    LATEST_ACC=$(tail -n 50 v14_step2_training.log | grep "Test (Progressive): Acc=" | tail -1 | sed 's/.*Acc=\([0-9.]*\).*/\1/')
    
    if [ ! -z "$LATEST_ACC" ]; then
        ACC_PERCENT=$(echo "$LATEST_ACC * 100" | bc)
        echo "[Step 2] Latest accuracy: ${ACC_PERCENT}%"
        
        # 检查是否达到87%
        if (( $(echo "$LATEST_ACC >= 0.87" | bc -l) )); then
            echo "[Step 2] 🎉 Achieved 87% target!"
            echo "Waiting for training to complete..."
        fi
    fi
    
    sleep 60
done

wait $STEP2_PID
STEP2_RESULT=$?

if [ $STEP2_RESULT -eq 0 ]; then
    echo "[Step 2] ✅ Progressive training completed"
    
    # 检查最终准确率
    FINAL_ACC=$(tail -n 100 v14_step2_training.log | grep "Best Test Accuracy:" | sed 's/.*Best Test Accuracy: \([0-9.]*\).*/\1/')
    
    if [ ! -z "$FINAL_ACC" ]; then
        ACC_PERCENT=$(echo "$FINAL_ACC * 100" | bc)
        echo "Final accuracy: ${ACC_PERCENT}%"
        
        if (( $(echo "$FINAL_ACC >= 0.87" | bc -l) )); then
            echo "✅ SUCCESS: Achieved 87% target!"
            echo "No need for Step 3 (Temperature Calibration)"
            exit 0
        fi
    fi
else
    echo "[Step 2] ❌ Progressive training failed"
fi

# Step 3: 温度校准 (如果Step 2没有达到87%)
echo ""
echo "[Step 3] Starting temperature calibration..."
python train_v14_progressive_step3.py > v14_step3_training.log 2>&1

if [ $? -eq 0 ]; then
    echo "[Step 3] ✅ Temperature calibration completed"
    
    # 检查最终结果
    FINAL_ACC=$(tail -n 100 v14_step3_training.log | grep "Best Test Accuracy:" | sed 's/.*Best Test Accuracy: \([0-9.]*\).*/\1/')
    
    if [ ! -z "$FINAL_ACC" ]; then
        ACC_PERCENT=$(echo "$FINAL_ACC * 100" | bc)
        echo "Final accuracy after calibration: ${ACC_PERCENT}%"
        
        if (( $(echo "$FINAL_ACC >= 0.87" | bc -l) )); then
            echo "✅ SUCCESS: Achieved 87% target with calibration!"
        else
            GAP=$(echo "0.87 - $FINAL_ACC" | bc)
            GAP_PERCENT=$(echo "$GAP * 100" | bc)
            echo "⚠️ Gap to 87%: ${GAP_PERCENT}%"
        fi
    fi
else
    echo "[Step 3] ❌ Temperature calibration failed"
fi

echo ""
echo "====================================="
echo "Training pipeline completed"
echo "====================================="