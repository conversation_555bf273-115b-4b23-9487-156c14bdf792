#!/bin/bash
# 渐进式实验执行脚本

echo "=================================="
echo "V14 Progressive Training Pipeline"
echo "=================================="

# Step 1: Baseline (如果还没完成)
echo "[Step 1] Checking baseline training..."
if [ ! -f "../logs/v14_progressive_*/best_model_baseline.pth" ]; then
    echo "Starting Step 1: Baseline training..."
    python train_v14_progressive_step1.py
    echo "Step 1 completed!"
else
    echo "Step 1 already completed, skipping..."
fi

# Step 2: Progressive Classification
echo ""
echo "[Step 2] Starting Progressive Classification..."
python train_v14_progressive_step2.py
echo "Step 2 completed!"

# 检查Step 2的结果
STEP2_ACC=$(python -c "
import glob
import torch
import json

models = glob.glob('../logs/v14_progressive_step2_*/results_progressive.json')
if models:
    with open(models[0]) as f:
        results = json.load(f)
        print(f\"{results['accuracy']:.4f}\")
else:
    print('0.0')
")

echo "Step 2 Test Accuracy: $STEP2_ACC"

# 如果Step 2没有达到87%，运行Step 3
if (( $(echo "$STEP2_ACC < 0.87" | bc -l) )); then
    echo ""
    echo "[Step 3] Step 2 didn't reach 87%, starting Temperature Calibration..."
    python train_v14_progressive_step3.py
    echo "Step 3 completed!"
else
    echo ""
    echo "✅ Target achieved in Step 2! (>= 87%)"
fi

echo ""
echo "=================================="
echo "All experiments completed!"
echo "=================================="