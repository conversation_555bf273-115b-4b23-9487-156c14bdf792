#!/usr/bin/env python3
"""
🎯 第1阶段高级训练：确保达到85%目标
使用更先进的策略从79.4%提升到85%+
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts, ReduceLROnPlateau
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import (
    confusion_matrix,
    classification_report,
    accuracy_score,
    f1_score,
    cohen_kappa_score,
)
import warnings

warnings.filterwarnings("ignore")

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.progressive_mambaformer_v1_fixed import ProgressiveMAMBAFORMER_V1_Fixed
from utils.sequence_dataset import SequenceSleepDataset


class FocalLoss(nn.Module):
    """Focal Loss专门处理类别不平衡"""
    def __init__(self, alpha=None, gamma=2.0, device="cuda"):
        super().__init__()
        self.gamma = gamma
        if alpha is not None:
            self.alpha = torch.tensor(alpha).to(device)
        else:
            self.alpha = None
        self.device = device

    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            batch_size, seq_len, n_classes = inputs.shape
            inputs = inputs.reshape(-1, n_classes)
            targets = targets.reshape(-1)

        ce_loss = F.cross_entropy(inputs, targets, reduction="none")
        p_t = torch.exp(-ce_loss)
        focal_loss = (1 - p_t) ** self.gamma * ce_loss

        if self.alpha is not None:
            alpha_t = self.alpha[targets]
            focal_loss = alpha_t * focal_loss

        return focal_loss.mean()


def train_one_epoch(model, data_loader, criterion, optimizer, device, epoch, config):
    """训练一个epoch - 高级版本"""
    model.train()
    running_loss = 0.0
    correct_predictions = 0
    total_predictions = 0
    
    # 类别统计
    class_correct = [0] * 5
    class_total = [0] * 5
    
    progress_bar = tqdm(data_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']}")
    
    for batch_idx, (data, target) in enumerate(progress_bar):
        data, target = data.to(device), target.to(device)
        
        # 转换数据格式
        data = data.permute(0, 1, 3, 2)
        
        # 轻微数据增强
        if torch.rand(1).item() < 0.15:
            noise_std = data.std() * 0.01
            noise = torch.randn_like(data) * noise_std
            data = data + noise
        
        optimizer.zero_grad()
        
        # 只使用主输出
        main_output, _ = model(data)
        
        # 先计算准确率（使用原始形状）
        batch_size, seq_len, n_classes = main_output.shape
        _, predicted = torch.max(main_output[:, seq_len//2, :], dim=-1)
        true_labels = target[:, seq_len//2]
        
        # 统计各类别准确率
        for label, pred in zip(true_labels, predicted):
            class_total[label] += 1
            if label == pred:
                class_correct[label] += 1
        
        correct_predictions += (predicted == true_labels).sum().item()
        total_predictions += true_labels.size(0)
        
        # 重塑以适应损失函数
        main_output_flat = main_output.reshape(-1, n_classes)
        target_flat = target.reshape(-1)
        
        # 计算损失
        loss = criterion(main_output_flat, target_flat)
        
        # 时间一致性正则化
        if seq_len > 1:
            diff = torch.diff(main_output, dim=1)
            smooth_loss = torch.mean(diff ** 2) * 0.01
            loss += smooth_loss
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
        optimizer.step()
        
        running_loss += loss.item()
        
        current_acc = 100 * correct_predictions / total_predictions
        current_lr = optimizer.param_groups[0]['lr']
        
        # 显示各类别准确率
        class_accs = [100*c/t if t > 0 else 0 for c, t in zip(class_correct, class_total)]
        
        progress_bar.set_postfix({
            'loss': f'{running_loss/(batch_idx+1):.4f}',
            'acc': f'{current_acc:.2f}%',
            'N1': f'{class_accs[1]:.1f}%',  # 重点关注N1
            'lr': f'{current_lr:.6f}'
        })
    
    epoch_loss = running_loss / len(data_loader)
    epoch_acc = 100 * correct_predictions / total_predictions
    
    # 记录各类别准确率
    class_accuracies = {}
    for i, class_name in enumerate(['W', 'N1', 'N2', 'N3', 'REM']):
        if class_total[i] > 0:
            class_accuracies[class_name] = 100 * class_correct[i] / class_total[i]
        else:
            class_accuracies[class_name] = 0
    
    return epoch_loss, epoch_acc, class_accuracies


def evaluate(model, data_loader, device):
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc="Evaluating"):
            data = data.to(device)
            data = data.permute(0, 1, 3, 2)
            main_output, _ = model(data)
            
            seq_len = main_output.size(1)
            predictions = torch.argmax(main_output[:, seq_len//2, :], dim=-1)
            targets = target[:, seq_len//2]
            
            all_predictions.extend(predictions.cpu().numpy())
            all_targets.extend(targets.numpy())
    
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    
    accuracy = accuracy_score(all_targets, all_predictions)
    f1_macro = f1_score(all_targets, all_predictions, average='macro')
    kappa = cohen_kappa_score(all_targets, all_predictions)
    
    report = classification_report(all_targets, all_predictions, 
                                 target_names=['W', 'N1', 'N2', 'N3', 'REM'],
                                 output_dict=True)
    
    cm = confusion_matrix(all_targets, all_predictions)
    
    return {
        'accuracy': accuracy,
        'f1_macro': f1_macro,
        'kappa': kappa,
        'classification_report': report,
        'confusion_matrix': cm
    }


def main():
    config = {
        "d_model": 512,
        "n_heads": 32,
        "n_layers": 12,
        "dropout": 0.15,
        "seq_len": 7,
        "batch_size": 20,  # 稍大的batch size
        "learning_rate": 3e-5,  # 更低的初始学习率
        "num_epochs": 30,  # 更多epochs
        "gradient_clip": 0.5,
        "weight_decay": 0.01,
        "warmup_epochs": 2,
    }

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_advanced_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(os.path.join(log_dir, "training.log")),
            logging.StreamHandler(),
        ],
    )

    logging.info("=" * 80)
    logging.info("🎯 第1阶段高级训练：确保达到85%目标")
    logging.info("=" * 80)
    logging.info(f"配置: {config}")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"Device: {device}")

    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"

    # 数据集
    train_files = [
        "SC4181E0.npz", "SC4182E0.npz", "SC4161E0.npz", "SC4162E0.npz",
        "SC4131E0.npz", "SC4101E0.npz", "SC4102E0.npz", "SC4111E0.npz",
        "SC4112E0.npz", "SC4041E0.npz", "SC4042E0.npz", "SC4191E0.npz",
        "SC4192E0.npz", "SC4061E0.npz", "SC4062E0.npz", "SC4091E0.npz",
        "SC4092E0.npz", "SC4121E0.npz", "SC4122E0.npz", "SC4141E0.npz",
        "SC4142E0.npz", "SC4051E0.npz", "SC4052E0.npz", "SC4081E0.npz",
        "SC4082E0.npz", "SC4151E0.npz", "SC4152E0.npz", "SC4171E0.npz",
        "SC4172E0.npz",
    ]
    val_files = [
        "SC4021E0.npz", "SC4022E0.npz", "SC4031E0.npz",
        "SC4032E0.npz", "SC4071E0.npz", "SC4072E0.npz",
    ]
    test_files = [
        "SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"
    ]

    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]

    logging.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=True,
    )

    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )

    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")

    model = ProgressiveMAMBAFORMER_V1_Fixed(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)

    # 尝试加载最佳模型
    best_model_paths = [
        "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_simple_*/best_model.pth",
        "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_full_fixed_20250815_031333/best_model.pth"
    ]
    
    import glob
    for pattern in best_model_paths:
        matches = glob.glob(pattern)
        if matches:
            best_model_path = sorted(matches)[-1]  # 获取最新的
            if os.path.exists(best_model_path):
                logging.info(f"加载最佳模型: {best_model_path}")
                checkpoint = torch.load(best_model_path)
                model.load_state_dict(checkpoint['model_state_dict'])
                logging.info(f"✅ 成功加载模型")
                break

    # 评估初始性能
    logging.info("\n评估初始性能...")
    initial_test = evaluate(model, test_loader, device)
    initial_acc = initial_test['accuracy'] * 100
    logging.info(f"初始测试准确率: {initial_acc:.2f}%")

    # 特别强化N1类别的权重（这是最弱的类）
    # 分析显示N1的准确率最低，需要特别关注
    class_weights = torch.tensor([1.5, 10.0, 0.8, 2.5, 1.5]).to(device)
    criterion = FocalLoss(alpha=class_weights, gamma=2.5, device=device)

    # 分层学习率 - 分类器头部使用更高的学习率
    base_params = []
    head_params = []
    for name, param in model.named_parameters():
        if 'classifier' in name or 'auxiliary' in name:
            head_params.append(param)
        else:
            base_params.append(param)
    
    optimizer = optim.AdamW([
        {'params': base_params, 'lr': config["learning_rate"] * 0.5},
        {'params': head_params, 'lr': config["learning_rate"] * 2}
    ], weight_decay=config["weight_decay"])

    # 使用Cosine Annealing with Warm Restarts
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=5,  # 每5个epoch重启
        T_mult=2,  # 每次重启周期翻倍
        eta_min=1e-7
    )
    
    # 额外的Plateau调度器
    scheduler_plateau = ReduceLROnPlateau(
        optimizer,
        mode='max',
        factor=0.5,
        patience=3,
        min_lr=1e-7,
        verbose=True
    )

    best_val_acc = initial_acc
    best_test_acc = initial_acc
    best_epoch = -1
    patience = 10
    patience_counter = 0
    
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_acc': [],
        'test_acc': [],
        'class_accuracies': []
    }
    
    for epoch in range(config["num_epochs"]):
        logging.info(f"\n{'='*60}")
        logging.info(f"Epoch {epoch+1}/{config['num_epochs']}")
        
        # Warmup期间使用更低的学习率
        if epoch < config["warmup_epochs"]:
            warmup_factor = (epoch + 1) / config["warmup_epochs"]
            for param_group in optimizer.param_groups:
                param_group['lr'] = param_group['initial_lr'] * warmup_factor * 0.3
            logging.info(f"Warmup: 学习率因子 {warmup_factor:.2f}")
        
        train_loss, train_acc, class_accs = train_one_epoch(
            model, train_loader, criterion, optimizer, 
            device, epoch, config
        )
        
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        logging.info(f"各类别准确率: W={class_accs['W']:.1f}%, N1={class_accs['N1']:.1f}%, "
                    f"N2={class_accs['N2']:.1f}%, N3={class_accs['N3']:.1f}%, REM={class_accs['REM']:.1f}%")
        
        # 验证
        val_metrics = evaluate(model, val_loader, device)
        val_acc = val_metrics['accuracy'] * 100
        
        # 测试
        test_metrics = evaluate(model, test_loader, device)
        test_acc = test_metrics['accuracy'] * 100
        
        logging.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_metrics['f1_macro']:.4f}")
        logging.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_metrics['f1_macro']:.4f}")
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_acc'].append(val_acc)
        history['test_acc'].append(test_acc)
        history['class_accuracies'].append(class_accs)
        
        # 检查是否达到目标
        if test_acc >= 85.0:
            logging.info(f"🎉🎉🎉 达到目标！测试准确率: {test_acc:.2f}% >= 85%")
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config,
                'history': history,
            }, os.path.join(log_dir, 'target_achieved_model.pth'))
            
            # Git commit
            os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '✅ Stage 1达到85%目标: {test_acc:.2f}% (高级训练版本)'")
            
            # 生成结果摘要
            result_summary = {
                'stage': '第1阶段高级训练',
                'test_accuracy': test_acc,
                'test_f1_macro': test_metrics['f1_macro'],
                'test_kappa': test_metrics['kappa'],
                'best_epoch': epoch,
                'config': config,
                'timestamp': timestamp,
            }
            
            with open(os.path.join(log_dir, 'result_summary.json'), 'w') as f:
                json.dump(result_summary, f, indent=2)
            
            # 启动下一阶段
            logging.info("启动第2阶段训练...")
            os.system("python /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_cross_attention.py &")
            break
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_val_acc = val_acc
            best_epoch = epoch
            patience_counter = 0
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config,
                'history': history,
            }, os.path.join(log_dir, 'best_model.pth'))
            logging.info(f"✅ 保存最佳模型 (Test: {test_acc:.2f}%)")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
        
        # 学习率调度
        if epoch >= config["warmup_epochs"]:
            scheduler.step()
            scheduler_plateau.step(test_acc)
        
        # 显示各类别性能
        logging.info("\n测试集各类别性能:")
        for class_name in ['W', 'N1', 'N2', 'N3', 'REM']:
            class_metrics = test_metrics['classification_report'][class_name]
            logging.info(f"{class_name:>4}: Precision={class_metrics['precision']:.3f}, "
                        f"Recall={class_metrics['recall']:.3f}, "
                        f"F1={class_metrics['f1-score']:.3f}")
    
    logging.info(f"\n{'='*60}")
    logging.info(f"训练完成! 最佳测试准确率: {best_test_acc:.2f}% (Epoch {best_epoch+1})")
    
    # 保存训练历史
    with open(os.path.join(log_dir, 'training_history.json'), 'w') as f:
        json.dump(history, f, indent=2)
    
    if best_test_acc < 85.0:
        logging.info(f"⚠️ 未达到85%目标 (最佳: {best_test_acc:.2f}%)")
        logging.info("可能需要更多数据增强或模型架构调整")
    
    return best_test_acc


if __name__ == "__main__":
    final_acc = main()
    print(f"\n最终准确率: {final_acc:.2f}%")