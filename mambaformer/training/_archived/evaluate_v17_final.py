#!/usr/bin/env python3
"""
Evaluate V17 model for final results
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score, f1_score, cohen_kappa_score
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


def evaluate_v17():
    """Evaluate V17 model"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f"../logs/v17_final_evaluation_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 V17 FINAL EVALUATION")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # V17 configuration
    config = {
        'd_model': 288,
        'n_heads': 18,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5
    }
    
    logging.info("\n📋 V17 Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Load model
    model_path = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v17_stable_20250811_004457/best_model.pth'
    
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # Load with weights_only=False to handle numpy arrays
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    logging.info(f"✅ Loaded V17 model from epoch {checkpoint.get('epoch', 'unknown')}")
    logging.info(f"  Val Acc: {checkpoint.get('val_acc', 0):.4f}")
    logging.info(f"  Val F1: {checkpoint.get('val_f1', 0):.4f}")
    logging.info(f"  Val Kappa: {checkpoint.get('val_kappa', 0):.4f}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    logging.info(f"\n📂 Test files: {len(test_files)}")
    
    # Create test dataset
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=32,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    logging.info(f"Test samples: {test_dataset.total_epochs} epochs")
    
    # Evaluate
    evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
    evaluator.total_epochs = test_dataset.total_epochs
    
    logging.info("\n📊 Evaluating on test set...")
    
    with torch.no_grad():
        batch_start_idx = 0
        for data, labels in tqdm(test_loader, desc='Testing'):
            data = data.to(device)
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    # Get metrics
    metrics = evaluator.evaluate()
    accuracy = metrics['accuracy']
    macro_f1 = metrics['macro_f1']
    kappa = metrics['kappa']
    
    # Get predictions for confusion matrix
    final_preds, final_labels, _ = evaluator.get_final_predictions()
    cm = confusion_matrix(final_labels, final_preds)
    
    logging.info("\n" + "="*80)
    logging.info("📊 TEST RESULTS")
    logging.info("="*80)
    
    logging.info(f"\n🎯 FINAL PERFORMANCE:")
    logging.info(f"  Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    logging.info(f"  Macro F1: {macro_f1:.4f} ({macro_f1*100:.2f}%)")
    logging.info(f"  Kappa: {kappa:.4f}")
    
    # Check targets
    logging.info("\n🎯 TARGET ACHIEVEMENT (87% / 80% / 0.80):")
    success = True
    
    if accuracy >= 0.87:
        logging.info(f"  ✅ Accuracy: {accuracy:.4f} ≥ 0.87")
    else:
        logging.info(f"  ❌ Accuracy: {accuracy:.4f} < 0.87 (gap: {0.87-accuracy:.4f})")
        success = False
    
    if macro_f1 >= 0.80:
        logging.info(f"  ✅ Macro F1: {macro_f1:.4f} ≥ 0.80")
    else:
        logging.info(f"  ❌ Macro F1: {macro_f1:.4f} < 0.80 (gap: {0.80-macro_f1:.4f})")
        success = False
    
    if kappa >= 0.80:
        logging.info(f"  ✅ Kappa: {kappa:.4f} ≥ 0.80")
    else:
        logging.info(f"  ❌ Kappa: {kappa:.4f} < 0.80 (gap: {0.80-kappa:.4f})")
        success = False
    
    # Confusion matrix
    logging.info("\n📊 Confusion Matrix:")
    logging.info("     Wake   N1    N2    N3   REM")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    for i, row in enumerate(cm):
        row_str = f"{class_names[i]:4} "
        for val in row:
            row_str += f"{val:5d} "
        logging.info(row_str)
    
    # Per-class metrics
    logging.info("\n📊 Per-Class Performance:")
    for class_name in class_names:
        class_metrics = metrics['per_class_metrics'][class_name]
        logging.info(f"{class_name}:")
        logging.info(f"  Precision: {class_metrics['precision']*100:.1f}%")
        logging.info(f"  Recall: {class_metrics['recall']*100:.1f}%")
        logging.info(f"  F1-Score: {class_metrics['f1']*100:.1f}%")
    
    # Save results
    results = {
        'timestamp': timestamp,
        'model': 'V17_Stable',
        'config': config,
        'checkpoint_info': {
            'epoch': checkpoint.get('epoch', 'unknown'),
            'val_acc': float(checkpoint.get('val_acc', 0)),
            'val_f1': float(checkpoint.get('val_f1', 0)),
            'val_kappa': float(checkpoint.get('val_kappa', 0))
        },
        'test_results': {
            'accuracy': float(accuracy),
            'macro_f1': float(macro_f1),
            'kappa': float(kappa)
        },
        'confusion_matrix': cm.tolist(),
        'per_class_metrics': metrics['per_class_metrics'],
        'targets_achieved': success
    }
    
    results_file = f"../logs/v17_final_results_{timestamp}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {results_file}")
    
    if success:
        logging.info("\n" + "="*80)
        logging.info("🎉🎉🎉 SUCCESS! ALL TARGETS ACHIEVED! 🎉🎉🎉")
        logging.info("V17 Model Ready for ICASSP 2026!")
        logging.info("="*80)
    else:
        logging.info("\n📈 V17 performance. Close to targets but needs further optimization.")
        logging.info("\nRecommendations:")
        logging.info("1. Continue training V17 for more epochs")
        logging.info("2. Try ensemble with other available models")
        logging.info("3. Fine-tune hyperparameters")
        logging.info("4. Implement data augmentation")
    
    return results


if __name__ == "__main__":
    results = evaluate_v17()