#!/usr/bin/env python3
"""
测试V9 N1专家模型
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix, precision_recall_fscore_support
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

class N1SimpleModel(nn.Module):
    """简化的N1专家模型 - 专注于N1检测"""
    def __init__(self, input_channels, n_classes, d_model, n_heads, n_layers, dropout, seq_len):
        super().__init__()
        
        # 主干网络
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # N1专门检测分支
        self.n1_detector = nn.Sequential(
            nn.Linear(n_classes, d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 2)
        )
        
        # N2/N3边界检测
        self.deep_sleep_detector = nn.Sequential(
            nn.Linear(n_classes, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 2)
        )
        
        # 阶段转换检测
        self.transition_detector = nn.Sequential(
            nn.Linear(n_classes, d_model // 4),
            nn.ReLU(),
            nn.Linear(d_model // 4, n_classes)
        )
        
        # 融合
        self.fusion = nn.Sequential(
            nn.Linear(n_classes + 2 + 2 + n_classes, d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, n_classes)
        )
        
        self.n1_boost = nn.Parameter(torch.tensor(2.0))
        
    def forward(self, x):
        backbone_out, _ = self.backbone(x)
        
        if backbone_out.dim() == 3:
            center_out = backbone_out[:, backbone_out.shape[1]//2, :]
        else:
            center_out = backbone_out
        
        n1_detection = self.n1_detector(center_out)
        n1_prob = F.softmax(n1_detection, dim=-1)[:, 1:2]
        
        deep_sleep = self.deep_sleep_detector(center_out)
        transition = self.transition_detector(center_out)
        
        combined = torch.cat([center_out, n1_detection, deep_sleep, transition], dim=-1)
        final_out = self.fusion(combined)
        
        final_out[:, 1] = final_out[:, 1] + self.n1_boost * n1_prob.squeeze()
        
        return final_out, center_out, n1_detection, deep_sleep

def evaluate_with_tta(model, data_loader, device, n_tta=5):
    """评估函数 - 带TTA"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Testing with TTA'):
            data = data.to(device)
            
            predictions = []
            for i in range(n_tta):
                if i == 0:
                    data_aug = data
                elif i == 1:
                    data_aug = data + torch.randn_like(data) * 0.002
                elif i == 2:
                    shift = random.randint(-20, 20)
                    data_aug = torch.roll(data, shifts=shift, dims=-1)
                elif i == 3:
                    data_aug = data * (0.97 + random.random() * 0.06)
                else:
                    data_aug = data + torch.randn_like(data) * 0.003
                
                final_out, center_out, n1_detection, deep_sleep = model(data_aug)
                
                combined = 0.7 * F.softmax(final_out, dim=-1) + 0.3 * F.softmax(center_out, dim=-1)
                
                n1_prob = F.softmax(n1_detection, dim=-1)[:, 1]
                combined[:, 1] = combined[:, 1] * (1 + n1_prob * 0.3)
                
                predictions.append(combined)
            
            avg_probs = torch.stack(predictions).mean(dim=0)
            preds = avg_probs.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    return np.array(all_targets), np.array(all_preds)

def main():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载测试数据
    print("Loading test dataset...")
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=5,
        max_samples_per_file=None
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=32,
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    print(f"Test dataset size: {len(test_dataset)}")
    
    # 加载模型
    model_path = '../logs/v9_n1_simple_20250812_185651/best_model.pth'
    print(f"Loading model from {model_path}")
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    config = checkpoint.get('config', {
        'd_model': 192,
        'n_heads': 12,
        'n_layers': 5,
        'dropout': 0.15,
        'seq_len': 5
    })
    
    model = N1SimpleModel(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"Model loaded - Val Acc: {checkpoint.get('val_acc', 'N/A'):.4f}, N1 F1: {checkpoint.get('val_n1_f1', 'N/A'):.3f}")
    
    # 测试
    print("\nEvaluating on test set with 5x TTA...")
    test_targets, test_preds = evaluate_with_tta(model, test_loader, device, n_tta=5)
    
    # 计算指标
    test_acc = accuracy_score(test_targets, test_preds)
    test_f1 = f1_score(test_targets, test_preds, average='macro')
    test_kappa = cohen_kappa_score(test_targets, test_preds)
    test_class_f1 = f1_score(test_targets, test_preds, average=None)
    
    precision, recall, _, support = precision_recall_fscore_support(
        test_targets, test_preds, average=None
    )
    
    test_cm = confusion_matrix(test_targets, test_preds)
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    
    # 结果
    print("\n" + "="*80)
    print("📊 V9 N1 SPECIALIST TEST RESULTS")
    print("="*80)
    print(f"\n🎯 Overall Metrics:")
    print(f"  Test Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    print(f"  Test Macro F1: {test_f1:.4f}")
    print(f"  Test Kappa: {test_kappa:.4f}")
    
    print(f"\n📈 Per-Class Metrics:")
    for i, class_name in enumerate(class_names):
        print(f"  {class_name:>6}: F1={test_class_f1[i]:.3f}, Prec={precision[i]:.3f}, "
              f"Recall={recall[i]:.3f}, Support={support[i]}")
    
    print(f"\n🎯 N1 Improvement Analysis:")
    print(f"  Previous N1 F1: 0.488")
    print(f"  Current N1 F1: {test_class_f1[1]:.3f}")
    improvement = test_class_f1[1] - 0.488
    print(f"  Improvement: {improvement:.3f} ({improvement/0.488*100:.1f}% relative)")
    
    if test_class_f1[1] >= 0.6:
        print(f"  ✅ N1 F1 TARGET ACHIEVED! (>0.6)")
    
    print(f"\n🔄 Confusion Matrix:")
    print("       " + "  ".join([f"{name:>6}" for name in class_names]))
    for i, true_class in enumerate(class_names):
        row = test_cm[i]
        row_str = " ".join([f"{val:>6}" for val in row])
        print(f"{true_class:>6} {row_str}")
    
    # N3→N2混淆分析
    n3_to_n2 = test_cm[3, 2]
    print(f"\n💤 Deep Sleep Confusion:")
    print(f"  N3→N2 errors: {n3_to_n2} (was 332)")
    
    # 与90%目标对比
    gap_to_90 = 0.90 - test_acc
    print(f"\n🎯 Progress to 90% Target:")
    print(f"  Previous best: 84.44%")
    print(f"  Current: {test_acc*100:.2f}%")
    if test_acc >= 0.90:
        print(f"  ✅ 90% TARGET ACHIEVED!")
        print("\n🎉🎉🎉 SUCCESS! REACHED 90% ACCURACY! 🎉🎉🎉")
    else:
        print(f"  Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
        print(f"  Progress: {test_acc - 0.8444:.4f}")

if __name__ == "__main__":
    main()