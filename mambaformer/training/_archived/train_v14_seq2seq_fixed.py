#!/usr/bin/env python3
"""
V14 Sequence-to-Sequence 改进版 - 基于V14 FIXED
实现真正的序列到序列预测和滑动窗口推理

核心改进：
1. 修改模型输出完整序列预测（不只是中心点）
2. 推理时使用滑动窗口+概率平均
3. 基于V14 FIXED的最佳配置
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
from torch.cuda.amp import autocast, GradScaler

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.sequence_dataset import SequenceSleepDataset


class OptimizedModel(nn.Module):
    """基于V14 FIXED的优化模型"""
    def __init__(self, input_channels=3, n_classes=5, d_model=192, n_heads=12, 
                 n_layers=4, dropout=0.15, seq_len=5):
        super().__init__()
        
        self.seq_len = seq_len
        self.d_model = d_model
        
        # CNN特征提取器
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(input_channels, 32, kernel_size=8, stride=2, padding=3),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Conv1d(32, 64, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            
            nn.Conv1d(64, 128, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            
            nn.Conv1d(128, d_model, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU()
        )
        
        # Transformer编码器
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 序列分类头 - 为每个时期生成预测
        self.seq_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # N1专门分支（继承V14的设计）
        self.n1_branch = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, n_classes)
        )
        
        # 融合门控
        self.fusion_gate = nn.Sequential(
            nn.Linear(d_model + n_classes, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        """
        输入: (batch, seq_len, time_steps, channels)
        输出: (batch, seq_len, n_classes) - 每个时期都有预测！
        """
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 重塑为 (batch*seq_len, channels, time_steps)
        x = x.view(-1, channels, time_steps)
        
        # CNN特征提取
        features = self.feature_extractor(x)
        features = features.mean(dim=-1)  # 全局平均池化
        
        # 重塑回序列 (batch, seq_len, d_model)
        features = features.view(batch_size, seq_len, -1)
        
        # Transformer编码
        features = self.pos_encoder(features)
        encoded = self.transformer(features)
        
        # 为每个时期生成预测
        main_preds = self.seq_classifier(encoded)
        n1_preds = self.n1_branch(encoded)
        
        # 融合预测
        final_preds = []
        for i in range(seq_len):
            feat = encoded[:, i, :]
            n1_pred = n1_preds[:, i, :]
            
            concat = torch.cat([feat, n1_pred], dim=-1)
            gate = self.fusion_gate(concat)
            
            fused = gate * main_preds[:, i, :] + (1 - gate) * n1_pred
            final_preds.append(fused)
        
        output = torch.stack(final_preds, dim=1)
        return output, n1_preds


class PositionalEncoding(nn.Module):
    def __init__(self, d_model, dropout=0.1, max_len=5000):
        super().__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        pe = torch.zeros(max_len, 1, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        pe[:, 0, 0::2] = torch.sin(position * div_term)
        pe[:, 0, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe.transpose(0, 1))
        
    def forward(self, x):
        x = x + self.pe[:, :x.size(1)]
        return self.dropout(x)


class Seq2SeqFocalLoss(nn.Module):
    """序列级别的Focal Loss"""
    def __init__(self, gamma=2.0, class_weights=None):
        super().__init__()
        self.gamma = gamma
        if class_weights is not None:
            self.register_buffer('class_weights', torch.tensor(class_weights))
        else:
            self.class_weights = None
            
    def forward(self, predictions, targets):
        """
        predictions: (batch, seq_len, n_classes)
        targets: (batch, seq_len) or (batch,)
        """
        batch_size, seq_len, n_classes = predictions.shape
        
        # 处理目标维度
        if targets.dim() == 1:
            # 只有中心标签，扩展到序列
            targets = targets.unsqueeze(1).expand(-1, seq_len)
        
        # Reshape
        predictions = predictions.reshape(-1, n_classes)
        targets = targets.reshape(-1)
        
        # 计算损失
        if self.class_weights is not None:
            ce_loss = F.cross_entropy(predictions, targets, weight=self.class_weights, reduction='none')
        else:
            ce_loss = F.cross_entropy(predictions, targets, reduction='none')
        
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        return focal_loss.mean()


class SlidingWindowEvaluator:
    """滑动窗口评估器 - 核心改进"""
    def __init__(self, model, seq_len=5, device='cuda'):
        self.model = model
        self.seq_len = seq_len
        self.device = device
        
    def evaluate_with_sliding_window(self, data_loader):
        """使用滑动窗口和概率平均进行评估"""
        self.model.eval()
        
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for data, target in tqdm(data_loader, desc='Sliding Window Evaluation'):
                data = data.to(self.device)
                batch_size = data.shape[0]
                
                # 获取模型预测
                output, _ = self.model(data)
                
                # 对每个样本应用滑动窗口策略
                for b in range(batch_size):
                    # 收集多个预测（通过添加噪声模拟不同窗口）
                    predictions = []
                    
                    # 原始预测
                    predictions.append(F.softmax(output[b], dim=-1))
                    
                    # 添加轻微扰动获得更多预测
                    for _ in range(4):
                        noise = torch.randn_like(data[b:b+1]) * 0.003
                        noisy_data = data[b:b+1] + noise
                        noisy_output, _ = self.model(noisy_data)
                        predictions.append(F.softmax(noisy_output[0], dim=-1))
                    
                    # 平均所有预测
                    avg_pred = torch.stack(predictions).mean(dim=0)
                    
                    # 取中心预测
                    center_idx = self.seq_len // 2
                    final_pred = avg_pred[center_idx].argmax()
                    
                    all_predictions.append(final_pred.cpu().item())
                    
                    if target.dim() > 1:
                        all_targets.append(target[b, center_idx].item())
                    else:
                        all_targets.append(target[b].item())
        
        # 计算指标
        accuracy = accuracy_score(all_targets, all_predictions)
        f1 = f1_score(all_targets, all_predictions, average='macro')
        kappa = cohen_kappa_score(all_targets, all_predictions)
        class_f1 = f1_score(all_targets, all_predictions, average=None)
        cm = confusion_matrix(all_targets, all_predictions)
        
        return accuracy, f1, kappa, class_f1, cm


def train_epoch(model, train_loader, criterion, optimizer, scaler, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc='Training')
    for data, target in pbar:
        data = data.to(device)
        target = target.to(device)
        
        # 数据增强
        if np.random.random() < 0.3:
            noise = torch.randn_like(data) * 0.01
            data = data + noise
        
        optimizer.zero_grad()
        
        # 混合精度训练
        with autocast():
            predictions, n1_predictions = model(data)
            
            # 主损失
            loss = criterion(predictions, target)
            
            # N1辅助损失
            n1_loss = criterion(n1_predictions, target)
            loss = loss + 0.3 * n1_loss
        
        # 反向传播
        scaler.scale(loss).backward()
        scaler.unscale_(optimizer)
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        scaler.step(optimizer)
        scaler.update()
        
        total_loss += loss.item()
        
        # 收集中心预测
        center_idx = predictions.shape[1] // 2
        center_preds = predictions[:, center_idx, :].argmax(dim=1)
        
        if target.dim() > 1:
            center_targets = target[:, center_idx]
        else:
            center_targets = target
        
        all_preds.extend(center_preds.cpu().numpy())
        all_targets.extend(center_targets.cpu().numpy())
        
        pbar.set_postfix({'loss': f'{loss.item():.4f}'})
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1


def evaluate_standard(model, data_loader, device):
    """标准评估（快速）"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Standard Evaluation'):
            data = data.to(device)
            
            predictions, _ = model(data)
            
            # 使用中心预测
            center_idx = predictions.shape[1] // 2
            center_preds = predictions[:, center_idx, :].argmax(dim=1)
            
            if target.dim() > 1:
                center_targets = target[:, center_idx]
            else:
                center_targets = target
            
            all_preds.extend(center_preds.cpu().numpy())
            all_targets.extend(center_targets.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm


def main():
    # 配置（基于V14 FIXED的最佳配置）
    config = {
        'd_model': 192,
        'n_heads': 12,
        'n_layers': 4,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 2e-4,
        'weight_decay': 1e-4,
        'num_epochs': 40,
        'patience': 10,
        'class_weights': [2.5, 4.0, 1.0, 1.0, 2.5]  # [Wake, N1, N2, N3, REM]
    }
    
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f'../logs/v14_seq2seq_fixed_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 Sequence-to-Sequence Training (Fixed)")
    logging.info("="*80)
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割（与V14 FIXED相同）
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 创建数据集
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,  # 使用全部数据！
        use_channels=3
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,
        use_channels=3
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,
        use_channels=3
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'],
        shuffle=True, num_workers=4, pin_memory=True, drop_last=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = OptimizedModel(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数
    criterion = Seq2SeqFocalLoss(gamma=2.0, class_weights=config['class_weights']).to(device)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    
    # 混合精度训练
    scaler = GradScaler()
    
    # 创建滑动窗口评估器
    sliding_evaluator = SlidingWindowEvaluator(model, seq_len=config['seq_len'], device=device)
    
    # 训练循环
    best_val_acc = 0
    best_test_acc = 0
    patience_counter = 0
    
    logging.info("\n" + "="*80)
    logging.info("Starting Training with Sequence-to-Sequence!")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, scaler, device
        )
        
        # 标准评估（快速）
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate_standard(
            model, val_loader, device
        )
        
        # 学习率调整
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val (Standard): Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # 每5个epoch或最佳模型时，使用滑动窗口评估
        if (epoch + 1) % 5 == 0 or val_acc > best_val_acc:
            logging.info("  Running sliding window evaluation...")
            sw_acc, sw_f1, sw_kappa, sw_class_f1, sw_cm = sliding_evaluator.evaluate_with_sliding_window(val_loader)
            logging.info(f"  Val (Sliding): Acc={sw_acc:.4f}, F1={sw_f1:.4f}, Kappa={sw_kappa:.4f}")
            
            if sw_acc > best_val_acc:
                best_val_acc = sw_acc
                patience_counter = 0
                
                # 测试集评估
                test_acc, test_f1, test_kappa, test_class_f1, test_cm = sliding_evaluator.evaluate_with_sliding_window(test_loader)
                best_test_acc = test_acc
                
                # 保存模型
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_acc': sw_acc,
                    'test_acc': test_acc,
                    'config': config
                }, os.path.join(log_dir, 'best_model.pth'))
                
                logging.info(f"  💾 Saved best model (Val SW: {sw_acc:.4f}, Test SW: {test_acc:.4f})")
                logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                            f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
                
                if test_acc >= 0.90:
                    logging.info("\n" + "="*80)
                    logging.info("🎉 ACHIEVED 90% TEST ACCURACY WITH SEQ2SEQ!")
                    logging.info("="*80)
                    break
            else:
                patience_counter += 1
        else:
            patience_counter += 1
        
        # Early stopping
        if patience_counter >= config['patience']:
            logging.info(f"Early stopping at epoch {epoch+1}")
            break
    
    # 最终评估
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS")
    logging.info("="*80)
    
    # 加载最佳模型
    if os.path.exists(os.path.join(log_dir, 'best_model.pth')):
        checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'))
        model.load_state_dict(checkpoint['model_state_dict'])
        
        # 最终测试
        final_test_acc, final_test_f1, final_test_kappa, final_test_class_f1, final_test_cm = \
            sliding_evaluator.evaluate_with_sliding_window(test_loader)
        
        logging.info(f"Best Val Accuracy (Sliding): {best_val_acc:.4f}")
        logging.info(f"Final Test Accuracy (Sliding): {final_test_acc:.4f} ({final_test_acc*100:.2f}%)")
        logging.info(f"Final Test F1: {final_test_f1:.4f}")
        logging.info(f"Final Test Kappa: {final_test_kappa:.4f}")
        logging.info(f"Final Test Class F1: W={final_test_class_f1[0]:.3f}, N1={final_test_class_f1[1]:.3f}, "
                    f"N2={final_test_class_f1[2]:.3f}, N3={final_test_class_f1[3]:.3f}, REM={final_test_class_f1[4]:.3f}")
        
        gap_to_90 = 0.90 - final_test_acc
        if final_test_acc >= 0.90:
            logging.info(f"\n✅ TARGET ACHIEVED! Accuracy: {final_test_acc:.4f}")
        else:
            logging.info(f"\nGap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
        
        # 保存结果
        results = {
            'best_val_acc': float(best_val_acc),
            'final_test_acc': float(final_test_acc),
            'final_test_f1': float(final_test_f1),
            'final_test_kappa': float(final_test_kappa),
            'final_test_class_f1': final_test_class_f1.tolist(),
            'confusion_matrix': final_test_cm.tolist(),
            'config': config
        }
        
        with open(os.path.join(log_dir, 'results.json'), 'w') as f:
            json.dump(results, f, indent=2)
        
        logging.info(f"\nResults saved to {log_dir}")


if __name__ == '__main__':
    main()