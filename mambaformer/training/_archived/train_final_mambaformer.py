"""
终极版MAMBAFORMER训练脚本
基于前期实验结果的终极优化版本
目标：解决过拟合和类别不平衡问题
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.final_mambaformer import (
    FinalMAMBAFORMER, 
    FocalLossWithClassBalance, 
    calculate_class_weights,
    EnhancedDataAugmentation
)
from models.sequential_mambaformer import TemporalConsistencyLoss
from utils.sequence_dataset import create_sequence_dataloaders


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"final_mambaformer_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file


def create_auxiliary_labels(labels):
    """创建辅助任务标签"""
    aux_labels = ((labels == 4) | (labels == 3)).long()
    return aux_labels


def train_epoch_final(model, train_loader, criterion, aux_criterion, temp_loss_fn,
                     optimizer, device, epoch, config, data_aug):
    """终极版训练函数"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} - Train')
    
    for batch_idx, (data, labels) in enumerate(pbar):
        data = data.to(device)
        labels = labels.to(device)
        
        # 增强数据增强
        data, labels = data_aug(data, labels)
        
        # 处理通道数
        if data.shape[-1] == 4:
            emg_channel = data[:, :, :, 3:4].clone()
            data = torch.cat([data, emg_channel], dim=-1)
        
        optimizer.zero_grad()
        
        # 前向传播
        main_output, aux_output = model(data)
        
        # 损失计算
        main_loss = criterion(main_output, labels)
        
        # 辅助损失
        aux_labels = create_auxiliary_labels(labels)
        aux_loss = aux_criterion(
            aux_output.view(-1, 2), 
            aux_labels.view(-1)
        )
        
        # 时序一致性损失
        temp_loss = temp_loss_fn(main_output)
        
        # 总损失 - 调整权重
        loss = main_loss + 0.1 * aux_loss + config['temp_loss_weight'] * temp_loss
        
        # 梯度惩罚 - 防止梯度爆炸
        l2_reg = 0
        for param in model.parameters():
            l2_reg += torch.norm(param, 2)
        loss += config['l2_weight'] * l2_reg
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.3)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        preds = torch.argmax(main_output, dim=-1)
        all_preds.extend(preds.cpu().numpy().flatten())
        all_labels.extend(labels.cpu().numpy().flatten())
        
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'main': f'{main_loss.item():.4f}',
            'aux': f'{aux_loss.item():.4f}',
            'temp': f'{temp_loss.item():.4f}'
        })
    
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    avg_loss = total_loss / len(train_loader)
    
    return avg_loss, acc, f1


def evaluate_final(model, data_loader, criterion, aux_criterion, device, phase='Val'):
    """终极版评估函数"""
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    seq_preds = []
    seq_labels = []
    
    with torch.no_grad():
        pbar = tqdm(data_loader, desc=f'{phase}')
        
        for data, labels in pbar:
            data = data.to(device)
            labels = labels.to(device)
            
            # 处理通道数
            if data.shape[-1] == 4:
                emg_channel = data[:, :, :, 3:4].clone()
                data = torch.cat([data, emg_channel], dim=-1)
            
            main_output, aux_output = model(data)
            
            # 损失计算
            main_loss = criterion(main_output, labels)
            aux_labels = create_auxiliary_labels(labels)
            aux_loss = aux_criterion(
                aux_output.view(-1, 2),
                aux_labels.view(-1)
            )
            loss = main_loss + 0.1 * aux_loss
            
            total_loss += loss.item()
            
            # 收集预测
            preds = torch.argmax(main_output, dim=-1)
            all_preds.extend(preds.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
            
            seq_preds.append(preds.cpu().numpy())
            seq_labels.append(labels.cpu().numpy())
    
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    # 位置准确率
    seq_preds = np.concatenate(seq_preds, axis=0)
    seq_labels = np.concatenate(seq_labels, axis=0)
    
    position_acc = []
    for pos in range(seq_preds.shape[1]):
        pos_acc = accuracy_score(seq_labels[:, pos], seq_preds[:, pos])
        position_acc.append(pos_acc)
    
    avg_loss = total_loss / len(data_loader)
    
    # 详细分析
    if phase == 'Test':
        report = classification_report(all_labels, all_preds, 
                                     target_names=['Wake', 'N1', 'N2', 'N3', 'REM'],
                                     output_dict=True)
        cm = confusion_matrix(all_labels, all_preds)
        return avg_loss, acc, f1, position_acc, report, cm
    
    return avg_loss, acc, f1, position_acc


def train_fold_final(fold_id, fold_info, config, device):
    """终极版fold训练"""
    logging.info(f"\n{'='*70}")
    logging.info(f"🚀 开始训练 Final MAMBAFORMER - Fold {fold_id + 1}")
    logging.info(f"训练文件数: {len(fold_info['train_files'])}")
    logging.info(f"测试文件数: {len(fold_info['test_files'])}")
    
    # 创建数据加载器
    train_loader, val_loader, test_loader = create_sequence_dataloaders(
        fold_info,
        batch_size=config['batch_size'],
        seq_len=config['seq_len'],
        use_channels=4,
        max_samples_per_file=config['max_samples_per_file']
    )
    
    # 增大验证集 - 40%
    train_dataset = train_loader.dataset
    total_size = len(train_dataset)
    val_size = int(0.4 * total_size)  # 进一步增大验证集
    train_size = total_size - val_size
    
    train_subset, val_subset = torch.utils.data.random_split(
        train_dataset, [train_size, val_size],
        generator=torch.Generator().manual_seed(42)
    )
    
    # 重新创建数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_subset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_subset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    logging.info(f"📊 数据分布:")
    logging.info(f"  训练批次数: {len(train_loader)} (样本数: {train_size})")
    logging.info(f"  验证批次数: {len(val_loader)} (样本数: {val_size})")
    logging.info(f"  测试批次数: {len(test_loader)}")
    
    # 计算类别权重
    class_weights = calculate_class_weights(train_loader)
    
    # 创建终极模型
    model = FinalMAMBAFORMER(
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # 类别平衡的损失函数
    criterion = FocalLossWithClassBalance(
        alpha=[0.5, 2.0, 1.0, 1.5, 3.0],  # 增加困难类别的权重
        gamma=2.0,
        class_weights=class_weights
    )
    
    aux_criterion = nn.CrossEntropyLoss(
        weight=torch.tensor([1.0, 2.0]).to(device)  # REM/SWS权重更高
    )
    
    temp_loss_fn = TemporalConsistencyLoss(weight=config['temp_loss_weight'])
    
    # 数据增强
    data_aug = EnhancedDataAugmentation(p=0.8)
    
    # 优化器 - 更保守的设置
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999),
        eps=1e-8
    )
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-7
    )
    
    # 训练循环
    best_val_f1 = 0
    patience_counter = 0
    best_model_state = None
    train_losses = []
    val_losses = []
    
    for epoch in range(1, config['num_epochs'] + 1):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch_final(
            model, train_loader, criterion, aux_criterion, temp_loss_fn,
            optimizer, device, epoch, config, data_aug
        )
        
        # 验证
        val_loss, val_acc, val_f1, val_pos_acc = evaluate_final(
            model, val_loader, criterion, aux_criterion, device, 'Val'
        )
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        # 学习率调度
        scheduler.step()
        
        # 记录
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        logging.info(f"Train - Loss: {train_loss:.4f}, Acc: {train_acc:.4f}, F1: {train_f1:.4f}")
        logging.info(f"Val   - Loss: {val_loss:.4f}, Acc: {val_acc:.4f}, F1: {val_f1:.4f}")
        logging.info(f"Val Position Acc: {[f'{acc:.4f}' for acc in val_pos_acc]}")
        logging.info(f"LR: {optimizer.param_groups[0]['lr']:.2e}")
        
        # 过拟合监控
        if len(train_losses) > 5:
            recent_train_loss = np.mean(train_losses[-3:])
            recent_val_loss = np.mean(val_losses[-3:])
            gap = recent_val_loss - recent_train_loss
            if gap > 0.05:
                logging.warning(f"⚠️  过拟合警告: Gap = {gap:.4f}")
        
        # 保存最佳模型
        if val_f1 > best_val_f1:
            best_val_f1 = val_f1
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            logging.info(f"💾 新的最佳Val F1: {best_val_f1:.4f}")
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logging.info(f"⏹️  早停: 验证F1已{config['patience']}轮未改善")
            break
    
    # 测试评估
    if best_model_state:
        model.load_state_dict(best_model_state)
        logging.info("✅ 已加载最佳模型进行测试")
    
    test_loss, test_acc, test_f1, test_pos_acc, report, cm = evaluate_final(
        model, test_loader, criterion, aux_criterion, device, 'Test'
    )
    
    # 详细测试结果输出
    logging.info(f"\n🎯 Final MAMBAFORMER - Fold {fold_id + 1} 完整测试结果:")
    logging.info(f"="*70)
    logging.info(f"测试准确率: {test_acc:.4f}")
    logging.info(f"测试F1分数: {test_f1:.4f}")
    logging.info(f"最佳验证F1: {best_val_f1:.4f}")
    logging.info(f"验证-测试F1差距: {best_val_f1 - test_f1:.4f}")
    logging.info(f"各位置准确率: {[f'{acc:.4f}' for acc in test_pos_acc]}")
    
    logging.info(f"\n📊 详细分类报告:")
    for class_name, metrics in report.items():
        if isinstance(metrics, dict) and class_name not in ['accuracy', 'macro avg', 'weighted avg']:
            logging.info(f"{class_name:>5}: Prec={metrics['precision']:.3f}, "
                        f"Recall={metrics['recall']:.3f}, F1={metrics['f1-score']:.3f}, "
                        f"Support={metrics['support']}")
    
    logging.info(f"Macro Avg: Prec={report['macro avg']['precision']:.3f}, "
                f"Recall={report['macro avg']['recall']:.3f}, F1={report['macro avg']['f1-score']:.3f}")
    
    logging.info(f"\n🔄 混淆矩阵:")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logging.info("     " + " ".join(f"{name:>6}" for name in class_names))
    for i, row in enumerate(cm):
        logging.info(f"{class_names[i]:>4} " + " ".join(f"{val:>6}" for val in row))
    
    # 保存模型
    os.makedirs('../../checkpoints', exist_ok=True)
    torch.save(model.state_dict(), f'../../checkpoints/final_fold_{fold_id}.pth')
    
    return {
        'fold_id': fold_id,
        'test_acc': test_acc,
        'test_f1': test_f1,
        'position_acc': test_pos_acc,
        'val_f1': best_val_f1,
        'val_test_gap': best_val_f1 - test_f1,
        'confusion_matrix': cm.tolist(),
        'classification_report': report
    }


def main():
    config = {
        'batch_size': 16,  # 稍大batch size提升稳定性
        'seq_len': 5,
        'learning_rate': 5e-6,  # 极低学习率
        'weight_decay': 1e-3,   # 强权重衰减
        'l2_weight': 5e-7,      # L2正则化
        'num_epochs': 60,       # 更多epoch但有早停
        'patience': 12,         # 更大patience
        'max_samples_per_file': 250,  # 更多数据
        'd_model': 64,          # 小模型
        'n_heads': 4,
        'n_layers': 2,          # 极简结构
        'dropout': 0.4,         # 强dropout
        'temp_loss_weight': 0.02  # 小时序损失权重
    }
    
    log_file = setup_logging()
    
    logging.info("🏆 终极版MAMBAFORMER训练 - Final Solution")
    logging.info("=" * 70)
    logging.info("🎯 终极优化:")
    logging.info("  • 极简架构防止过拟合 (<300K参数)")
    logging.info("  • 类别平衡Focal损失")
    logging.info("  • 40%验证集强力防过拟合")
    logging.info("  • 增强数据增强和正则化")
    logging.info("  • 分离式分类头解决类别不平衡")
    logging.info(f"📋 配置: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️  使用设备: {device}")
    
    with open('../../configs/subject_aware_folds.json', 'r') as f:
        fold_data = json.load(f)
    
    all_results = []
    
    for fold_id, fold_info in enumerate(fold_data['folds'].values()):
        if fold_id >= 1:  # 先测试一个fold
            break
        
        result = train_fold_final(fold_id, fold_info, config, device)
        all_results.append(result)
    
    # 汇总结果
    avg_acc = np.mean([r['test_acc'] for r in all_results])
    avg_f1 = np.mean([r['test_f1'] for r in all_results])
    avg_gap = np.mean([r['val_test_gap'] for r in all_results])
    
    logging.info("\n" + "="*70)
    logging.info("🏆 终极版MAMBAFORMER训练完成")
    logging.info(f"📈 平均测试准确率: {avg_acc:.4f}")
    logging.info(f"📈 平均测试F1分数: {avg_f1:.4f}")
    logging.info(f"📊 平均验证-测试F1差距: {avg_gap:.4f}")
    
    if avg_gap < 0.03:
        logging.info("✅ 过拟合问题完全解决!")
    elif avg_gap < 0.08:
        logging.info("✅ 过拟合问题显著改善!")
    else:
        logging.info("⚠️  过拟合仍需进一步优化")
    
    # 对比之前的结果
    logging.info(f"\n📊 与CrossModal对比:")
    logging.info(f"  CrossModal: 验证F1=80.66%, 测试F1=57.67%, 差距=23.0%")
    logging.info(f"  Final版本: 验证F1={avg_acc:.2%}, 测试F1={avg_f1:.2%}, 差距={avg_gap:.1%}")
    
    results = {
        'config': config,
        'fold_results': all_results,
        'summary': {
            'mean_accuracy': avg_acc,
            'mean_f1_score': avg_f1,
            'mean_val_test_gap': avg_gap,
            'log_file': log_file
        }
    }
    
    with open('../../configs/final_mambaformer_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"💾 结果已保存至: ../../configs/final_mambaformer_results.json")


if __name__ == "__main__":
    main()