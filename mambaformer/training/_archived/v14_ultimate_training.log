2025-08-12 01:13:43,404 - INFO - ================================================================================
2025-08-12 01:13:43,404 - INFO - 🚀 V14 Ultimate - Target: 90% Accuracy
2025-08-12 01:13:43,404 - INFO - ================================================================================
2025-08-12 01:13:43,404 - INFO - Configuration: {
  "d_model": 288,
  "n_heads": 18,
  "n_layers": 10,
  "dropout": 0.1,
  "seq_len": 5,
  "batch_size": 28,
  "learning_rate": 0.00018,
  "weight_decay": 3e-05,
  "num_epochs": 60,
  "patience": 20,
  "label_smoothing": 0.1
}
2025-08-12 01:13:43,437 - INFO - Device: cuda
2025-08-12 01:13:43,437 - INFO - Loading datasets...
2025-08-12 01:13:45,336 - INFO - 从 28 个文件加载了 29402 个epochs, 创建了 29290 个序列
2025-08-12 01:13:45,337 - INFO - 创建序列数据集: 29290个序列, 序列长度=5, 通道数=3, 总epochs=29402
2025-08-12 01:13:45,522 - INFO - 从 3 个文件加载了 3160 个epochs, 创建了 3148 个序列
2025-08-12 01:13:45,523 - INFO - 创建序列数据集: 3148个序列, 序列长度=5, 通道数=3, 总epochs=3160
2025-08-12 01:13:46,051 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9714 个序列
2025-08-12 01:13:46,052 - INFO - 创建序列数据集: 9714个序列, 序列长度=5, 通道数=3, 总epochs=9746
2025-08-12 01:13:46,052 - INFO - Dataset sizes: Train=29290, Val=3148, Test=9714
2025-08-12 01:13:46,585 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=10,279,199, d_model=288, n_heads=18, n_layers=10
2025-08-12 01:13:46,841 - INFO - Model parameters: 11,074,923
2025-08-12 01:13:48,391 - INFO - Starting Ultimate training...

Epoch 1:   0%|          | 0/1046 [00:00<?, ?it/s]
Epoch 1:   0%|          | 0/1046 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_ultimate.py", line 531, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_ultimate.py", line 455, in main
    train_loss, train_acc, train_f1 = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_ultimate.py", line 281, in train_epoch
    outputs = model(data)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_ultimate.py", line 167, in forward
    enhanced_features = self.attention_enhancer(main_features)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/container.py", line 240, in forward
    input = module(input)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (28x5 and 288x288)
