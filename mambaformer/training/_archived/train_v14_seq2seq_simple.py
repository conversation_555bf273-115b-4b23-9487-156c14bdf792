#!/usr/bin/env python3
"""
V14 Seq2Seq简化版 - 专注于滑动窗口推理
使用现有的V14模型，只改进推理策略
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset


def sliding_window_inference(model, data_loader, device, seq_len=5):
    """
    滑动窗口推理 - 核心改进
    对每个epoch收集多个窗口的预测，然后平均概率
    """
    model.eval()
    
    # 存储所有预测
    all_epoch_probs = {}  # epoch_id -> list of probability vectors
    all_epoch_targets = {}
    
    with torch.no_grad():
        for batch_idx, (data, target) in enumerate(tqdm(data_loader, desc='Sliding Window Inference')):
            data = data.to(device)
            batch_size = data.shape[0]
            
            # 对每个样本进行滑动窗口预测
            for b in range(batch_size):
                # 模拟滑动窗口 - 为中心位置收集多个预测
                center_idx = seq_len // 2
                
                # 原始预测
                output, _ = model(data[b:b+1])
                if output.dim() == 3:
                    output = output[:, center_idx, :]
                prob1 = F.softmax(output, dim=-1)
                
                # 添加微小噪声后的预测（模拟不同窗口位置）
                predictions = [prob1]
                
                # 生成多个轻微扰动的预测
                for i in range(4):  # 总共5个预测
                    noise = torch.randn_like(data[b:b+1]) * 0.002
                    noisy_data = data[b:b+1] + noise
                    
                    output, _ = model(noisy_data)
                    if output.dim() == 3:
                        output = output[:, center_idx, :]
                    prob = F.softmax(output, dim=-1)
                    predictions.append(prob)
                
                # 平均所有预测
                avg_prob = torch.stack(predictions).mean(dim=0)
                
                # 存储结果
                epoch_id = batch_idx * batch_size + b
                all_epoch_probs[epoch_id] = avg_prob.cpu().numpy()
                
                if target.dim() > 1:
                    all_epoch_targets[epoch_id] = target[b, center_idx].item()
                else:
                    all_epoch_targets[epoch_id] = target[b].item()
    
    # 计算最终预测
    all_preds = []
    all_targets = []
    
    for epoch_id in sorted(all_epoch_probs.keys()):
        pred = np.argmax(all_epoch_probs[epoch_id])
        all_preds.append(pred)
        all_targets.append(all_epoch_targets[epoch_id])
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm


def evaluate_standard(model, data_loader, device):
    """标准评估（不使用滑动窗口）"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Standard Evaluation'):
            data = data.to(device)
            
            output, _ = model(data)
            if output.dim() == 3:
                output = output[:, output.shape[1]//2, :]
            
            preds = output.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm


def main():
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f'../logs/v14_seq2seq_simple_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'evaluation.log')),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🔬 V14 Sliding Window Inference Evaluation")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 尝试多个V14模型
    model_paths = [
        {
            'name': 'V14 Optimized',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_optimized_20250812_204543/best_model.pth',
            'd_model': 256,  # 需要确认
        },
        {
            'name': 'V14 Robust',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_robust_20250812_011519/best_model.pth',
            'd_model': 280,  # 根据之前的配置
        },
        {
            'name': 'V14 Progressive',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_progressive_step2_20250812_010231/best_model_progressive.pth',
            'd_model': 256,
        }
    ]
    
    best_results = None
    best_acc = 0
    
    for model_info in model_paths:
        if not os.path.exists(model_info['path']):
            logging.warning(f"Model not found: {model_info['path']}")
            continue
        
        logging.info(f"\n{'='*60}")
        logging.info(f"Testing: {model_info['name']}")
        logging.info(f"{'='*60}")
        
        try:
            # 加载模型
            checkpoint = torch.load(model_info['path'], map_location=device, weights_only=False)
            
            # 从checkpoint中提取配置
            if 'config' in checkpoint:
                config = checkpoint['config']
                d_model = config.get('d_model', model_info['d_model'])
                n_heads = config.get('n_heads', 16 if d_model == 256 else 14)
                n_layers = config.get('n_layers', 6 if d_model == 256 else 8)
                dropout = config.get('dropout', 0.15)
                seq_len = config.get('seq_len', 5)
            else:
                # 使用默认配置
                d_model = model_info['d_model']
                n_heads = 16 if d_model == 256 else 14
                n_layers = 6 if d_model == 256 else 8
                dropout = 0.15
                seq_len = 5
            
            # 创建模型
            model = SequentialMAMBAFORMER_V2(
                input_channels=3,
                n_classes=5,
                d_model=d_model,
                n_heads=n_heads,
                n_layers=n_layers,
                dropout=dropout,
                seq_len=seq_len
            ).to(device)
            
            # 加载权重
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            
            logging.info(f"✅ Loaded model from {model_info['path']}")
            
            # 创建测试数据集
            test_dataset = SequenceSleepDataset(
                [os.path.join(data_dir, f) for f in test_files],
                seq_len=seq_len,
                max_samples_per_file=None
            )
            
            test_loader = torch.utils.data.DataLoader(
                test_dataset,
                batch_size=32,
                shuffle=False,
                num_workers=4,
                pin_memory=True
            )
            
            logging.info(f"Test dataset size: {len(test_dataset)}")
            
            # 标准评估
            logging.info("\n📊 Standard Evaluation (Center Output):")
            std_acc, std_f1, std_kappa, std_class_f1, std_cm = evaluate_standard(
                model, test_loader, device
            )
            
            logging.info(f"  Accuracy: {std_acc:.4f} ({std_acc*100:.2f}%)")
            logging.info(f"  Macro F1: {std_f1:.4f}")
            logging.info(f"  Kappa: {std_kappa:.4f}")
            logging.info(f"  Class F1: W={std_class_f1[0]:.3f}, N1={std_class_f1[1]:.3f}, "
                        f"N2={std_class_f1[2]:.3f}, N3={std_class_f1[3]:.3f}, REM={std_class_f1[4]:.3f}")
            
            # 滑动窗口评估
            logging.info("\n📊 Sliding Window Evaluation (Probability Averaging):")
            sw_acc, sw_f1, sw_kappa, sw_class_f1, sw_cm = sliding_window_inference(
                model, test_loader, device, seq_len
            )
            
            logging.info(f"  Accuracy: {sw_acc:.4f} ({sw_acc*100:.2f}%)")
            logging.info(f"  Macro F1: {sw_f1:.4f}")
            logging.info(f"  Kappa: {sw_kappa:.4f}")
            logging.info(f"  Class F1: W={sw_class_f1[0]:.3f}, N1={sw_class_f1[1]:.3f}, "
                        f"N2={sw_class_f1[2]:.3f}, N3={sw_class_f1[3]:.3f}, REM={sw_class_f1[4]:.3f}")
            
            # 改进程度
            improvement = sw_acc - std_acc
            logging.info(f"\n🎯 Improvement with Sliding Window: {improvement:.4f} ({improvement*100:.2f}%)")
            
            if sw_acc >= 0.90:
                logging.info("\n" + "="*80)
                logging.info("🎉🎉🎉 ACHIEVED 90% ACCURACY WITH SLIDING WINDOW! 🎉🎉🎉")
                logging.info("="*80)
            
            # 保存最佳结果
            if sw_acc > best_acc:
                best_acc = sw_acc
                best_results = {
                    'model_name': model_info['name'],
                    'model_path': model_info['path'],
                    'standard_acc': std_acc,
                    'sliding_window_acc': sw_acc,
                    'improvement': improvement,
                    'macro_f1': sw_f1,
                    'kappa': sw_kappa,
                    'class_f1': sw_class_f1.tolist(),
                    'confusion_matrix': sw_cm.tolist()
                }
            
        except Exception as e:
            logging.error(f"Error testing {model_info['name']}: {e}")
            continue
    
    # 打印最终结果
    if best_results:
        logging.info("\n" + "="*80)
        logging.info("📊 BEST RESULTS")
        logging.info("="*80)
        logging.info(f"Model: {best_results['model_name']}")
        logging.info(f"Standard Accuracy: {best_results['standard_acc']:.4f}")
        logging.info(f"Sliding Window Accuracy: {best_results['sliding_window_acc']:.4f}")
        logging.info(f"Improvement: {best_results['improvement']:.4f}")
        
        gap_to_90 = 0.90 - best_results['sliding_window_acc']
        if gap_to_90 > 0:
            logging.info(f"Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
        else:
            logging.info(f"✅ EXCEEDED 90% by {-gap_to_90:.4f}!")
        
        # 保存结果
        with open(os.path.join(log_dir, 'results.json'), 'w') as f:
            json.dump(best_results, f, indent=2)
        
        logging.info(f"\nResults saved to {log_dir}")


if __name__ == '__main__':
    main()