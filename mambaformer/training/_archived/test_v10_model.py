#!/usr/bin/env python3
"""
测试V10 Stable N1 Expert模型
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix, precision_recall_fscore_support
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

class StableN1Model(nn.Module):
    """稳定的N1专家模型 - 基于V8架构 + N1检测分支"""
    def __init__(self, input_channels, n_classes, d_model, n_heads, n_layers, dropout, seq_len):
        super().__init__()
        
        # 主干网络 - V8的稳定架构
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # 辅助头 - 来自V8
        self.auxiliary_head = nn.Sequential(
            nn.Linear(n_classes, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, n_classes)
        )
        
        # N1专门检测分支 - 改进自V9
        self.n1_detector = nn.Sequential(
            nn.Linear(n_classes, d_model // 2),
            nn.BatchNorm1d(d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model // 4),
            nn.BatchNorm1d(d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 4, 2)  # 二分类：N1 vs 非N1
        )
        
        # N3/N2边界检测 - 解决深睡眠混淆
        self.deep_sleep_detector = nn.Sequential(
            nn.Linear(n_classes, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, 2)  # 二分类：N3 vs 其他
        )
        
        # 融合层 - 更简单的设计
        self.fusion_weight = nn.Parameter(torch.tensor([0.6, 0.2, 0.1, 0.1]))  # main, aux, n1, deep
        
    def forward(self, x):
        # 主干输出
        main_out, _ = self.backbone(x)
        
        # 提取中心时间步
        if main_out.dim() == 3:
            center_out = main_out[:, main_out.shape[1]//2, :]
        else:
            center_out = main_out
        
        # 辅助输出
        aux_out = self.auxiliary_head(center_out)
        
        # N1检测
        n1_detection = self.n1_detector(center_out)
        n1_prob = F.softmax(n1_detection, dim=-1)[:, 1]  # N1概率
        
        # 深睡眠检测
        deep_detection = self.deep_sleep_detector(center_out)
        deep_prob = F.softmax(deep_detection, dim=-1)[:, 1]  # N3概率
        
        # 融合输出 - 加权平均
        weights = F.softmax(self.fusion_weight, dim=0)
        
        # 基础融合
        final_out = weights[0] * center_out + weights[1] * aux_out
        
        # N1增强 - 如果N1检测器认为是N1，增强N1分数
        n1_boost = n1_prob * weights[2] * 3.0  # 温和的增强
        final_out[:, 1] = final_out[:, 1] + n1_boost
        
        # N3增强 - 如果深睡眠检测器认为是N3，增强N3分数
        n3_boost = deep_prob * weights[3] * 2.0
        final_out[:, 3] = final_out[:, 3] + n3_boost
        
        return final_out, aux_out, n1_detection, deep_detection

def evaluate_with_tta(model, data_loader, device, n_tta=5):
    """评估函数 - 带TTA"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Testing with TTA'):
            data = data.to(device)
            
            predictions = []
            for i in range(n_tta):
                if i == 0:
                    data_aug = data
                elif i == 1:
                    data_aug = data + torch.randn_like(data) * 0.002
                elif i == 2:
                    shift = random.randint(-20, 20)
                    data_aug = torch.roll(data, shifts=shift, dims=-1)
                elif i == 3:
                    data_aug = data * (0.97 + random.random() * 0.06)
                else:
                    data_aug = data + torch.randn_like(data) * 0.003
                
                final_out, aux_out, n1_detection, deep_detection = model(data_aug)
                
                # 组合输出
                combined = 0.7 * F.softmax(final_out, dim=-1) + 0.3 * F.softmax(aux_out, dim=-1)
                
                # N1概率增强
                n1_prob = F.softmax(n1_detection, dim=-1)[:, 1]
                combined[:, 1] = combined[:, 1] * (1 + n1_prob * 0.2)
                
                # N3概率增强
                deep_prob = F.softmax(deep_detection, dim=-1)[:, 1]
                combined[:, 3] = combined[:, 3] * (1 + deep_prob * 0.1)
                
                predictions.append(combined)
            
            avg_probs = torch.stack(predictions).mean(dim=0)
            preds = avg_probs.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    return np.array(all_targets), np.array(all_preds)

def main():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载测试数据
    print("Loading test dataset...")
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=5,
        max_samples_per_file=None
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=32,
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    print(f"Test dataset size: {len(test_dataset)}")
    
    # 加载模型
    model_path = '../logs/v10_stable_n1_20250812_193656/best_model.pth'
    print(f"Loading model from {model_path}")
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    config = checkpoint.get('config', {
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.2,
        'seq_len': 5
    })
    
    model = StableN1Model(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    val_metrics = checkpoint.get('val_metrics', {})
    print(f"Model loaded - Val Acc: {val_metrics.get('accuracy', 'N/A'):.4f}")
    
    # 测试
    print("\nEvaluating on test set with 5x TTA...")
    test_targets, test_preds = evaluate_with_tta(model, test_loader, device, n_tta=5)
    
    # 计算指标
    test_acc = accuracy_score(test_targets, test_preds)
    test_f1 = f1_score(test_targets, test_preds, average='macro')
    test_kappa = cohen_kappa_score(test_targets, test_preds)
    test_class_f1 = f1_score(test_targets, test_preds, average=None)
    
    precision, recall, _, support = precision_recall_fscore_support(
        test_targets, test_preds, average=None
    )
    
    test_cm = confusion_matrix(test_targets, test_preds)
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    
    # 结果
    print("\n" + "="*80)
    print("📊 V10 STABLE N1 EXPERT TEST RESULTS")
    print("="*80)
    print(f"\n🎯 Overall Metrics:")
    print(f"  Test Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    print(f"  Test Macro F1: {test_f1:.4f}")
    print(f"  Test Kappa: {test_kappa:.4f}")
    
    print(f"\n📈 Per-Class Metrics:")
    for i, class_name in enumerate(class_names):
        print(f"  {class_name:>6}: F1={test_class_f1[i]:.3f}, Prec={precision[i]:.3f}, "
              f"Recall={recall[i]:.3f}, Support={support[i]}")
    
    print(f"\n🎯 N1 Improvement Analysis:")
    print(f"  Previous N1 F1: 0.488 (V8)")
    print(f"  V9 Simple N1 F1: 0.515")
    print(f"  Current N1 F1: {test_class_f1[1]:.3f}")
    improvement = test_class_f1[1] - 0.488
    print(f"  Improvement from baseline: {improvement:.3f} ({improvement/0.488*100:.1f}% relative)")
    
    if test_class_f1[1] >= 0.6:
        print(f"  ✅ N1 F1 TARGET ACHIEVED! (>0.6)")
    else:
        print(f"  Gap to N1 target (0.6): {0.6 - test_class_f1[1]:.3f}")
    
    print(f"\n🔄 Confusion Matrix:")
    print("       " + "  ".join([f"{name:>6}" for name in class_names]))
    for i, true_class in enumerate(class_names):
        row = test_cm[i]
        row_str = " ".join([f"{val:>6}" for val in row])
        print(f"{true_class:>6} {row_str}")
    
    # N3→N2混淆分析
    n3_to_n2 = test_cm[3, 2]
    print(f"\n💤 Deep Sleep Confusion:")
    print(f"  N3→N2 errors: {n3_to_n2} (was 332 in V8)")
    
    # 与90%目标对比
    gap_to_90 = 0.90 - test_acc
    print(f"\n🎯 Progress to 90% Target:")
    print(f"  V8 best: 84.44%")
    print(f"  V9 Simple: 84.80%")
    print(f"  V10 Current: {test_acc*100:.2f}%")
    if test_acc >= 0.90:
        print(f"  ✅ 90% TARGET ACHIEVED!")
        print("\n🎉🎉🎉 SUCCESS! REACHED 90% ACCURACY! 🎉🎉🎉")
    else:
        print(f"  Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
        print(f"  Progress from V8: {test_acc - 0.8444:.4f}")

if __name__ == "__main__":
    main()