#!/usr/bin/env python3
"""
V14 Sequence-to-Sequence 改进版
基于V14 FIXED实现真正的序列到序列预测

核心改进：
1. 模型输出完整序列预测，不只是中心点
2. 训练时使用所有时期的损失
3. 推理时使用滑动窗口+概率平均
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import math

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset


class Seq2SeqMAMBAFORMER(nn.Module):
    """
    真正的Sequence-to-Sequence MAMBAFORMER
    为序列中的每个时期都生成预测
    """
    def __init__(self, input_channels=3, n_classes=5, d_model=256, 
                 n_heads=16, n_layers=6, dropout=0.15, seq_len=5):
        super().__init__()
        
        self.seq_len = seq_len
        self.d_model = d_model
        self.n_classes = n_classes
        
        # 使用原始backbone作为特征提取器
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # 序列级别的分类头 - 为每个时期生成独立预测
        self.seq_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # N1专门分支（继承V14的设计）
        self.n1_specialist = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, n_classes)
        )
        
        # 融合门控机制
        self.fusion_gate = nn.Sequential(
            nn.Linear(d_model + n_classes, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 1),
            nn.Sigmoid()
        )
        
        logging.info(f"创建Seq2SeqMAMBAFORMER: 参数量={sum(p.numel() for p in self.parameters()):,}")
        
    def forward(self, x):
        """
        输入: (batch, seq_len, time_steps, channels)
        输出: (batch, seq_len, n_classes) - 每个时期都有预测！
        """
        # 获取backbone特征
        features, aux_output = self.backbone(x)
        # features: (batch, seq_len, d_model)
        
        # 为每个时期生成主预测
        main_predictions = self.seq_classifier(features)
        
        # N1专门预测
        n1_predictions = self.n1_specialist(features)
        
        # 融合主预测和N1预测
        batch_size, seq_len, _ = features.shape
        fused_predictions = []
        
        for i in range(seq_len):
            feat = features[:, i, :]  # (batch, d_model)
            n1_pred = n1_predictions[:, i, :]  # (batch, n_classes)
            
            # 计算融合权重
            concat_feat = torch.cat([feat, n1_pred], dim=-1)
            gate = self.fusion_gate(concat_feat)  # (batch, 1)
            
            # 融合预测
            main_pred = main_predictions[:, i, :]
            fused = gate * main_pred + (1 - gate) * n1_pred
            fused_predictions.append(fused)
        
        final_predictions = torch.stack(fused_predictions, dim=1)
        
        return final_predictions, n1_predictions


class Seq2SeqFocalLoss(nn.Module):
    """
    序列级别的Focal Loss
    对序列中所有时期计算损失
    """
    def __init__(self, alpha=None, gamma=2.0, rem_weight=3.0, n1_weight=5.0):
        super().__init__()
        self.gamma = gamma
        self.rem_weight = rem_weight
        self.n1_weight = n1_weight
        
        # 类别权重 [Wake, N1, N2, N3, REM]
        self.class_weights = torch.tensor([2.5, n1_weight, 1.0, 1.0, rem_weight])
        
    def forward(self, predictions, targets):
        """
        predictions: (batch, seq_len, n_classes)
        targets: (batch, seq_len) or (batch,)
        """
        batch_size = predictions.shape[0]
        seq_len = predictions.shape[1]
        n_classes = predictions.shape[2]
        
        # 处理目标维度
        if targets.dim() == 1:
            # 如果只有中心标签，扩展到序列
            targets = targets.unsqueeze(1).expand(-1, seq_len)
        
        # Reshape for loss calculation
        predictions = predictions.reshape(-1, n_classes)
        targets = targets.reshape(-1)
        
        # 计算交叉熵
        if self.class_weights is not None:
            self.class_weights = self.class_weights.to(predictions.device)
            ce_loss = F.cross_entropy(predictions, targets, weight=self.class_weights, reduction='none')
        else:
            ce_loss = F.cross_entropy(predictions, targets, reduction='none')
        
        # Focal loss
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        return focal_loss.mean()


class TemporalConsistencyLoss(nn.Module):
    """时序一致性损失 - 鼓励相邻预测的平滑性"""
    def __init__(self, weight=0.1):
        super().__init__()
        self.weight = weight
    
    def forward(self, predictions):
        """
        predictions: (batch, seq_len, n_classes)
        """
        if predictions.shape[1] <= 1:
            return 0.0
        
        # 计算相邻预测的差异
        diff = predictions[:, 1:] - predictions[:, :-1]
        consistency_loss = torch.mean(torch.sum(diff ** 2, dim=-1))
        
        return self.weight * consistency_loss


class SlidingWindowEvaluator:
    """
    滑动窗口评估器
    使用概率平均策略进行推理
    """
    def __init__(self, model, seq_len=5, device='cuda'):
        self.model = model
        self.seq_len = seq_len
        self.device = device
        
    def evaluate_with_sliding_window(self, data_loader):
        """
        使用滑动窗口和概率平均进行评估
        """
        self.model.eval()
        
        all_epoch_predictions = {}  # 存储每个epoch的所有预测
        all_epoch_targets = {}
        epoch_counter = 0
        
        with torch.no_grad():
            for batch_data, batch_target in tqdm(data_loader, desc='Sliding Window Evaluation'):
                batch_data = batch_data.to(self.device)
                batch_size, seq_len, _, _ = batch_data.shape
                
                # 获取模型预测
                predictions, _ = self.model(batch_data)
                probs = F.softmax(predictions, dim=-1)  # (batch, seq_len, n_classes)
                
                # 处理目标
                if batch_target.dim() > 1:
                    # 如果是序列标签，使用所有标签
                    targets = batch_target
                else:
                    # 如果只有中心标签，只评估中心
                    targets = batch_target.unsqueeze(1).expand(-1, seq_len)
                
                # 存储每个时期的预测
                for b in range(batch_size):
                    for s in range(seq_len):
                        epoch_id = epoch_counter + s
                        
                        if epoch_id not in all_epoch_predictions:
                            all_epoch_predictions[epoch_id] = []
                        
                        all_epoch_predictions[epoch_id].append(probs[b, s].cpu().numpy())
                        
                        if epoch_id not in all_epoch_targets:
                            all_epoch_targets[epoch_id] = targets[b, s].item() if s == seq_len // 2 else targets[b, 0].item()
                    
                    epoch_counter += 1
        
        # 平均每个epoch的所有预测
        final_predictions = []
        final_targets = []
        
        for epoch_id in sorted(all_epoch_predictions.keys()):
            # 平均该epoch的所有预测概率
            avg_prob = np.mean(all_epoch_predictions[epoch_id], axis=0)
            pred = np.argmax(avg_prob)
            
            final_predictions.append(pred)
            if epoch_id in all_epoch_targets:
                final_targets.append(all_epoch_targets[epoch_id])
        
        # 计算指标
        accuracy = accuracy_score(final_targets[:len(final_predictions)], final_predictions[:len(final_targets)])
        f1 = f1_score(final_targets[:len(final_predictions)], final_predictions[:len(final_targets)], average='macro')
        kappa = cohen_kappa_score(final_targets[:len(final_predictions)], final_predictions[:len(final_targets)])
        class_f1 = f1_score(final_targets[:len(final_predictions)], final_predictions[:len(final_targets)], average=None)
        cm = confusion_matrix(final_targets[:len(final_predictions)], final_predictions[:len(final_targets)])
        
        return accuracy, f1, kappa, class_f1, cm


def train_epoch(model, train_loader, criterion, temporal_loss, optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # 数据增强
        if np.random.random() < 0.3:
            noise = torch.randn_like(data) * 0.01
            data = data + noise
        
        optimizer.zero_grad()
        
        # 前向传播
        predictions, n1_predictions = model(data)
        
        # 计算主损失 - 使用所有时期
        main_loss = criterion(predictions, target)
        
        # N1辅助损失
        n1_loss = criterion(n1_predictions, target)
        
        # 时序一致性损失
        temp_loss = temporal_loss(predictions)
        
        # 总损失
        loss = main_loss + 0.3 * n1_loss + temp_loss
        
        # L2正则化
        l2_lambda = 1e-5
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集中心预测用于监控
        center_idx = predictions.shape[1] // 2
        center_preds = predictions[:, center_idx, :].argmax(dim=1)
        
        if target.dim() > 1:
            center_targets = target[:, center_idx]
        else:
            center_targets = target
        
        all_preds.extend(center_preds.cpu().numpy())
        all_targets.extend(center_targets.cpu().numpy())
        
        pbar.set_postfix({'loss': f'{loss.item():.4f}'})
    
    # 计算训练指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1


def evaluate_standard(model, data_loader, device):
    """标准评估（不使用滑动窗口）"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Standard Evaluation'):
            data = data.to(device)
            
            # 获取预测
            predictions, _ = model(data)
            
            # 使用中心预测
            center_idx = predictions.shape[1] // 2
            center_preds = predictions[:, center_idx, :].argmax(dim=1)
            
            if target.dim() > 1:
                center_targets = target[:, center_idx]
            else:
                center_targets = target
            
            all_preds.extend(center_preds.cpu().numpy())
            all_targets.extend(center_targets.numpy())
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm


def main():
    # 配置
    config = {
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 2e-4,
        'weight_decay': 1e-4,
        'num_epochs': 50,
        'patience': 15,
        'rem_weight': 2.5,  # 比V30温和
        'n1_weight': 4.0,   # 重点改善N1
    }
    
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f'../logs/v14_seq2seq_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 Sequence-to-Sequence Training")
    logging.info("="*80)
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割（与V14 FIXED相同）
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 创建数据集
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None  # 使用全部数据！
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'],
        shuffle=True, num_workers=4, pin_memory=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = Seq2SeqMAMBAFORMER(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 尝试加载V14 FIXED的预训练权重
    v14_checkpoint = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_optimized_20250812_204543/best_model.pth'
    if os.path.exists(v14_checkpoint):
        try:
            checkpoint = torch.load(v14_checkpoint, map_location=device, weights_only=False)
            # 只加载backbone权重
            backbone_dict = {}
            for k, v in checkpoint['model_state_dict'].items():
                if 'backbone.' in k:
                    backbone_dict[k] = v
                elif not k.startswith('seq_classifier') and not k.startswith('n1_specialist'):
                    # 尝试映射到backbone
                    backbone_dict[f'backbone.{k}'] = v
            
            model.load_state_dict(backbone_dict, strict=False)
            logging.info(f"✅ Loaded V14 FIXED backbone weights from {v14_checkpoint}")
        except Exception as e:
            logging.warning(f"Could not load V14 weights: {e}")
    
    # 损失函数
    criterion = Seq2SeqFocalLoss(
        gamma=2.0,
        rem_weight=config['rem_weight'],
        n1_weight=config['n1_weight']
    )
    temporal_loss = TemporalConsistencyLoss(weight=0.1)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    
    # 创建滑动窗口评估器
    sliding_evaluator = SlidingWindowEvaluator(model, seq_len=config['seq_len'], device=device)
    
    # 训练循环
    best_val_acc = 0
    best_test_acc = 0
    patience_counter = 0
    
    logging.info("\n" + "="*80)
    logging.info("Starting Training with Sequence-to-Sequence!")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, temporal_loss, optimizer, device, epoch
        )
        
        # 标准评估（快速）
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate_standard(
            model, val_loader, device
        )
        
        # 学习率调整
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val (Standard): Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # 每5个epoch或最佳模型时，使用滑动窗口评估
        if (epoch + 1) % 5 == 0 or val_acc > best_val_acc:
            logging.info("  Running sliding window evaluation...")
            sw_acc, sw_f1, sw_kappa, sw_class_f1, sw_cm = sliding_evaluator.evaluate_with_sliding_window(val_loader)
            logging.info(f"  Val (Sliding): Acc={sw_acc:.4f}, F1={sw_f1:.4f}, Kappa={sw_kappa:.4f}")
            
            # 使用滑动窗口结果判断最佳模型
            if sw_acc > best_val_acc:
                best_val_acc = sw_acc
                patience_counter = 0
                
                # 在测试集上评估
                test_acc, test_f1, test_kappa, test_class_f1, test_cm = sliding_evaluator.evaluate_with_sliding_window(test_loader)
                best_test_acc = test_acc
                
                # 保存模型
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_acc': sw_acc,
                    'test_acc': test_acc,
                    'config': config
                }, os.path.join(log_dir, 'best_model.pth'))
                
                logging.info(f"  💾 Saved best model (Val SW Acc: {sw_acc:.4f}, Test SW Acc: {test_acc:.4f})")
                logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                            f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
                
                if test_acc >= 0.90:
                    logging.info("\n" + "="*80)
                    logging.info("🎉 ACHIEVED 90% TEST ACCURACY WITH SEQ2SEQ!")
                    logging.info("="*80)
                    break
            else:
                patience_counter += 1
        else:
            if val_acc > best_val_acc * 0.98:  # 接近最佳时重置patience
                patience_counter = 0
            else:
                patience_counter += 1
        
        # Early stopping
        if patience_counter >= config['patience']:
            logging.info(f"Early stopping at epoch {epoch+1}")
            break
    
    # 最终评估
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS")
    logging.info("="*80)
    
    # 加载最佳模型
    checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 最终测试集评估（滑动窗口）
    final_test_acc, final_test_f1, final_test_kappa, final_test_class_f1, final_test_cm = \
        sliding_evaluator.evaluate_with_sliding_window(test_loader)
    
    logging.info(f"Best Val Accuracy (Sliding Window): {best_val_acc:.4f}")
    logging.info(f"Final Test Accuracy (Sliding Window): {final_test_acc:.4f} ({final_test_acc*100:.2f}%)")
    logging.info(f"Final Test F1: {final_test_f1:.4f}")
    logging.info(f"Final Test Kappa: {final_test_kappa:.4f}")
    logging.info(f"Final Test Class F1: W={final_test_class_f1[0]:.3f}, N1={final_test_class_f1[1]:.3f}, "
                f"N2={final_test_class_f1[2]:.3f}, N3={final_test_class_f1[3]:.3f}, REM={final_test_class_f1[4]:.3f}")
    
    # 打印混淆矩阵
    logging.info("\nConfusion Matrix:")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logging.info("       " + "  ".join([f"{name:>6}" for name in class_names]))
    for i, true_class in enumerate(class_names):
        row = final_test_cm[i]
        row_str = " ".join([f"{val:>6}" for val in row])
        logging.info(f"{true_class:>6} {row_str}")
    
    # 与目标对比
    gap_to_90 = 0.90 - final_test_acc
    if final_test_acc >= 0.90:
        logging.info(f"\n✅ TARGET ACHIEVED! Accuracy: {final_test_acc:.4f}")
    else:
        logging.info(f"\nGap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
    
    # 保存结果
    results = {
        'best_val_acc': float(best_val_acc),
        'final_test_acc': float(final_test_acc),
        'final_test_f1': float(final_test_f1),
        'final_test_kappa': float(final_test_kappa),
        'final_test_class_f1': final_test_class_f1.tolist(),
        'confusion_matrix': final_test_cm.tolist(),
        'config': config
    }
    
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\nResults saved to {log_dir}")


if __name__ == '__main__':
    main()