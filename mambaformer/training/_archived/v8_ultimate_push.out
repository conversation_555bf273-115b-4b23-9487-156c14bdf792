2025-08-12 14:39:10,737 - INFO - ================================================================================
2025-08-12 14:39:10,737 - INFO - 🚀 V8 ULTIMATE PUSH - FINAL 3.24% TO 90%
2025-08-12 14:39:10,737 - INFO - ================================================================================
2025-08-12 14:39:10,737 - INFO - Configuration: {
  "d_model": 320,
  "n_heads": 20,
  "n_layers": 7,
  "dropout": 0.15,
  "seq_len": 5,
  "batch_size": 32,
  "learning_rate": 0.00015,
  "weight_decay": 5e-05,
  "num_epochs": 150,
  "patience": 30
}
2025-08-12 14:39:10,737 - INFO - Strategy: 3-Expert Fusion + Extreme Augmentation + TTA
2025-08-12 14:39:10,737 - INFO - Current best: 86.76% | Target: 90.00% | Gap: 3.24%
2025-08-12 14:39:10,905 - INFO - Device: cuda
2025-08-12 14:39:10,905 - INFO - Loading datasets...
2025-08-12 14:39:11,969 - INFO - 从 24 个文件加载了 25362 个epochs, 创建了 25266 个序列
2025-08-12 14:39:11,969 - INFO - 创建序列数据集: 25266个序列, 序列长度=5, 通道数=3, 总epochs=25362
2025-08-12 14:39:12,353 - INFO - 从 5 个文件加载了 4951 个epochs, 创建了 4931 个序列
2025-08-12 14:39:12,354 - INFO - 创建序列数据集: 4931个序列, 序列长度=5, 通道数=3, 总epochs=4951
2025-08-12 14:39:12,873 - INFO - 从 10 个文件加载了 11995 个epochs, 创建了 11955 个序列
2025-08-12 14:39:12,874 - INFO - 创建序列数据集: 11955个序列, 序列长度=5, 通道数=3, 总epochs=11995
2025-08-12 14:39:12,874 - INFO - Dataset sizes: Train=25266, Val=4931, Test=11955
2025-08-12 14:39:12,982 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=8,950,519, d_model=320, n_heads=20, n_layers=7
2025-08-12 14:39:13,174 - INFO - Model parameters: 8,971,704
2025-08-12 14:39:14,434 - INFO - STARTING ULTIMATE PUSH TRAINING!
2025-08-12 14:39:14,434 - INFO - Mission: Bridge final 3.24% gap to reach 90%

Epoch 1 - ULTIMATE PUSH:   0%|          | 0/789 [00:00<?, ?it/s]
Epoch 1 - ULTIMATE PUSH:   0%|          | 0/789 [00:00<?, ?it/s, loss=3.6624, acc=0.156, N1=0.000]
Epoch 1 - ULTIMATE PUSH:   0%|          | 1/789 [00:00<10:21,  1.27it/s, loss=3.6624, acc=0.156, N1=0.000]
Epoch 1 - ULTIMATE PUSH:   0%|          | 1/789 [00:00<10:21,  1.27it/s, loss=4.4964, acc=0.188, N1=0.000]
Epoch 1 - ULTIMATE PUSH:   0%|          | 1/789 [00:00<10:21,  1.27it/s, loss=4.2130, acc=0.198, N1=0.200]
Epoch 1 - ULTIMATE PUSH:   0%|          | 1/789 [00:00<10:21,  1.27it/s, loss=5.0796, acc=0.219, N1=0.500]
Epoch 1 - ULTIMATE PUSH:   1%|          | 4/789 [00:00<02:28,  5.28it/s, loss=5.0796, acc=0.219, N1=0.500]
Epoch 1 - ULTIMATE PUSH:   1%|          | 4/789 [00:00<02:28,  5.28it/s, loss=4.5564, acc=0.200, N1=0.600]
Epoch 1 - ULTIMATE PUSH:   1%|          | 4/789 [00:01<02:28,  5.28it/s, loss=5.6266, acc=0.188, N1=0.643]
Epoch 1 - ULTIMATE PUSH:   1%|          | 6/789 [00:01<01:41,  7.73it/s, loss=5.6266, acc=0.188, N1=0.643]
Epoch 1 - ULTIMATE PUSH:   1%|          | 6/789 [00:01<01:41,  7.73it/s, loss=6.3984, acc=0.183, N1=0.684]
Epoch 1 - ULTIMATE PUSH:   1%|          | 6/789 [00:01<01:41,  7.73it/s, loss=4.4796, acc=0.172, N1=0.714]
Epoch 1 - ULTIMATE PUSH:   1%|          | 8/789 [00:01<01:19,  9.85it/s, loss=4.4796, acc=0.172, N1=0.714]
Epoch 1 - ULTIMATE PUSH:   1%|          | 8/789 [00:01<01:19,  9.85it/s, loss=4.3491, acc=0.160, N1=0.739]
Epoch 1 - ULTIMATE PUSH:   1%|          | 8/789 [00:01<01:19,  9.85it/s, loss=3.9909, acc=0.153, N1=0.760]
Epoch 1 - ULTIMATE PUSH:   1%|▏         | 10/789 [00:01<01:07, 11.58it/s, loss=3.9909, acc=0.153, N1=0.760]
Epoch 1 - ULTIMATE PUSH:   1%|▏         | 10/789 [00:01<01:07, 11.58it/s, loss=3.9455, acc=0.145, N1=0.769]
Epoch 1 - ULTIMATE PUSH:   1%|▏         | 10/789 [00:01<01:07, 11.58it/s, loss=4.5711, acc=0.151, N1=0.786]
Epoch 1 - ULTIMATE PUSH:   2%|▏         | 12/789 [00:01<01:02, 12.51it/s, loss=4.5711, acc=0.151, N1=0.786]
Epoch 1 - ULTIMATE PUSH:   2%|▏         | 12/789 [00:01<01:02, 12.51it/s, loss=5.0835, acc=0.159, N1=0.806]
Epoch 1 - ULTIMATE PUSH:   2%|▏         | 13/789 [00:01<01:33,  8.27it/s, loss=5.0835, acc=0.159, N1=0.806]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ultimate_push.py", line 611, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ultimate_push.py", line 483, in main
    train_loss, train_acc, train_f1 = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ultimate_push.py", line 242, in train_epoch
    data = extreme_augment(data, target if target.dim() == 1 else target[:, target.shape[1]//2], epoch)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_ultimate_push.py", line 219, in extreme_augment
    mask_len = random.randint(30, min(100, seq_len // 3))
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/random.py", line 338, in randint
    return self.randrange(a, b+1)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/random.py", line 316, in randrange
    raise ValueError("empty range for randrange() (%d, %d, %d)" % (istart, istop, width))
ValueError: empty range for randrange() (30, 2, -28)
