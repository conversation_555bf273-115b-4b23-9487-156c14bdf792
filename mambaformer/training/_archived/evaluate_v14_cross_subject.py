#!/usr/bin/env python3
"""
Cross-subject evaluation for V14 model
Evaluates the trained V14 model on different subject groups to simulate cross-validation
"""

import os
import sys
import json
import numpy as np
import torch
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator
from torch.utils.data import DataLoader


def get_subject_groups():
    """Define 5 subject groups for cross-evaluation"""
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # Define 5 groups of subjects (4 subjects each)
    subject_groups = [
        # Group 1 (original test set)
        {
            'subjects': ['00', '01', '15', '17'],
            'files': ['SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
                     'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz']
        },
        # Group 2
        {
            'subjects': ['02', '03', '04', '05'],
            'files': ['SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
                     'SC4041E0.npz', 'SC4042E0.npz', 'SC4051E0.npz', 'SC4052E0.npz']
        },
        # Group 3
        {
            'subjects': ['06', '07', '08', '09'],
            'files': ['SC4061E0.npz', 'SC4062E0.npz', 'SC4071E0.npz', 'SC4072E0.npz',
                     'SC4081E0.npz', 'SC4082E0.npz', 'SC4091E0.npz', 'SC4092E0.npz']
        },
        # Group 4
        {
            'subjects': ['10', '11', '12', '13'],
            'files': ['SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz', 'SC4112E0.npz',
                     'SC4121E0.npz', 'SC4122E0.npz', 'SC4131E0.npz']
        },
        # Group 5
        {
            'subjects': ['14', '16', '18', '19'],
            'files': ['SC4141E0.npz', 'SC4142E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
                     'SC4181E0.npz', 'SC4182E0.npz', 'SC4191E0.npz', 'SC4192E0.npz']
        }
    ]
    
    # Convert to full paths
    for group in subject_groups:
        group['files'] = [os.path.join(data_dir, f) for f in group['files']]
    
    return subject_groups


def evaluate_on_group(model, group_data, config, device, group_idx):
    """Evaluate model on a specific subject group"""
    
    logging.info(f"\n{'='*60}")
    logging.info(f"Evaluating on Group {group_idx + 1}")
    logging.info(f"Subjects: {group_data['subjects']}")
    logging.info(f"Files: {len(group_data['files'])}")
    
    # Create dataset
    dataset = SequenceSleepDataset(
        group_data['files'],
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None  # Use all data
    )
    
    data_loader = DataLoader(
        dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=config['num_workers'],
        pin_memory=True
    )
    
    logging.info(f"Total epochs: {dataset.total_epochs}")
    
    # Evaluate
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
    evaluator.total_epochs = dataset.total_epochs
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for data, labels in tqdm(data_loader, desc=f'Group {group_idx+1}'):
            data = data.to(device)
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(dataset):
                    seq_info = dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    # Get metrics
    metrics = evaluator.evaluate()
    
    # Get confusion matrix
    final_preds, final_labels, _ = evaluator.get_final_predictions()
    cm = confusion_matrix(final_labels, final_preds)
    
    results = {
        'group': group_idx + 1,
        'subjects': group_data['subjects'],
        'n_files': len(group_data['files']),
        'n_epochs': dataset.total_epochs,
        'accuracy': float(metrics['accuracy']),
        'macro_f1': float(metrics['macro_f1']),
        'kappa': float(metrics['kappa']),
        'confusion_matrix': cm.tolist(),
        'per_class_metrics': {}
    }
    
    # Add per-class metrics
    for class_name, class_metrics in metrics['per_class_metrics'].items():
        results['per_class_metrics'][class_name] = {
            'precision': float(class_metrics['precision']),
            'recall': float(class_metrics['recall']),
            'f1': float(class_metrics['f1']),
            'support': int(class_metrics['support'])
        }
    
    logging.info(f"Group {group_idx + 1} Results:")
    logging.info(f"  Accuracy: {results['accuracy']:.4f} ({results['accuracy']*100:.2f}%)")
    logging.info(f"  Macro F1: {results['macro_f1']:.4f} ({results['macro_f1']*100:.2f}%)")
    logging.info(f"  Kappa: {results['kappa']:.4f}")
    
    return results


def main():
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = f"../logs/v14_cross_subject_{timestamp}.log"
    os.makedirs("../logs", exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 MAMBAFORMER - Cross-Subject Evaluation")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Configuration
    config = {
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'num_workers': 4
    }
    
    # Load model
    model_path = '../../checkpoints/v14_fixed.pth'
    if not os.path.exists(model_path):
        logging.error(f"Model not found at {model_path}")
        return None
    
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    checkpoint = torch.load(model_path, map_location=device)
    if isinstance(checkpoint, dict):
        model.load_state_dict(checkpoint.get('model_state_dict', checkpoint))
    else:
        model.load_state_dict(checkpoint)
    
    logging.info(f"✅ Model loaded from {model_path}")
    
    # Get subject groups
    subject_groups = get_subject_groups()
    
    # Evaluate on each group
    all_results = []
    
    for group_idx, group_data in enumerate(subject_groups):
        group_results = evaluate_on_group(model, group_data, config, device, group_idx)
        all_results.append(group_results)
    
    # Calculate average metrics
    avg_accuracy = np.mean([r['accuracy'] for r in all_results])
    avg_f1 = np.mean([r['macro_f1'] for r in all_results])
    avg_kappa = np.mean([r['kappa'] for r in all_results])
    
    std_accuracy = np.std([r['accuracy'] for r in all_results])
    std_f1 = np.std([r['macro_f1'] for r in all_results])
    std_kappa = np.std([r['kappa'] for r in all_results])
    
    # Log summary
    logging.info("\n" + "="*80)
    logging.info("📊 CROSS-SUBJECT EVALUATION SUMMARY")
    logging.info("="*80)
    
    logging.info("\nPer-Group Results:")
    for i, result in enumerate(all_results):
        logging.info(f"Group {i+1} (Subjects {result['subjects']}): "
                    f"Acc={result['accuracy']:.4f}, "
                    f"F1={result['macro_f1']:.4f}, "
                    f"Kappa={result['kappa']:.4f}")
    
    logging.info("\n📊 Average Metrics (Mean ± Std):")
    logging.info(f"  Accuracy: {avg_accuracy:.4f} ± {std_accuracy:.4f} ({avg_accuracy*100:.2f}% ± {std_accuracy*100:.2f}%)")
    logging.info(f"  Macro F1: {avg_f1:.4f} ± {std_f1:.4f} ({avg_f1*100:.2f}% ± {std_f1*100:.2f}%)")
    logging.info(f"  Kappa: {avg_kappa:.4f} ± {std_kappa:.4f}")
    
    # Performance range
    logging.info("\n📈 Performance Range:")
    acc_range = [min(r['accuracy'] for r in all_results), max(r['accuracy'] for r in all_results)]
    f1_range = [min(r['macro_f1'] for r in all_results), max(r['macro_f1'] for r in all_results)]
    kappa_range = [min(r['kappa'] for r in all_results), max(r['kappa'] for r in all_results)]
    
    logging.info(f"  Accuracy: [{acc_range[0]:.4f}, {acc_range[1]:.4f}] ({acc_range[0]*100:.2f}% - {acc_range[1]*100:.2f}%)")
    logging.info(f"  Macro F1: [{f1_range[0]:.4f}, {f1_range[1]:.4f}] ({f1_range[0]*100:.2f}% - {f1_range[1]*100:.2f}%)")
    logging.info(f"  Kappa: [{kappa_range[0]:.4f}, {kappa_range[1]:.4f}]")
    
    # Check against targets
    logging.info("\n🎯 Target Achievement (Based on Average):")
    if avg_accuracy >= 0.87:
        logging.info(f"  ✅ Accuracy: {avg_accuracy:.4f} ≥ 0.87")
    else:
        logging.info(f"  ❌ Accuracy: {avg_accuracy:.4f} < 0.87 (gap: {0.87-avg_accuracy:.4f})")
    
    if avg_f1 >= 0.80:
        logging.info(f"  ✅ Macro F1: {avg_f1:.4f} ≥ 0.80")
    else:
        logging.info(f"  ❌ Macro F1: {avg_f1:.4f} < 0.80 (gap: {0.80-avg_f1:.4f})")
    
    if avg_kappa >= 0.80:
        logging.info(f"  ✅ Kappa: {avg_kappa:.4f} ≥ 0.80")
    else:
        logging.info(f"  ❌ Kappa: {avg_kappa:.4f} < 0.80 (gap: {0.80-avg_kappa:.4f})")
    
    # Convert numpy types for JSON serialization
    def convert_to_python_types(obj):
        if isinstance(obj, (np.bool_, np.integer)):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: convert_to_python_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_to_python_types(item) for item in obj]
        else:
            return obj
    
    # Save results
    results = {
        'timestamp': timestamp,
        'model': 'V14_FIXED',
        'config': config,
        'group_results': all_results,
        'average_metrics': {
            'accuracy': {'mean': float(avg_accuracy), 'std': float(std_accuracy)},
            'macro_f1': {'mean': float(avg_f1), 'std': float(std_f1)},
            'kappa': {'mean': float(avg_kappa), 'std': float(std_kappa)}
        },
        'performance_range': {
            'accuracy': {'min': float(acc_range[0]), 'max': float(acc_range[1])},
            'macro_f1': {'min': float(f1_range[0]), 'max': float(f1_range[1])},
            'kappa': {'min': float(kappa_range[0]), 'max': float(kappa_range[1])}
        },
        'targets_achieved': {
            'accuracy': bool(avg_accuracy >= 0.87),
            'macro_f1': bool(avg_f1 >= 0.80),
            'kappa': bool(avg_kappa >= 0.80)
        }
    }
    
    # Convert all results to ensure JSON serialization
    results = convert_to_python_types(results)
    
    results_file = f'../../configs/v14_cross_subject_results.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {results_file}")
    
    # Create visualization
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    metrics = ['Accuracy', 'Macro F1', 'Kappa']
    group_metrics = [
        [r['accuracy'] for r in all_results],
        [r['macro_f1'] for r in all_results],
        [r['kappa'] for r in all_results]
    ]
    targets = [0.87, 0.80, 0.80]
    
    for ax, metric_name, metric_values, target in zip(axes, metrics, group_metrics, targets):
        x = range(1, 6)
        bars = ax.bar(x, metric_values, color='skyblue', edgecolor='navy', alpha=0.7)
        
        # Color bars based on target achievement
        for i, (bar, val) in enumerate(zip(bars, metric_values)):
            if val >= target:
                bar.set_color('lightgreen')
            else:
                bar.set_color('lightcoral')
        
        ax.axhline(y=np.mean(metric_values), color='red', linestyle='--', 
                  label=f'Mean: {np.mean(metric_values):.4f}', linewidth=2)
        ax.axhline(y=target, color='green', linestyle=':', 
                  label=f'Target: {target}', linewidth=2)
        
        # Add value labels on bars
        for i, v in enumerate(metric_values):
            ax.text(i + 1, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
        
        ax.set_xlabel('Subject Group')
        ax.set_ylabel(metric_name)
        ax.set_title(f'{metric_name} Across Subject Groups')
        ax.set_xticks(x)
        ax.set_ylim([min(metric_values) - 0.05, max(max(metric_values), target) + 0.05])
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.suptitle('V14 Model - Cross-Subject Evaluation', fontsize=16)
    plt.tight_layout()
    plot_file = '../../configs/v14_cross_subject_plot.png'
    plt.savefig(plot_file, dpi=150, bbox_inches='tight')
    plt.close()
    
    logging.info(f"📊 Plot saved to {plot_file}")
    logging.info("\n✅ Cross-Subject Evaluation Complete!")
    
    return results


if __name__ == "__main__":
    main()