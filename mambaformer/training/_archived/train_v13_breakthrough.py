"""
MAMBAFORMER V13 - 突破性能瓶颈
目标：ACC=87%, Kappa=0.8, MF1=80%

核心策略：
1. 回归单模态EEG，优化核心架构
2. 增强数据增强和正则化
3. 优化损失函数权重
4. 后处理优化（HMM平滑）
5. 集成学习策略
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
import datetime
from tqdm import tqdm
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import (
    SequentialMAMBAFORMER_V2,
    SequentialFocalLoss,
    TemporalConsistencyLoss
)
from utils.multimodal_dataset import create_multimodal_dataloaders
from utils.epoch_level_evaluation import EpochLevelEvaluator, log_epoch_level_metrics
from utils.enhanced_metrics import get_comprehensive_metrics
from utils.hmm_postprocessing import HMMPostProcessor


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"v13_breakthrough_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file


class EnhancedLabelSmoothingLoss(nn.Module):
    """增强的标签平滑损失，针对困难类别调整"""
    def __init__(self, n_classes, smoothing=0.1, class_weights=None):
        super().__init__()
        self.n_classes = n_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
        self.class_weights = class_weights
        
    def forward(self, pred, target):
        pred = pred.log_softmax(dim=-1)
        true_dist = torch.zeros_like(pred)
        true_dist.fill_(self.smoothing / (self.n_classes - 1))
        true_dist.scatter_(1, target.data.unsqueeze(1), self.confidence)
        
        if self.class_weights is not None:
            weights = self.class_weights[target].unsqueeze(1)
            loss = torch.sum(-true_dist * pred * weights, dim=-1)
        else:
            loss = torch.sum(-true_dist * pred, dim=-1)
            
        return torch.mean(loss)


class MixupAugmentation:
    """Mixup数据增强"""
    def __init__(self, alpha=0.2):
        self.alpha = alpha
        
    def __call__(self, x, y):
        if self.alpha > 0:
            lam = np.random.beta(self.alpha, self.alpha)
            batch_size = x.size(0)
            index = torch.randperm(batch_size).to(x.device)
            mixed_x = lam * x + (1 - lam) * x[index]
            y_a, y_b = y, y[index]
            return mixed_x, y_a, y_b, lam
        return x, y, y, 1


class V13Model(nn.Module):
    """V13模型 - 优化的单模态架构"""
    def __init__(self, config):
        super().__init__()
        
        # 输入通道数应该是3（与V7一致）
        input_channels = 3
        
        # 主干网络
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=config['n_classes'],
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len']
        )
        
        # 增强的分类头
        self.classifier = nn.Sequential(
            nn.Linear(config['n_classes'], config['d_model']),
            nn.LayerNorm(config['d_model']),
            nn.GELU(),
            nn.Dropout(config['dropout']),
            nn.Linear(config['d_model'], config['d_model']//2),
            nn.LayerNorm(config['d_model']//2),
            nn.GELU(),
            nn.Dropout(config['dropout']),
            nn.Linear(config['d_model']//2, config['n_classes'])
        )
        
    def forward(self, x):
        # x应该是 [B, S, F] 格式，其中F是特征维度
        if len(x.shape) != 3:
            raise ValueError(f"Expected input shape [B, S, F], got {x.shape}")
        
        B, S, F = x.shape
        
        # 直接使用主干网络处理（因为输入已经是处理过的特征）
        main_out, aux_out = self.backbone(x)
        
        # 增强分类
        enhanced_out = self.classifier(main_out)
        
        return enhanced_out, aux_out


def train_epoch_v13(model, train_loader, criterion, temp_loss_fn, optimizer, 
                   device, epoch, config, scaler, mixup, hmm_processor=None):
    """V13训练函数"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} - Train')
    
    for batch_idx, (eeg_data, _, _, labels) in enumerate(pbar):
        eeg_data = eeg_data.to(device, non_blocking=True)
        labels = labels.to(device, non_blocking=True)
        
        # Mixup增强
        if mixup and np.random.random() < 0.5:
            eeg_data, labels_a, labels_b, lam = mixup(eeg_data, labels.view(-1))
            mixed = True
        else:
            mixed = False
        
        optimizer.zero_grad()
        
        with torch.amp.autocast('cuda', enabled=config.get('use_amp', False)):
            main_output, aux_output = model(eeg_data)
            
            if mixed:
                loss = lam * criterion(main_output.view(-1, 5), labels_a) + \
                       (1 - lam) * criterion(main_output.view(-1, 5), labels_b)
            else:
                loss = criterion(main_output.view(-1, 5), labels.view(-1))
            
            # 时序一致性损失
            temp_loss = temp_loss_fn(main_output)
            
            # 辅助任务损失
            aux_labels = (labels >= 2).long()  # 睡眠vs清醒
            aux_loss = nn.CrossEntropyLoss()(aux_output.view(-1, 2), aux_labels.view(-1))
            
            total_loss_value = loss + config['temp_loss_weight'] * temp_loss + 0.1 * aux_loss
        
        if config.get('use_amp', False):
            scaler.scale(total_loss_value).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            scaler.step(optimizer)
            scaler.update()
        else:
            total_loss_value.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
        
        total_loss += total_loss_value.item()
        
        if not mixed:
            preds = torch.argmax(main_output, dim=-1)
            all_preds.extend(preds.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
        
        pbar.set_postfix({
            'loss': f'{total_loss_value.item():.4f}',
            'main': f'{loss.item():.4f}',
            'temp': f'{temp_loss.item():.4f}'
        })
    
    metrics = get_comprehensive_metrics(np.array(all_labels), np.array(all_preds))
    avg_loss = total_loss / len(train_loader)
    
    return avg_loss, metrics


def evaluate_with_hmm(model, test_dataset, test_loader, device, config, hmm_processor=None):
    """带HMM后处理的评估"""
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
    
    all_probs = []
    all_labels = []
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for batch_idx, (eeg_data, _, _, labels) in enumerate(tqdm(test_loader, desc="Evaluating")):
            eeg_data = eeg_data.to(device, non_blocking=True)
            labels = labels.to(device, non_blocking=True)
            
            outputs, _ = model(eeg_data)
            probs = torch.softmax(outputs, dim=-1)
            
            # 收集概率和标签
            all_probs.append(probs.cpu().numpy())
            all_labels.append(labels.cpu().numpy())
            
            # Epoch级别评估
            batch_size = eeg_data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
                    else:
                        start_indices.append(seq_idx)
                else:
                    break
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    # 基础评估结果
    base_metrics = evaluator.evaluate()
    
    # HMM后处理
    if hmm_processor is not None:
        all_probs = np.concatenate(all_probs, axis=0)
        all_labels = np.concatenate(all_labels, axis=0)
        
        # 应用HMM平滑
        smoothed_preds = hmm_processor.smooth_predictions(all_probs.reshape(-1, 5))
        smoothed_preds = smoothed_preds.reshape(all_labels.shape)
        
        # 计算HMM后的指标
        hmm_metrics = get_comprehensive_metrics(
            all_labels.flatten(), 
            smoothed_preds.flatten()
        )
        
        return base_metrics, hmm_metrics
    
    return base_metrics, None


def train_v13(config, device):
    """V13训练主函数"""
    logging.info("\n" + "="*80)
    logging.info("🚀 开始训练 MAMBAFORMER V13 - 突破性能瓶颈")
    logging.info(f"🎯 目标: ACC=87%, Kappa=0.8, MF1=80%")
    
    # 加载数据
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    train_files = [os.path.join(data_dir, f) for f in splits['splits']['train']['files']]
    val_files = [os.path.join(data_dir, f) for f in splits['splits']['val']['files']]
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    # 创建数据加载器（只用EEG）
    (train_loader, val_loader, test_loader, 
     train_dataset, val_dataset, test_dataset) = create_multimodal_dataloaders(
        train_files, val_files, test_files, 
        config,
        use_eog=False,
        use_emg=False
    )
    
    logging.info(f"📊 数据集大小: 训练={len(train_dataset)}, 验证={len(val_dataset)}, 测试={len(test_dataset)}")
    
    # 计算类别权重
    train_labels = []
    for _, _, _, labels in train_loader:
        train_labels.extend(labels.numpy().flatten())
    
    class_counts = np.bincount(train_labels, minlength=5)
    class_weights = 1.0 / (class_counts / class_counts.sum() + 1e-6)
    class_weights = torch.tensor(class_weights / class_weights.sum() * 5, dtype=torch.float32).to(device)
    
    logging.info(f"类别权重: {class_weights.cpu().numpy()}")
    
    # 创建模型
    model = V13Model(config).to(device)
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"模型参数量: {total_params:,}")
    
    # 损失函数
    criterion = EnhancedLabelSmoothingLoss(
        n_classes=5, 
        smoothing=config['label_smoothing'],
        class_weights=class_weights
    )
    temp_loss_fn = TemporalConsistencyLoss(weight=config['temp_loss_weight'])
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(), 
        lr=config['learning_rate'], 
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # 学习率调度器 - 余弦退火重启
    scheduler = CosineAnnealingWarmRestarts(
        optimizer, 
        T_0=10,  # 首次重启周期
        T_mult=2,  # 重启周期倍数
        eta_min=1e-6
    )
    
    # 混合精度训练
    scaler = torch.amp.GradScaler('cuda', enabled=config.get('use_amp', False))
    
    # Mixup增强
    mixup = MixupAugmentation(alpha=0.2)
    
    # HMM后处理器
    hmm_processor = HMMPostProcessor(n_states=5)
    
    logging.info("📋 V13核心策略:")
    logging.info("  • 增强的单模态EEG架构")
    logging.info("  • 多尺度特征融合")
    logging.info("  • Mixup数据增强")
    logging.info("  • 类别平衡损失")
    logging.info("  • 余弦退火学习率")
    logging.info("  • HMM后处理平滑")
    
    # 训练循环
    best_val_metrics = {'macro_f1': 0, 'kappa': 0}
    best_model_state = None
    patience_counter = 0
    
    for epoch in range(1, config['num_epochs'] + 1):
        # 训练
        train_loss, train_metrics = train_epoch_v13(
            model, train_loader, criterion, temp_loss_fn, 
            optimizer, device, epoch, config, scaler, mixup
        )
        
        # 验证
        val_metrics, val_hmm_metrics = evaluate_with_hmm(
            model, val_dataset, val_loader, device, config, hmm_processor
        )
        
        # 学习率调度
        scheduler.step()
        
        # 记录
        current_lr = optimizer.param_groups[0]['lr']
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        logging.info(f"Train - Loss: {train_loss:.4f}, Acc: {train_metrics['accuracy']:.4f}, "
                    f"F1: {train_metrics['macro_f1']:.4f}, Kappa: {train_metrics['kappa']:.4f}")
        logging.info(f"Val   - Acc: {val_metrics['accuracy']:.4f}, F1: {val_metrics['macro_f1']:.4f}, "
                    f"Kappa: {val_metrics['kappa']:.4f}")
        
        if val_hmm_metrics:
            logging.info(f"Val HMM - Acc: {val_hmm_metrics['accuracy']:.4f}, "
                        f"F1: {val_hmm_metrics['macro_f1']:.4f}, Kappa: {val_hmm_metrics['kappa']:.4f}")
        
        logging.info(f"LR: {current_lr:.2e}")
        
        # 保存最佳模型
        val_score = val_metrics['macro_f1']
        if val_hmm_metrics and val_hmm_metrics['macro_f1'] > val_score:
            val_score = val_hmm_metrics['macro_f1']
        
        if val_score > best_val_metrics['macro_f1']:
            best_val_metrics = val_metrics.copy()
            if val_hmm_metrics:
                best_val_metrics['hmm_f1'] = val_hmm_metrics['macro_f1']
                best_val_metrics['hmm_kappa'] = val_hmm_metrics['kappa']
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            logging.info(f"💾 新的最佳模型: F1={val_score:.4f}")
        else:
            patience_counter += 1
        
        if patience_counter >= config['patience']:
            logging.info(f"⏹️  早停: {config['patience']}轮未改善")
            break
    
    # 测试评估
    if best_model_state:
        model.load_state_dict(best_model_state)
    
    test_metrics, test_hmm_metrics = evaluate_with_hmm(
        model, test_dataset, test_loader, device, config, hmm_processor
    )
    
    # 详细测试结果
    log_epoch_level_metrics(test_metrics, phase='Test V13', logger=logging)
    
    if test_hmm_metrics:
        logging.info("\n📊 HMM后处理结果:")
        logging.info(f"Accuracy: {test_hmm_metrics['accuracy']:.4f}")
        logging.info(f"Macro F1: {test_hmm_metrics['macro_f1']:.4f}")
        logging.info(f"Kappa: {test_hmm_metrics['kappa']:.4f}")
    
    # 保存模型
    os.makedirs('../../checkpoints', exist_ok=True)
    torch.save(model.state_dict(), '../../checkpoints/v13_breakthrough.pth')
    
    return test_metrics, test_hmm_metrics


def main():
    # V13配置
    config = {
        'batch_size': 32,
        'seq_len': 5,
        'learning_rate': 3e-5,
        'weight_decay': 1e-4,
        'num_epochs': 100,
        'patience': 20,
        'd_model': 256,  # 增大模型容量
        'n_heads': 16,
        'n_layers': 6,   # 适中的深度
        'n_classes': 5,
        'dropout': 0.2,
        'temp_loss_weight': 0.15,
        'label_smoothing': 0.08,
        'use_amp': True,
        'use_channels': 2,
        'num_workers': 4
    }
    
    log_file = setup_logging()
    
    logging.info("🚀 MAMBAFORMER V13 - 突破性能瓶颈")
    logging.info("=" * 80)
    logging.info("🎯 核心策略:")
    logging.info("  1. 增强的单模态架构")
    logging.info("  2. 多尺度特征融合")
    logging.info("  3. 高级数据增强")
    logging.info("  4. 类别平衡训练")
    logging.info("  5. HMM后处理")
    logging.info(f"📋 配置: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️  使用设备: {device}")
    
    # 训练
    test_metrics, test_hmm_metrics = train_v13(config, device)
    
    # 结果对比
    logging.info("\n" + "="*80)
    logging.info("📊 V13突破性结果")
    logging.info("="*80)
    
    # 目标评估
    target_acc = 0.87
    target_kappa = 0.8
    target_f1 = 0.8
    
    final_acc = test_hmm_metrics['accuracy'] if test_hmm_metrics else test_metrics['accuracy']
    final_kappa = test_hmm_metrics['kappa'] if test_hmm_metrics else test_metrics['kappa']
    final_f1 = test_hmm_metrics['macro_f1'] if test_hmm_metrics else test_metrics['macro_f1']
    
    logging.info(f"最终结果:")
    logging.info(f"Accuracy: {final_acc:.4f} (目标: {target_acc:.2f}, 差距: {target_acc - final_acc:.4f})")
    logging.info(f"Kappa:    {final_kappa:.4f} (目标: {target_kappa:.2f}, 差距: {target_kappa - final_kappa:.4f})")
    logging.info(f"Macro F1: {final_f1:.4f} (目标: {target_f1:.2f}, 差距: {target_f1 - final_f1:.4f})")
    
    if final_acc >= target_acc and final_kappa >= target_kappa and final_f1 >= target_f1:
        logging.info("🎉 恭喜！已达到所有目标！")
    else:
        logging.info("🔥 接近目标，继续优化...")
    
    # 保存结果
    results = {
        'version': 'V13_Breakthrough',
        'test_metrics': test_metrics,
        'test_hmm_metrics': test_hmm_metrics,
        'final_results': {
            'accuracy': float(final_acc),
            'kappa': float(final_kappa),
            'macro_f1': float(final_f1)
        },
        'target_gaps': {
            'acc_gap': float(target_acc - final_acc),
            'kappa_gap': float(target_kappa - final_kappa),
            'f1_gap': float(target_f1 - final_f1)
        },
        'config': config,
        'log_file': log_file
    }
    
    with open('../../configs/v13_breakthrough_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=lambda x: float(x) if hasattr(x, 'item') else x)
    
    logging.info(f"\n💾 V13结果已保存")
    logging.info("🌟 V13突破性训练完成！")


if __name__ == "__main__":
    main()