#!/usr/bin/env python3
"""
创建真正的Sequence-to-Sequence MAMBAFORMER模型
实现多时期同时预测+推理时平均概率策略

重要改进：
1. 序列中每个时期都生成预测（不只是中心点）
2. 推理时使用滑动窗口收集多个预测
3. 平均所有预测的概率分布作为最终结果
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset


class Seq2SeqMAMBAFORMER(nn.Module):
    """
    真正的Sequence-to-Sequence MAMBAFORMER
    输入: (batch, seq_len, time_steps, channels)
    输出: (batch, seq_len, n_classes) - 每个时期都有预测！
    """
    def __init__(self, base_model_config):
        super().__init__()
        
        # 使用原始backbone
        self.backbone = SequentialMAMBAFORMER_V2(**base_model_config)
        
        # 序列级别的分类头（为每个时期生成预测）
        d_model = base_model_config['d_model']
        n_classes = base_model_config['n_classes']
        dropout = base_model_config.get('dropout', 0.1)
        
        self.seq_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
    def forward(self, x):
        """
        前向传播 - 为序列中的每个时期生成预测
        """
        # 获取backbone输出
        features, aux = self.backbone(x)
        
        # features shape: (batch, seq_len, d_model)
        # 为每个时期生成预测
        seq_predictions = self.seq_classifier(features)
        
        # 返回完整序列预测
        return seq_predictions, aux


class SlidingWindowInference:
    """
    滑动窗口推理策略
    对每个时期收集多个窗口位置的预测，然后平均概率
    """
    def __init__(self, model, seq_len=5, device='cuda'):
        self.model = model
        self.seq_len = seq_len
        self.device = device
        self.model.eval()
        
    def predict_with_averaging(self, data_sequence, labels_sequence):
        """
        使用滑动窗口和概率平均进行预测
        
        Args:
            data_sequence: 完整的数据序列 (n_epochs, time_steps, channels)
            labels_sequence: 对应的标签序列 (n_epochs,)
        
        Returns:
            predictions: 最终预测 (n_epochs,)
            probabilities: 平均概率 (n_epochs, n_classes)
        """
        n_epochs = data_sequence.shape[0]
        n_classes = 5
        
        # 存储每个时期的所有预测概率
        all_probs = [[] for _ in range(n_epochs)]
        
        # 滑动窗口遍历
        for start_idx in range(max(0, n_epochs - self.seq_len + 1)):
            end_idx = min(start_idx + self.seq_len, n_epochs)
            
            # 如果序列太短，使用padding
            if end_idx - start_idx < self.seq_len:
                # Pad序列到正确长度
                seq_data = torch.zeros(self.seq_len, *data_sequence.shape[1:])
                seq_data[:end_idx-start_idx] = data_sequence[start_idx:end_idx]
                valid_len = end_idx - start_idx
            else:
                seq_data = data_sequence[start_idx:end_idx]
                valid_len = self.seq_len
            
            # 添加batch维度
            seq_data = seq_data.unsqueeze(0).to(self.device)
            
            # 获取预测
            with torch.no_grad():
                seq_predictions, _ = self.model(seq_data)
                seq_probs = F.softmax(seq_predictions[0], dim=-1)  # (seq_len, n_classes)
            
            # 将预测分配到对应的时期
            for i in range(valid_len):
                epoch_idx = start_idx + i
                if epoch_idx < n_epochs:
                    all_probs[epoch_idx].append(seq_probs[i].cpu().numpy())
        
        # 平均每个时期的所有预测
        avg_probs = np.zeros((n_epochs, n_classes))
        final_preds = np.zeros(n_epochs, dtype=np.int32)
        
        for i in range(n_epochs):
            if len(all_probs[i]) > 0:
                # 平均该时期的所有预测概率
                avg_probs[i] = np.mean(all_probs[i], axis=0)
                final_preds[i] = np.argmax(avg_probs[i])
            else:
                # 如果没有预测（不应该发生），使用默认值
                final_preds[i] = 2  # N2是最常见的
        
        return final_preds, avg_probs


def evaluate_seq2seq(model, test_loader, device):
    """
    评估Seq2Seq模型，使用滑动窗口推理
    """
    inference = SlidingWindowInference(model, seq_len=model.backbone.seq_len, device=device)
    
    all_preds = []
    all_targets = []
    
    for data, target in tqdm(test_loader, desc='Seq2Seq Evaluation'):
        # 对每个batch中的序列单独处理
        batch_size = data.shape[0]
        
        for b in range(batch_size):
            seq_data = data[b]  # (seq_len, time_steps, channels)
            seq_target = target[b] if target.dim() > 1 else target[b].unsqueeze(0)
            
            # 使用滑动窗口推理
            preds, _ = inference.predict_with_averaging(seq_data, seq_target)
            
            # 收集中心预测（为了与原始评估兼容）
            center_idx = len(preds) // 2
            all_preds.append(preds[center_idx])
            
            if seq_target.dim() > 0 and len(seq_target) > 1:
                all_targets.append(seq_target[center_idx].item())
            else:
                all_targets.append(seq_target.item() if hasattr(seq_target, 'item') else seq_target)
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm


def test_three_models():
    """
    测试三个关键模型：V30 Ultra, V14 FIXED, V8
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f'../logs/seq2seq_test_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'test.log')),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🔬 Testing Three Key Models with Seq2Seq Enhancement")
    logging.info("="*80)
    
    # 模型配置
    models_to_test = [
        {
            'name': 'V30 Ultra (Final Test 90)',
            'config': {
                'd_model': 512,
                'n_heads': 32,
                'n_layers': 12,
                'dropout': 0.25,
                'seq_len': 7,
                'n_classes': 5,
                'input_channels': 3
            },
            'checkpoint': None,  # 需要找到具体路径
            'training_script': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/final_test_90.py'
        },
        {
            'name': 'V14 FIXED',
            'config': {
                'd_model': 256,
                'n_heads': 16,
                'n_layers': 6,
                'dropout': 0.15,
                'seq_len': 5,
                'n_classes': 5,
                'input_channels': 3
            },
            'checkpoint': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_optimized_20250812_204543/best_model.pth',
            'training_script': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_FIXED.py'
        },
        {
            'name': 'V8 Proper Eval',
            'config': {
                'd_model': 256,
                'n_heads': 16,
                'n_layers': 6,
                'dropout': 0.2,
                'seq_len': 5,
                'n_classes': 5,
                'input_channels': 3
            },
            'checkpoint': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v8_proper_eval_20250812_155847/best_model.pth',
            'training_script': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_proper_eval.py'
        }
    ]
    
    # 数据配置
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 保存模型位置信息
    model_locations = {
        'description': 'Three key MAMBAFORMER models for sleep stage classification',
        'models': []
    }
    
    for model_info in models_to_test:
        logging.info(f"\n{'='*60}")
        logging.info(f"Testing: {model_info['name']}")
        logging.info(f"{'='*60}")
        
        # 创建Seq2Seq模型
        model = Seq2SeqMAMBAFORMER(model_info['config']).to(device)
        
        # 尝试加载checkpoint
        if model_info['checkpoint'] and os.path.exists(model_info['checkpoint']):
            try:
                checkpoint = torch.load(model_info['checkpoint'], map_location=device, weights_only=False)
                # 尝试加载state dict
                if 'model_state_dict' in checkpoint:
                    # 只加载backbone部分，因为seq_classifier是新的
                    backbone_dict = {k: v for k, v in checkpoint['model_state_dict'].items() 
                                   if k.startswith('backbone.') or not k.startswith('seq_classifier')}
                    model.load_state_dict(backbone_dict, strict=False)
                    logging.info(f"✅ Loaded checkpoint: {model_info['checkpoint']}")
            except Exception as e:
                logging.warning(f"⚠️ Could not load checkpoint: {e}")
        
        # 创建测试数据集
        test_dataset = SequenceSleepDataset(
            [os.path.join(data_dir, f) for f in test_files],
            seq_len=model_info['config']['seq_len'],
            max_samples_per_file=None  # 使用全部数据！
        )
        
        test_loader = torch.utils.data.DataLoader(
            test_dataset,
            batch_size=32,
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        # 评估
        accuracy, f1, kappa, class_f1, cm = evaluate_seq2seq(model, test_loader, device)
        
        # 记录结果
        results = {
            'model_name': model_info['name'],
            'accuracy': float(accuracy),
            'macro_f1': float(f1),
            'kappa': float(kappa),
            'class_f1': {
                'Wake': float(class_f1[0]),
                'N1': float(class_f1[1]),
                'N2': float(class_f1[2]),
                'N3': float(class_f1[3]),
                'REM': float(class_f1[4])
            },
            'confusion_matrix': cm.tolist(),
            'checkpoint_path': model_info['checkpoint'],
            'training_script': model_info['training_script']
        }
        
        # 打印结果
        logging.info(f"\n📊 Results for {model_info['name']}:")
        logging.info(f"  Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
        logging.info(f"  Macro F1: {f1:.4f}")
        logging.info(f"  Kappa: {kappa:.4f}")
        logging.info(f"  Class F1: Wake={class_f1[0]:.3f}, N1={class_f1[1]:.3f}, "
                    f"N2={class_f1[2]:.3f}, N3={class_f1[3]:.3f}, REM={class_f1[4]:.3f}")
        
        # 检查REM性能
        if class_f1[4] < 0.1:
            logging.warning(f"  ⚠️ REM F1 extremely low: {class_f1[4]:.3f}")
            logging.info("  This confirms data truncation issue affecting REM detection!")
        
        # 保存模型位置
        model_locations['models'].append({
            'name': model_info['name'],
            'checkpoint': model_info['checkpoint'],
            'training_script': model_info['training_script'],
            'test_results': results
        })
    
    # 保存所有模型位置信息
    locations_file = os.path.join(log_dir, 'model_locations.json')
    with open(locations_file, 'w') as f:
        json.dump(model_locations, f, indent=2)
    
    logging.info(f"\n💾 Model locations saved to: {locations_file}")
    
    return model_locations


if __name__ == '__main__':
    # 测试三个关键模型
    locations = test_three_models()
    
    print("\n" + "="*80)
    print("📍 Model Locations Summary:")
    print("="*80)
    for model in locations['models']:
        print(f"\n{model['name']}:")
        print(f"  Script: {model['training_script']}")
        print(f"  Checkpoint: {model['checkpoint']}")
        if 'test_results' in model:
            print(f"  Accuracy: {model['test_results']['accuracy']:.4f}")
            print(f"  REM F1: {model['test_results']['class_f1']['REM']:.3f}")