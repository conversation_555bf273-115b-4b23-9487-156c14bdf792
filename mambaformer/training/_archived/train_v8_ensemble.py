#!/usr/bin/env python3
"""
V8 Ensemble - Multi-Head Voting Architecture
单模型内的集成策略，多个分类头投票决策
目标: 90% accuracy through ensemble-like behavior
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v8_ensemble_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 V8 Ensemble - Multi-Head Voting for 90% Target")
    logging.info("="*80)
    
    return log_dir

class AttentionPooling(nn.Module):
    """注意力池化层"""
    def __init__(self, d_model):
        super().__init__()
        self.attention = nn.Linear(d_model, 1)
        
    def forward(self, x):
        """
        x: (batch, seq_len, d_model)
        return: (batch, d_model)
        """
        weights = F.softmax(self.attention(x), dim=1)  # (batch, seq_len, 1)
        pooled = (x * weights).sum(dim=1)  # (batch, d_model)
        return pooled

class EnsembleV8Model(nn.Module):
    """V8 Ensemble模型 - 多头投票架构"""
    def __init__(self, config):
        super().__init__()
        
        # 共享主干网络
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len']
        )
        
        # 注意力池化
        self.attention_pool = AttentionPooling(config['d_model'])
        
        # 多个独立的分类头（不同架构）
        self.n_heads = 5
        
        # Head 1: 深度MLP
        self.head1 = nn.Sequential(
            nn.Linear(config['d_model'], config['d_model']),
            nn.LayerNorm(config['d_model']),
            nn.ReLU(),
            nn.Dropout(config['dropout']),
            nn.Linear(config['d_model'], config['d_model'] // 2),
            nn.ReLU(),
            nn.Dropout(config['dropout'] * 0.5),
            nn.Linear(config['d_model'] // 2, 5)
        )
        
        # Head 2: 宽度MLP
        self.head2 = nn.Sequential(
            nn.Linear(config['d_model'], config['d_model'] * 2),
            nn.BatchNorm1d(config['d_model'] * 2),
            nn.ReLU(),
            nn.Dropout(config['dropout']),
            nn.Linear(config['d_model'] * 2, 5)
        )
        
        # Head 3: 残差连接
        self.head3_fc1 = nn.Linear(config['d_model'], config['d_model'])
        self.head3_fc2 = nn.Linear(config['d_model'], 5)
        self.head3_norm = nn.LayerNorm(config['d_model'])
        self.head3_dropout = nn.Dropout(config['dropout'])
        
        # Head 4: 注意力增强
        self.head4_attention = nn.MultiheadAttention(config['d_model'], num_heads=4, dropout=config['dropout'])
        self.head4_classifier = nn.Sequential(
            nn.LayerNorm(config['d_model']),
            nn.Linear(config['d_model'], 5)
        )
        
        # Head 5: 专家网络（针对困难类别）
        self.head5 = nn.Sequential(
            nn.Linear(config['d_model'], 128),
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Dropout(config['dropout']),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 5)
        )
        
        # 学习权重（用于加权投票）
        self.head_weights = nn.Parameter(torch.ones(self.n_heads) / self.n_heads)
        
        # 特征融合层（可选）
        self.fusion_layer = nn.Sequential(
            nn.Linear(config['d_model'] + 5 * self.n_heads, config['d_model']),
            nn.ReLU(),
            nn.Dropout(config['dropout'] * 0.5),
            nn.Linear(config['d_model'], 5)
        )
        
        self.d_model = config['d_model']
        self.use_fusion = config.get('use_fusion', False)
        
    def forward(self, x, return_all_heads=False):
        """前向传播"""
        # 提取特征
        output, aux_features = self.backbone(x)
        
        # 获取批次维度
        if output.dim() == 3:
            batch_size, seq_len, d_model = output.shape
            # 直接使用中间时间步，避免池化维度问题
            features = output[:, output.shape[1]//2, :]  # (batch, d_model)
        else:
            features = output
            batch_size = features.size(0)
        
        # 各个头的预测
        heads_outputs = []
        
        # Head 1: 深度MLP
        out1 = self.head1(features)
        heads_outputs.append(out1)
        
        # Head 2: 宽度MLP
        out2 = self.head2(features)
        heads_outputs.append(out2)
        
        # Head 3: 残差连接
        residual = features
        out3 = F.relu(self.head3_fc1(features))
        out3 = self.head3_dropout(out3)
        out3 = out3 + residual
        out3 = self.head3_norm(out3)
        out3 = self.head3_fc2(out3)
        heads_outputs.append(out3)
        
        # Head 4: 注意力增强
        features_unsqueeze = features.unsqueeze(0)  # (1, batch, d_model)
        attn_out, _ = self.head4_attention(features_unsqueeze, features_unsqueeze, features_unsqueeze)
        attn_out = attn_out.squeeze(0)  # (batch, d_model)
        out4 = self.head4_classifier(attn_out)
        heads_outputs.append(out4)
        
        # Head 5: 专家网络
        out5 = self.head5(features)
        heads_outputs.append(out5)
        
        # 组合策略
        if self.use_fusion:
            # 特征级融合
            all_logits = torch.cat(heads_outputs, dim=-1)  # (batch, 5*n_heads)
            combined_features = torch.cat([features, all_logits], dim=-1)
            final_output = self.fusion_layer(combined_features)
        else:
            # 加权平均投票
            weights = F.softmax(self.head_weights, dim=0)
            stacked_outputs = torch.stack(heads_outputs, dim=0)  # (n_heads, batch, 5)
            
            # 两种策略：
            # 1. Soft voting (概率平均)
            probs = F.softmax(stacked_outputs, dim=-1)  # (n_heads, batch, 5)
            weighted_probs = (probs * weights.view(-1, 1, 1)).sum(dim=0)  # (batch, 5)
            final_output = torch.log(weighted_probs + 1e-8)  # 返回log概率
            
            # 2. Hard voting (可选)
            # votes = stacked_outputs.argmax(dim=-1)  # (n_heads, batch)
            # final_output = ...  # 实现投票逻辑
        
        if return_all_heads:
            return final_output, heads_outputs
        
        return final_output

class EnsembleLoss(nn.Module):
    """集成损失函数 - 训练所有头"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # Focal Loss参数
        self.gamma = 2.0
        
        # 类别权重（强调N1和REM）
        self.class_weights = torch.tensor([3.5, 3.0, 1.0, 1.0, 3.5]).to(device)
        
    def focal_loss(self, inputs, targets, weights=None, gamma=2.0):
        """Focal Loss"""
        ce_loss = F.cross_entropy(inputs, targets, weight=weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** gamma * ce_loss
        return focal_loss.mean()
        
    def forward(self, final_output, all_heads, targets):
        """计算集成损失"""
        # 处理目标
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        # 主输出损失
        main_loss = self.focal_loss(final_output, targets, self.class_weights, self.gamma)
        
        # 各个头的损失（辅助）
        aux_losses = []
        for head_output in all_heads:
            aux_loss = self.focal_loss(head_output, targets, self.class_weights, self.gamma)
            aux_losses.append(aux_loss)
        
        # 组合损失
        aux_loss_weight = 0.1  # 辅助损失权重
        total_aux_loss = sum(aux_losses) / len(aux_losses)
        
        total_loss = main_loss + aux_loss_weight * total_aux_loss
        
        return total_loss

def mixup_data(x, y, alpha=0.2):
    """Mixup数据增强"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).to(x.device)

    mixed_x = lam * x + (1 - lam) * x[index]
    y_a, y_b = y, y[index]
    
    return mixed_x, y_a, y_b, lam

def train_epoch(model, train_loader, criterion, optimizer, device, use_mixup=True):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc='Training')
    for batch_idx, (data, target) in enumerate(pbar):
        data, target = data.to(device), target.to(device)
        
        # Mixup增强
        if use_mixup and random.random() < 0.3:
            data, target_a, target_b, lam = mixup_data(data, target)
            
            optimizer.zero_grad()
            output, all_heads = model(data, return_all_heads=True)
            
            # Mixup损失
            loss_a = criterion(output, all_heads, target_a)
            loss_b = criterion(output, all_heads, target_b)
            loss = lam * loss_a + (1 - lam) * loss_b
        else:
            optimizer.zero_grad()
            output, all_heads = model(data, return_all_heads=True)
            loss = criterion(output, all_heads, target)
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        preds = output.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy() if not use_mixup else target_a.cpu().numpy())
        
        pbar.set_postfix({'loss': loss.item()})
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate(model, data_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            output = model(data, return_all_heads=False)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            preds = output.argmax(dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    # V8 Ensemble配置
    config = {
        'd_model': 256,  # 更大以支持多头
        'n_heads': 16,   
        'n_layers': 6,   
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 24,  # 较小batch因为模型更大
        'learning_rate': 1.5e-4,
        'weight_decay': 4e-5,
        'num_epochs': 60,
        'patience': 15,
        'use_fusion': False  # 使用投票而非融合
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = EnsembleV8Model(config).to(device)
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = EnsembleLoss(device)
    optimizer = optim.AdamW(model.parameters(), 
                           lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'])
    
    # 学习率调度
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'] * 10,
        epochs=config['num_epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.3,
        anneal_strategy='cos',
        div_factor=25,
        final_div_factor=10000
    )
    
    # 训练
    best_test_acc = 0
    best_test_metrics = {}
    patience_counter = 0
    
    logging.info("Starting V8 Ensemble training...")
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device,
            use_mixup=(epoch > 5)  # 前几个epoch不用mixup
        )
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(model, val_loader, device)
        
        # 测试
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(model, test_loader, device)
        
        # 学习率调度
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Test: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_test_metrics = {
                'accuracy': test_acc,
                'f1': test_f1,
                'kappa': test_kappa,
                'class_f1': test_class_f1.tolist(),
                'confusion_matrix': test_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_metrics': best_test_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Test Acc: {test_acc:.4f})")
            
            if test_acc >= 0.87:
                logging.info("  🎯 Reached 87% target!")
                os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '🎯 V8 Ensemble reached 87%: {test_acc:.4f}'")
            
            if test_acc >= 0.90:
                logging.info("  🎉 ACHIEVED 90% TARGET!")
                os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '✅ V8 Ensemble ACHIEVED 90%: {test_acc:.4f}'")
                break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS (V8 Ensemble)")
    logging.info("="*80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test F1: {best_test_metrics['f1']:.4f}")
    logging.info(f"Best Test Kappa: {best_test_metrics['kappa']:.4f}")
    
    if best_test_acc >= 0.90:
        logging.info("✅ SUCCESS: Achieved 90% target!")
    else:
        gap = 0.90 - best_test_acc
        logging.info(f"Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
    
    # 保存结果
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(best_test_metrics, f, indent=2)

if __name__ == "__main__":
    main()