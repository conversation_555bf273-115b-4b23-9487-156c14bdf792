Warning: No data loaded for train, creating random data for testing
Loaded 1000 sequences for train
Warning: No data loaded for val, creating random data for testing
Loaded 200 sequences for val
Warning: No data loaded for test, creating random data for testing
Loaded 200 sequences for test

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:06,  1.87it/s]
Evaluating:  62%|██████▏   | 8/13 [00:00<00:00, 15.75it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 16.45it/s]
WARNING:__main__:⚠️ 初始准确率偏离Stage 1基线！

Training:   0%|          | 0/63 [00:00<?, ?it/s]
Training:   0%|          | 0/63 [00:00<?, ?it/s, loss=5.7547, scale=0.0000, lr=0.000015]
Training:   2%|▏         | 1/63 [00:00<00:30,  2.04it/s, loss=5.7547, scale=0.0000, lr=0.000015]
Training:  10%|▉         | 6/63 [00:00<00:04, 12.14it/s, loss=5.7547, scale=0.0000, lr=0.000015]
Training:  16%|█▌        | 10/63 [00:00<00:02, 18.14it/s, loss=5.7547, scale=0.0000, lr=0.000015]
Training:  16%|█▌        | 10/63 [00:00<00:02, 18.14it/s, loss=4.9045, scale=0.0000, lr=0.000015]
Training:  22%|██▏       | 14/63 [00:00<00:02, 22.23it/s, loss=4.9045, scale=0.0000, lr=0.000015]
Training:  29%|██▊       | 18/63 [00:00<00:01, 26.04it/s, loss=4.9045, scale=0.0000, lr=0.000015]
Training:  29%|██▊       | 18/63 [00:01<00:01, 26.04it/s, loss=4.7799, scale=0.0000, lr=0.000015]
Training:  35%|███▍      | 22/63 [00:01<00:01, 28.77it/s, loss=4.7799, scale=0.0000, lr=0.000015]
Training:  41%|████▏     | 26/63 [00:01<00:01, 30.64it/s, loss=4.7799, scale=0.0000, lr=0.000015]
Training:  48%|████▊     | 30/63 [00:01<00:01, 31.94it/s, loss=4.7799, scale=0.0000, lr=0.000015]
Training:  48%|████▊     | 30/63 [00:01<00:01, 31.94it/s, loss=4.7929, scale=0.0000, lr=0.000015]
Training:  54%|█████▍    | 34/63 [00:01<00:00, 33.16it/s, loss=4.7929, scale=0.0000, lr=0.000015]
Training:  60%|██████    | 38/63 [00:01<00:00, 33.84it/s, loss=4.7929, scale=0.0000, lr=0.000015]
Training:  60%|██████    | 38/63 [00:01<00:00, 33.84it/s, loss=4.8953, scale=0.0000, lr=0.000015]
Training:  67%|██████▋   | 42/63 [00:01<00:00, 33.63it/s, loss=4.8953, scale=0.0000, lr=0.000015]
Training:  73%|███████▎  | 46/63 [00:01<00:00, 34.42it/s, loss=4.8953, scale=0.0000, lr=0.000015]
Training:  79%|███████▉  | 50/63 [00:01<00:00, 34.66it/s, loss=4.8953, scale=0.0000, lr=0.000015]
Training:  79%|███████▉  | 50/63 [00:01<00:00, 34.66it/s, loss=4.9399, scale=0.0000, lr=0.000015]
Training:  86%|████████▌ | 54/63 [00:01<00:00, 34.74it/s, loss=4.9399, scale=0.0000, lr=0.000015]
Training:  92%|█████████▏| 58/63 [00:02<00:00, 35.61it/s, loss=4.9399, scale=0.0000, lr=0.000015]
Training:  92%|█████████▏| 58/63 [00:02<00:00, 35.61it/s, loss=4.7938, scale=0.0000, lr=0.000015]
Training:  98%|█████████▊| 62/63 [00:02<00:00, 36.40it/s, loss=4.7938, scale=0.0000, lr=0.000015]
Training: 100%|██████████| 63/63 [00:02<00:00, 27.61it/s, loss=4.7938, scale=0.0000, lr=0.000015]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:03,  3.82it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 22.37it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 23.52it/s]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.07it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 23.51it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 35.41it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 24.22it/s]

Training:   0%|          | 0/63 [00:00<?, ?it/s]
Training:   0%|          | 0/63 [00:00<?, ?it/s, loss=5.1315, scale=0.0000, lr=0.000015]
Training:   2%|▏         | 1/63 [00:00<00:18,  3.42it/s, loss=5.1315, scale=0.0000, lr=0.000015]
Training:   6%|▋         | 4/63 [00:00<00:04, 12.11it/s, loss=5.1315, scale=0.0000, lr=0.000015]
Training:  13%|█▎        | 8/63 [00:00<00:02, 19.82it/s, loss=5.1315, scale=0.0000, lr=0.000015]
Training:  13%|█▎        | 8/63 [00:00<00:02, 19.82it/s, loss=4.6753, scale=0.0000, lr=0.000015]
Training:  19%|█▉        | 12/63 [00:00<00:02, 24.64it/s, loss=4.6753, scale=0.0000, lr=0.000015]
Training:  25%|██▌       | 16/63 [00:00<00:01, 27.98it/s, loss=4.6753, scale=0.0000, lr=0.000015]
Training:  32%|███▏      | 20/63 [00:00<00:01, 29.91it/s, loss=4.6753, scale=0.0000, lr=0.000015]
Training:  32%|███▏      | 20/63 [00:00<00:01, 29.91it/s, loss=5.3133, scale=0.0000, lr=0.000015]
Training:  38%|███▊      | 24/63 [00:00<00:01, 31.79it/s, loss=5.3133, scale=0.0000, lr=0.000015]
Training:  44%|████▍     | 28/63 [00:01<00:01, 32.57it/s, loss=5.3133, scale=0.0000, lr=0.000015]
Training:  44%|████▍     | 28/63 [00:01<00:01, 32.57it/s, loss=4.6702, scale=0.0000, lr=0.000015]
Training:  51%|█████     | 32/63 [00:01<00:00, 32.41it/s, loss=4.6702, scale=0.0000, lr=0.000015]
Training:  57%|█████▋    | 36/63 [00:01<00:00, 32.73it/s, loss=4.6702, scale=0.0000, lr=0.000015]
Training:  63%|██████▎   | 40/63 [00:01<00:00, 32.29it/s, loss=4.6702, scale=0.0000, lr=0.000015]
Training:  63%|██████▎   | 40/63 [00:01<00:00, 32.29it/s, loss=4.9828, scale=0.0000, lr=0.000015]
Training:  70%|██████▉   | 44/63 [00:01<00:00, 32.37it/s, loss=4.9828, scale=0.0000, lr=0.000015]
Training:  76%|███████▌  | 48/63 [00:01<00:00, 32.46it/s, loss=4.9828, scale=0.0000, lr=0.000015]
Training:  76%|███████▌  | 48/63 [00:01<00:00, 32.46it/s, loss=4.7740, scale=0.0000, lr=0.000015]
Training:  83%|████████▎ | 52/63 [00:01<00:00, 32.95it/s, loss=4.7740, scale=0.0000, lr=0.000015]
Training:  89%|████████▉ | 56/63 [00:01<00:00, 34.18it/s, loss=4.7740, scale=0.0000, lr=0.000015]
Training:  89%|████████▉ | 56/63 [00:02<00:00, 34.18it/s, loss=4.9501, scale=0.0000, lr=0.000015]
Training:  97%|█████████▋| 61/63 [00:02<00:00, 36.04it/s, loss=4.9501, scale=0.0000, lr=0.000015]
Training: 100%|██████████| 63/63 [00:02<00:00, 28.96it/s, loss=4.9501, scale=0.0000, lr=0.000015]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.47it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 25.37it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 26.24it/s]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.02it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 23.97it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 25.20it/s]

Training:   0%|          | 0/63 [00:00<?, ?it/s]
Training:   0%|          | 0/63 [00:00<?, ?it/s, loss=5.1331, scale=0.0000, lr=0.000015]
Training:   2%|▏         | 1/63 [00:00<00:16,  3.83it/s, loss=5.1331, scale=0.0000, lr=0.000015]
Training:   8%|▊         | 5/63 [00:00<00:03, 15.26it/s, loss=5.1331, scale=0.0000, lr=0.000015]
Training:  14%|█▍        | 9/63 [00:00<00:02, 21.47it/s, loss=5.1331, scale=0.0000, lr=0.000015]
Training:  14%|█▍        | 9/63 [00:00<00:02, 21.47it/s, loss=4.6435, scale=0.0000, lr=0.000015]
Training:  21%|██        | 13/63 [00:00<00:01, 25.40it/s, loss=4.6435, scale=0.0000, lr=0.000015]
Training:  27%|██▋       | 17/63 [00:00<00:01, 27.79it/s, loss=4.6435, scale=0.0000, lr=0.000015]
Training:  27%|██▋       | 17/63 [00:00<00:01, 27.79it/s, loss=5.6648, scale=0.0000, lr=0.000015]
Training:  33%|███▎      | 21/63 [00:00<00:01, 29.65it/s, loss=5.6648, scale=0.0000, lr=0.000015]
Training:  40%|███▉      | 25/63 [00:00<00:01, 31.17it/s, loss=5.6648, scale=0.0000, lr=0.000015]
Training:  46%|████▌     | 29/63 [00:01<00:01, 32.35it/s, loss=5.6648, scale=0.0000, lr=0.000015]
Training:  46%|████▌     | 29/63 [00:01<00:01, 32.35it/s, loss=4.9227, scale=0.0000, lr=0.000015]
Training:  52%|█████▏    | 33/63 [00:01<00:00, 32.70it/s, loss=4.9227, scale=0.0000, lr=0.000015]
Training:  59%|█████▊    | 37/63 [00:01<00:00, 32.94it/s, loss=4.9227, scale=0.0000, lr=0.000015]
Training:  59%|█████▊    | 37/63 [00:01<00:00, 32.94it/s, loss=4.2935, scale=0.0000, lr=0.000015]
Training:  65%|██████▌   | 41/63 [00:01<00:00, 33.36it/s, loss=4.2935, scale=0.0000, lr=0.000015]
Training:  71%|███████▏  | 45/63 [00:01<00:00, 33.54it/s, loss=4.2935, scale=0.0000, lr=0.000015]
Training:  78%|███████▊  | 49/63 [00:01<00:00, 33.62it/s, loss=4.2935, scale=0.0000, lr=0.000015]
Training:  78%|███████▊  | 49/63 [00:01<00:00, 33.62it/s, loss=4.5766, scale=0.0000, lr=0.000015]
Training:  84%|████████▍ | 53/63 [00:01<00:00, 33.75it/s, loss=4.5766, scale=0.0000, lr=0.000015]
Training:  90%|█████████ | 57/63 [00:01<00:00, 34.56it/s, loss=4.5766, scale=0.0000, lr=0.000015]
Training:  90%|█████████ | 57/63 [00:02<00:00, 34.56it/s, loss=5.0015, scale=0.0000, lr=0.000015]
Training:  98%|█████████▊| 62/63 [00:02<00:00, 36.81it/s, loss=5.0015, scale=0.0000, lr=0.000015]
Training: 100%|██████████| 63/63 [00:02<00:00, 29.37it/s, loss=5.0015, scale=0.0000, lr=0.000015]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.14it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 23.95it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 25.29it/s]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.37it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 25.17it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 26.21it/s]

Training:   0%|          | 0/63 [00:00<?, ?it/s]
Training:   0%|          | 0/63 [00:00<?, ?it/s, loss=4.5945, scale=0.0000, lr=0.000014]
Training:   2%|▏         | 1/63 [00:00<00:15,  4.06it/s, loss=4.5945, scale=0.0000, lr=0.000014]
Training:   8%|▊         | 5/63 [00:00<00:03, 16.29it/s, loss=4.5945, scale=0.0000, lr=0.000014]
Training:  14%|█▍        | 9/63 [00:00<00:02, 23.48it/s, loss=4.5945, scale=0.0000, lr=0.000014]
Training:  14%|█▍        | 9/63 [00:00<00:02, 23.48it/s, loss=4.8230, scale=0.0000, lr=0.000014]
Training:  21%|██        | 13/63 [00:00<00:01, 27.35it/s, loss=4.8230, scale=0.0000, lr=0.000014]
Training:  27%|██▋       | 17/63 [00:00<00:01, 29.29it/s, loss=4.8230, scale=0.0000, lr=0.000014]
Training:  27%|██▋       | 17/63 [00:00<00:01, 29.29it/s, loss=4.4077, scale=0.0000, lr=0.000014]
Training:  33%|███▎      | 21/63 [00:00<00:01, 30.56it/s, loss=4.4077, scale=0.0000, lr=0.000014]
Training:  40%|███▉      | 25/63 [00:00<00:01, 31.34it/s, loss=4.4077, scale=0.0000, lr=0.000014]
Training:  46%|████▌     | 29/63 [00:01<00:01, 31.97it/s, loss=4.4077, scale=0.0000, lr=0.000014]
Training:  46%|████▌     | 29/63 [00:01<00:01, 31.97it/s, loss=5.0322, scale=0.0000, lr=0.000014]
Training:  52%|█████▏    | 33/63 [00:01<00:00, 32.20it/s, loss=5.0322, scale=0.0000, lr=0.000014]
Training:  59%|█████▊    | 37/63 [00:01<00:00, 32.55it/s, loss=5.0322, scale=0.0000, lr=0.000014]
Training:  59%|█████▊    | 37/63 [00:01<00:00, 32.55it/s, loss=4.6344, scale=0.0000, lr=0.000014]
Training:  65%|██████▌   | 41/63 [00:01<00:00, 32.76it/s, loss=4.6344, scale=0.0000, lr=0.000014]
Training:  71%|███████▏  | 45/63 [00:01<00:00, 33.05it/s, loss=4.6344, scale=0.0000, lr=0.000014]
Training:  78%|███████▊  | 49/63 [00:01<00:00, 33.05it/s, loss=4.6344, scale=0.0000, lr=0.000014]
Training:  78%|███████▊  | 49/63 [00:01<00:00, 33.05it/s, loss=5.3634, scale=0.0000, lr=0.000014]
Training:  84%|████████▍ | 53/63 [00:01<00:00, 33.07it/s, loss=5.3634, scale=0.0000, lr=0.000014]
Training:  90%|█████████ | 57/63 [00:01<00:00, 33.36it/s, loss=5.3634, scale=0.0000, lr=0.000014]
Training:  90%|█████████ | 57/63 [00:02<00:00, 33.36it/s, loss=4.3709, scale=0.0000, lr=0.000014]
Training:  97%|█████████▋| 61/63 [00:02<00:00, 34.33it/s, loss=4.3709, scale=0.0000, lr=0.000014]
Training: 100%|██████████| 63/63 [00:02<00:00, 29.36it/s, loss=4.3709, scale=0.0000, lr=0.000014]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.02it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 23.65it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 23.79it/s]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.64it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 26.59it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 26.27it/s]

Training:   0%|          | 0/63 [00:00<?, ?it/s]
Training:   0%|          | 0/63 [00:00<?, ?it/s, loss=4.5902, scale=0.0000, lr=0.000014]
Training:   2%|▏         | 1/63 [00:00<00:14,  4.17it/s, loss=4.5902, scale=0.0000, lr=0.000014]
Training:   8%|▊         | 5/63 [00:00<00:03, 15.75it/s, loss=4.5902, scale=0.0000, lr=0.000014]
Training:  14%|█▍        | 9/63 [00:00<00:02, 22.14it/s, loss=4.5902, scale=0.0000, lr=0.000014]
Training:  14%|█▍        | 9/63 [00:00<00:02, 22.14it/s, loss=4.6109, scale=0.0000, lr=0.000014]
Training:  21%|██        | 13/63 [00:00<00:01, 25.95it/s, loss=4.6109, scale=0.0000, lr=0.000014]
Training:  27%|██▋       | 17/63 [00:00<00:01, 28.55it/s, loss=4.6109, scale=0.0000, lr=0.000014]
Training:  27%|██▋       | 17/63 [00:00<00:01, 28.55it/s, loss=4.7390, scale=0.0000, lr=0.000014]
Training:  33%|███▎      | 21/63 [00:00<00:01, 30.00it/s, loss=4.7390, scale=0.0000, lr=0.000014]
Training:  40%|███▉      | 25/63 [00:00<00:01, 31.32it/s, loss=4.7390, scale=0.0000, lr=0.000014]
Training:  46%|████▌     | 29/63 [00:01<00:01, 32.69it/s, loss=4.7390, scale=0.0000, lr=0.000014]
Training:  46%|████▌     | 29/63 [00:01<00:01, 32.69it/s, loss=4.9889, scale=0.0000, lr=0.000014]
Training:  52%|█████▏    | 33/63 [00:01<00:00, 33.89it/s, loss=4.9889, scale=0.0000, lr=0.000014]
Training:  59%|█████▊    | 37/63 [00:01<00:00, 34.62it/s, loss=4.9889, scale=0.0000, lr=0.000014]
Training:  59%|█████▊    | 37/63 [00:01<00:00, 34.62it/s, loss=4.9876, scale=0.0000, lr=0.000014]
Training:  65%|██████▌   | 41/63 [00:01<00:00, 35.25it/s, loss=4.9876, scale=0.0000, lr=0.000014]
Training:  71%|███████▏  | 45/63 [00:01<00:00, 35.45it/s, loss=4.9876, scale=0.0000, lr=0.000014]
Training:  78%|███████▊  | 49/63 [00:01<00:00, 35.77it/s, loss=4.9876, scale=0.0000, lr=0.000014]
Training:  78%|███████▊  | 49/63 [00:01<00:00, 35.77it/s, loss=4.8120, scale=0.0000, lr=0.000014]
Training:  84%|████████▍ | 53/63 [00:01<00:00, 36.06it/s, loss=4.8120, scale=0.0000, lr=0.000014]
Training:  90%|█████████ | 57/63 [00:01<00:00, 36.74it/s, loss=4.8120, scale=0.0000, lr=0.000014]
Training:  90%|█████████ | 57/63 [00:01<00:00, 36.74it/s, loss=4.7322, scale=0.0000, lr=0.000014]
Training:  98%|█████████▊| 62/63 [00:01<00:00, 37.92it/s, loss=4.7322, scale=0.0000, lr=0.000014]
Training: 100%|██████████| 63/63 [00:02<00:00, 30.43it/s, loss=4.7322, scale=0.0000, lr=0.000014]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.84it/s]
Evaluating:  62%|██████▏   | 8/13 [00:00<00:00, 29.89it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 27.82it/s]

Evaluating:   0%|          | 0/13 [00:00<?, ?it/s]
Evaluating:   8%|▊         | 1/13 [00:00<00:02,  4.03it/s]
Evaluating:  54%|█████▍    | 7/13 [00:00<00:00, 23.71it/s]
Evaluating: 100%|██████████| 13/13 [00:00<00:00, 25.18it/s]
