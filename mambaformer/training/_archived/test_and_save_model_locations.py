#!/usr/bin/env python3
"""
测试并保存三个关键模型的位置
V30 Ultra, V14 FIXED, V8 Proper Eval

主要目标：
1. 确认V30 Ultra的REM失败是否因为数据截断
2. 实现真正的sequence-to-sequence预测
3. 保存模型位置以备后续使用
"""

import os
import sys
import json
import torch
import numpy as np
from datetime import datetime
import logging
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset


def analyze_v30_ultra():
    """分析V30 Ultra的问题"""
    print("\n" + "="*80)
    print("🔍 分析V30 Ultra (Final Test 90)")
    print("="*80)
    
    # 读取训练日志
    log_file = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/final_test_90_20250811_201958/training.log'
    results_file = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/final_test_90_20250811_201958/results.json'
    
    print(f"\n📄 检查训练配置:")
    
    # 检查训练脚本
    training_script = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/final_test_90.py'
    
    # 分析数据加载
    with open(training_script, 'r') as f:
        content = f.read()
        if 'max_samples_per_file' in content:
            print("  ⚠️ 找到max_samples_per_file设置")
            # 提取相关行
            for line in content.split('\n'):
                if 'max_samples_per_file' in line:
                    print(f"    {line.strip()}")
        else:
            print("  ✅ 未找到max_samples_per_file (使用全部数据)")
            
        # 检查SequenceSleepDataset调用
        if 'SequenceSleepDataset(' in content:
            print("\n  数据集创建:")
            in_dataset_creation = False
            for line in content.split('\n'):
                if 'SequenceSleepDataset(' in line:
                    in_dataset_creation = True
                if in_dataset_creation:
                    print(f"    {line.strip()}")
                    if ')' in line and in_dataset_creation:
                        in_dataset_creation = False
                        break
    
    # 读取结果
    if os.path.exists(results_file):
        with open(results_file, 'r') as f:
            results = json.load(f)
            print(f"\n📊 训练结果:")
            print(f"  Test Accuracy: {results.get('best_test_accuracy', 'N/A')}")
            print(f"  Test F1: {results.get('final_test_f1', 'N/A')}")
            print(f"  Test Kappa: {results.get('final_test_kappa', 'N/A')}")
            
            if 'final_test_class_f1' in results:
                class_f1 = results['final_test_class_f1']
                print(f"  Class F1: Wake={class_f1[0]:.3f}, N1={class_f1[1]:.3f}, "
                      f"N2={class_f1[2]:.3f}, N3={class_f1[3]:.3f}, REM={class_f1[4]:.3f}")
                
                if class_f1[4] < 0.1:
                    print(f"\n  ❌ REM F1={class_f1[4]:.3f} - 严重失败!")
                    print("  📝 原因分析:")
                    print("    1. 数据可能被截断，导致REM样本缺失")
                    print("    2. 过高的类权重(5.0)导致梯度不稳定")
                    print("    3. 模型过大(38M参数)容易过拟合")
    
    # 读取训练日志分析
    if os.path.exists(log_file):
        with open(log_file, 'r') as f:
            lines = f.readlines()
            
        print(f"\n📋 训练日志分析:")
        for line in lines:
            if 'REM' in line and 'F1' in line:
                print(f"  {line.strip()}")
                
        # 检查最终测试结果
        for line in lines[-20:]:
            if 'Test Class F1' in line or 'TEST' in line:
                print(f"  {line.strip()}")


def save_model_locations():
    """保存三个关键模型的位置"""
    
    model_locations = {
        "description": "Three key MAMBAFORMER models for sleep stage classification analysis",
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "models": {
            "V30_Ultra": {
                "name": "V30 Ultra (Final Test 90)",
                "training_script": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/final_test_90.py",
                "log_dir": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/final_test_90_20250811_201958/",
                "checkpoint": None,  # 没有保存.pth文件
                "config": {
                    "d_model": 512,
                    "n_heads": 32,
                    "n_layers": 12,
                    "dropout": 0.25,
                    "seq_len": 7,
                    "batch_size": 16,
                    "learning_rate": 3e-4,
                    "num_epochs": 10,
                    "label_smoothing": 0.1,
                    "class_weights": [1.0, 3.0, 1.0, 1.0, 5.0]
                },
                "results": {
                    "test_accuracy": 0.8837,
                    "test_f1": "N/A",
                    "test_kappa": "N/A",
                    "REM_F1": 0.000,
                    "issue": "Complete REM detection failure due to extreme class weight and possible data truncation"
                }
            },
            "V14_FIXED": {
                "name": "V14 FIXED (REM Focused)",
                "training_script": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_FIXED.py",
                "log_dir": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_detailed_evaluation_20250810_202805/",
                "checkpoint": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_optimized_20250812_204543/best_model.pth",
                "alternative_checkpoints": [
                    "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_robust_20250812_011519/best_model.pth",
                    "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/checkpoints/v14_fixed.pth"
                ],
                "config": {
                    "d_model": 256,
                    "n_heads": 16,
                    "n_layers": 6,
                    "dropout": 0.15,
                    "seq_len": 5,
                    "batch_size": 32,
                    "learning_rate": 2e-4,
                    "max_samples_per_file": None,  # 使用全部数据
                    "REMFocusedLoss": True,
                    "rem_weight": 3.0,
                    "wake_weight": 5.0
                },
                "results": {
                    "test_accuracy": 0.8635,
                    "macro_f1": 0.8078,
                    "kappa": 0.8140,
                    "REM_F1": 0.7929,
                    "strengths": "Balanced performance, good REM detection"
                }
            },
            "V8_Proper_Eval": {
                "name": "V8 Proper Evaluation",
                "training_script": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_proper_eval.py",
                "log_dir": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/sequential_v8_enhanced_20250809_222418/",
                "checkpoint": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v8_proper_eval_20250812_155847/best_model.pth",
                "alternative_checkpoints": [
                    "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v8_fixed_20250812_022342/best_model.pth",
                    "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v8_improved_20250812_013748/best_model.pth"
                ],
                "config": {
                    "d_model": 256,
                    "n_heads": 16,
                    "n_layers": 6,
                    "dropout": 0.2,
                    "seq_len": 5,
                    "batch_size": 32,
                    "learning_rate": 2e-4,
                    "max_samples_per_file": None,
                    "label_smoothing": 0.1,
                    "TTA": True,
                    "auxiliary_head": True
                },
                "results": {
                    "test_accuracy": 0.8374,
                    "macro_f1": 0.7880,
                    "kappa": 0.7815,
                    "REM_F1": 0.797,
                    "strengths": "Stable training, extensive augmentation, proper evaluation protocol"
                }
            }
        },
        "key_findings": {
            "data_truncation": "V30 Ultra可能因为数据截断导致REM失败，但脚本中未显式设置max_samples_per_file",
            "sequence_prediction": "所有模型都使用center output extraction，不是真正的seq-to-seq",
            "voting_strategy": "发现了ensemble voting脚本，但不是模型内部机制",
            "best_overall": "V14 FIXED - 最平衡的性能和REM检测",
            "recommendations": [
                "实现真正的sequence-to-sequence预测",
                "使用滑动窗口推理+概率平均",
                "确保所有模型使用完整数据(max_samples_per_file=None)",
                "避免极端的类权重设置"
            ]
        }
    }
    
    # 保存到文件
    save_path = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/model_locations.json'
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(model_locations, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 模型位置信息已保存到: {save_path}")
    
    # 打印摘要
    print("\n" + "="*80)
    print("📍 三个关键模型位置摘要")
    print("="*80)
    
    for model_key, model_info in model_locations['models'].items():
        print(f"\n{model_key}:")
        print(f"  名称: {model_info['name']}")
        print(f"  脚本: {model_info['training_script']}")
        print(f"  日志: {model_info['log_dir']}")
        if model_info['checkpoint']:
            print(f"  模型: {model_info['checkpoint']}")
        else:
            print(f"  模型: ⚠️ 未保存checkpoint")
        print(f"  准确率: {model_info['results']['test_accuracy']:.2%}")
        print(f"  REM F1: {model_info['results']['REM_F1']:.3f}")
    
    return model_locations


def create_seq2seq_recommendation():
    """创建实现真正seq2seq的建议"""
    
    print("\n" + "="*80)
    print("🎯 Sequence-to-Sequence实现建议")
    print("="*80)
    
    print("""
1. 模型架构改进:
   - 修改forward()输出完整序列预测 (batch, seq_len, n_classes)
   - 不再只提取中心点: 删除 output[:, output.shape[1]//2, :]
   - 每个时期都有独立预测

2. 训练改进:
   - 损失计算使用所有时期，不只是中心点
   - 可以使用序列级别的时序一致性损失
   - 梯度从所有预测回传

3. 推理策略 (重点):
   - 滑动窗口: 序列长度5，步长1
   - 每个时期收集多个预测 (最多5个)
   - 平均所有预测的softmax概率
   - 选择平均概率最高的类别

4. 实现示例:
   ```python
   # 推理时
   all_predictions = []
   for start in range(len(data) - seq_len + 1):
       seq = data[start:start+seq_len]
       pred = model(seq)  # (seq_len, n_classes)
       all_predictions.append(pred)
   
   # 平均概率
   avg_prob = torch.stack(all_predictions).mean(dim=0)
   final_pred = avg_prob.argmax(dim=-1)
   ```

5. 预期改进:
   - 准确率提升2-3%
   - 边界时期预测更准确
   - REM检测更稳定
   - 减少噪声影响
    """)


if __name__ == '__main__':
    # 1. 分析V30 Ultra的问题
    analyze_v30_ultra()
    
    # 2. 保存模型位置
    model_locations = save_model_locations()
    
    # 3. 提供seq2seq改进建议
    create_seq2seq_recommendation()
    
    print("\n" + "="*80)
    print("✅ 分析完成！")
    print("="*80)
    print("\n关键结论:")
    print("1. V30 Ultra的REM失败主要因为过高的类权重(5.0)，不一定是数据截断")
    print("2. 所有模型都使用center extraction，没有真正的seq2seq")
    print("3. V14 FIXED是最平衡的模型，值得作为基础继续改进")
    print("4. 实现真正的seq2seq + 滑动窗口推理可能带来显著提升")