#!/usr/bin/env python3
"""
自动监控和重启训练脚本
"""
import os
import time
import subprocess
import glob
from datetime import datetime

def check_process(stage_name):
    """检查进程是否在运行"""
    try:
        result = subprocess.run(
            ['pgrep', '-f', f'stage{stage_name}'],
            capture_output=True,
            text=True
        )
        return len(result.stdout.strip()) > 0
    except:
        return False

def get_latest_log(stage_num):
    """获取最新的日志文件"""
    log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/"
    pattern = f"{log_dir}stage{stage_num}_*/training.log"
    logs = glob.glob(pattern)
    if logs:
        return sorted(logs)[-1]
    return None

def check_training_status(stage_num):
    """检查训练状态"""
    log_file = get_latest_log(stage_num)
    if not log_file:
        return "NOT_STARTED"
    
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        # 检查最后几行
        for line in reversed(lines[-100:]):
            if "达到目标" in line or "目标！" in line:
                return "COMPLETED"
            if "ERROR" in line or "RuntimeError" in line:
                return "ERROR"
            if "Early stopping" in line:
                return "STOPPED"
            if "Epoch" in line:
                return "TRAINING"
        
        return "UNKNOWN"
    except:
        return "ERROR"

def restart_stage(stage_num):
    """重启指定阶段的训练"""
    script_map = {
        4: "stage4_mamba_modeling.py",
        5: "stage5_complete_fusion.py"
    }
    
    if stage_num not in script_map:
        return False
    
    script = script_map[stage_num]
    cmd = f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && python mambaformer/training/{script} > mambaformer/logs/stage{stage_num}_output_$(date +%Y%m%d_%H%M%S).log 2>&1 &"
    
    try:
        subprocess.run(cmd, shell=True)
        return True
    except:
        return False

def monitor_and_restart():
    """主监控循环"""
    print("="*60)
    print("🤖 自动监控和重启系统")
    print("="*60)
    
    completed_stages = {1: True, 2: True, 3: True}  # 已完成的阶段
    
    while True:
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"\n[{current_time}] 检查训练状态...")
        
        # 只监控Stage 4和5
        for stage in [4, 5]:
            if stage in completed_stages and completed_stages[stage]:
                continue
            
            status = check_training_status(stage)
            process_running = check_process(stage)
            
            print(f"Stage {stage}: 状态={status}, 进程={'运行中' if process_running else '未运行'}")
            
            if status == "COMPLETED":
                print(f"  ✅ Stage {stage} 已完成!")
                completed_stages[stage] = True
            elif status == "ERROR" or (status != "TRAINING" and not process_running):
                print(f"  ⚠️ Stage {stage} 需要重启")
                if restart_stage(stage):
                    print(f"  🔄 Stage {stage} 已重新启动")
                else:
                    print(f"  ❌ Stage {stage} 重启失败")
            elif status == "TRAINING" and process_running:
                print(f"  🟢 Stage {stage} 正常运行中")
        
        # 检查是否所有阶段都完成
        if all(completed_stages.get(i, False) for i in [4, 5]):
            print("\n🎉 所有阶段训练完成!")
            break
        
        # 等待5分钟后再次检查
        print("\n等待5分钟后再次检查...")
        time.sleep(300)

if __name__ == "__main__":
    monitor_and_restart()