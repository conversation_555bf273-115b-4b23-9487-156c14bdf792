#!/usr/bin/env python3
"""
V8 Ultimate - 终极版本冲击90%
融合所有成功策略的最强模型
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import random
import math

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v8_ultimate_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V8 ULTIMATE - FINAL PUSH TO 90%")
    logging.info("="*80)
    
    return log_dir

class UltimateAttention(nn.Module):
    """终极注意力机制"""
    def __init__(self, d_model):
        super().__init__()
        self.multihead = nn.MultiheadAttention(d_model, num_heads=8, dropout=0.1)
        self.norm = nn.LayerNorm(d_model)
        
    def forward(self, x):
        # x: (batch, seq_len, d_model)
        x = x.transpose(0, 1)  # (seq_len, batch, d_model)
        attn_out, _ = self.multihead(x, x, x)
        x = x + attn_out
        x = self.norm(x)
        return x.transpose(0, 1)  # (batch, seq_len, d_model)

class V8UltimateModel(nn.Module):
    """V8终极模型 - 融合所有成功策略"""
    def __init__(self, config):
        super().__init__()
        
        # 主干网络 - 使用成功的V8架构
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len']
        )
        
        # 额外的注意力增强
        self.attention_enhancer = UltimateAttention(config['d_model'])
        
        # 多个专家头（集成思想）
        self.expert_heads = nn.ModuleList([
            # Expert 1: 标准分类
            nn.Sequential(
                nn.LayerNorm(config['d_model']),
                nn.Linear(config['d_model'], config['d_model'] // 2),
                nn.ReLU(),
                nn.Dropout(config['dropout']),
                nn.Linear(config['d_model'] // 2, 5)
            ),
            # Expert 2: 深度网络
            nn.Sequential(
                nn.Linear(config['d_model'], config['d_model']),
                nn.LayerNorm(config['d_model']),
                nn.ReLU(),
                nn.Dropout(config['dropout']),
                nn.Linear(config['d_model'], config['d_model'] // 2),
                nn.ReLU(),
                nn.Linear(config['d_model'] // 2, 5)
            ),
            # Expert 3: N1专家（针对最弱类别）
            nn.Sequential(
                nn.Linear(config['d_model'], 128),
                nn.ReLU(),
                nn.Dropout(config['dropout']),
                nn.Linear(128, 64),
                nn.ReLU(),
                nn.Linear(64, 5)
            )
        ])
        
        # 粗分类器（渐进式策略）
        self.coarse_classifier = nn.Sequential(
            nn.Linear(config['d_model'], 64),
            nn.ReLU(),
            nn.Linear(64, 3)  # Wake, NREM, REM
        )
        
        # 细分类器
        self.fine_classifier = nn.Sequential(
            nn.Linear(config['d_model'] + 3, 128),  # 特征+粗分类
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Dropout(config['dropout']),
            nn.Linear(128, 5)
        )
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(5 * len(self.expert_heads) + 5 + 3, 32),
            nn.ReLU(),
            nn.Dropout(config['dropout'] * 0.5),
            nn.Linear(32, 5)
        )
        
        # 学习的专家权重
        self.expert_weights = nn.Parameter(torch.ones(len(self.expert_heads)) / len(self.expert_heads))
        
        self.d_model = config['d_model']
        
    def forward(self, x):
        """前向传播"""
        # 主干网络提取特征
        backbone_output, aux_features = self.backbone(x)
        
        # 处理输出维度
        if backbone_output.dim() == 3:
            # 先通过注意力增强
            enhanced = self.attention_enhancer(backbone_output)
            # 取中间时间步
            features = enhanced[:, enhanced.shape[1]//2, :]
            raw_logits = backbone_output[:, backbone_output.shape[1]//2, :]
        else:
            features = backbone_output
            raw_logits = backbone_output
        
        # 多专家预测
        expert_outputs = []
        for expert in self.expert_heads:
            expert_outputs.append(expert(features))
        
        # 粗分类
        coarse_logits = self.coarse_classifier(features)
        coarse_probs = F.softmax(coarse_logits, dim=-1)
        
        # 细分类（结合粗分类信息）
        fine_input = torch.cat([features, coarse_probs], dim=-1)
        fine_logits = self.fine_classifier(fine_input)
        
        # 融合所有预测
        all_predictions = torch.cat(expert_outputs + [fine_logits, coarse_logits], dim=-1)
        final_output = self.final_fusion(all_predictions)
        
        # 加权平均专家输出作为辅助
        expert_weights = F.softmax(self.expert_weights, dim=0)
        weighted_expert = sum(w * out for w, out in zip(expert_weights, expert_outputs))
        
        # 残差连接
        final_output = final_output + 0.3 * weighted_expert
        
        return final_output, expert_outputs, coarse_logits, fine_logits

class UltimateLoss(nn.Module):
    """终极损失函数"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 动态类别权重（强调N1和REM）
        self.base_weights = torch.tensor([3.0, 5.0, 1.0, 1.0, 3.5]).to(device)
        self.gamma = 2.5  # Focal loss gamma
        
    def focal_loss(self, inputs, targets, weights, gamma=2.5):
        """增强的Focal Loss"""
        ce_loss = F.cross_entropy(inputs, targets, weight=weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = ((1 - pt) ** gamma) * ce_loss
        return focal_loss.mean()
    
    def forward(self, final_output, expert_outputs, coarse_logits, fine_logits, targets, epoch=0):
        """计算损失"""
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        # 动态调整权重（随训练进展增加N1权重）
        dynamic_weights = self.base_weights.clone()
        n1_boost = 1.0 + min(epoch * 0.05, 1.5)  # 最多2.5倍
        dynamic_weights[1] *= n1_boost
        
        # 主损失
        main_loss = self.focal_loss(final_output, targets, dynamic_weights, self.gamma)
        
        # 专家损失（辅助）
        expert_losses = []
        for expert_out in expert_outputs:
            expert_loss = self.focal_loss(expert_out, targets, dynamic_weights, self.gamma)
            expert_losses.append(expert_loss)
        avg_expert_loss = sum(expert_losses) / len(expert_losses)
        
        # 粗分类损失
        coarse_targets = targets.clone()
        coarse_targets[targets == 1] = 1  # N1 -> NREM
        coarse_targets[targets == 2] = 1  # N2 -> NREM
        coarse_targets[targets == 3] = 1  # N3 -> NREM
        coarse_targets[targets == 0] = 0  # W -> Wake
        coarse_targets[targets == 4] = 2  # REM -> REM
        coarse_loss = F.cross_entropy(coarse_logits, coarse_targets)
        
        # 细分类损失
        fine_loss = self.focal_loss(fine_logits, targets, dynamic_weights, self.gamma)
        
        # N1特殊惩罚（如果N1性能太差）
        n1_penalty = 0.0
        n1_mask = (targets == 1)
        if n1_mask.sum() > 0:
            n1_predictions = final_output[n1_mask].argmax(dim=-1)
            n1_accuracy = (n1_predictions == 1).float().mean()
            if n1_accuracy < 0.5:  # 如果N1准确率低于50%
                n1_penalty = 0.3 * (0.5 - n1_accuracy)
        
        # 组合损失
        total_loss = main_loss + \
                    0.2 * avg_expert_loss + \
                    0.15 * coarse_loss + \
                    0.15 * fine_loss + \
                    n1_penalty
        
        return total_loss

class AdvancedAugmentation:
    """高级数据增强"""
    def __init__(self, p=0.5):
        self.p = p
        
    def __call__(self, x, y=None):
        if random.random() > self.p:
            return x
        
        # 多种增强策略
        augmentations = []
        
        # 1. 时间偏移
        if random.random() < 0.3:
            shift = random.randint(-100, 100)
            x = torch.roll(x, shifts=shift, dims=-1)
            augmentations.append('shift')
        
        # 2. 幅度缩放
        if random.random() < 0.3:
            scale = 0.7 + random.random() * 0.6
            x = x * scale
            augmentations.append('scale')
        
        # 3. 高斯噪声
        if random.random() < 0.2:
            noise = torch.randn_like(x) * 0.02
            x = x + noise
            augmentations.append('noise')
        
        # 4. 时间拉伸
        if random.random() < 0.2:
            # 简化的时间拉伸
            stretch_factor = 0.9 + random.random() * 0.2
            if stretch_factor != 1.0:
                seq_len = x.shape[-1]
                new_len = int(seq_len * stretch_factor)
                indices = torch.linspace(0, seq_len-1, new_len).long()
                x = x[..., indices]
                # 恢复原始长度
                if new_len < seq_len:
                    pad = torch.zeros(*x.shape[:-1], seq_len - new_len)
                    x = torch.cat([x, pad], dim=-1)
                else:
                    x = x[..., :seq_len]
            augmentations.append('stretch')
        
        return x

def mixup_cutmix(data, target, alpha=0.2, cutmix_prob=0.5):
    """MixUp和CutMix组合"""
    batch_size = data.size(0)
    
    if random.random() < cutmix_prob:
        # CutMix
        lam = np.random.beta(alpha, alpha)
        index = torch.randperm(batch_size).to(data.device)
        
        # 随机选择切割位置
        seq_len = data.shape[-1]
        cut_len = int(seq_len * (1 - lam))
        start = random.randint(0, seq_len - cut_len)
        
        mixed_data = data.clone()
        mixed_data[..., start:start+cut_len] = data[index][..., start:start+cut_len]
        
        return mixed_data, target, target[index], lam
    else:
        # MixUp
        lam = np.random.beta(alpha, alpha)
        index = torch.randperm(batch_size).to(data.device)
        
        mixed_data = lam * data + (1 - lam) * data[index]
        
        return mixed_data, target, target[index], lam

def train_epoch(model, train_loader, criterion, optimizer, device, augmentation, epoch):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    n1_correct = 0
    n1_total = 0
    
    pbar = tqdm(train_loader, desc=f'Training Epoch {epoch+1}')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # 数据增强
        data = augmentation(data)
        
        # MixUp/CutMix（后期才使用）
        use_mix = epoch > 5 and random.random() < 0.3
        if use_mix:
            data, target_a, target_b, lam = mixup_cutmix(data, target)
            
            optimizer.zero_grad()
            final_output, expert_outputs, coarse_logits, fine_logits = model(data)
            
            # 混合损失
            loss_a = criterion(final_output, expert_outputs, coarse_logits, fine_logits, target_a, epoch)
            loss_b = criterion(final_output, expert_outputs, coarse_logits, fine_logits, target_b, epoch)
            loss = lam * loss_a + (1 - lam) * loss_b
        else:
            optimizer.zero_grad()
            final_output, expert_outputs, coarse_logits, fine_logits = model(data)
            loss = criterion(final_output, expert_outputs, coarse_logits, fine_logits, target, epoch)
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        preds = final_output.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy() if not use_mix else target_a.cpu().numpy())
        
        # N1统计
        n1_mask = target == 1
        if n1_mask.sum() > 0:
            n1_total += n1_mask.sum().item()
            n1_correct += ((preds == 1) & n1_mask).sum().item()
        
        pbar.set_postfix({
            'loss': loss.item(),
            'N1_acc': f'{n1_correct/max(n1_total,1):.3f}'
        })
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate(model, data_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            final_output, _, _, _ = model(data)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            preds = final_output.argmax(dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    # V8 Ultimate配置
    config = {
        'd_model': 320,  # 更大的模型
        'n_heads': 20,   
        'n_layers': 7,   # 7层
        'dropout': 0.12,
        'seq_len': 5,
        'batch_size': 24,  # 稍小的batch以适应更大的模型
        'learning_rate': 1.5e-4,
        'weight_decay': 3e-5,
        'num_epochs': 100,  # 更多epochs
        'patience': 20
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = V8UltimateModel(config).to(device)
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = UltimateLoss(device)
    optimizer = optim.AdamW(model.parameters(), 
                           lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'])
    
    # 学习率调度 - OneCycleLR with higher peak
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'] * 15,  # 更高的峰值
        epochs=config['num_epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.3,
        anneal_strategy='cos',
        div_factor=25,
        final_div_factor=10000
    )
    
    # 数据增强
    augmentation = AdvancedAugmentation(p=0.5)
    
    # 训练
    best_test_acc = 0
    best_test_metrics = {}
    patience_counter = 0
    
    logging.info("Starting V8 ULTIMATE training...")
    logging.info("Target: 90% accuracy")
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, augmentation, epoch
        )
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(model, val_loader, device)
        
        # 测试
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(model, test_loader, device)
        
        # 学习率调度
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Test: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_test_metrics = {
                'accuracy': test_acc,
                'f1': test_f1,
                'kappa': test_kappa,
                'class_f1': test_class_f1.tolist(),
                'confusion_matrix': test_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_metrics': best_test_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Test Acc: {test_acc:.4f})")
            
            if test_acc >= 0.87:
                logging.info("  🎯 Reached 87% target!")
                os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '🎯 V8 ULTIMATE reached 87%: {test_acc:.4f}'")
            
            if test_acc >= 0.90:
                logging.info("  🎉 ACHIEVED 90% TARGET!")
                logging.info("  🎉 SUCCESS! SUCCESS! SUCCESS!")
                os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '✅ V8 ULTIMATE ACHIEVED 90%: {test_acc:.4f}'")
                
                # 保存成功配置
                with open(os.path.join(log_dir, 'SUCCESS_90.json'), 'w') as f:
                    success_info = {
                        'model': 'V8 ULTIMATE',
                        'accuracy': test_acc,
                        'f1': test_f1,
                        'kappa': test_kappa,
                        'epoch': epoch + 1,
                        'config': config,
                        'class_f1': test_class_f1.tolist()
                    }
                    json.dump(success_info, f, indent=2)
                
                break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS (V8 ULTIMATE)")
    logging.info("="*80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test F1: {best_test_metrics['f1']:.4f}")
    logging.info(f"Best Test Kappa: {best_test_metrics['kappa']:.4f}")
    
    if best_test_acc >= 0.90:
        logging.info("✅ SUCCESS: ACHIEVED 90% TARGET!")
        logging.info("🎉 MISSION ACCOMPLISHED!")
    else:
        gap = 0.90 - best_test_acc
        logging.info(f"Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
        logging.info("Keep pushing forward! Never give up!")
    
    # 保存结果
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(best_test_metrics, f, indent=2)

if __name__ == "__main__":
    main()