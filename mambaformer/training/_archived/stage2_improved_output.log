2025-08-16 03:53:16,094 - INFO - ================================================================================
2025-08-16 03:53:16,094 - INFO - 🔧 改进版Stage 2训练：保护预训练权重
2025-08-16 03:53:16,094 - INFO - ================================================================================
2025-08-16 03:53:16,094 - INFO - 配置: {'d_model': 512, 'n_heads': 32, 'n_layers': 12, 'dropout': 0.15, 'seq_len': 7, 'batch_size': 16, 'learning_rate': 5e-06, 'num_epochs': 15, 'gradient_clip': 0.3, 'weight_decay': 0.001, 'unfreeze_at_epoch': 5}
2025-08-16 03:53:16,128 - INFO - Device: cuda
2025-08-16 03:53:16,128 - INFO - 加载数据集...
2025-08-16 03:53:18,494 - INFO - 从 29 个文件加载了 31905 个epochs, 创建了 31731 个序列
2025-08-16 03:53:18,494 - INFO - 创建序列数据集: 31731个序列, 序列长度=7, 通道数=3, 总epochs=31905
2025-08-16 03:53:18,920 - INFO - 从 6 个文件加载了 6146 个epochs, 创建了 6110 个序列
2025-08-16 03:53:18,920 - INFO - 创建序列数据集: 6110个序列, 序列长度=7, 通道数=3, 总epochs=6146
2025-08-16 03:53:19,211 - INFO - 从 4 个文件加载了 4257 个epochs, 创建了 4233 个序列
2025-08-16 03:53:19,212 - INFO - 创建序列数据集: 4233个序列, 序列长度=7, 通道数=3, 总epochs=4257
2025-08-16 03:53:19,212 - INFO - 训练集: 31731 sequences
2025-08-16 03:53:19,213 - INFO - 验证集: 6110 sequences
2025-08-16 03:53:19,213 - INFO - 测试集: 4233 sequences
2025-08-16 03:53:19,541 - INFO - 创建ProgressiveMAMBAFORMER_V1_Fixed: 参数量=41,655,367, d_model=512, n_heads=32, n_layers=12
2025-08-16 03:53:19,541 - INFO - 修复版本：简化架构，稳定训练
2025-08-16 03:53:19,790 - INFO - 加载Stage 1最佳模型: /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_simple_20250815_042107/best_model.pth
2025-08-16 03:53:20,265 - INFO - ✅ 成功加载Stage 1模型 (测试准确率: 81.64422395464209%)
2025-08-16 03:53:20,278 - INFO - 可训练参数: 1,252,232 / 42,707,016 (2.93%)
2025-08-16 03:53:20,278 - INFO - 
评估初始性能...

Evaluating:   0%|          | 0/265 [00:00<?, ?it/s]
Evaluating:   0%|          | 0/265 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_improved.py", line 465, in <module>
    final_acc = main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_improved.py", line 353, in main
    initial_metrics = evaluate(model, test_loader, device)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_improved.py", line 179, in evaluate
    main_output, _ = model(data)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_improved.py", line 113, in forward
    enhanced_output = self.cross_modal_adapter(base_output)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_improved.py", line 64, in forward
    attn_output, _ = self.cross_attention(query, x, x)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/activation.py", line 1373, in forward
    attn_output, attn_output_weights = F.multi_head_attention_forward(
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/functional.py", line 6202, in multi_head_attention_forward
    assert (
AssertionError: was expecting embedding dimension of 512, but got 5
