#!/usr/bin/env python3
"""
V14_Progressive_Step3: 添加温度校准和不确定性优化
创新点: 温度缩放校准 + 自适应阈值
目标: 进一步提升准确率和可靠性
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
from sklearn.calibration import calibration_curve

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset


class CalibratedProgressiveMAMBAFORMER(nn.Module):
    """带温度校准的渐进式MAMBAFORMER"""
    
    def __init__(self, config):
        super().__init__()
        
        # 基础MAMBAFORMER编码器
        self.encoder = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len']
        )
        
        # 移除原始分类头
        self.encoder.classifier = nn.Identity()
        
        # 粗分类头 (3类)
        self.coarse_classifier = nn.Sequential(
            nn.Linear(config['d_model'], 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 3)
        )
        
        # 细分类头 (5类)
        self.fine_classifier = nn.Sequential(
            nn.Linear(config['d_model'], 256),
            nn.ReLU(),
            nn.Dropout(0.15),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 5)
        )
        
        # 温度参数（可学习）
        self.temperature_coarse = nn.Parameter(torch.ones(1) * 1.5)
        self.temperature_fine = nn.Parameter(torch.ones(1) * 1.2)
        
        # 自适应不确定性阈值
        self.uncertainty_threshold = nn.Parameter(torch.tensor(0.3))
        
        self.training_mode = True
        
    def compute_calibrated_uncertainty(self, logits, temperature):
        """计算校准后的不确定性"""
        # 温度缩放
        calibrated_logits = logits / temperature
        probs = F.softmax(calibrated_logits, dim=-1)
        
        # 计算熵作为不确定性度量
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
        max_entropy = torch.log(torch.tensor(logits.shape[-1], dtype=torch.float32))
        normalized_entropy = entropy / max_entropy
        
        return normalized_entropy, probs
    
    def forward(self, x, return_all=False):
        # 获取特征
        features, _ = self.encoder(x)
        
        if features.dim() == 3:
            features = features[:, features.shape[1]//2, :]
        
        # 粗分类
        coarse_logits = self.coarse_classifier(features)
        coarse_uncertainty, coarse_probs = self.compute_calibrated_uncertainty(
            coarse_logits, self.temperature_coarse
        )
        
        # 细分类
        fine_logits = self.fine_classifier(features)
        fine_uncertainty, fine_probs = self.compute_calibrated_uncertainty(
            fine_logits, self.temperature_fine
        )
        
        if self.training or return_all:
            return coarse_logits, fine_logits, coarse_uncertainty, fine_uncertainty
        else:
            return self.calibrated_progressive_inference(
                coarse_probs, fine_probs, coarse_uncertainty
            )
    
    def calibrated_progressive_inference(self, coarse_probs, fine_probs, uncertainty):
        """校准的渐进式推理"""
        batch_size = coarse_probs.shape[0]
        final_predictions = torch.zeros(batch_size, 5).to(coarse_probs.device)
        
        # 使用sigmoid确保阈值在合理范围
        threshold = torch.sigmoid(self.uncertainty_threshold)
        
        for i in range(batch_size):
            if uncertainty[i] < threshold:
                # 低不确定性：使用粗分类
                coarse_pred = torch.argmax(coarse_probs[i])
                if coarse_pred == 0:  # Wake
                    final_predictions[i, 0] = coarse_probs[i, 0]
                elif coarse_pred == 1:  # NREM
                    # 根据细分类的N1,N2,N3分布
                    nrem_sum = fine_probs[i, 1:4].sum()
                    if nrem_sum > 0:
                        final_predictions[i, 1:4] = fine_probs[i, 1:4] / nrem_sum * coarse_probs[i, 1]
                    else:
                        final_predictions[i, 2] = coarse_probs[i, 1]  # 默认N2
                else:  # REM
                    final_predictions[i, 4] = coarse_probs[i, 2]
            else:
                # 高不确定性：使用细分类
                final_predictions[i] = fine_probs[i]
        
        return final_predictions


class CalibratedProgressiveLoss(nn.Module):
    """带正则化的渐进式损失"""
    
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 类权重
        self.coarse_weights = torch.tensor([2.0, 1.0, 1.5]).to(device)
        self.fine_weights = torch.tensor([3.0, 2.0, 1.0, 1.0, 2.5]).to(device)  # 增加N1权重
        
        # 损失权重（动态调整）
        self.coarse_weight = 0.25
        self.fine_weight = 0.75
        
    def forward(self, coarse_logits, fine_logits, targets, epoch=0):
        # 动态调整权重
        progress = min(epoch / 20, 1.0)  # 前20个epoch逐渐增加细分类权重
        coarse_w = self.coarse_weight * (1 - 0.5 * progress)
        fine_w = self.fine_weight * (1 + 0.2 * progress)
        
        # 映射到粗分类标签
        coarse_targets = self.map_to_coarse(targets)
        
        # 粗分类损失
        coarse_ce = F.cross_entropy(coarse_logits, coarse_targets, reduction='none')
        coarse_pt = torch.exp(-coarse_ce)
        coarse_focal = (1 - coarse_pt) ** 2.0 * coarse_ce
        coarse_loss = (coarse_focal * self.coarse_weights[coarse_targets]).mean()
        
        # 细分类损失（带标签平滑）
        fine_loss = self.label_smoothing_loss(fine_logits, targets, smoothing=0.1)
        
        # 加权平均
        fine_weights = self.fine_weights[targets]
        fine_loss = (fine_loss * fine_weights).mean()
        
        total_loss = coarse_w * coarse_loss + fine_w * fine_loss
        
        return total_loss, coarse_loss, fine_loss
    
    def label_smoothing_loss(self, logits, targets, smoothing=0.1):
        """标签平滑损失"""
        n_classes = logits.shape[-1]
        log_probs = F.log_softmax(logits, dim=-1)
        
        # 创建平滑的标签分布
        smooth_labels = torch.zeros_like(log_probs)
        smooth_labels.fill_(smoothing / (n_classes - 1))
        smooth_labels.scatter_(1, targets.unsqueeze(1), 1.0 - smoothing)
        
        # KL散度损失
        loss = -(smooth_labels * log_probs).sum(dim=-1)
        return loss
    
    def map_to_coarse(self, targets):
        coarse = torch.zeros_like(targets)
        coarse[targets == 0] = 0  # Wake
        coarse[(targets >= 1) & (targets <= 3)] = 1  # NREM
        coarse[targets == 4] = 2  # REM
        return coarse


def calibrate_temperature(model, val_loader, device):
    """在验证集上校准温度参数"""
    model.eval()
    
    logging.info("Calibrating temperature parameters...")
    
    best_ece = float('inf')
    best_temp_coarse = 1.0
    best_temp_fine = 1.0
    
    # 网格搜索温度参数
    for temp_c in np.arange(0.5, 3.0, 0.2):
        for temp_f in np.arange(0.5, 3.0, 0.2):
            model.temperature_coarse.data = torch.tensor([temp_c]).to(device)
            model.temperature_fine.data = torch.tensor([temp_f]).to(device)
            
            # 计算ECE
            all_probs = []
            all_preds = []
            all_targets = []
            
            with torch.no_grad():
                for data, target in val_loader:
                    data = data.to(device)
                    if target.dim() > 1:
                        target = target[:, target.shape[1]//2]
                    
                    _, fine_logits, _, _ = model(data, return_all=True)
                    probs = F.softmax(fine_logits / model.temperature_fine, dim=-1)
                    preds = probs.argmax(dim=-1)
                    
                    all_probs.append(probs.max(dim=-1)[0].cpu())
                    all_preds.extend(preds.cpu().numpy())
                    all_targets.extend(target.numpy())
            
            # 计算ECE
            all_probs = torch.cat(all_probs).numpy()
            all_preds = np.array(all_preds)
            all_targets = np.array(all_targets)
            
            ece = compute_ece(all_probs, all_preds == all_targets)
            
            if ece < best_ece:
                best_ece = ece
                best_temp_coarse = temp_c
                best_temp_fine = temp_f
    
    # 设置最佳温度
    model.temperature_coarse.data = torch.tensor([best_temp_coarse]).to(device)
    model.temperature_fine.data = torch.tensor([best_temp_fine]).to(device)
    
    logging.info(f"Best temperature: Coarse={best_temp_coarse:.2f}, Fine={best_temp_fine:.2f}, ECE={best_ece:.4f}")
    
    return best_temp_coarse, best_temp_fine


def compute_ece(probs, accuracies, n_bins=10):
    """计算期望校准误差(ECE)"""
    bin_boundaries = np.linspace(0, 1, n_bins + 1)
    bin_lowers = bin_boundaries[:-1]
    bin_uppers = bin_boundaries[1:]
    
    ece = 0
    for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
        in_bin = (probs > bin_lower) & (probs <= bin_upper)
        prop_in_bin = in_bin.mean()
        
        if prop_in_bin > 0:
            accuracy_in_bin = accuracies[in_bin].mean()
            avg_confidence_in_bin = probs[in_bin].mean()
            ece += np.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin
    
    return ece


def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    model.train()
    model.training_mode = True
    
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc=f'Training Epoch {epoch+1}')
    for data, target in pbar:
        data, target = data.to(device), target.to(device)
        
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        optimizer.zero_grad()
        
        # 前向传播
        coarse_logits, fine_logits, _, _ = model(data, return_all=True)
        
        # 计算损失（传入epoch用于动态权重）
        loss, coarse_loss, fine_loss = criterion(coarse_logits, fine_logits, target, epoch)
        
        # 添加温度正则化（防止温度过大或过小）
        temp_reg = 0.01 * (torch.abs(model.temperature_coarse - 1.0) + torch.abs(model.temperature_fine - 1.0))
        loss = loss + temp_reg
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        
        # 限制温度范围
        model.temperature_coarse.data.clamp_(0.5, 3.0)
        model.temperature_fine.data.clamp_(0.5, 3.0)
        model.uncertainty_threshold.data.clamp_(-2.0, 2.0)  # sigmoid后是0.12-0.88
        
        total_loss += loss.item()
        
        # 收集预测
        preds = fine_logits.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
        
        pbar.set_postfix({'loss': loss.item()})
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1


def evaluate(model, data_loader, device):
    model.eval()
    model.training_mode = False
    
    all_preds = []
    all_targets = []
    all_confidences = []
    coarse_usage = 0
    total_samples = 0
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            # 获取所有输出
            coarse_logits, fine_logits, coarse_unc, fine_unc = model(data, return_all=True)
            
            # 渐进式推理
            coarse_probs = F.softmax(coarse_logits / model.temperature_coarse, dim=-1)
            predictions = model.calibrated_progressive_inference(
                coarse_probs,
                F.softmax(fine_logits / model.temperature_fine, dim=-1),
                coarse_unc
            )
            
            preds = predictions.argmax(dim=1)
            confidences = predictions.max(dim=1)[0]
            
            # 统计粗分类使用率
            threshold = torch.sigmoid(model.uncertainty_threshold)
            coarse_usage += (coarse_unc < threshold).sum().item()
            total_samples += len(coarse_unc)
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
            all_confidences.extend(confidences.cpu().numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    
    # 计算ECE
    all_confidences = np.array(all_confidences)
    all_preds = np.array(all_preds)
    all_targets = np.array(all_targets)
    ece = compute_ece(all_confidences, all_preds == all_targets)
    
    coarse_ratio = coarse_usage / total_samples
    
    return accuracy, f1, kappa, class_f1, coarse_ratio, ece


def main():
    config = {
        'd_model': 256,
        'n_heads': 16,
        'n_layers': 6,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 1e-4,
        'weight_decay': 1e-4,
        'num_epochs': 45,
        'patience': 15
    }
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v14_progressive_step3_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 Progressive - Step 3: Temperature Calibration")
    logging.info("="*80)
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据加载（代码略，与Step 2相同）
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=4
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4
    )
    
    # 创建模型
    model = CalibratedProgressiveMAMBAFORMER(config).to(device)
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 尝试加载Step 2的权重
    import glob
    step2_models = glob.glob('../logs/v14_progressive_step2_*/best_model_progressive.pth')
    if step2_models:
        logging.info(f"Loading Step 2 model from {step2_models[0]}")
        checkpoint = torch.load(step2_models[0], map_location=device)
        # 只加载非温度参数
        state_dict = {k: v for k, v in checkpoint['model_state_dict'].items() 
                     if 'temperature' not in k and 'uncertainty_threshold' not in k}
        model.load_state_dict(state_dict, strict=False)
        logging.info("✅ Loaded Step 2 weights")
    
    # 在验证集上校准温度
    calibrate_temperature(model, val_loader, device)
    
    # 损失函数和优化器
    criterion = CalibratedProgressiveLoss(device)
    optimizer = optim.AdamW(model.parameters(), lr=config['learning_rate'], weight_decay=config['weight_decay'])
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'],
        epochs=config['num_epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.1,
        anneal_strategy='cos'
    )
    
    # 训练
    best_val_acc = 0
    best_test_acc = 0
    best_test_metrics = {}
    patience_counter = 0
    
    logging.info("\n🎯 Starting Calibrated Progressive Training...")
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch
        )
        scheduler.step()
        
        # 评估
        val_acc, val_f1, val_kappa, val_class_f1, val_coarse_ratio, val_ece = evaluate(
            model, val_loader, device
        )
        
        test_acc, test_f1, test_kappa, test_class_f1, test_coarse_ratio, test_ece = evaluate(
            model, test_loader, device
        )
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}, ECE={val_ece:.4f}")
        logging.info(f"  Test: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}, ECE={test_ece:.4f}")
        logging.info(f"  Temperatures: Coarse={model.temperature_coarse.item():.2f}, "
                    f"Fine={model.temperature_fine.item():.2f}")
        logging.info(f"  Uncertainty Threshold: {torch.sigmoid(model.uncertainty_threshold).item():.3f}")
        logging.info(f"  Coarse Usage: Val={val_coarse_ratio:.1%}, Test={test_coarse_ratio:.1%}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_test_acc = test_acc
            best_test_metrics = {
                'accuracy': test_acc,
                'f1': test_f1,
                'kappa': test_kappa,
                'class_f1': test_class_f1.tolist(),
                'ece': test_ece,
                'coarse_usage': test_coarse_ratio,
                'temperature_coarse': model.temperature_coarse.item(),
                'temperature_fine': model.temperature_fine.item(),
                'uncertainty_threshold': torch.sigmoid(model.uncertainty_threshold).item()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_metrics': best_test_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model_calibrated.pth'))
            
            logging.info(f"  💾 Saved best model (Test Acc: {test_acc:.4f})")
            
            if test_acc >= 0.87:
                logging.info("  🎉 ACHIEVED 87% TARGET WITH CALIBRATION!")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
        
        # 每10个epoch重新校准
        if (epoch + 1) % 10 == 0:
            calibrate_temperature(model, val_loader, device)
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS (Calibrated Progressive)")
    logging.info("="*80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test ECE: {best_test_metrics['ece']:.4f}")
    logging.info(f"Coarse Usage: {best_test_metrics['coarse_usage']:.1%}")
    
    if best_test_acc >= 0.87:
        logging.info("✅ SUCCESS: Achieved 87% with calibrated progressive classification!")
    else:
        gap = 0.87 - best_test_acc
        logging.info(f"Gap to 87%: {gap:.4f} ({gap*100:.2f}%)")
    
    # 保存结果
    with open(os.path.join(log_dir, 'results_calibrated.json'), 'w') as f:
        json.dump(best_test_metrics, f, indent=2)
    
    logging.info("\n✅ Step 3 Complete: Temperature calibration implemented")
    logging.info("All progressive steps completed!")

if __name__ == "__main__":
    main()