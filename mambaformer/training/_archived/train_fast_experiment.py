#!/usr/bin/env python3
"""
快速实验脚本 - 使用部分真实Sleep-EDF-20数据
目标：快速达到80%+准确率
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
import logging
from datetime import datetime
import json
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from tqdm import tqdm
import gc

# 设置CUDA设备为GPU 0
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

class EfficientSleepNet(nn.Module):
    """高效的睡眠分期网络 - 优化内存使用"""
    def __init__(self, input_channels=1, num_classes=5, seq_len=3000):
        super().__init__()
        
        # 使用1D CNN提取局部特征，减少序列长度
        self.cnn_features = nn.Sequential(
            # 第一层：大幅降采样
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=25, padding=25),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4, stride=4),
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=10, stride=1, padding=5),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
            
            # 第三层
            nn.Conv1d(128, 256, kernel_size=5, stride=1, padding=2),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
        )
        
        # 计算CNN输出大小
        # 3000 -> 120 (stride 25) -> 30 (pool 4) -> 15 (pool 2) -> 7 (pool 2)
        cnn_out_len = 7
        
        # 轻量级Transformer层
        self.transformer_layer = nn.TransformerEncoderLayer(
            d_model=256,
            nhead=4,
            dim_feedforward=512,
            dropout=0.3,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(self.transformer_layer, num_layers=2)
        
        # 跨模态融合
        self.cross_modal_attn = nn.MultiheadAttention(
            embed_dim=256,
            num_heads=4,
            dropout=0.3,
            batch_first=True
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(256 * 2, 256),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(128, num_classes)
        )
        
    def forward(self, eeg, eog):
        # 输入: [batch, 1, 3000]
        
        # CNN特征提取
        eeg_feat = self.cnn_features(eeg)  # [batch, 256, 7]
        eog_feat = self.cnn_features(eog)  # [batch, 256, 7]
        
        # 转置为transformer格式
        eeg_feat = eeg_feat.transpose(1, 2)  # [batch, 7, 256]
        eog_feat = eog_feat.transpose(1, 2)  # [batch, 7, 256]
        
        # Transformer编码
        eeg_encoded = self.transformer(eeg_feat)
        eog_encoded = self.transformer(eog_feat)
        
        # 跨模态注意力
        eeg_cross, _ = self.cross_modal_attn(eeg_encoded, eog_encoded, eog_encoded)
        eog_cross, _ = self.cross_modal_attn(eog_encoded, eeg_encoded, eeg_encoded)
        
        # 全局池化
        eeg_global = eeg_cross.mean(dim=1)    # [batch, 256]
        eog_global = eog_cross.mean(dim=1)    # [batch, 256]
        
        # 融合特征
        combined = torch.cat([eeg_global, eog_global], dim=1)  # [batch, 512]
        
        # 分类
        logits = self.classifier(combined)
        
        return logits

class FastSleepDataset(Dataset):
    """快速数据加载器 - 使用部分数据"""
    def __init__(self, data_path, subjects, max_epochs_per_subject=100):
        self.eeg_data = []
        self.eog_data = []
        self.labels = []
        
        print(f"加载数据集 (每个受试者最多{max_epochs_per_subject}个epoch)...")
        
        for subj in subjects:
            eeg_file = os.path.join(data_path, f'x{int(subj):02d}.h5')
            eog_file = os.path.join(data_path, f'eog{int(subj):02d}.h5')
            label_file = os.path.join(data_path, f'y{int(subj):02d}.h5')
            
            if os.path.exists(eeg_file):
                with h5py.File(eeg_file, 'r') as f:
                    data = f['data'][:]
                    # 只使用部分数据
                    n_samples = min(len(data), max_epochs_per_subject)
                    self.eeg_data.append(data[:n_samples])
                    
                with h5py.File(eog_file, 'r') as f:
                    data = f['data'][:]
                    self.eog_data.append(data[:n_samples])
                    
                with h5py.File(label_file, 'r') as f:
                    data = f['data'][:]
                    self.labels.append(data[:n_samples])
        
        # 合并数据
        self.eeg_data = np.concatenate(self.eeg_data, axis=0).astype(np.float32)
        self.eog_data = np.concatenate(self.eog_data, axis=0).astype(np.float32)
        self.labels = np.concatenate(self.labels, axis=0).astype(np.int64)
        
        # 简单标准化
        self.eeg_data = (self.eeg_data - self.eeg_data.mean()) / (self.eeg_data.std() + 1e-6)
        self.eog_data = (self.eog_data - self.eog_data.mean()) / (self.eog_data.std() + 1e-6)
        
        print(f"数据集大小: {len(self.labels)} 样本")
        print(f"标签分布: {np.bincount(self.labels)}")
        
    def __len__(self):
        return len(self.labels)
    
    def __getitem__(self, idx):
        eeg = torch.FloatTensor(self.eeg_data[idx]).unsqueeze(0)
        eog = torch.FloatTensor(self.eog_data[idx]).unsqueeze(0)
        label = torch.LongTensor([self.labels[idx]]).squeeze()
        return eeg, eog, label

def train_epoch(model, train_loader, optimizer, criterion, device, scheduler=None):
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    for batch_idx, (eeg, eog, labels) in enumerate(tqdm(train_loader, desc='Training')):
        eeg = eeg.to(device)
        eog = eog.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        outputs = model(eeg, eog)
        loss = criterion(outputs, labels)
        
        # L2正则化
        l2_lambda = 0.001
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += loss.item()
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()
        
        # 动态学习率调整
        if scheduler is not None and batch_idx % 50 == 0:
            scheduler.step()
    
    return total_loss / len(train_loader), correct / total

def validate(model, val_loader, criterion, device):
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for eeg, eog, labels in tqdm(val_loader, desc='Validating'):
            eeg = eeg.to(device)
            eog = eog.to(device)
            labels = labels.to(device)
            
            outputs = model(eeg, eog)
            loss = criterion(outputs, labels)
            
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    accuracy = accuracy_score(all_labels, all_preds)
    return total_loss / len(val_loader), accuracy, all_preds, all_labels

def main():
    # 清理内存
    gc.collect()
    torch.cuda.empty_cache()
    
    # 配置
    config = {
        'data_path': './processed_data_fixed',
        'train_subjects': [1, 2, 3, 4, 5],      # 5个训练受试者
        'val_subjects': [6, 7],                 # 2个验证受试者
        'test_subjects': [8],                   # 1个测试受试者
        'max_epochs_per_subject': 150,         # 每个受试者最多150个epoch
        'batch_size': 64,                       # 较大批次大小
        'epochs': 100,                          # 训练轮数
        'lr': 0.001,
        'patience': 15,
        'weights': [1., 2., 1., 2., 2.]        # 类别权重
    }
    
    # 设置日志
    os.makedirs('./log', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f'./log/fast_experiment_{timestamp}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 快速实验 - 高效睡眠分期网络")
    logger.info(f"配置: {json.dumps(config, indent=2)}")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据集
    train_dataset = FastSleepDataset(
        config['data_path'], 
        config['train_subjects'],
        config['max_epochs_per_subject']
    )
    val_dataset = FastSleepDataset(
        config['data_path'], 
        config['val_subjects'],
        config['max_epochs_per_subject']
    )
    test_dataset = FastSleepDataset(
        config['data_path'], 
        config['test_subjects'],
        config['max_epochs_per_subject']
    )
    
    train_loader = DataLoader(
        train_dataset, 
        batch_size=config['batch_size'], 
        shuffle=True, 
        num_workers=4,
        pin_memory=True
    )
    val_loader = DataLoader(
        val_dataset, 
        batch_size=config['batch_size'], 
        shuffle=False, 
        num_workers=4,
        pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, 
        batch_size=config['batch_size'], 
        shuffle=False, 
        num_workers=4,
        pin_memory=True
    )
    
    # 模型
    model = EfficientSleepNet().to(device)
    logger.info(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失和优化器
    weights = torch.tensor(config['weights']).to(device)
    criterion = nn.CrossEntropyLoss(weight=weights)
    optimizer = torch.optim.Adam(model.parameters(), lr=config['lr'])
    
    # 学习率调度器 - 使用余弦退火
    scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
        optimizer, 
        T_max=len(train_loader) * config['epochs'],
        eta_min=1e-6
    )
    
    # 训练
    best_val_acc = 0
    patience_counter = 0
    
    for epoch in range(1, config['epochs'] + 1):
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, optimizer, criterion, device, scheduler
        )
        
        # 验证
        val_loss, val_acc, _, _ = validate(model, val_loader, criterion, device)
        
        logger.info(f"Epoch {epoch}/{config['epochs']}: "
                   f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} | "
                   f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), './checkpoints/fast_experiment_best.pth')
            logger.info(f"✓ 新的最佳准确率: {val_acc:.4f}")
            patience_counter = 0
            
            # 如果达到80%，立即测试
            if val_acc >= 0.80:
                logger.info("🎉 达到80%准确率目标！")
                break
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logger.info("早停")
            break
        
        # 清理内存
        if epoch % 10 == 0:
            gc.collect()
            torch.cuda.empty_cache()
    
    # 测试评估
    logger.info("\n📊 测试集评估")
    model.load_state_dict(torch.load('./checkpoints/fast_experiment_best.pth'))
    test_loss, test_acc, test_preds, test_labels = validate(model, test_loader, criterion, device)
    
    logger.info(f"测试准确率: {test_acc:.4f}")
    
    # 分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(test_labels, test_preds, target_names=class_names)
    logger.info(f"\n分类报告:\n{report}")
    
    # 混淆矩阵
    cm = confusion_matrix(test_labels, test_preds)
    logger.info(f"\n混淆矩阵:\n{cm}")
    
    logger.info(f"\n✅ 实验完成！最佳验证准确率: {best_val_acc:.4f}, 测试准确率: {test_acc:.4f}")
    
    # 保存结果
    results = {
        'best_val_acc': best_val_acc,
        'test_acc': test_acc,
        'config': config,
        'classification_report': report,
        'confusion_matrix': cm.tolist()
    }
    
    with open(f'./log/fast_experiment_results_{timestamp}.json', 'w') as f:
        json.dump(results, f, indent=2)

if __name__ == "__main__":
    main()