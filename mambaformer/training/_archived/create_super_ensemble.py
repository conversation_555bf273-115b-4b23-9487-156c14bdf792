#!/usr/bin/env python3
"""
Super Ensemble: Combining best models to reach 90% accuracy
Combines V17, V18, V21 (pseudo), V22 (deep) with advanced strategies
Target: 90% Test Accuracy
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator

def load_model(checkpoint_path, device, config):
    """Load a trained model from checkpoint"""
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=0.0,  # No dropout during inference
        seq_len=config['seq_len']
    ).to(device)
    
    checkpoint = torch.load(checkpoint_path, map_location=device, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    return model

def apply_tta(model, data, device, n_augments=5):
    """Test-Time Augmentation with multiple strategies"""
    batch_size, seq_len, n_channels, epoch_len = data.shape
    predictions = []
    
    with torch.no_grad():
        # Original prediction
        outputs, _ = model(data)
        predictions.append(torch.softmax(outputs, dim=-1))
        
        # Augmentation 1: Noise
        for _ in range(2):
            noise = torch.randn_like(data) * 0.02
            noisy_data = data + noise
            outputs, _ = model(noisy_data)
            predictions.append(torch.softmax(outputs, dim=-1))
        
        # Augmentation 2: Slight scaling
        for scale in [0.98, 1.02]:
            scaled_data = data * scale
            outputs, _ = model(scaled_data)
            predictions.append(torch.softmax(outputs, dim=-1))
    
    # Average predictions
    avg_pred = torch.stack(predictions).mean(dim=0)
    return avg_pred

def advanced_post_processing(predictions, threshold=0.7):
    """Advanced post-processing with temporal smoothing"""
    processed = predictions.copy()
    n_epochs = len(predictions)
    
    # Temporal smoothing with adaptive window
    for i in range(1, n_epochs - 1):
        if np.max(predictions[i]) < threshold:
            # Low confidence - use neighbors
            window = predictions[max(0, i-2):min(n_epochs, i+3)]
            smoothed = np.mean(window, axis=0)
            processed[i] = smoothed
    
    # Fix isolated transitions
    for i in range(2, n_epochs - 2):
        prev_class = np.argmax(processed[i-1])
        curr_class = np.argmax(processed[i])
        next_class = np.argmax(processed[i+1])
        
        # If current is different from both neighbors
        if curr_class != prev_class and curr_class != next_class and prev_class == next_class:
            # Check confidence
            if np.max(processed[i]) < 0.8:
                processed[i] = (processed[i-1] + processed[i+1]) / 2
    
    return processed

def evaluate_super_ensemble():
    """Main evaluation function"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/super_ensemble_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'evaluation.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 SUPER ENSEMBLE EVALUATION - TARGET 90%")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Model configurations - corrected based on actual checkpoints
    models_config = [
        {
            'name': 'V17_Stable',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v17_stable_20250811_004457/best_model.pth',
            'd_model': 288,  # Corrected
            'n_heads': 18,   # Corrected
            'n_layers': 6,
            'seq_len': 5,    # Corrected
            'weight': 0.20
        },
        {
            'name': 'V18_Fixed',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v18_fixed_20250811_014911/best_model.pth',  # Corrected path
            'd_model': 384,  # Verified
            'n_heads': 24,
            'n_layers': 7,   # Corrected
            'seq_len': 6,
            'weight': 0.25
        },
        {
            'name': 'V21_Pseudo',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v21_pseudo_20250811_030354/best_model.pth',
            'd_model': 384,
            'n_heads': 24,
            'n_layers': 7,   # Corrected
            'seq_len': 7,
            'weight': 0.30
        },
        {
            'name': 'V22_Deep',
            'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v22_deep_20250811_030913/best_model.pth',
            'd_model': 448,
            'n_heads': 28,
            'n_layers': 10,
            'seq_len': 9,
            'weight': 0.25
        }
    ]
    
    # Normalize weights
    total_weight = sum(m['weight'] for m in models_config)
    for m in models_config:
        m['weight'] /= total_weight
    
    logging.info(f"Loading {len(models_config)} models with weights:")
    for m in models_config:
        logging.info(f"  {m['name']}: {m['weight']:.3f}")
    
    # Test data
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Process each model
    all_predictions = []
    all_labels = None
    
    for model_config in models_config:
        logging.info(f"\nProcessing {model_config['name']}...")
        
        # Load model
        model = load_model(model_config['path'], device, model_config)
        
        # Create dataset with matching sequence length
        test_dataset = SequenceSleepDataset(
            test_files,
            seq_len=model_config['seq_len'],
            use_channels=3,
            max_samples_per_file=None
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=32,
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        # Evaluate
        evaluator = EpochLevelEvaluator(seq_len=model_config['seq_len'], n_classes=5)
        evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc=f'Evaluating {model_config["name"]}'):
                data = data.to(device)
                
                # Apply TTA
                probs = apply_tta(model, data, device)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get predictions - store probability distributions
        final_preds, final_labels, final_probs = evaluator.get_final_predictions()
        
        # Store weighted probabilities
        all_predictions.append(final_probs * model_config['weight'])
        
        if all_labels is None:
            all_labels = final_labels
        
        # Individual model metrics
        metrics = evaluator.evaluate()
        logging.info(f"  {model_config['name']}: Acc={metrics['accuracy']:.4f}, F1={metrics['macro_f1']:.4f}")
    
    # Ensemble predictions
    logging.info("\n" + "="*80)
    logging.info("ENSEMBLE STRATEGIES:")
    logging.info("="*80)
    
    # Strategy 1: Weighted Average
    ensemble_probs = np.sum(all_predictions, axis=0)
    ensemble_preds_weighted = np.argmax(ensemble_probs, axis=-1)
    
    # Apply post-processing
    ensemble_probs_processed = np.array([
        advanced_post_processing(ensemble_probs[i])
        for i in range(len(ensemble_probs))
    ])
    ensemble_preds_processed = np.argmax(ensemble_probs_processed, axis=-1)
    
    # Calculate metrics
    from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score
    
    # Weighted average results
    acc_weighted = accuracy_score(all_labels, ensemble_preds_weighted)
    f1_weighted = f1_score(all_labels, ensemble_preds_weighted, average='macro')
    kappa_weighted = cohen_kappa_score(all_labels, ensemble_preds_weighted)
    
    logging.info(f"\n1. Weighted Average:")
    logging.info(f"   Accuracy: {acc_weighted:.4f} ({acc_weighted*100:.2f}%)")
    logging.info(f"   Macro F1: {f1_weighted:.4f} ({f1_weighted*100:.2f}%)")
    logging.info(f"   Kappa: {kappa_weighted:.4f}")
    
    # With post-processing
    acc_processed = accuracy_score(all_labels, ensemble_preds_processed)
    f1_processed = f1_score(all_labels, ensemble_preds_processed, average='macro')
    kappa_processed = cohen_kappa_score(all_labels, ensemble_preds_processed)
    
    logging.info(f"\n2. Weighted + Post-Processing:")
    logging.info(f"   Accuracy: {acc_processed:.4f} ({acc_processed*100:.2f}%)")
    logging.info(f"   Macro F1: {f1_processed:.4f} ({f1_processed*100:.2f}%)")
    logging.info(f"   Kappa: {kappa_processed:.4f}")
    
    # Strategy 2: Confidence-based voting
    confidence_preds = []
    for i in range(len(ensemble_probs)):
        # Get predictions from each model
        model_preds = [np.argmax(all_predictions[j][i] / models_config[j]['weight'], axis=-1) 
                      for j in range(len(models_config))]
        model_confs = [np.max(all_predictions[j][i] / models_config[j]['weight'], axis=-1) 
                      for j in range(len(models_config))]
        
        # Vote based on confidence
        voted_preds = []
        for epoch_idx in range(len(model_preds[0])):
            votes = {}
            for model_idx in range(len(models_config)):
                pred = model_preds[model_idx][epoch_idx]
                conf = model_confs[model_idx][epoch_idx]
                if pred not in votes:
                    votes[pred] = 0
                votes[pred] += conf * models_config[model_idx]['weight']
            
            # Select prediction with highest weighted confidence
            best_pred = max(votes.keys(), key=lambda k: votes[k])
            voted_preds.append(best_pred)
        
        confidence_preds.extend(voted_preds)
    
    confidence_preds = np.array(confidence_preds)
    
    acc_confidence = accuracy_score(all_labels, confidence_preds)
    f1_confidence = f1_score(all_labels, confidence_preds, average='macro')
    kappa_confidence = cohen_kappa_score(all_labels, confidence_preds)
    
    logging.info(f"\n3. Confidence-Weighted Voting:")
    logging.info(f"   Accuracy: {acc_confidence:.4f} ({acc_confidence*100:.2f}%)")
    logging.info(f"   Macro F1: {f1_confidence:.4f} ({f1_confidence*100:.2f}%)")
    logging.info(f"   Kappa: {kappa_confidence:.4f}")
    
    # Find best strategy
    best_acc = max(acc_weighted, acc_processed, acc_confidence)
    best_name = ["Weighted", "Weighted+PP", "Confidence"][
        [acc_weighted, acc_processed, acc_confidence].index(best_acc)
    ]
    
    logging.info("\n" + "="*80)
    logging.info(f"🏆 BEST STRATEGY: {best_name}")
    logging.info(f"   Test Accuracy: {best_acc:.4f} ({best_acc*100:.2f}%)")
    logging.info("="*80)
    
    # Target check
    if best_acc >= 0.90:
        logging.info("\n🎉🎉🎉 SUCCESS! 90% TARGET ACHIEVED! 🎉🎉🎉")
    else:
        gap = 0.90 - best_acc
        logging.info(f"\n📈 Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
    
    # Save results
    results = {
        'timestamp': timestamp,
        'models': [m['name'] for m in models_config],
        'weights': [m['weight'] for m in models_config],
        'weighted_average': {
            'accuracy': float(acc_weighted),
            'macro_f1': float(f1_weighted),
            'kappa': float(kappa_weighted)
        },
        'with_post_processing': {
            'accuracy': float(acc_processed),
            'macro_f1': float(f1_processed),
            'kappa': float(kappa_processed)
        },
        'confidence_voting': {
            'accuracy': float(acc_confidence),
            'macro_f1': float(f1_confidence),
            'kappa': float(kappa_confidence)
        },
        'best_strategy': best_name,
        'best_accuracy': float(best_acc),
        'target_achieved': best_acc >= 0.90
    }
    
    results_file = os.path.join(log_dir, 'results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {results_file}")
    
    # Per-class analysis for best strategy
    if best_name == "Weighted":
        best_preds = ensemble_preds_weighted
    elif best_name == "Weighted+PP":
        best_preds = ensemble_preds_processed
    else:
        best_preds = confidence_preds
    
    cm = confusion_matrix(all_labels, best_preds)
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    
    logging.info("\n📊 Per-Class Performance (Best Strategy):")
    report = classification_report(all_labels, best_preds, 
                                  target_names=class_names, 
                                  output_dict=True)
    
    for class_name in class_names:
        metrics = report[class_name]
        logging.info(f"{class_name}: P={metrics['precision']*100:.1f}%, "
                    f"R={metrics['recall']*100:.1f}%, "
                    f"F1={metrics['f1-score']*100:.1f}%")
    
    return results


if __name__ == "__main__":
    results = evaluate_super_ensemble()