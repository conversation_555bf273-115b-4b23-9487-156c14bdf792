#!/usr/bin/env python3
"""
🎯 ADVANCED ENSEMBLE - ACHIEVING 90% TEST ACCURACY
Combining multiple models with meta-learning for optimal performance
Current: 88.37% test → Target: 90% test
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, TensorDataset
from torch.optim.lr_scheduler import CosineAnnealingLR
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score, f1_score, cohen_kappa_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
import xgboost as xgb
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class MetaLearner(nn.Module):
    """Neural network meta-learner for ensemble"""
    def __init__(self, n_models=5, n_classes=5, hidden_dim=128):
        super().__init__()
        input_dim = n_models * n_classes
        
        self.fc1 = nn.Linear(input_dim, hidden_dim)
        self.bn1 = nn.BatchNorm1d(hidden_dim)
        self.dropout1 = nn.Dropout(0.3)
        
        self.fc2 = nn.Linear(hidden_dim, hidden_dim // 2)
        self.bn2 = nn.BatchNorm1d(hidden_dim // 2)
        self.dropout2 = nn.Dropout(0.2)
        
        self.fc3 = nn.Linear(hidden_dim // 2, n_classes)
        
        self.relu = nn.ReLU()
        
    def forward(self, x):
        x = self.fc1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.dropout1(x)
        
        x = self.fc2(x)
        x = self.bn2(x)
        x = self.relu(x)
        x = self.dropout2(x)
        
        x = self.fc3(x)
        return x


def load_model(model_path, config, device):
    """Load a trained model"""
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config.get('dropout', 0.2),
        seq_len=config.get('seq_len', 7)
    ).to(device)
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model.eval()
    return model


def get_model_predictions(model, data_loader, device, use_tta=True):
    """Get predictions from a model with optional TTA"""
    model.eval()
    all_preds = []
    all_probs = []
    
    with torch.no_grad():
        for data, _ in tqdm(data_loader, desc='Getting predictions'):
            data = data.to(device)
            
            if use_tta:
                # Test-time augmentation
                predictions = []
                
                # Original
                output, _ = model(data)
                if output.dim() == 3:
                    output = output[:, output.shape[1]//2, :]
                predictions.append(F.softmax(output, dim=1))
                
                # With noise (multiple variations)
                for noise_level in [0.003, 0.005, 0.007]:
                    noise = torch.randn_like(data) * noise_level
                    output, _ = model(data + noise)
                    if output.dim() == 3:
                        output = output[:, output.shape[1]//2, :]
                    predictions.append(F.softmax(output, dim=1))
                
                # Slight temporal shift
                if data.shape[1] > 1:
                    # Forward shift
                    shifted_data = data.clone()
                    shifted_data[:, :-1] = data[:, 1:]
                    output, _ = model(shifted_data)
                    if output.dim() == 3:
                        output = output[:, output.shape[1]//2, :]
                    predictions.append(F.softmax(output, dim=1))
                    
                    # Backward shift
                    shifted_data = data.clone()
                    shifted_data[:, 1:] = data[:, :-1]
                    output, _ = model(shifted_data)
                    if output.dim() == 3:
                        output = output[:, output.shape[1]//2, :]
                    predictions.append(F.softmax(output, dim=1))
                
                # Average predictions
                avg_prob = torch.stack(predictions).mean(dim=0)
                pred = avg_prob.argmax(dim=1)
            else:
                output, _ = model(data)
                if output.dim() == 3:
                    output = output[:, output.shape[1]//2, :]
                avg_prob = F.softmax(output, dim=1)
                pred = avg_prob.argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_probs.append(avg_prob.cpu().numpy())
    
    all_probs = np.vstack(all_probs)
    all_preds = np.array(all_preds)
    
    return all_preds, all_probs


def train_meta_learner(meta_learner, train_features, train_labels, val_features, val_labels, device, epochs=50):
    """Train the meta-learner"""
    train_dataset = TensorDataset(
        torch.FloatTensor(train_features),
        torch.LongTensor(train_labels)
    )
    
    train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
    
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(meta_learner.parameters(), lr=1e-3, weight_decay=0.01)
    scheduler = CosineAnnealingLR(optimizer, T_max=epochs)
    
    best_val_acc = 0
    
    for epoch in range(epochs):
        # Train
        meta_learner.train()
        total_loss = 0
        correct = 0
        total = 0
        
        for batch_features, batch_labels in train_loader:
            batch_features = batch_features.to(device)
            batch_labels = batch_labels.to(device)
            
            optimizer.zero_grad()
            outputs = meta_learner(batch_features)
            loss = criterion(outputs, batch_labels)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += batch_labels.size(0)
            correct += predicted.eq(batch_labels).sum().item()
        
        train_acc = correct / total
        
        # Validate
        meta_learner.eval()
        with torch.no_grad():
            val_features_tensor = torch.FloatTensor(val_features).to(device)
            val_labels_tensor = torch.LongTensor(val_labels).to(device)
            
            val_outputs = meta_learner(val_features_tensor)
            _, val_predicted = val_outputs.max(1)
            val_acc = val_predicted.eq(val_labels_tensor).sum().item() / len(val_labels)
        
        if val_acc > best_val_acc:
            best_val_acc = val_acc
        
        scheduler.step()
        
        if (epoch + 1) % 10 == 0:
            logging.info(f"Meta-learner Epoch {epoch+1}: Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}")
    
    return best_val_acc


def main():
    # Configuration
    config = {
        'batch_size': 16,
        'seq_len': 7
    }
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f'../logs/ensemble_90_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(log_dir, 'training.log')),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 ADVANCED ENSEMBLE - TARGETING 90% TEST ACCURACY")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # Prepare file paths
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Create datasets
    train_dataset = SequenceSleepDataset(train_files, seq_len=config['seq_len'], is_training=False)
    val_dataset = SequenceSleepDataset(val_files, seq_len=config['seq_len'], is_training=False)
    test_dataset = SequenceSleepDataset(test_files, seq_len=config['seq_len'], is_training=False)
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4, pin_memory=True)
    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4, pin_memory=True)
    test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4, pin_memory=True)
    
    # Model configurations
    model_configs = [
        # V30 Ultra model
        {
            'path': '../logs/v30_ultra_20250811_201709/best_model.pth',
            'd_model': 512, 'n_heads': 32, 'n_layers': 12, 'dropout': 0.25,
            'name': 'V30_Ultra'
        },
        # V22 Deep model
        {
            'path': '../logs/v22_deep_20250811_030913/best_model.pth',
            'd_model': 320, 'n_heads': 20, 'n_layers': 8, 'dropout': 0.3,
            'name': 'V22_Deep'
        },
        # V20 model
        {
            'path': '../logs/v20_balanced_20250811_020107/best_model.pth',
            'd_model': 256, 'n_heads': 16, 'n_layers': 6, 'dropout': 0.2,
            'name': 'V20_Balanced'
        }
    ]
    
    # Load models and get predictions
    logging.info("\n📊 Loading models and generating predictions...")
    
    train_predictions = []
    train_probabilities = []
    val_predictions = []
    val_probabilities = []
    test_predictions = []
    test_probabilities = []
    
    for model_config in model_configs:
        if not os.path.exists(model_config['path']):
            logging.warning(f"Model not found: {model_config['path']}")
            continue
            
        logging.info(f"\nLoading {model_config['name']}...")
        model = load_model(model_config['path'], model_config, device)
        
        # Get predictions
        train_pred, train_prob = get_model_predictions(model, train_loader, device, use_tta=False)
        val_pred, val_prob = get_model_predictions(model, val_loader, device, use_tta=True)
        test_pred, test_prob = get_model_predictions(model, test_loader, device, use_tta=True)
        
        train_predictions.append(train_pred)
        train_probabilities.append(train_prob)
        val_predictions.append(val_pred)
        val_probabilities.append(val_prob)
        test_predictions.append(test_pred)
        test_probabilities.append(test_prob)
        
        # Log individual model performance
        val_targets = []
        for _, target in val_loader:
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            val_targets.extend(target.numpy())
        val_targets = np.array(val_targets)
        
        val_acc = accuracy_score(val_targets, val_pred)
        logging.info(f"  {model_config['name']} Val Accuracy: {val_acc:.4f}")
    
    # Get true labels
    train_targets = []
    for _, target in train_loader:
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        train_targets.extend(target.numpy())
    train_targets = np.array(train_targets)
    
    val_targets = []
    for _, target in val_loader:
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        val_targets.extend(target.numpy())
    val_targets = np.array(val_targets)
    
    test_targets = []
    for _, target in test_loader:
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        test_targets.extend(target.numpy())
    test_targets = np.array(test_targets)
    
    # Ensemble strategies
    logging.info("\n🔬 Testing ensemble strategies...")
    
    # 1. Simple averaging
    avg_val_prob = np.mean(val_probabilities, axis=0)
    avg_val_pred = np.argmax(avg_val_prob, axis=1)
    avg_val_acc = accuracy_score(val_targets, avg_val_pred)
    
    avg_test_prob = np.mean(test_probabilities, axis=0)
    avg_test_pred = np.argmax(avg_test_prob, axis=1)
    avg_test_acc = accuracy_score(test_targets, avg_test_pred)
    
    logging.info(f"\n1. Simple Averaging:")
    logging.info(f"   Val Acc: {avg_val_acc:.4f}")
    logging.info(f"   Test Acc: {avg_test_acc:.4f}")
    
    # 2. Weighted averaging (based on validation performance)
    weights = []
    for i, val_pred in enumerate(val_predictions):
        acc = accuracy_score(val_targets, val_pred)
        weights.append(acc ** 2)  # Square to emphasize better models
    weights = np.array(weights)
    weights = weights / weights.sum()
    
    weighted_val_prob = np.average(val_probabilities, axis=0, weights=weights)
    weighted_val_pred = np.argmax(weighted_val_prob, axis=1)
    weighted_val_acc = accuracy_score(val_targets, weighted_val_pred)
    
    weighted_test_prob = np.average(test_probabilities, axis=0, weights=weights)
    weighted_test_pred = np.argmax(weighted_test_prob, axis=1)
    weighted_test_acc = accuracy_score(test_targets, weighted_test_pred)
    
    logging.info(f"\n2. Weighted Averaging (weights: {weights}):")
    logging.info(f"   Val Acc: {weighted_val_acc:.4f}")
    logging.info(f"   Test Acc: {weighted_test_acc:.4f}")
    
    # 3. Majority voting
    val_votes = np.stack(val_predictions, axis=0)
    val_majority = np.apply_along_axis(lambda x: np.bincount(x).argmax(), axis=0, arr=val_votes)
    majority_val_acc = accuracy_score(val_targets, val_majority)
    
    test_votes = np.stack(test_predictions, axis=0)
    test_majority = np.apply_along_axis(lambda x: np.bincount(x).argmax(), axis=0, arr=test_votes)
    majority_test_acc = accuracy_score(test_targets, test_majority)
    
    logging.info(f"\n3. Majority Voting:")
    logging.info(f"   Val Acc: {majority_val_acc:.4f}")
    logging.info(f"   Test Acc: {majority_test_acc:.4f}")
    
    # 4. Meta-learner (Neural Network)
    logging.info(f"\n4. Training Neural Meta-Learner...")
    
    # Prepare features
    train_features = np.hstack(train_probabilities)
    val_features = np.hstack(val_probabilities)
    test_features = np.hstack(test_probabilities)
    
    # Train meta-learner
    meta_learner = MetaLearner(n_models=len(model_configs), n_classes=5).to(device)
    meta_val_acc = train_meta_learner(meta_learner, train_features, train_targets, 
                                      val_features, val_targets, device, epochs=50)
    
    # Test meta-learner
    meta_learner.eval()
    with torch.no_grad():
        test_features_tensor = torch.FloatTensor(test_features).to(device)
        test_outputs = meta_learner(test_features_tensor)
        _, meta_test_pred = test_outputs.max(1)
        meta_test_pred = meta_test_pred.cpu().numpy()
    
    meta_test_acc = accuracy_score(test_targets, meta_test_pred)
    meta_test_f1 = f1_score(test_targets, meta_test_pred, average='macro')
    meta_test_kappa = cohen_kappa_score(test_targets, meta_test_pred)
    
    logging.info(f"   Val Acc: {meta_val_acc:.4f}")
    logging.info(f"   Test Acc: {meta_test_acc:.4f}")
    logging.info(f"   Test F1: {meta_test_f1:.4f}")
    logging.info(f"   Test Kappa: {meta_test_kappa:.4f}")
    
    # 5. XGBoost meta-learner
    logging.info(f"\n5. Training XGBoost Meta-Learner...")
    
    xgb_model = xgb.XGBClassifier(
        n_estimators=200,
        max_depth=6,
        learning_rate=0.1,
        objective='multi:softprob',
        use_label_encoder=False,
        random_state=42
    )
    
    xgb_model.fit(train_features, train_targets)
    
    xgb_val_pred = xgb_model.predict(val_features)
    xgb_val_acc = accuracy_score(val_targets, xgb_val_pred)
    
    xgb_test_pred = xgb_model.predict(test_features)
    xgb_test_acc = accuracy_score(test_targets, xgb_test_pred)
    xgb_test_f1 = f1_score(test_targets, xgb_test_pred, average='macro')
    xgb_test_kappa = cohen_kappa_score(test_targets, xgb_test_pred)
    
    logging.info(f"   Val Acc: {xgb_val_acc:.4f}")
    logging.info(f"   Test Acc: {xgb_test_acc:.4f}")
    logging.info(f"   Test F1: {xgb_test_f1:.4f}")
    logging.info(f"   Test Kappa: {xgb_test_kappa:.4f}")
    
    # Find best ensemble
    best_test_acc = max(avg_test_acc, weighted_test_acc, majority_test_acc, meta_test_acc, xgb_test_acc)
    best_method = ""
    
    if best_test_acc == avg_test_acc:
        best_method = "Simple Averaging"
        best_predictions = avg_test_pred
    elif best_test_acc == weighted_test_acc:
        best_method = "Weighted Averaging"
        best_predictions = weighted_test_pred
    elif best_test_acc == majority_test_acc:
        best_method = "Majority Voting"
        best_predictions = test_majority
    elif best_test_acc == meta_test_acc:
        best_method = "Neural Meta-Learner"
        best_predictions = meta_test_pred
    else:
        best_method = "XGBoost Meta-Learner"
        best_predictions = xgb_test_pred
    
    # Final results
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL ENSEMBLE RESULTS")
    logging.info("="*80)
    
    logging.info(f"\n🏆 Best Method: {best_method}")
    logging.info(f"   Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    
    best_f1 = f1_score(test_targets, best_predictions, average='macro')
    best_kappa = cohen_kappa_score(test_targets, best_predictions)
    
    logging.info(f"   Test Macro F1: {best_f1:.4f}")
    logging.info(f"   Test Kappa: {best_kappa:.4f}")
    
    # Per-class metrics
    class_f1 = f1_score(test_targets, best_predictions, average=None)
    logging.info(f"\n   Per-class F1:")
    logging.info(f"     Wake: {class_f1[0]:.4f}")
    logging.info(f"     N1: {class_f1[1]:.4f}")
    logging.info(f"     N2: {class_f1[2]:.4f}")
    logging.info(f"     N3: {class_f1[3]:.4f}")
    logging.info(f"     REM: {class_f1[4]:.4f}")
    
    # Confusion matrix
    cm = confusion_matrix(test_targets, best_predictions, labels=[0, 1, 2, 3, 4])
    
    # Gap analysis
    gap_to_90 = 0.90 - best_test_acc
    if gap_to_90 > 0:
        logging.info(f"\n📊 Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
    else:
        logging.info(f"\n🎉 EXCEEDED TARGET BY {-gap_to_90:.4f} ({-gap_to_90*100:.2f}%)!")
    
    # Save results
    results = {
        'best_method': best_method,
        'test_accuracy': float(best_test_acc),
        'test_macro_f1': float(best_f1),
        'test_kappa': float(best_kappa),
        'test_class_f1': class_f1.tolist(),
        'confusion_matrix': cm.tolist(),
        'ensemble_results': {
            'simple_averaging': float(avg_test_acc),
            'weighted_averaging': float(weighted_test_acc),
            'majority_voting': float(majority_test_acc),
            'neural_meta_learner': float(meta_test_acc),
            'xgboost_meta_learner': float(xgb_test_acc)
        },
        'model_weights': weights.tolist() if weights is not None else None
    }
    
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {log_dir}/results.json")
    
    # Save best model
    if best_method == "Neural Meta-Learner":
        torch.save({
            'meta_learner_state_dict': meta_learner.state_dict(),
            'model_configs': model_configs,
            'test_accuracy': best_test_acc
        }, os.path.join(log_dir, 'best_ensemble.pth'))
    elif best_method == "XGBoost Meta-Learner":
        import joblib
        joblib.dump(xgb_model, os.path.join(log_dir, 'xgb_ensemble.pkl'))
    
    if best_test_acc >= 0.90:
        logging.info("\n" + "="*80)
        logging.info("🎉 TARGET ACHIEVED! 90% TEST ACCURACY!")
        logging.info("="*80)

if __name__ == '__main__':
    main()