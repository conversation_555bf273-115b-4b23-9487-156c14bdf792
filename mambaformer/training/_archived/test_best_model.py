#!/usr/bin/env python3
"""
测试最佳模型并进行详细分析
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix, precision_recall_fscore_support

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"test_results_{timestamp}.log"
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return log_file

class ProperEvalModel(nn.Module):
    """基于最佳架构的模型"""
    def __init__(self, input_channels, n_classes, d_model, n_heads, n_layers, dropout, seq_len):
        super().__init__()
        
        # 主干网络
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # 额外的分类头用于正则化 - 基于n_classes输入
        self.auxiliary_head = nn.Sequential(
            nn.Linear(n_classes, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, n_classes)
        )
        
    def forward(self, x):
        # 主输出
        main_out, _ = self.backbone(x)
        
        # 提取中心时间步输出
        if main_out.dim() == 3:
            center_out = main_out[:, main_out.shape[1]//2, :]
        else:
            center_out = main_out
            
        # 辅助输出（用于训练时的正则化）
        aux_out = self.auxiliary_head(center_out)
        
        return center_out, aux_out

def evaluate_with_tta(model, data_loader, device, n_tta=5):
    """评估函数 - 带TTA"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating with TTA'):
            data = data.to(device)
            
            # Test Time Augmentation
            predictions = []
            for i in range(n_tta):
                if i == 0:
                    data_aug = data
                elif i == 1:
                    data_aug = data + torch.randn_like(data) * 0.002
                elif i == 2:
                    shift = np.random.randint(-20, 20)
                    data_aug = torch.roll(data, shifts=shift, dims=-1)
                elif i == 3:
                    data_aug = data * (0.97 + np.random.random() * 0.06)
                else:
                    data_aug = data + torch.randn_like(data) * 0.003
                    shift = np.random.randint(-30, 30)
                    data_aug = torch.roll(data_aug, shifts=shift, dims=-1)
                
                main_out, aux_out = model(data_aug)
                
                # 组合主输出和辅助输出
                combined = 0.7 * F.softmax(main_out, dim=-1) + 0.3 * F.softmax(aux_out, dim=-1)
                predictions.append(combined)
            
            avg_probs = torch.stack(predictions).mean(dim=0)
            preds = avg_probs.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    return np.array(all_targets), np.array(all_preds)

def analyze_results(targets, predictions, class_names):
    """详细分析结果"""
    # 基本指标
    accuracy = accuracy_score(targets, predictions)
    f1 = f1_score(targets, predictions, average='macro')
    kappa = cohen_kappa_score(targets, predictions)
    
    # 每类指标
    precision, recall, f1_scores, support = precision_recall_fscore_support(
        targets, predictions, average=None
    )
    
    # 混淆矩阵
    cm = confusion_matrix(targets, predictions)
    
    # 打印结果
    logging.info("\n" + "="*80)
    logging.info("📊 DETAILED TEST RESULTS")
    logging.info("="*80)
    
    logging.info(f"\n🎯 Overall Metrics:")
    logging.info(f"  Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    logging.info(f"  Macro F1: {f1:.4f}")
    logging.info(f"  Cohen's Kappa: {kappa:.4f}")
    
    logging.info(f"\n🔄 Confusion Matrix:")
    logging.info("       " + "  ".join([f"{name:>6}" for name in class_names]))
    for i, true_class in enumerate(class_names):
        row = cm[i]
        row_str = " ".join([f"{val:>6}" for val in row])
        logging.info(f"{true_class:>6} {row_str}")
    logging.info(f"Total samples: {cm.sum()}")
    
    logging.info(f"\n📈 Per-Class Metrics:")
    for i, class_name in enumerate(class_names):
        logging.info(f"{class_name:>6}: F1={f1_scores[i]:.3f}, Prec={precision[i]:.3f}, "
                    f"Recall={recall[i]:.3f}, Support={support[i]}")
    
    # 错误分析
    logging.info(f"\n🔍 Error Analysis:")
    total_errors = cm.sum() - np.trace(cm)
    logging.info(f"Total errors: {total_errors} ({total_errors/cm.sum()*100:.2f}%)")
    
    # 主要混淆对
    logging.info(f"\n📊 Major Confusion Pairs:")
    confusion_pairs = []
    for i in range(5):
        for j in range(5):
            if i != j and cm[i, j] > 20:
                confusion_pairs.append((class_names[i], class_names[j], cm[i, j]))
    
    confusion_pairs.sort(key=lambda x: x[2], reverse=True)
    for true_class, pred_class, count in confusion_pairs[:10]:
        error_rate = count / cm[class_names.index(true_class)].sum() * 100
        logging.info(f"  {true_class} → {pred_class}: {count} errors ({error_rate:.1f}% of {true_class})")
    
    # 性能总结
    logging.info(f"\n📋 Performance Summary:")
    logging.info(f"  Strongest class: {class_names[np.argmax(f1_scores)]} (F1={np.max(f1_scores):.3f})")
    logging.info(f"  Weakest class: {class_names[np.argmin(f1_scores)]} (F1={np.min(f1_scores):.3f})")
    logging.info(f"  F1 variance: {np.std(f1_scores):.3f}")
    
    # 与目标对比
    logging.info(f"\n🎯 Target Comparison:")
    gap_to_90 = 0.90 - accuracy
    if accuracy >= 0.90:
        logging.info(f"  ✅ TARGET ACHIEVED! Accuracy: {accuracy:.4f}")
    else:
        logging.info(f"  Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
        logging.info(f"  Required improvement: {gap_to_90/accuracy*100:.2f}% relative increase")
    
    # 改进建议
    logging.info(f"\n💡 Improvement Suggestions:")
    
    # N1分析
    n1_f1 = f1_scores[1]
    if n1_f1 < 0.6:
        logging.info(f"  ⚠️ N1 stage critically weak (F1={n1_f1:.3f})")
        n1_errors = cm[1].sum() - cm[1,1]
        n1_confused_with = [(class_names[j], cm[1,j]) for j in range(5) if j != 1]
        n1_confused_with.sort(key=lambda x: x[1], reverse=True)
        logging.info(f"     N1 mainly confused with: {n1_confused_with[0][0]} ({n1_confused_with[0][1]} times)")
        logging.info(f"     Suggestions:")
        logging.info(f"       - Increase N1 class weight to 15-20")
        logging.info(f"       - Add specialized N1 detection branch")
        logging.info(f"       - Use focal loss with higher gamma (3.0)")
        logging.info(f"       - Augment N1 samples more aggressively")
    
    # 类别不平衡分析
    if np.std(f1_scores) > 0.15:
        logging.info(f"  ⚠️ High class imbalance (F1 std={np.std(f1_scores):.3f})")
        logging.info(f"     Suggestions:")
        logging.info(f"       - Use balanced sampling strategy")
        logging.info(f"       - Implement SMOTE for minority classes")
        logging.info(f"       - Add class-specific loss weights")
    
    # 混淆对分析
    if confusion_pairs:
        worst_confusion = confusion_pairs[0]
        logging.info(f"  ⚠️ Major confusion: {worst_confusion[0]} → {worst_confusion[1]} ({worst_confusion[2]} errors)")
        if worst_confusion[0] == 'Wake' and worst_confusion[1] == 'N1':
            logging.info(f"     Wake-N1 confusion detected - common issue")
            logging.info(f"     Suggestions:")
            logging.info(f"       - Add transition detection module")
            logging.info(f"       - Use temporal consistency constraints")
        elif worst_confusion[0] == 'N3' and worst_confusion[1] == 'N2':
            logging.info(f"     Deep sleep confusion detected")
            logging.info(f"     Suggestions:")
            logging.info(f"       - Extract delta wave features explicitly")
            logging.info(f"       - Use multi-scale temporal modeling")
    
    return accuracy, f1, kappa

def main():
    log_file = setup_logging()
    
    logging.info("="*80)
    logging.info("🧪 TESTING BEST MODELS")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载测试数据
    logging.info("Loading test dataset...")
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=5,
        max_samples_per_file=None
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=32, 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    logging.info(f"Test dataset size: {len(test_dataset)}")
    
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    
    # 查找最新的模型
    log_dirs = [
        '../logs/v8_proper_eval_20250812_155847',
        '../logs/v8_extreme_20250812_160347'
    ]
    
    results = {}
    
    for log_dir in log_dirs:
        model_path = os.path.join(log_dir, 'best_model.pth')
        if not os.path.exists(model_path):
            logging.info(f"Model not found: {model_path}")
            continue
        
        logging.info(f"\n{'='*60}")
        logging.info(f"Testing model: {log_dir}")
        logging.info(f"{'='*60}")
        
        # 加载模型配置
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        
        if 'config' in checkpoint:
            config = checkpoint['config']
        else:
            # 默认配置
            config = {
                'd_model': 256,
                'n_heads': 16,
                'n_layers': 6,
                'dropout': 0.2,
                'seq_len': 5
            }
        
        logging.info(f"Model config: {config}")
        
        # 创建模型
        if 'extreme' in log_dir:
            # Extreme模型使用不同的架构
            logging.info("Using Extreme model architecture")
            # 暂时跳过extreme模型
            continue
        else:
            model = ProperEvalModel(
                input_channels=3,
                n_classes=5,
                d_model=config['d_model'],
                n_heads=config['n_heads'],
                n_layers=config['n_layers'],
                dropout=config['dropout'],
                seq_len=config['seq_len']
            ).to(device)
        
        # 加载权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # 获取验证集指标
        if 'val_metrics' in checkpoint:
            val_metrics = checkpoint['val_metrics']
            logging.info(f"\nValidation metrics:")
            logging.info(f"  Epoch: {val_metrics.get('epoch', 'N/A')}")
            logging.info(f"  Val Acc: {val_metrics.get('accuracy', 'N/A'):.4f}")
            logging.info(f"  Val F1: {val_metrics.get('f1', 'N/A'):.4f}")
            logging.info(f"  Val Kappa: {val_metrics.get('kappa', 'N/A'):.4f}")
        
        # 测试评估
        logging.info(f"\nEvaluating on test set with TTA...")
        targets, predictions = evaluate_with_tta(model, test_loader, device, n_tta=5)
        
        # 分析结果
        accuracy, f1, kappa = analyze_results(targets, predictions, class_names)
        
        model_name = os.path.basename(log_dir)
        results[model_name] = {
            'accuracy': accuracy,
            'f1': f1,
            'kappa': kappa
        }
    
    # 最终总结
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL SUMMARY")
    logging.info("="*80)
    
    if results:
        best_model = max(results.items(), key=lambda x: x[1]['accuracy'])
        logging.info(f"\n🏆 Best Model: {best_model[0]}")
        logging.info(f"  Accuracy: {best_model[1]['accuracy']:.4f}")
        logging.info(f"  F1: {best_model[1]['f1']:.4f}")
        logging.info(f"  Kappa: {best_model[1]['kappa']:.4f}")
        
        gap = 0.90 - best_model[1]['accuracy']
        if gap > 0:
            logging.info(f"\n📊 Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
            logging.info(f"\n🎯 Next Steps to Reach 90%:")
            logging.info(f"  1. Focus on N1 stage improvement (currently weakest)")
            logging.info(f"  2. Implement specialized N1 detection module")
            logging.info(f"  3. Use more aggressive data augmentation")
            logging.info(f"  4. Try ensemble of multiple models")
            logging.info(f"  5. Implement post-processing with temporal constraints")
        else:
            logging.info(f"\n✅ TARGET ACHIEVED! 🎉")
    
    logging.info(f"\nResults saved to: {log_file}")

if __name__ == "__main__":
    main()