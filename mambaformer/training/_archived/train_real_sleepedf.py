#!/usr/bin/env python3
"""
使用真实Sleep-EDF-20数据训练
确保没有数据泄漏，真实评估模型性能
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np
import h5py
import logging
from datetime import datetime
import json
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from tqdm import tqdm
import gc
from pathlib import Path

# 设置CUDA设备
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

class RealSleepEDFDataset(Dataset):
    """真实Sleep-EDF数据集加载器"""
    def __init__(self, data_dir, subjects, augment=False):
        self.data_dir = Path(data_dir)
        self.augment = augment
        
        # 加载所有数据
        self.eeg_data = []
        self.eog_data = []
        self.labels = []
        
        for subj_idx in subjects:
            eeg_file = self.data_dir / f'subj{subj_idx:02d}_eeg.h5'
            eog_file = self.data_dir / f'subj{subj_idx:02d}_eog.h5'
            label_file = self.data_dir / f'subj{subj_idx:02d}_labels.h5'
            
            if eeg_file.exists() and eog_file.exists() and label_file.exists():
                # 加载EEG数据
                with h5py.File(eeg_file, 'r') as f:
                    eeg = f['data'][:]
                    self.eeg_data.append(eeg)
                
                # 加载EOG数据
                with h5py.File(eog_file, 'r') as f:
                    eog = f['data'][:]
                    self.eog_data.append(eog)
                
                # 加载标签
                with h5py.File(label_file, 'r') as f:
                    labels = f['data'][:]
                    self.labels.append(labels)
                    
                print(f"加载受试者 {subj_idx}: {len(labels)} epochs")
            else:
                print(f"警告：受试者 {subj_idx} 的数据文件不完整")
        
        if len(self.eeg_data) > 0:
            # 合并所有数据
            self.eeg_data = np.concatenate(self.eeg_data, axis=0).astype(np.float32)
            self.eog_data = np.concatenate(self.eog_data, axis=0).astype(np.float32)
            self.labels = np.concatenate(self.labels, axis=0).astype(np.int64)
            
            print(f"\n数据集统计:")
            print(f"  总样本数: {len(self.labels)}")
            print(f"  EEG形状: {self.eeg_data.shape}")
            print(f"  EOG形状: {self.eog_data.shape}")
            print(f"  标签分布: {np.bincount(self.labels)}")
            
            # 计算类别比例
            class_counts = np.bincount(self.labels)
            class_props = class_counts / len(self.labels)
            print(f"  类别比例: W={class_props[0]:.2%}, N1={class_props[1]:.2%}, "
                  f"N2={class_props[2]:.2%}, N3={class_props[3]:.2%}, REM={class_props[4]:.2%}")
        else:
            raise ValueError("没有成功加载任何数据")
    
    def __len__(self):
        return len(self.labels)
    
    def augment_signal(self, signal, label):
        """数据增强"""
        if not self.augment:
            return signal
            
        # 对少数类别进行更强的增强
        if label in [1, 4]:  # N1和REM
            if np.random.rand() > 0.3:
                # 时间偏移
                shift = np.random.randint(-50, 50)
                signal = np.roll(signal, shift)
                
                # 添加噪声
                noise = np.random.normal(0, 0.02, signal.shape)
                signal = signal + noise
        
        # 通用增强
        if np.random.rand() > 0.5:
            # 幅度缩放
            scale = np.random.uniform(0.9, 1.1)
            signal = signal * scale
            
        return signal
    
    def __getitem__(self, idx):
        eeg = self.eeg_data[idx].copy()
        eog = self.eog_data[idx].copy()
        label = self.labels[idx]
        
        # 数据增强
        if self.augment:
            eeg = self.augment_signal(eeg, label)
            eog = self.augment_signal(eog, label)
        
        eeg = torch.FloatTensor(eeg).unsqueeze(0)  # [1, seq_len]
        eog = torch.FloatTensor(eog).unsqueeze(0)  # [1, seq_len]
        label = torch.LongTensor([label]).squeeze()
        
        return eeg, eog, label

# 使用之前成功的模型架构
class EfficientSleepNet(nn.Module):
    """高效的睡眠分期网络"""
    def __init__(self, input_channels=1, num_classes=5, seq_len=3000):
        super().__init__()
        
        # CNN特征提取
        self.cnn_features = nn.Sequential(
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=25, padding=25),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4, stride=4),
            
            nn.Conv1d(64, 128, kernel_size=10, stride=1, padding=5),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
            
            nn.Conv1d(128, 256, kernel_size=5, stride=1, padding=2),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=2, stride=2),
        )
        
        cnn_out_len = 7
        
        # Transformer层
        self.transformer_layer = nn.TransformerEncoderLayer(
            d_model=256,
            nhead=4,
            dim_feedforward=512,
            dropout=0.3,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(self.transformer_layer, num_layers=2)
        
        # 跨模态注意力
        self.cross_modal_attn = nn.MultiheadAttention(
            embed_dim=256,
            num_heads=4,
            dropout=0.3,
            batch_first=True
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(256 * 2, 256),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(128, num_classes)
        )
        
    def forward(self, eeg, eog):
        # CNN特征提取
        eeg_feat = self.cnn_features(eeg)
        eog_feat = self.cnn_features(eog)
        
        # 转置为transformer格式
        eeg_feat = eeg_feat.transpose(1, 2)
        eog_feat = eog_feat.transpose(1, 2)
        
        # Transformer编码
        eeg_encoded = self.transformer(eeg_feat)
        eog_encoded = self.transformer(eog_feat)
        
        # 跨模态注意力
        eeg_cross, _ = self.cross_modal_attn(eeg_encoded, eog_encoded, eog_encoded)
        eog_cross, _ = self.cross_modal_attn(eog_encoded, eeg_encoded, eeg_encoded)
        
        # 全局池化
        eeg_global = eeg_cross.mean(dim=1)
        eog_global = eog_cross.mean(dim=1)
        
        # 融合特征
        combined = torch.cat([eeg_global, eog_global], dim=1)
        
        # 分类
        logits = self.classifier(combined)
        
        return logits

def train_epoch(model, train_loader, optimizer, criterion, device):
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    
    for eeg, eog, labels in tqdm(train_loader, desc='Training'):
        eeg = eeg.to(device)
        eog = eog.to(device)
        labels = labels.to(device)
        
        optimizer.zero_grad()
        outputs = model(eeg, eog)
        loss = criterion(outputs, labels)
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += loss.item()
        _, predicted = outputs.max(1)
        total += labels.size(0)
        correct += predicted.eq(labels).sum().item()
    
    return total_loss / len(train_loader), correct / total

def validate(model, val_loader, criterion, device):
    model.eval()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for eeg, eog, labels in tqdm(val_loader, desc='Validating'):
            eeg = eeg.to(device)
            eog = eog.to(device)
            labels = labels.to(device)
            
            outputs = model(eeg, eog)
            loss = criterion(outputs, labels)
            
            total_loss += loss.item()
            _, predicted = outputs.max(1)
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    accuracy = accuracy_score(all_labels, all_preds)
    return total_loss / len(val_loader), accuracy, all_preds, all_labels

def main():
    # 清理内存
    gc.collect()
    torch.cuda.empty_cache()
    
    # 配置
    config = {
        'data_dir': './real_sleepedf_processed',
        'batch_size': 64,
        'epochs': 100,
        'lr': 0.001,
        'patience': 20,
        'weights': [1., 2., 1., 2., 2.]  # 类别权重
    }
    
    # 检查数据分割文件
    split_file = Path(config['data_dir']) / 'data_split.json'
    if split_file.exists():
        with open(split_file, 'r') as f:
            split_info = json.load(f)
        
        config['train_subjects'] = split_info['train']
        config['val_subjects'] = split_info['val']
        config['test_subjects'] = split_info['test']
        
        print(f"数据集划分:")
        print(f"  训练集: {len(config['train_subjects'])} 个受试者")
        print(f"  验证集: {len(config['val_subjects'])} 个受试者")
        print(f"  测试集: {len(config['test_subjects'])} 个受试者")
    else:
        print("警告：未找到数据分割文件，使用默认分割")
        # 假设有30个受试者
        config['train_subjects'] = list(range(1, 21))   # 20个训练
        config['val_subjects'] = list(range(21, 26))    # 5个验证
        config['test_subjects'] = list(range(26, 31))   # 5个测试
    
    # 设置日志
    os.makedirs('./log', exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f'./log/real_sleepedf_{timestamp}.log'
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 真实Sleep-EDF-20数据训练")
    logger.info(f"配置: {json.dumps(config, indent=2)}")
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据集
    logger.info("\n加载训练集...")
    train_dataset = RealSleepEDFDataset(
        config['data_dir'], 
        config['train_subjects'],
        augment=True
    )
    
    logger.info("\n加载验证集...")
    val_dataset = RealSleepEDFDataset(
        config['data_dir'], 
        config['val_subjects'],
        augment=False
    )
    
    logger.info("\n加载测试集...")
    test_dataset = RealSleepEDFDataset(
        config['data_dir'], 
        config['test_subjects'],
        augment=False
    )
    
    # 数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=config['batch_size'], 
        shuffle=True, 
        num_workers=4,
        pin_memory=True
    )
    val_loader = DataLoader(
        val_dataset, 
        batch_size=config['batch_size'], 
        shuffle=False, 
        num_workers=4,
        pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, 
        batch_size=config['batch_size'], 
        shuffle=False, 
        num_workers=4,
        pin_memory=True
    )
    
    # 模型
    model = EfficientSleepNet().to(device)
    logger.info(f"\n模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失和优化器
    weights = torch.tensor(config['weights']).to(device)
    criterion = nn.CrossEntropyLoss(weight=weights)
    optimizer = torch.optim.Adam(model.parameters(), lr=config['lr'])
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=10
    )
    
    # 训练
    best_val_acc = 0
    patience_counter = 0
    
    logger.info("\n开始训练...")
    for epoch in range(1, config['epochs'] + 1):
        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, optimizer, criterion, device)
        
        # 验证
        val_loss, val_acc, val_preds, val_labels = validate(model, val_loader, criterion, device)
        
        # 调整学习率
        scheduler.step(val_acc)
        
        logger.info(f"Epoch {epoch}/{config['epochs']}: "
                   f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f} | "
                   f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            torch.save(model.state_dict(), './checkpoints/real_sleepedf_best.pth')
            logger.info(f"✓ 新的最佳准确率: {val_acc:.4f}")
            patience_counter = 0
            
            # 如果达到80%，显示详细信息
            if val_acc >= 0.80:
                logger.info("🎉 达到80%准确率目标！")
                # 计算验证集混淆矩阵
                val_cm = confusion_matrix(val_labels, val_preds)
                logger.info(f"验证集混淆矩阵:\n{val_cm}")
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logger.info("早停")
            break
    
    # 测试评估
    logger.info("\n📊 测试集评估")
    model.load_state_dict(torch.load('./checkpoints/real_sleepedf_best.pth'))
    test_loss, test_acc, test_preds, test_labels = validate(model, test_loader, criterion, device)
    
    logger.info(f"测试准确率: {test_acc:.4f}")
    
    # 详细的分类报告
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    report = classification_report(test_labels, test_preds, 
                                 target_names=class_names, digits=3)
    logger.info(f"\n分类报告:\n{report}")
    
    # 混淆矩阵
    cm = confusion_matrix(test_labels, test_preds)
    logger.info(f"\n混淆矩阵:\n{cm}")
    
    # 保存结果
    results = {
        'best_val_acc': best_val_acc,
        'test_acc': test_acc,
        'config': config,
        'classification_report': report,
        'confusion_matrix': cm.tolist()
    }
    
    result_file = f'./log/real_sleepedf_results_{timestamp}.json'
    with open(result_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"\n✅ 训练完成！最佳验证准确率: {best_val_acc:.4f}, 测试准确率: {test_acc:.4f}")
    logger.info(f"结果保存在: {result_file}")

if __name__ == "__main__":
    main()