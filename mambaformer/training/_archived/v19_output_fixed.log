2025-08-11 02:57:20,486 - INFO - ================================================================================
2025-08-11 02:57:20,486 - INFO - 🚀 MAMBAFORMER V19 MEGA - FINAL PUSH TO 90%
2025-08-11 02:57:20,486 - INFO - ================================================================================
2025-08-11 02:57:20,486 - INFO - 🎯 Targets: Accuracy ≥90%, Macro F1 ≥82%, Kappa ≥0.82
2025-08-11 02:57:20,670 - INFO - Device: cuda
2025-08-11 02:57:20,671 - INFO - 
📋 V19 MEGA Configuration:
2025-08-11 02:57:20,671 - INFO -   d_model: 512
2025-08-11 02:57:20,671 - INFO -   n_heads: 32
2025-08-11 02:57:20,671 - INFO -   n_layers: 8
2025-08-11 02:57:20,671 - INFO -   dropout: 0.22
2025-08-11 02:57:20,671 - INFO -   seq_len: 8
2025-08-11 02:57:20,671 - INFO -   batch_size: 16
2025-08-11 02:57:20,671 - INFO -   learning_rate: 5e-05
2025-08-11 02:57:20,671 - INFO -   num_epochs: 60
2025-08-11 02:57:20,671 - INFO -   patience: 25
2025-08-11 02:57:20,671 - INFO -   gradient_clip: 0.4
2025-08-11 02:57:20,671 - INFO -   weight_decay: 0.025
2025-08-11 02:57:20,671 - INFO -   n1_oversample: 3.0
2025-08-11 02:57:20,671 - INFO -   warmup_epochs: 4
2025-08-11 02:57:20,671 - INFO -   mixed_precision: True
2025-08-11 02:57:20,671 - INFO -   mixup_alpha: 0.2
2025-08-11 02:57:20,671 - INFO - 
📂 Data Split:
2025-08-11 02:57:20,671 - INFO -   Train: 25 files
2025-08-11 02:57:20,671 - INFO -   Val: 6 files
2025-08-11 02:57:20,671 - INFO -   Test: 8 files
2025-08-11 02:57:20,672 - INFO - 
📊 Loading datasets with extreme augmentation...
2025-08-11 02:57:21,999 - INFO - 从 25 个文件加载了 26416 个epochs, 创建了 26241 个序列
2025-08-11 02:57:22,000 - INFO - 创建序列数据集: 26241个序列, 序列长度=8, 通道数=3, 总epochs=26416
2025-08-11 02:57:23,044 - INFO - 从 6 个文件加载了 6146 个epochs, 创建了 6104 个序列
2025-08-11 02:57:23,044 - INFO - 创建序列数据集: 6104个序列, 序列长度=8, 通道数=3, 总epochs=6146
2025-08-11 02:57:23,473 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9690 个序列
2025-08-11 02:57:23,473 - INFO - 创建序列数据集: 9690个序列, 序列长度=8, 通道数=3, 总epochs=9746
2025-08-11 02:57:23,473 - INFO - Train samples: 38722 (with extreme oversampling)
2025-08-11 02:57:23,473 - INFO - Val samples: 6146 epochs
2025-08-11 02:57:23,473 - INFO - Test samples: 9746 epochs
2025-08-11 02:57:23,740 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=25,759,047, d_model=512, n_heads=32, n_layers=8
2025-08-11 02:57:23,952 - INFO - 
Model Parameters: 25,759,047
2025-08-11 02:57:23,952 - INFO - Model Size: 98.26 MB
2025-08-11 02:57:25,201 - INFO - 
🏋️ Starting Training...
2025-08-11 02:57:25,202 - INFO - ================================================================================
2025-08-11 02:57:25,202 - INFO - Warmup LR: 1.25e-05

Epoch 1/60:   0%|          | 0/2420 [00:00<?, ?it/s]
Epoch 1/60:   0%|          | 0/2420 [00:01<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v19_mega.py", line 765, in <module>
    results = train_v19()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v19_mega.py", line 470, in train_v19
    loss = criterion(outputs, labels, labels2, lam)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v19_mega.py", line 159, in forward
    temporal_loss = self.temporal(predictions, targets)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v19_mega.py", line 111, in forward
    invalid_mask[stage_mask] = ~is_valid
RuntimeError: Index put requires the source and destination dtypes match, got Float for the destination and Bool for the source.
