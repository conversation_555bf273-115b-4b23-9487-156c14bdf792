# 睡眠分期模型训练结果分析报告

## 一、性能排行榜

### 顶级模型（≥88%准确率）
| 排名 | 版本 | 准确率 | 特点 |
|------|------|--------|------|
| 1 | v30_ultra | 90.62% (val) | 最大模型，但训练崩溃 |
| 2 | final_test_90 | 88.37% (test) | 稳定的最佳测试准确率 |
| 3 | v22_deep | 87.49% (val) | 深度模型+过采样 |
| 4 | v18_fixed | 87.47% (val) | 中等规模+余弦退火 |

### 留一被试交叉验证结果
- **平均准确率**: 86.88% ± 5.78%
- **最佳被试**: Subject 16 (93.24%)
- **最差被试**: Subject 12 (67.02%)
- **表现优秀被试** (>90%): 16, 15, 07, 08, 11

## 二、有效组件分析

### 1. 模型架构要素

#### 最佳配置（v30_ultra/final_test_90）
```python
{
    "d_model": 512,      # 大模型维度
    "n_heads": 32,       # 多注意力头
    "n_layers": 12,      # 深层网络
    "dropout": 0.25,     # 适中的dropout
    "seq_len": 7         # 最优序列长度
}
```

#### 关键发现
- **模型规模**: d_model ≥384 显著提升性能
- **网络深度**: 7-12层效果最佳
- **注意力头数**: 24-32个头获得最好效果
- **序列长度**: 7个epoch的序列长度最优

### 2. 训练策略

#### 高效技术组合
1. **标签平滑** (Label Smoothing: 0.1)
   - 防止过拟合
   - 提升泛化能力

2. **类别平衡策略**
   - N1过采样: 2.5x-4.0x
   - Wake过采样: 2.0x-5.0x
   - REM加权: 3.0x

3. **学习率策略**
   - Warmup: 2-5 epochs
   - 余弦退火或指数衰减
   - 初始LR: 3e-5 到 3e-4

4. **正则化技术**
   - Weight Decay: 0.015-0.05
   - Gradient Clipping: 0.3-1.0
   - Dropout: 0.15-0.25
   - Stochastic Depth: 0.1

### 3. 数据增强

#### 有效的增强方法
1. **Mixup增强** (α=0.2)
   - 线性插值训练样本
   - 提升模型鲁棒性

2. **测试时增强** (TTA)
   - 多次预测+噪声平均
   - 提升1-2%准确率

3. **噪声注入**
   - 训练时随机噪声
   - 增强泛化能力

### 4. 损失函数设计

#### REM/Wake专注损失
```python
class_weights = [5.0, 1.0, 1.0, 1.0, 3.0]  # Wake, N1, N2, N3, REM
focal_loss_gamma = 2.0
```

### 5. 优化器配置

#### AdamW最佳参数
```python
{
    "lr": 2e-4 到 3e-4,
    "weight_decay": 0.01 到 0.05,
    "betas": (0.9, 0.999),
    "eps": 1e-8
}
```

## 三、成功模式总结

### 高性能模型的共同特征

1. **大规模架构**
   - d_model ≥ 384
   - n_layers ≥ 7
   - n_heads ≥ 24

2. **强正则化**
   - 标签平滑
   - 高weight decay
   - Dropout + Stochastic Depth

3. **类别平衡**
   - 少数类过采样
   - 类别权重调整
   - Focal Loss

4. **数据增强**
   - Mixup
   - 测试时增强
   - 噪声注入

5. **优化技巧**
   - Warmup学习率
   - 梯度裁剪
   - 混合精度训练

## 四、失败案例分析

### 性能不佳的原因
1. **模型过小**: d_model < 256
2. **训练不足**: epochs < 30
3. **类别不平衡未处理**
4. **缺乏正则化**
5. **学习率设置不当**

## 五、推荐配置

### 最优配置（基于分析）
```python
config = {
    # 模型架构
    "d_model": 512,
    "n_heads": 32,
    "n_layers": 10,  # 平衡性能和训练时间
    "dropout": 0.2,
    "seq_len": 7,
    
    # 训练参数
    "batch_size": 16,
    "learning_rate": 2e-4,
    "weight_decay": 0.03,
    "num_epochs": 50,
    "patience": 15,
    "gradient_clip": 0.5,
    "warmup_epochs": 3,
    
    # 数据增强
    "label_smoothing": 0.1,
    "mixup_alpha": 0.2,
    "n1_oversample": 3.0,
    "wake_oversample": 2.5,
    
    # 损失函数
    "class_weights": [5.0, 1.0, 1.0, 1.0, 3.0],
    "focal_gamma": 2.0
}
```

### 稳健配置（泛化优先）
```python
config = {
    # 中等模型
    "d_model": 384,
    "n_heads": 24,
    "n_layers": 8,
    "dropout": 0.25,
    "seq_len": 6,
    
    # 保守训练
    "batch_size": 24,
    "learning_rate": 1e-4,
    "weight_decay": 0.02,
    "num_epochs": 40,
    "patience": 10,
    
    # 强正则化
    "label_smoothing": 0.15,
    "stochastic_depth": 0.1
}
```

## 六、下一步改进建议

### 优先级高
1. **集成学习**: 组合多个高性能模型
2. **迁移学习**: 从v30_ultra预训练权重开始
3. **自适应过采样**: 根据验证性能动态调整
4. **交叉验证训练**: 使用k-fold提升稳定性

### 优先级中
1. **架构搜索**: 自动寻找最优配置
2. **多任务学习**: 添加辅助任务
3. **对比学习**: 改善特征表示
4. **知识蒸馏**: 从大模型蒸馏到小模型

### 优先级低
1. **模型剪枝**: 减小模型大小
2. **量化**: 加速推理
3. **对抗训练**: 提升鲁棒性

## 七、结论

最有效的组件组合：
1. **大规模Transformer架构** (d_model=512, n_layers=12)
2. **标签平滑 + Mixup数据增强**
3. **类别平衡策略** (过采样+类权重)
4. **强正则化** (dropout + weight decay + 梯度裁剪)
5. **优化的学习率调度** (warmup + 余弦退火)

这些组件的协同作用是达到88%以上准确率的关键。单独使用某个技术效果有限，但组合使用可以显著提升性能。