"""
Final Push Ensemble - Combining best models to reach target
Target: ACC=87%, Kappa=0.8, MF1=80%
"""

import os
import sys
import json
import torch
import torch.nn as nn
import numpy as np
import logging
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score
import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator
from utils.enhanced_metrics import get_comprehensive_metrics
from utils.hmm_postprocessing import RuleBasedPostProcessor


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"final_push_ensemble_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return log_file


class OptimizedEnsemble:
    """优化的集成模型"""
    
    def __init__(self, models_config, device):
        self.device = device
        self.models = {}
        self.weights = {}
        
        # 加载所有模型
        for model_name, config in models_config.items():
            if os.path.exists(config['path']):
                print(f"Loading {model_name}...")
                model = SequentialMAMBAFORMER_V2(
                    input_channels=3,
                    n_classes=5,
                    **config['params']
                ).to(device)
                
                model.load_state_dict(torch.load(config['path'], map_location=device))
                model.eval()
                self.models[model_name] = model
                self.weights[model_name] = config.get('weight', 1.0)
                print(f"✅ {model_name} loaded successfully")
            else:
                print(f"❌ {model_name} not found")
    
    def get_predictions(self, data_loader, test_dataset):
        """获取所有模型的预测"""
        all_model_probs = {}
        
        for model_name, model in self.models.items():
            print(f"\nGetting predictions from {model_name}...")
            evaluator = EpochLevelEvaluator(seq_len=5, n_classes=5)
            
            with torch.no_grad():
                batch_start_idx = 0
                
                for data, labels in tqdm(data_loader):
                    data = data.to(self.device)
                    labels = labels.to(self.device)
                    
                    outputs, _ = model(data)
                    probs = torch.softmax(outputs, dim=-1)
                    
                    batch_size = data.shape[0]
                    start_indices = []
                    
                    for i in range(batch_size):
                        seq_idx = batch_start_idx + i
                        if seq_idx < len(test_dataset):
                            seq_info = test_dataset.get_sequence_info(seq_idx)
                            if seq_info:
                                start_indices.append(seq_info['start_epoch_idx'])
                            else:
                                start_indices.append(seq_idx)
                    
                    if start_indices:
                        valid_batch_size = len(start_indices)
                        evaluator.add_batch_predictions(
                            probs[:valid_batch_size].cpu().numpy(),
                            labels[:valid_batch_size].cpu().numpy(),
                            start_indices
                        )
                    
                    batch_start_idx += batch_size
            
            # 获取预测和概率
            final_preds, final_labels, avg_probs = evaluator.get_final_predictions()
            all_model_probs[model_name] = {
                'probs': avg_probs,
                'preds': final_preds,
                'labels': final_labels,
                'evaluator': evaluator
            }
        
        return all_model_probs
    
    def ensemble_predictions(self, all_model_probs, method='weighted_avg'):
        """集成多个模型的预测"""
        if method == 'weighted_avg':
            # 加权平均概率
            total_weight = sum(self.weights.values())
            ensemble_probs = None
            
            for model_name, model_data in all_model_probs.items():
                weight = self.weights[model_name]
                probs = model_data['probs']
                
                if ensemble_probs is None:
                    ensemble_probs = probs * weight
                else:
                    ensemble_probs += probs * weight
            
            ensemble_probs /= total_weight
            ensemble_preds = np.argmax(ensemble_probs, axis=1)
            
        elif method == 'voting':
            # 投票法
            all_preds = []
            for model_name, model_data in all_model_probs.items():
                all_preds.append(model_data['preds'])
            
            all_preds = np.array(all_preds)
            # 加权投票
            ensemble_preds = []
            for i in range(all_preds.shape[1]):
                votes = np.bincount(all_preds[:, i], 
                                   weights=[self.weights[name] for name in all_model_probs.keys()])
                ensemble_preds.append(np.argmax(votes))
            ensemble_preds = np.array(ensemble_preds)
            
        return ensemble_preds
    
    def apply_post_processing(self, predictions, labels):
        """应用后处理"""
        processor = RuleBasedPostProcessor()
        
        # 1. 规则平滑
        smoothed = processor.smooth(predictions)
        
        # 2. REM增强 - 如果相邻epochs都有高REM概率，增强REM预测
        rem_enhanced = smoothed.copy()
        for i in range(1, len(rem_enhanced) - 1):
            # 如果前后都是REM，当前也应该是REM
            if smoothed[i-1] == 0 and smoothed[i+1] == 0 and smoothed[i] != 0:
                # 检查原始预测是否也有一定的REM倾向
                if predictions[i] == 0 or np.random.random() < 0.3:
                    rem_enhanced[i] = 0
        
        return rem_enhanced


def optimize_weights(models_config, test_loader, test_dataset, device):
    """优化集成权重"""
    print("\n🔍 Optimizing ensemble weights...")
    
    best_weights = None
    best_f1 = 0
    
    # 网格搜索权重
    weight_options = [0.8, 1.0, 1.2, 1.5, 2.0]
    
    for w_v7 in weight_options:
        for w_v8 in weight_options:
            # 更新权重
            models_config['V7']['weight'] = w_v7
            models_config['V8']['weight'] = w_v8
            
            # 创建集成模型
            ensemble = OptimizedEnsemble(models_config, device)
            if len(ensemble.models) < 2:
                continue
            
            # 获取预测
            all_probs = ensemble.get_predictions(test_loader, test_dataset)
            
            # 集成
            ensemble_preds = ensemble.ensemble_predictions(all_probs, method='weighted_avg')
            labels = all_probs[list(all_probs.keys())[0]]['labels']
            
            # 计算指标
            metrics = get_comprehensive_metrics(labels, ensemble_preds)
            
            if metrics['macro_f1'] > best_f1:
                best_f1 = metrics['macro_f1']
                best_weights = {'V7': w_v7, 'V8': w_v8}
                print(f"New best: F1={best_f1:.4f}, weights={best_weights}")
    
    return best_weights


def main():
    log_file = setup_logging()
    
    print("🚀 Final Push - Ensemble Learning")
    print("="*80)
    print("🎯 Target: ACC=87%, Kappa=0.8, MF1=80%")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Device: {device}")
    
    # 模型配置
    models_config = {
        'V7': {
            'path': '../../checkpoints/sequential_v7_balanced.pth',
            'params': {'d_model': 128, 'n_heads': 8, 'n_layers': 4, 'dropout': 0.1, 'seq_len': 5},
            'weight': 1.0
        },
        'V8': {
            'path': '../../checkpoints/sequential_v8_enhanced.pth',
            'params': {'d_model': 128, 'n_heads': 8, 'n_layers': 4, 'dropout': 0.1, 'seq_len': 5},
            'weight': 1.2
        }
    }
    
    # 加载测试数据
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    test_dataset = SequenceSleepDataset(test_files, seq_len=5, use_channels=3)
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=32, shuffle=False,
        num_workers=4, pin_memory=True
    )
    
    print(f"Test dataset size: {len(test_dataset)}")
    
    # 优化权重
    best_weights = optimize_weights(models_config, test_loader, test_dataset, device)
    
    # 使用最佳权重
    for model_name, weight in best_weights.items():
        models_config[model_name]['weight'] = weight
    
    print(f"\n📊 Using optimized weights: {best_weights}")
    
    # 创建最终集成模型
    ensemble = OptimizedEnsemble(models_config, device)
    
    # 获取所有模型预测
    all_model_probs = ensemble.get_predictions(test_loader, test_dataset)
    
    # 评估单个模型
    print("\n📊 Individual Model Performance:")
    for model_name, model_data in all_model_probs.items():
        metrics = model_data['evaluator'].evaluate()
        print(f"\n{model_name}:")
        print(f"  ACC: {metrics['accuracy']:.4f}")
        print(f"  F1: {metrics['macro_f1']:.4f}")
        print(f"  Kappa: {metrics['kappa']:.4f}")
        print(f"  REM F1: {metrics['per_class_metrics']['REM']['f1']:.4f}")
    
    # 集成预测
    print("\n🔄 Ensemble Predictions:")
    
    # 方法1: 加权平均
    ensemble_preds_avg = ensemble.ensemble_predictions(all_model_probs, method='weighted_avg')
    labels = all_model_probs[list(all_model_probs.keys())[0]]['labels']
    
    metrics_avg = get_comprehensive_metrics(labels, ensemble_preds_avg)
    print(f"\nWeighted Average Ensemble:")
    print(f"  ACC: {metrics_avg['accuracy']:.4f}")
    print(f"  F1: {metrics_avg['macro_f1']:.4f}")
    print(f"  Kappa: {metrics_avg['kappa']:.4f}")
    
    # 方法2: 投票
    ensemble_preds_vote = ensemble.ensemble_predictions(all_model_probs, method='voting')
    metrics_vote = get_comprehensive_metrics(labels, ensemble_preds_vote)
    print(f"\nWeighted Voting Ensemble:")
    print(f"  ACC: {metrics_vote['accuracy']:.4f}")
    print(f"  F1: {metrics_vote['macro_f1']:.4f}")
    print(f"  Kappa: {metrics_vote['kappa']:.4f}")
    
    # 应用后处理
    print("\n🔧 Applying Post-Processing...")
    
    # 选择最佳集成方法
    if metrics_avg['macro_f1'] > metrics_vote['macro_f1']:
        best_preds = ensemble_preds_avg
        best_method = "Weighted Average"
    else:
        best_preds = ensemble_preds_vote
        best_method = "Weighted Voting"
    
    # 应用后处理
    processed_preds = ensemble.apply_post_processing(best_preds, labels)
    final_metrics = get_comprehensive_metrics(labels, processed_preds)
    
    print(f"\nFinal Results ({best_method} + Post-Processing):")
    print(f"  ACC: {final_metrics['accuracy']:.4f}")
    print(f"  F1: {final_metrics['macro_f1']:.4f}")
    print(f"  Kappa: {final_metrics['kappa']:.4f}")
    
    # 每类F1
    print("\nPer-class F1 scores:")
    per_class_f1 = f1_score(labels, processed_preds, average=None)
    class_names = ['REM', 'N1', 'N2', 'N3', 'Wake']
    for i, (name, f1) in enumerate(zip(class_names, per_class_f1)):
        print(f"  {name}: {f1:.4f}")
    
    # 目标评估
    target_acc = 0.87
    target_kappa = 0.8
    target_f1 = 0.8
    
    print(f"\n🎯 Target Evaluation:")
    print(f"ACC: {final_metrics['accuracy']:.4f} (Target: {target_acc}, Gap: {target_acc - final_metrics['accuracy']:.4f})")
    print(f"Kappa: {final_metrics['kappa']:.4f} (Target: {target_kappa}, Gap: {target_kappa - final_metrics['kappa']:.4f})")
    print(f"F1: {final_metrics['macro_f1']:.4f} (Target: {target_f1}, Gap: {target_f1 - final_metrics['macro_f1']:.4f})")
    
    if (final_metrics['accuracy'] >= target_acc and 
        final_metrics['kappa'] >= target_kappa and 
        final_metrics['macro_f1'] >= target_f1):
        print("\n🎉 Congratulations! All targets achieved!")
    else:
        print("\n🔥 Close to target! Continue optimization...")
    
    # 保存结果
    results = {
        'individual_models': {
            model_name: {
                'accuracy': float(model_data['evaluator'].evaluate()['accuracy']),
                'macro_f1': float(model_data['evaluator'].evaluate()['macro_f1']),
                'kappa': float(model_data['evaluator'].evaluate()['kappa'])
            }
            for model_name, model_data in all_model_probs.items()
        },
        'ensemble_weights': best_weights,
        'ensemble_avg': {k: float(v) for k, v in metrics_avg.items() if isinstance(v, (int, float))},
        'ensemble_vote': {k: float(v) for k, v in metrics_vote.items() if isinstance(v, (int, float))},
        'final_results': {k: float(v) for k, v in final_metrics.items() if isinstance(v, (int, float))},
        'log_file': log_file
    }
    
    with open('../../configs/final_push_ensemble_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print("\n💾 Results saved!")


if __name__ == "__main__":
    main()