2025-08-12 03:04:03,638 - INFO - ================================================================================
2025-08-12 03:04:03,638 - INFO - 🚀 V8 BREAKTHROUGH - BRIDGING THE FINAL 6.41% GAP TO 90%
2025-08-12 03:04:03,639 - INFO - ================================================================================
2025-08-12 03:04:03,639 - INFO - Configuration: {
  "d_model": 224,
  "n_heads": 14,
  "n_layers": 5,
  "dropout": 0.12,
  "seq_len": 5,
  "batch_size": 40,
  "learning_rate": 0.00025,
  "weight_decay": 3e-05,
  "num_epochs": 100,
  "patience": 20
}
2025-08-12 03:04:03,639 - INFO - Strategy: Smart augmentation + N1 expert + TTA ensemble
2025-08-12 03:04:03,639 - INFO - Current baseline: 83.59% | Target: 90.00% | Gap: 6.41%
2025-08-12 03:04:03,670 - INFO - Device: cuda
2025-08-12 03:04:03,670 - INFO - Loading datasets...
2025-08-12 03:04:05,194 - INFO - 从 24 个文件加载了 25362 个epochs, 创建了 25266 个序列
2025-08-12 03:04:05,194 - INFO - 创建序列数据集: 25266个序列, 序列长度=5, 通道数=3, 总epochs=25362
2025-08-12 03:04:05,627 - INFO - 从 5 个文件加载了 4951 个epochs, 创建了 4931 个序列
2025-08-12 03:04:05,627 - INFO - 创建序列数据集: 4931个序列, 序列长度=5, 通道数=3, 总epochs=4951
2025-08-12 03:04:06,290 - INFO - 从 10 个文件加载了 11995 个epochs, 创建了 11955 个序列
2025-08-12 03:04:06,291 - INFO - 创建序列数据集: 11955个序列, 序列长度=5, 通道数=3, 总epochs=11995
2025-08-12 03:04:06,291 - INFO - Dataset sizes: Train=25266, Val=4931, Test=11955
2025-08-12 03:04:06,580 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=3,255,567, d_model=224, n_heads=14, n_layers=5
2025-08-12 03:04:06,803 - INFO - Model parameters: 3,281,334
2025-08-12 03:04:08,726 - INFO - STARTING BREAKTHROUGH TRAINING!
2025-08-12 03:04:08,726 - INFO - Mission: Bridge 6.41% gap to reach 90%

Epoch 1 - BREAKTHROUGH:   0%|          | 0/631 [00:00<?, ?it/s]
Epoch 1 - BREAKTHROUGH:   0%|          | 0/631 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_breakthrough.py", line 574, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_breakthrough.py", line 450, in main
    train_loss, train_acc, train_f1 = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_breakthrough.py", line 216, in train_epoch
    final_out, backbone_out, n1_expert_out = model(data)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_breakthrough.py", line 87, in forward
    n1_expert_out = self.n1_expert(center_features)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/container.py", line 240, in forward
    input = module(input)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/linear.py", line 125, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (40x2 and 224x112)
