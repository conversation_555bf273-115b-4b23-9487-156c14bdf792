"""
MAMBAFORMER V13简化版 - 基于V7优化
目标：ACC=87%, Kappa=0.8, MF1=80%

核心改进：
1. 使用正确的epoch级别评估
2. 优化的损失函数和权重
3. 更好的数据增强
4. HMM后处理
5. 学习率调度优化
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import logging
import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import (
    SequentialMAMBAFORMER_V2,
    SequentialFocalLoss,
    TemporalConsistencyLoss
)
from utils.sequence_dataset import create_sequence_dataloaders
from utils.epoch_level_evaluation import EpochLevelEvaluator, log_epoch_level_metrics
from utils.enhanced_metrics import get_comprehensive_metrics
from utils.hmm_postprocessing import RuleBasedPostProcessor


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"v13_simple_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file


class ImprovedFocalLoss(nn.Module):
    """改进的Focal Loss，动态调整gamma"""
    def __init__(self, gamma=2.0, alpha=None, reduction='mean'):
        super().__init__()
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = reduction
        
    def forward(self, input, target):
        ce_loss = nn.functional.cross_entropy(input, target, reduction='none')
        p = torch.exp(-ce_loss)
        loss = (1 - p) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if target.dim() > 1:
                target = target.view(-1)
            # Ensure alpha is a 1D tensor
            if self.alpha.dim() > 1:
                alpha = self.alpha.view(-1)
            else:
                alpha = self.alpha
            # Get alpha values for each target
            alpha_t = alpha.gather(0, target)
            loss = alpha_t * loss
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss


def create_auxiliary_labels(labels):
    """创建辅助任务标签"""
    # 睡眠vs清醒
    sleep_wake = (labels > 0).long()
    
    # 深睡眠检测
    deep_sleep = ((labels == 3) | (labels == 4)).long()
    
    # REM检测
    rem_detection = (labels == 0).long()
    
    return sleep_wake


class DataAugmentation:
    """数据增强"""
    def __init__(self, noise_level=0.05, shift_range=50):
        self.noise_level = noise_level
        self.shift_range = shift_range
        
    def __call__(self, data, labels):
        # 添加高斯噪声
        if np.random.random() < 0.5:
            noise = torch.randn_like(data) * self.noise_level
            data = data + noise
        
        # 时间平移
        if np.random.random() < 0.5:
            shift = np.random.randint(-self.shift_range, self.shift_range)
            data = torch.roll(data, shifts=shift, dims=-1)
        
        return data, labels


def train_epoch_v13(model, train_loader, criterion, temp_loss_fn, aux_criterion,
                   optimizer, device, epoch, config, data_aug):
    """V13训练函数"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} - Train')
    
    for batch_idx, (data, labels) in enumerate(pbar):
        data = data.to(device)
        labels = labels.to(device)
        
        # 数据增强
        if data_aug and epoch > 5:  # 前5个epoch不增强
            data, labels = data_aug(data, labels)
        
        optimizer.zero_grad()
        
        # 前向传播
        main_output, aux_output = model(data)
        
        # 计算损失
        # 检查输出形状
        if main_output.dim() == 3:  # [batch_size, seq_len, n_classes]
            batch_size, seq_len, n_classes = main_output.shape
            main_output = main_output.view(-1, n_classes)  # [batch_size*seq_len, n_classes]
        
        # labels: [batch_size, seq_len] -> [batch_size*seq_len]
        labels_flat = labels.view(-1)
        main_loss = criterion(main_output, labels_flat)
        
        # 辅助任务损失
        aux_labels = create_auxiliary_labels(labels)
        aux_loss = aux_criterion(
            aux_output.view(-1, 2),
            aux_labels.view(-1)
        )
        
        # 时序一致性损失
        temp_loss = temp_loss_fn(main_output)
        
        # 总损失 - 动态调整权重
        if epoch < 10:
            aux_weight = 0.2
            temp_weight = config['temp_loss_weight'] * 0.5
        else:
            aux_weight = 0.1
            temp_weight = config['temp_loss_weight']
        
        loss = main_loss + aux_weight * aux_loss + temp_weight * temp_loss
        
        # 反向传播
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        preds = torch.argmax(main_output, dim=-1)
        all_preds.extend(preds.cpu().numpy().flatten())
        all_labels.extend(labels.cpu().numpy().flatten())
        
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'main': f'{main_loss.item():.4f}',
            'aux': f'{aux_loss.item():.4f}',
            'temp': f'{temp_loss.item():.4f}'
        })
    
    # 计算指标
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    kappa = cohen_kappa_score(all_labels, all_preds)
    
    avg_loss = total_loss / len(train_loader)
    
    return avg_loss, acc, f1, kappa


def evaluate_epoch_level_v13(model, test_dataset, test_loader, device, seq_len=5):
    """正确的epoch级别评估"""
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=seq_len, n_classes=5)
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for batch_idx, (data, labels) in enumerate(tqdm(test_loader, desc="Evaluating")):
            data = data.to(device)
            labels = labels.to(device)
            
            # 获取模型输出
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            # 获取序列信息
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
                    else:
                        start_indices.append(seq_idx)
                else:
                    break
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    return evaluator.evaluate()


def train_v13(config, device):
    """V13训练主函数"""
    logging.info("\n" + "="*80)
    logging.info("🚀 开始训练 MAMBAFORMER V13简化版 - 突破性能瓶颈")
    logging.info(f"🎯 目标: ACC=87%, Kappa=0.8, MF1=80%")
    
    # 加载数据
    split_config_path = '../../configs/subject_aware_splits.json'
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    train_files = [os.path.join(data_dir, f) for f in splits['splits']['train']['files']]
    val_files = [os.path.join(data_dir, f) for f in splits['splits']['val']['files']]
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    # 创建数据加载器
    fold_info = {
        'train_files': train_files,
        'test_files': test_files
    }
    
    # 由于create_sequence_dataloaders只返回loaders，需要手动创建dataset
    from utils.sequence_dataset import SequenceSleepDataset
    
    train_dataset = SequenceSleepDataset(train_files, seq_len=config['seq_len'], use_channels=3)
    val_dataset = SequenceSleepDataset(val_files, seq_len=config['seq_len'], use_channels=3)
    test_dataset = SequenceSleepDataset(test_files, seq_len=config['seq_len'], use_channels=3)
    
    # 创建loaders
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], shuffle=True,
        num_workers=config['num_workers'], pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], shuffle=False,
        num_workers=config['num_workers'], pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], shuffle=False,
        num_workers=config['num_workers'], pin_memory=True
    )
    
    logging.info(f"📊 数据集大小: 训练={len(train_dataset)}, 验证={len(val_dataset)}, 测试={len(test_dataset)}")
    
    # 计算类别权重
    train_labels = []
    for _, labels in train_loader:
        train_labels.extend(labels.numpy().flatten())
    
    class_counts = np.bincount(train_labels, minlength=5)
    class_weights = len(train_labels) / (5 * class_counts + 1)
    class_weights = torch.tensor(class_weights, dtype=torch.float32).to(device)
    
    logging.info(f"类别分布: {class_counts}")
    logging.info(f"类别权重: {class_weights.cpu().numpy()}")
    
    # 创建模型
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"模型参数量: {total_params:,}")
    
    # 损失函数
    criterion = ImprovedFocalLoss(gamma=2.5, alpha=class_weights)
    aux_criterion = nn.CrossEntropyLoss()
    temp_loss_fn = TemporalConsistencyLoss(weight=1.0)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(), 
        lr=config['learning_rate'], 
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # 学习率调度器 - 余弦退火重启
    scheduler = CosineAnnealingWarmRestarts(
        optimizer, 
        T_0=15,  # 首次重启周期
        T_mult=2,  # 重启周期倍数
        eta_min=1e-6
    )
    
    # 数据增强
    data_aug = DataAugmentation(noise_level=0.05, shift_range=50)
    
    # 规则后处理器
    rule_processor = RuleBasedPostProcessor()
    
    logging.info("📋 V13核心策略:")
    logging.info("  • 基于V7的成功架构")
    logging.info("  • 正确的epoch级别评估")
    logging.info("  • 改进的Focal Loss (γ=2.5)")
    logging.info("  • 动态损失权重调整")
    logging.info("  • 余弦退火学习率")
    logging.info("  • 规则后处理平滑")
    
    # 训练循环
    best_val_metrics = {'macro_f1': 0, 'kappa': 0}
    best_model_state = None
    patience_counter = 0
    
    for epoch in range(1, config['num_epochs'] + 1):
        # 训练
        train_loss, train_acc, train_f1, train_kappa = train_epoch_v13(
            model, train_loader, criterion, temp_loss_fn, aux_criterion,
            optimizer, device, epoch, config, data_aug
        )
        
        # 验证（使用正确的epoch级别评估）
        val_metrics = evaluate_epoch_level_v13(
            model, val_dataset, val_loader, device, config['seq_len']
        )
        
        # 学习率调度
        scheduler.step()
        
        # 记录
        current_lr = optimizer.param_groups[0]['lr']
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        logging.info(f"Train - Loss: {train_loss:.4f}, Acc: {train_acc:.4f}, "
                    f"F1: {train_f1:.4f}, Kappa: {train_kappa:.4f}")
        logging.info(f"Val   - Acc: {val_metrics['accuracy']:.4f}, F1: {val_metrics['macro_f1']:.4f}, "
                    f"Kappa: {val_metrics['kappa']:.4f}")
        logging.info(f"Val REM F1: {val_metrics['per_class_metrics']['REM']['f1']:.4f}")
        logging.info(f"LR: {current_lr:.2e}")
        
        # 保存最佳模型
        val_score = val_metrics['macro_f1'] + 0.05 * val_metrics['per_class_metrics']['REM']['f1']
        best_score = best_val_metrics['macro_f1'] + 0.05 * best_val_metrics.get('rem_f1', 0)
        
        if val_score > best_score:
            best_val_metrics = val_metrics.copy()
            best_val_metrics['rem_f1'] = val_metrics['per_class_metrics']['REM']['f1']
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            logging.info(f"💾 新的最佳模型: F1={val_metrics['macro_f1']:.4f}, Kappa={val_metrics['kappa']:.4f}")
        else:
            patience_counter += 1
        
        # 早停
        if patience_counter >= config['patience']:
            logging.info(f"⏹️  早停: {config['patience']}轮未改善")
            break
        
        # 动态调整学习率
        if epoch % 30 == 0 and patience_counter > 10:
            for param_group in optimizer.param_groups:
                param_group['lr'] *= 0.5
            logging.info(f"降低学习率到: {optimizer.param_groups[0]['lr']:.2e}")
    
    # 测试评估
    if best_model_state:
        model.load_state_dict(best_model_state)
    
    test_metrics = evaluate_epoch_level_v13(
        model, test_dataset, test_loader, device, config['seq_len']
    )
    
    # 详细测试结果
    log_epoch_level_metrics(test_metrics, phase='Test V13', logger=logging)
    
    # 规则后处理
    logging.info("\n📊 应用规则后处理...")
    
    # 收集所有预测用于后处理
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for data, labels in test_loader:
            data = data.to(device)
            outputs, _ = model(data)
            preds = torch.argmax(outputs, dim=-1)
            all_preds.extend(preds.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
    
    # 应用规则后处理
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)
    smoothed_preds = rule_processor.smooth(all_preds)
    
    # 计算后处理后的指标
    pp_metrics = get_comprehensive_metrics(all_labels, smoothed_preds)
    
    logging.info(f"后处理结果:")
    logging.info(f"Accuracy: {pp_metrics['accuracy']:.4f}")
    logging.info(f"Macro F1: {pp_metrics['macro_f1']:.4f}")
    logging.info(f"Kappa: {pp_metrics['kappa']:.4f}")
    
    # 保存模型
    os.makedirs('../../checkpoints', exist_ok=True)
    torch.save(model.state_dict(), '../../checkpoints/v13_simple.pth')
    
    return test_metrics, pp_metrics


def main():
    # V13配置
    config = {
        'batch_size': 32,
        'seq_len': 5,
        'learning_rate': 3e-5,
        'weight_decay': 1e-4,
        'num_epochs': 100,
        'patience': 20,
        'd_model': 256,  # 增大模型容量
        'n_heads': 16,
        'n_layers': 6,   # 6层
        'dropout': 0.15,
        'temp_loss_weight': 0.12,
        'num_workers': 4
    }
    
    log_file = setup_logging()
    
    logging.info("🚀 MAMBAFORMER V13简化版 - 突破性能瓶颈")
    logging.info("=" * 80)
    logging.info("🎯 核心策略:")
    logging.info("  1. 基于V7架构优化")
    logging.info("  2. 正确的epoch级别评估")
    logging.info("  3. 改进的损失函数")
    logging.info("  4. 动态训练策略")
    logging.info("  5. 规则后处理")
    logging.info(f"📋 配置: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️  使用设备: {device}")
    
    # 训练
    test_metrics, pp_metrics = train_v13(config, device)
    
    # 结果对比
    logging.info("\n" + "="*80)
    logging.info("📊 V13简化版结果")
    logging.info("="*80)
    
    # 所有版本对比
    comparison_results = {
        'V7_Sequential': {'accuracy': 0.8459, 'macro_f1': 0.7788, 'kappa': 0.7911},
        'V8_Enhanced': {'accuracy': 0.8374, 'macro_f1': 0.7880, 'kappa': 0.7815},
        'V10_EEG_EOG': {'accuracy': 0.8450, 'macro_f1': 0.7896, 'kappa': 0.7897},
        'V11_Complete': {'accuracy': 0.845, 'macro_f1': 0.7919, 'kappa': 0.79},
        'V12_Deep': {'accuracy': 0.8385, 'macro_f1': 0.7813, 'kappa': 0.7785}
    }
    
    # 添加V13结果
    comparison_results['V13_Base'] = {
        'accuracy': test_metrics['accuracy'],
        'macro_f1': test_metrics['macro_f1'],
        'kappa': test_metrics['kappa']
    }
    
    comparison_results['V13_PostProc'] = {
        'accuracy': pp_metrics['accuracy'],
        'macro_f1': pp_metrics['macro_f1'],
        'kappa': pp_metrics['kappa']
    }
    
    logging.info("版本对比:")
    for version, metrics in comparison_results.items():
        logging.info(f"{version:>15} - Acc: {metrics['accuracy']:.4f}, "
                    f"F1: {metrics['macro_f1']:.4f}, Kappa: {metrics['kappa']:.4f}")
    
    # 目标评估
    target_acc = 0.87
    target_kappa = 0.8
    target_f1 = 0.8
    
    final_acc = pp_metrics['accuracy']
    final_kappa = pp_metrics['kappa']
    final_f1 = pp_metrics['macro_f1']
    
    logging.info(f"\n最终结果 vs 目标:")
    logging.info(f"Accuracy: {final_acc:.4f} (目标: {target_acc:.2f}, 差距: {target_acc - final_acc:.4f})")
    logging.info(f"Kappa:    {final_kappa:.4f} (目标: {target_kappa:.2f}, 差距: {target_kappa - final_kappa:.4f})")
    logging.info(f"Macro F1: {final_f1:.4f} (目标: {target_f1:.2f}, 差距: {target_f1 - final_f1:.4f})")
    
    if final_acc >= target_acc and final_kappa >= target_kappa and final_f1 >= target_f1:
        logging.info("🎉 恭喜！已达到所有目标！")
    else:
        logging.info("🔥 接近目标，需要进一步优化...")
    
    # 保存结果
    results = {
        'version': 'V13_Simple',
        'test_metrics': test_metrics,
        'postprocessed_metrics': pp_metrics,
        'comparison': comparison_results,
        'final_results': {
            'accuracy': float(final_acc),
            'kappa': float(final_kappa),
            'macro_f1': float(final_f1)
        },
        'target_gaps': {
            'acc_gap': float(target_acc - final_acc),
            'kappa_gap': float(target_kappa - final_kappa),
            'f1_gap': float(target_f1 - final_f1)
        },
        'config': config,
        'log_file': log_file
    }
    
    with open('../../configs/v13_simple_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=lambda x: float(x) if hasattr(x, 'item') else x)
    
    logging.info(f"\n💾 V13结果已保存")
    logging.info("🌟 V13简化版训练完成！")


if __name__ == "__main__":
    main()