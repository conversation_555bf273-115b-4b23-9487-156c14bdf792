#!/usr/bin/env python3
"""
日志清理脚本 - 智能清理失败和垃圾日志
"""
import os
import shutil
from pathlib import Path
from datetime import datetime, timedelta

def cleanup_logs():
    log_dir = Path("/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs")
    
    stats = {
        'removed_dirs': 0,
        'removed_files': 0,
        'space_freed': 0
    }
    
    print("="*60)
    print("🧹 开始清理日志...")
    print("="*60)
    
    # 1. 清理失败的训练日志（<10KB的training.log）
    for stage_dir in log_dir.glob("stage*_*"):
        if stage_dir.is_dir():
            training_log = stage_dir / "training.log"
            
            # 检查是否是失败的训练
            if training_log.exists():
                if training_log.stat().st_size < 10240:  # <10KB
                    stats['space_freed'] += get_dir_size(stage_dir)
                    shutil.rmtree(stage_dir)
                    stats['removed_dirs'] += 1
                    print(f"✗ 删除失败日志: {stage_dir.name}")
            elif not any(stage_dir.glob("*.pth")):  # 没有模型文件
                stats['space_freed'] += get_dir_size(stage_dir)
                shutil.rmtree(stage_dir)
                stats['removed_dirs'] += 1
                print(f"✗ 删除空目录: {stage_dir.name}")
    
    # 2. 清理单独的输出日志文件（<10KB）
    for log_file in log_dir.glob("*.log"):
        if log_file.stat().st_size < 10240:
            stats['space_freed'] += log_file.stat().st_size
            log_file.unlink()
            stats['removed_files'] += 1
            print(f"✗ 删除小日志: {log_file.name}")
    
    # 3. 清理特定的垃圾日志
    garbage_patterns = [
        "auto_pipeline.log",
        "stage4_output_*.log",
        "stage5_output_*.log"
    ]
    
    for pattern in garbage_patterns:
        for file in log_dir.glob(pattern):
            if file.exists():
                stats['space_freed'] += file.stat().st_size
                file.unlink()
                stats['removed_files'] += 1
                print(f"✗ 删除垃圾日志: {file.name}")
    
    # 4. 统计剩余的有效日志
    valid_logs = {}
    for stage in range(1, 6):
        stage_dirs = list(log_dir.glob(f"stage{stage}_*"))
        valid_dirs = []
        
        for d in stage_dirs:
            if d.is_dir():
                # 检查是否有有效内容
                has_model = any(d.glob("*.pth"))
                has_large_log = False
                
                training_log = d / "training.log"
                if training_log.exists() and training_log.stat().st_size > 10240:
                    has_large_log = True
                
                if has_model or has_large_log:
                    valid_dirs.append(d)
        
        valid_logs[f"Stage {stage}"] = valid_dirs
    
    # 打印结果
    print("\n" + "="*60)
    print("📊 清理结果:")
    print("-"*60)
    print(f"删除目录: {stats['removed_dirs']}")
    print(f"删除文件: {stats['removed_files']}")
    print(f"释放空间: {stats['space_freed'] / 1024 / 1024:.2f} MB")
    
    print("\n" + "="*60)
    print("✅ 保留的有效日志:")
    print("-"*60)
    
    for stage, dirs in valid_logs.items():
        if dirs:
            print(f"\n{stage} ({len(dirs)} 个):")
            for d in sorted(dirs)[-3:]:  # 只显示最新的3个
                size = get_dir_size(d) / 1024 / 1024
                print(f"  • {d.name} ({size:.1f} MB)")
    
    # 显示总体磁盘使用
    total_size = get_dir_size(log_dir) / 1024 / 1024 / 1024
    print("\n" + "="*60)
    print(f"📁 日志目录总大小: {total_size:.2f} GB")
    print("="*60)

def get_dir_size(path):
    """获取目录大小"""
    total = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            if os.path.exists(fp):
                total += os.path.getsize(fp)
    return total

if __name__ == "__main__":
    cleanup_logs()