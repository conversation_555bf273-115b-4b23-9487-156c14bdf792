#!/usr/bin/env python3
"""
V9 N1 SPECIALIST - 专门解决N1识别问题
基于84.44%的基础，专注于N1检测 (F1=0.488) 和深睡眠混淆
使用更小的模型 + 专门的N1检测分支 + 分层注意力机制
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix, precision_recall_fscore_support
import random
import math

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v9_n1_specialist_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 V9 N1 SPECIALIST - SOLVING N1 RECOGNITION")
    logging.info("="*80)
    
    return log_dir

class HierarchicalAttention(nn.Module):
    """分层注意力机制 - 基于spec改进：局部窗口 + 跨通道 + 自适应门控"""
    def __init__(self, d_model, n_heads, n_levels=3, window_size=32):
        super().__init__()
        self.n_levels = n_levels
        self.window_size = window_size
        self.d_model = d_model
        
        # 层内局部自注意力（窗口化）
        self.local_attentions = nn.ModuleList([
            nn.MultiheadAttention(d_model, max(1, n_heads // (2**i)), dropout=0.1, batch_first=True)
            for i in range(n_levels)
        ])
        
        # 跨通道注意力（类似spec中的跨模态）
        self.cross_channel_attn = nn.MultiheadAttention(d_model, n_heads // 2, dropout=0.1, batch_first=True)
        
        # 自适应模态门控 (g1*local + g2*cross_channel + g3*global)
        self.gate_generator = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 3)  # 3个门控权重
        )
        
        # 层级融合
        self.level_norms = nn.ModuleList([nn.LayerNorm(d_model) for _ in range(n_levels)])
        self.level_ffs = nn.ModuleList([
            nn.Sequential(
                nn.Linear(d_model, d_model * 2),
                nn.GELU(),
                nn.Dropout(0.1),
                nn.Linear(d_model * 2, d_model)
            ) for _ in range(n_levels)
        ])
        
        # 全局池化用于生成全局特征
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 门熵正则化系数
        self.gate_beta = 0.01
        
    def forward(self, x):
        batch_size, seq_len, d_model = x.shape
        level_outputs = []
        gate_entropies = []
        
        current = x
        
        for i in range(self.n_levels):
            # 1. 局部窗口自注意力
            if seq_len > self.window_size:
                # 分窗口处理
                local_outs = []
                for start in range(0, seq_len, self.window_size):
                    end = min(start + self.window_size, seq_len)
                    window = current[:, start:end, :]
                    window_out, _ = self.local_attentions[i](window, window, window)
                    local_outs.append(window_out)
                local_attn_out = torch.cat(local_outs, dim=1)
            else:
                local_attn_out, _ = self.local_attentions[i](current, current, current)
            
            # 2. 跨通道注意力（将不同时间步作为不同"通道"）
            # 重排形状以模拟跨通道
            cross_q = current.mean(dim=1, keepdim=True)  # [B, 1, D]
            cross_out, _ = self.cross_channel_attn(cross_q, current, current)
            cross_out = cross_out.expand(-1, seq_len, -1)  # [B, L, D]
            
            # 3. 全局特征
            global_feat = self.global_pool(current.transpose(1, 2)).transpose(1, 2)  # [B, 1, D]
            global_feat = global_feat.expand(-1, seq_len, -1)  # [B, L, D]
            
            # 4. 生成门控权重
            gate_input = current.mean(dim=1)  # [B, D]
            gates = F.sigmoid(self.gate_generator(gate_input))  # [B, 3]
            gates = F.softmax(gates, dim=-1)  # 归一化
            
            # 记录门熵（用于正则化）
            gate_entropy = -(gates * (gates + 1e-8).log()).sum(dim=-1).mean()
            gate_entropies.append(gate_entropy)
            
            # 5. 融合：g1*local + g2*cross + g3*global
            fused = (gates[:, 0:1].unsqueeze(1) * local_attn_out +
                    gates[:, 1:2].unsqueeze(1) * cross_out +
                    gates[:, 2:3].unsqueeze(1) * global_feat)
            
            # 6. 残差连接和层归一化
            current = self.level_norms[i](current + fused)
            
            # 7. 前馈
            ff_out = self.level_ffs[i](current)
            current = current + ff_out
            
            level_outputs.append(current)
        
        # 融合所有层级
        fused = sum(level_outputs) / len(level_outputs)
        
        # 计算门熵损失（用于正则化，避免退化）
        self.gate_entropy_loss = self.gate_beta * sum(gate_entropies) / len(gate_entropies)
        
        return fused

class TopDownAttention(nn.Module):
    """自顶向下注意力 - 基于spec的bias模式：粗分类引导细分类"""
    def __init__(self, d_model, n_classes):
        super().__init__()
        self.d_model = d_model
        self.n_classes = n_classes
        
        # 粗分类头：5类 -> 3类 (Wake, NREM, REM)
        self.coarse_classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, 3)  # Wake, NREM (N1+N2+N3), REM
        )
        
        # 阶段特定的偏置生成器
        # Wake -> 增强高频特征（类似EMG）
        # NREM -> 增强低频特征（深睡眠delta波）
        # REM -> 增强中频特征（眼动相关）
        self.stage_bias_generators = nn.ModuleList([
            nn.Linear(1, d_model),  # Wake bias
            nn.Linear(1, d_model),  # NREM bias
            nn.Linear(1, d_model),  # REM bias
        ])
        
        # 可学习的强度参数 alpha
        self.alpha = nn.Parameter(torch.tensor(0.5))
        
        # 阶段感知的注意力
        self.stage_attention = nn.MultiheadAttention(d_model, 8, dropout=0.1, batch_first=True)
        
        # N1特殊处理（N1属于NREM但需要特殊关注）
        self.n1_detector = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Linear(d_model // 4, 2)  # 是否N1
        )
        
        # 启用阈值
        self.tau_high = 0.6  # 置信度阈值
        
    def forward(self, x, force_enable=False):
        batch_size, seq_len, d_model = x.shape
        
        # 1. 提取全局特征用于粗分类
        global_feat = x.mean(dim=1)  # [B, D]
        
        # 2. 粗分类预测
        coarse_logits = self.coarse_classifier(global_feat)  # [B, 3]
        coarse_probs = F.softmax(coarse_logits, dim=-1)
        
        # 3. 计算置信度（1 - entropy）
        entropy = -(coarse_probs * (coarse_probs + 1e-8).log()).sum(dim=-1)
        confidence = 1 - entropy / math.log(3)  # 归一化熵
        
        # 4. 决定是否启用top-down
        enable_mask = (confidence > self.tau_high) | force_enable
        
        if not enable_mask.any():
            # 置信度太低，不启用
            return x
        
        # 5. 生成阶段特定的偏置
        # 根据粗分类概率加权组合偏置
        stage_biases = []
        for i in range(3):
            prob = coarse_probs[:, i:i+1]  # [B, 1]
            bias = self.stage_bias_generators[i](prob)  # [B, D]
            stage_biases.append(bias)
        
        # 加权组合
        weighted_bias = sum([
            coarse_probs[:, i:i+1].unsqueeze(1) * stage_biases[i].unsqueeze(1)
            for i in range(3)
        ])  # [B, 1, D]
        
        # 6. N1特殊检测
        n1_logits = self.n1_detector(global_feat)
        n1_prob = F.softmax(n1_logits, dim=-1)[:, 1]  # N1概率
        
        # 如果检测到N1，增强特定模式
        if (n1_prob > 0.3).any():
            # N1特征增强：介于Wake和N2之间的过渡特征
            n1_bias = (stage_biases[0] * 0.3 + stage_biases[1] * 0.7)  # Wake和NREM的混合
            weighted_bias = weighted_bias + n1_prob.unsqueeze(1).unsqueeze(2) * n1_bias.unsqueeze(1) * 0.5
        
        # 7. 应用偏置到注意力
        # 使用偏置调制注意力计算
        q = x + self.alpha * weighted_bias.expand(-1, seq_len, -1)
        attended, attn_weights = self.stage_attention(q, x, x)
        
        # 8. 残差连接，仅对高置信样本应用
        output = x.clone()
        for i in range(batch_size):
            if enable_mask[i]:
                output[i] = x[i] + attended[i]
        
        # 保存一些信息供损失函数使用
        self.coarse_probs = coarse_probs.detach()
        self.confidence = confidence.detach()
        self.n1_prob = n1_prob.detach()
        
        return output

class N1SpecialistModel(nn.Module):
    """N1专家模型 - 专门的N1检测分支 + 分层注意力"""
    def __init__(self, input_channels, n_classes, d_model, n_heads, n_layers, dropout, seq_len):
        super().__init__()
        
        # 主干网络 - 使用较小的模型
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # 分层注意力机制
        self.hierarchical_attention = HierarchicalAttention(d_model, n_heads, n_levels=3)
        
        # 自顶向下注意力
        self.top_down_attention = TopDownAttention(d_model, n_classes)
        
        # N1专门检测分支 - 独立的深度网络
        self.n1_specialist = nn.Sequential(
            # 第一层：特征提取 (使用d_model作为输入)
            nn.Linear(d_model, d_model * 2),
            nn.BatchNorm1d(d_model * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            # 第二层：特征细化
            nn.Linear(d_model * 2, d_model),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            
            # 第三层：N1特征学习
            nn.Linear(d_model, d_model // 2),
            nn.BatchNorm1d(d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            
            # 输出层：二分类（N1 vs 非N1）
            nn.Linear(d_model // 2, 2)  # 二分类输出
        )
        
        # N3/N2边界检测分支
        self.deep_sleep_branch = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 3)  # N2, N3, 其他
        )
        
        # 最终融合层
        self.fusion = nn.Sequential(
            nn.Linear(n_classes + 2 + 3, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 阶段转换检测器
        self.transition_detector = nn.Sequential(
            nn.Linear(d_model * seq_len, d_model),
            nn.ReLU(),
            nn.Linear(d_model, 5)  # 转换概率
        )
        
        # 投影层：将注意力特征投影到分类空间
        self.hier_proj = nn.Linear(d_model, n_classes)
        self.td_proj = nn.Linear(d_model, n_classes)
        
    def forward(self, x):
        batch_size = x.shape[0]
        
        # 主干网络
        backbone_out, sequence_features = self.backbone(x)
        
        # sequence_features 是隐藏特征 [B, seq_len, d_model]
        # backbone_out 是分类输出 [B, seq_len, n_classes]
        
        # 提取中心时间步
        if backbone_out.dim() == 3:
            center_out = backbone_out[:, backbone_out.shape[1]//2, :]
        else:
            center_out = backbone_out
            
        # 使用隐藏特征进行注意力处理
        if sequence_features is not None and sequence_features.dim() == 3:
            # 应用分层注意力到隐藏特征
            hier_features = self.hierarchical_attention(sequence_features)
            hier_center_feat = hier_features[:, hier_features.shape[1]//2, :]
            hier_center = self.hier_proj(hier_center_feat)  # 投影到分类空间
            
            # 应用自顶向下注意力到隐藏特征
            td_features = self.top_down_attention(sequence_features)
            td_center_feat = td_features[:, td_features.shape[1]//2, :]
            td_center = self.td_proj(td_center_feat)  # 投影到分类空间
        else:
            hier_center = center_out
            td_center = center_out
        
        # N1专门检测 - 使用中心特征
        n1_detection = self.n1_specialist(center_out)
        
        # 深睡眠边界检测
        deep_sleep_out = self.deep_sleep_branch(center_out)
        
        # 阶段转换检测
        if sequence_features is not None and sequence_features.dim() == 3:
            seq_flat = sequence_features.view(batch_size, -1)
            transition_probs = self.transition_detector(seq_flat)
        else:
            transition_probs = torch.zeros(batch_size, 5).to(x.device)
        
        # 融合所有信息
        # 将主干输出、N1检测、深睡眠检测结合
        combined_features = torch.cat([
            center_out,         # 主干输出
            n1_detection,       # N1专门检测
            deep_sleep_out      # 深睡眠检测
        ], dim=-1)
        
        # 最终预测
        final_out = self.fusion(combined_features)
        
        # N1增强：如果N1检测分支认为是N1，增强N1的输出
        n1_confidence = F.softmax(n1_detection, dim=-1)[:, 1]  # N1概率
        final_out[:, 1] = final_out[:, 1] + n1_confidence * 2.0  # 增强N1分数
        
        return final_out, center_out, hier_center, td_center, n1_detection, transition_probs

class N1SpecialistLoss(nn.Module):
    """N1专家损失函数 - 极度关注N1"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 极端N1权重
        self.class_weights = torch.tensor([4.0, 20.0, 1.0, 1.5, 3.0]).to(device)  # N1权重20
        self.gamma = 3.0  # 高focal loss gamma
        self.label_smoothing = 0.1
        
    def forward(self, final_out, center_out, hier_out, td_out, n1_detection, transition_probs, targets, epoch=0):
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        # 动态调整权重
        weights = self.class_weights.clone()
        if epoch > 10:
            # N1权重持续增长
            weights[1] = min(25.0, weights[1] + (epoch - 10) * 0.2)
        
        # 主损失 - Focal Loss
        ce_loss = F.cross_entropy(final_out, targets, weight=weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = ((1 - pt) ** self.gamma) * ce_loss
        
        # 辅助损失
        center_loss = F.cross_entropy(center_out, targets, weight=weights)
        hier_loss = F.cross_entropy(hier_out, targets, weight=weights)
        td_loss = F.cross_entropy(td_out, targets, weight=weights)
        
        # N1专门损失 - 二分类
        n1_targets = (targets == 1).long()  # N1为1，其他为0
        n1_loss = F.cross_entropy(n1_detection, n1_targets, weight=torch.tensor([1.0, 10.0]).to(self.device))
        
        # N1错误的超级惩罚
        n1_mask = (targets == 1).float()
        n1_pred_wrong = n1_mask * (final_out.argmax(dim=1) != 1).float()
        n1_penalty = n1_pred_wrong.sum() * 5.0  # 每个N1错误5倍惩罚
        
        # N3→N2混淆惩罚
        n3_mask = (targets == 3).float()
        n3_to_n2_pred = n3_mask * (final_out.argmax(dim=1) == 2).float()
        n3_penalty = n3_to_n2_pred.sum() * 2.0
        
        # 阶段转换损失
        transition_loss = F.cross_entropy(transition_probs, targets)
        
        # 总损失
        total_loss = (
            focal_loss.mean() +
            0.1 * center_loss +
            0.15 * hier_loss +
            0.15 * td_loss +
            0.3 * n1_loss +  # N1专门损失权重高
            0.1 * n1_penalty +
            0.05 * n3_penalty +
            0.05 * transition_loss
        )
        
        # 标签平滑
        if self.label_smoothing > 0:
            n_classes = final_out.size(-1)
            smooth_loss = -F.log_softmax(final_out, dim=-1).mean(dim=-1)
            total_loss = (1 - self.label_smoothing) * total_loss + self.label_smoothing * smooth_loss.mean()
        
        return total_loss

def targeted_augment(x, targets=None, epoch=0):
    """针对性增强 - 特别关注N1样本"""
    if random.random() > 0.6:
        return x
    
    # 如果是N1样本，更激进的增强
    if targets is not None:
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        n1_mask = (targets == 1)
        
        # 对N1样本进行更强的增强
        for idx in torch.where(n1_mask)[0]:
            if random.random() < 0.7:
                # 强噪声
                x[idx] = x[idx] + torch.randn_like(x[idx]) * 0.03
                
                # 幅度变化
                x[idx] = x[idx] * (0.7 + random.random() * 0.6)
                
                # 时间偏移
                shift = random.randint(-100, 100)
                x[idx] = torch.roll(x[idx], shifts=shift, dims=-1)
    
    # 一般增强
    if random.random() < 0.3:
        noise = torch.randn_like(x) * 0.01
        x = x + noise
    
    if random.random() < 0.3:
        shift = random.randint(-50, 50)
        x = torch.roll(x, shifts=shift, dims=-1)
    
    return x

def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    # N1统计
    n1_correct = 0
    n1_total = 0
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1} N1-SPECIALIST')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # 针对性增强
        data = targeted_augment(data, target, epoch)
        
        optimizer.zero_grad()
        
        # 前向传播
        final_out, center_out, hier_out, td_out, n1_detection, transition_probs = model(data)
        
        # 计算损失
        loss = criterion(final_out, center_out, hier_out, td_out, n1_detection, transition_probs, target, epoch)
        
        # L2正则化
        l2_lambda = 1e-5
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        preds = final_out.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
        
        # N1统计
        n1_mask = (target == 1)
        n1_total += n1_mask.sum().item()
        n1_correct += ((preds == 1) & n1_mask).sum().item()
        
        # 显示进度
        n1_acc = n1_correct / max(n1_total, 1)
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'N1_acc': f'{n1_acc:.3f}'
        })
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    # 计算每类F1
    class_f1 = f1_score(all_targets, all_preds, average=None)
    
    logging.info(f"  Train N1 F1: {class_f1[1]:.3f} (Target: >0.6)")
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate_with_tta(model, data_loader, device, n_tta=5):
    """评估函数 - 带TTA"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating with TTA'):
            data = data.to(device)
            
            # Test Time Augmentation
            predictions = []
            for i in range(n_tta):
                if i == 0:
                    data_aug = data
                elif i == 1:
                    data_aug = data + torch.randn_like(data) * 0.002
                elif i == 2:
                    shift = random.randint(-20, 20)
                    data_aug = torch.roll(data, shifts=shift, dims=-1)
                elif i == 3:
                    data_aug = data * (0.97 + random.random() * 0.06)
                else:
                    data_aug = data + torch.randn_like(data) * 0.003
                    shift = random.randint(-30, 30)
                    data_aug = torch.roll(data_aug, shifts=shift, dims=-1)
                
                final_out, center_out, hier_out, td_out, n1_detection, transition_probs = model(data_aug)
                
                # 组合多个输出
                combined = (0.5 * F.softmax(final_out, dim=-1) + 
                           0.2 * F.softmax(center_out, dim=-1) +
                           0.15 * F.softmax(hier_out, dim=-1) +
                           0.15 * F.softmax(td_out, dim=-1))
                
                # N1增强
                n1_conf = F.softmax(n1_detection, dim=-1)[:, 1]
                combined[:, 1] = combined[:, 1] * (1 + n1_conf * 0.5)
                
                predictions.append(combined)
            
            avg_probs = torch.stack(predictions).mean(dim=0)
            preds = avg_probs.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    return np.array(all_targets), np.array(all_preds)

def analyze_results(targets, predictions, class_names):
    """详细分析结果"""
    accuracy = accuracy_score(targets, predictions)
    f1 = f1_score(targets, predictions, average='macro')
    kappa = cohen_kappa_score(targets, predictions)
    
    precision, recall, f1_scores, support = precision_recall_fscore_support(
        targets, predictions, average=None
    )
    
    cm = confusion_matrix(targets, predictions)
    
    logging.info("\n" + "="*80)
    logging.info("📊 EVALUATION RESULTS")
    logging.info("="*80)
    
    logging.info(f"\n🎯 Overall Metrics:")
    logging.info(f"  Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    logging.info(f"  Macro F1: {f1:.4f}")
    logging.info(f"  Cohen's Kappa: {kappa:.4f}")
    
    logging.info(f"\n📈 Per-Class Metrics:")
    for i, class_name in enumerate(class_names):
        logging.info(f"{class_name:>6}: F1={f1_scores[i]:.3f}, Prec={precision[i]:.3f}, "
                    f"Recall={recall[i]:.3f}, Support={support[i]}")
    
    # N1专门分析
    n1_f1 = f1_scores[1]
    logging.info(f"\n🎯 N1 Analysis:")
    logging.info(f"  N1 F1 Score: {n1_f1:.3f} (Target: >0.6)")
    if n1_f1 < 0.6:
        logging.info(f"  ⚠️ N1 still weak, needs more improvement")
        n1_errors = cm[1].sum() - cm[1,1]
        n1_confused_with = [(class_names[j], cm[1,j]) for j in range(5) if j != 1]
        n1_confused_with.sort(key=lambda x: x[1], reverse=True)
        logging.info(f"  N1 confused with: {n1_confused_with[0][0]} ({n1_confused_with[0][1]} times)")
    else:
        logging.info(f"  ✅ N1 recognition improved!")
    
    # 深睡眠分析
    n3_to_n2 = cm[3, 2]
    logging.info(f"\n💤 Deep Sleep Analysis:")
    logging.info(f"  N3→N2 confusion: {n3_to_n2} errors")
    if n3_to_n2 > 200:
        logging.info(f"  ⚠️ Still significant N3→N2 confusion")
    
    # 与目标对比
    gap_to_90 = 0.90 - accuracy
    logging.info(f"\n🎯 Target Progress:")
    if accuracy >= 0.90:
        logging.info(f"  ✅ TARGET ACHIEVED! Accuracy: {accuracy:.4f}")
    else:
        logging.info(f"  Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
    
    return accuracy, f1, kappa, f1_scores, cm

def main():
    # 配置 - 更小的模型
    config = {
        'd_model': 192,  # 更小的模型
        'n_heads': 12,   
        'n_layers': 5,   # 更少的层
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 2e-4,
        'weight_decay': 1e-4,
        'num_epochs': 100,
        'patience': 20
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    logging.info("Strategy: Specialized N1 branch + Hierarchical Attention + Top-Down Attention")
    logging.info("Focus: N1 recognition (F1=0.488) and N3→N2 confusion")
    logging.info("Current best: 84.44% | Target: 90.00%")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割 - 使用proper eval的分割
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None  # 不截断
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = N1SpecialistModel(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = N1SpecialistLoss(device)
    optimizer = optim.AdamW(model.parameters(), 
                           lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'])
    
    # 学习率调度
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5
    )
    
    # 训练
    best_val_acc = 0
    best_val_metrics = {}
    patience_counter = 0
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    
    logging.info("STARTING N1 SPECIALIST TRAINING!")
    logging.info("Focus: Improve N1 F1 from 0.488 to >0.6")
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch
        )
        
        # 验证集评估
        val_targets, val_preds = evaluate_with_tta(model, val_loader, device, n_tta=3)
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = analyze_results(
            val_targets, val_preds, class_names
        )
        
        # 学习率调度
        scheduler.step(val_acc)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # 特别关注N1
        if val_class_f1[1] > 0.6:
            logging.info(f"  🎯 N1 F1 > 0.6! Progress!")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_val_metrics = {
                'epoch': epoch + 1,
                'accuracy': val_acc,
                'f1': val_f1,
                'kappa': val_kappa,
                'class_f1': val_class_f1.tolist(),
                'confusion_matrix': val_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_metrics': best_val_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Val Acc: {val_acc:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 加载最佳模型进行测试
    logging.info("\n" + "="*80)
    logging.info("Loading best model for final test evaluation...")
    checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 测试集评估
    logging.info("Final evaluation on TEST SET (with 5x TTA)...")
    test_targets, test_preds = evaluate_with_tta(model, test_loader, device, n_tta=5)
    test_acc, test_f1, test_kappa, test_class_f1, test_cm = analyze_results(
        test_targets, test_preds, class_names
    )
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS - N1 SPECIALIST")
    logging.info("="*80)
    logging.info(f"Best Validation Accuracy: {best_val_acc:.4f}")
    logging.info("-"*40)
    logging.info("FINAL TEST RESULTS:")
    logging.info(f"Test Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    logging.info(f"Test F1: {test_f1:.4f}")
    logging.info(f"Test Kappa: {test_kappa:.4f}")
    logging.info(f"Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
    
    # N1改进分析
    logging.info(f"\n🎯 N1 Improvement:")
    logging.info(f"  Previous N1 F1: 0.488")
    logging.info(f"  Current N1 F1: {test_class_f1[1]:.3f}")
    improvement = test_class_f1[1] - 0.488
    logging.info(f"  Improvement: {improvement:.3f} ({improvement/0.488*100:.1f}% relative)")
    
    # 与目标对比
    gap_to_90 = 0.90 - test_acc
    if test_acc >= 0.90:
        logging.info(f"\n✅ TARGET ACHIEVED! Accuracy: {test_acc:.4f}")
        logging.info("\n🎉🎉🎉 N1 SPECIALIST SUCCESS! ACHIEVED 90% TARGET! 🎉🎉🎉")
    else:
        logging.info(f"\nGap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
        logging.info(f"Progress from 84.44%: {test_acc - 0.8444:.4f}")
    
    # 保存结果
    final_results = {
        'best_val_metrics': best_val_metrics,
        'test_metrics': {
            'accuracy': test_acc,
            'f1': test_f1,
            'kappa': test_kappa,
            'class_f1': test_class_f1.tolist(),
            'confusion_matrix': test_cm.tolist()
        },
        'config': config,
        'n1_improvement': improvement
    }
    
    with open(os.path.join(log_dir, 'final_results.json'), 'w') as f:
        json.dump(final_results, f, indent=2)
    
    logging.info(f"\nResults saved to {log_dir}")

if __name__ == "__main__":
    main()