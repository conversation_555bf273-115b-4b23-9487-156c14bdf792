#!/usr/bin/env python3
"""
V14 OPTIMIZED - 优化版本
结合V12的快速训练和全数据使用
关键改进：
1. 渐进式batch size增长
2. 混合精度训练
3. 更高效的数据加载
4. 智能采样策略
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import random
from torch.cuda.amp import autocast, GradScaler

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v14_optimized_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 OPTIMIZED - EFFICIENT FULL DATA TRAINING")
    logging.info("="*80)
    
    return log_dir

class OptimizedModel(nn.Module):
    """优化模型 - 高效架构"""
    def __init__(self, input_channels, n_classes, d_model, n_heads, n_layers, dropout, seq_len):
        super().__init__()
        
        # 轻量主干
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # 高效N1分支
        self.n1_branch = nn.Sequential(
            nn.Linear(n_classes, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 动态融合
        self.fusion_gate = nn.Sequential(
            nn.Linear(n_classes * 2, n_classes),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # 主干输出
        main_out, _ = self.backbone(x)
        
        # 提取中心时间步
        if main_out.dim() == 3:
            center_out = main_out[:, main_out.shape[1]//2, :]
        else:
            center_out = main_out
        
        # N1分支
        n1_out = self.n1_branch(center_out)
        
        # 动态融合
        concat_feat = torch.cat([center_out, n1_out], dim=-1)
        gate = self.fusion_gate(concat_feat)
        
        # 加权输出
        final_out = gate * center_out + (1 - gate) * n1_out
        
        return final_out, n1_out

class AdaptiveLoss(nn.Module):
    """自适应损失函数"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 初始权重 - 特别关注N1和REM
        self.base_weights = torch.tensor([2.0, 4.0, 1.0, 1.2, 2.5]).to(device)
        self.gamma = 2.0
        self.label_smoothing = 0.1
        
    def forward(self, final_out, n1_out, targets, epoch=0):
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        # 动态权重调整
        weights = self.base_weights.clone()
        
        # 渐进式N1权重增强
        if epoch < 10:
            weights[1] = 4.0 + epoch * 0.3  # 逐步增加N1权重
        else:
            weights[1] = 7.0
        
        # 主损失 - Focal Loss
        ce_loss = F.cross_entropy(final_out, targets, weight=weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = ((1 - pt) ** self.gamma) * ce_loss
        
        # N1专门损失
        n1_loss = F.cross_entropy(n1_out, targets, weight=weights)
        
        # 总损失
        total_loss = focal_loss.mean() + 0.3 * n1_loss
        
        # 标签平滑
        if self.label_smoothing > 0 and self.training:
            n_classes = final_out.size(-1)
            smooth_loss = -F.log_softmax(final_out, dim=-1).mean(dim=-1)
            total_loss = (1 - self.label_smoothing) * total_loss + self.label_smoothing * smooth_loss.mean()
        
        return total_loss

def mixup_data(x, y, alpha=0.2):
    """Mixup数据增强"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).to(x.device)

    mixed_x = lam * x + (1 - lam) * x[index, :]
    y_a, y_b = y, y[index]
    
    return mixed_x, y_a, y_b, lam

def train_epoch(model, train_loader, criterion, optimizer, device, epoch, scaler):
    model.train()
    criterion.training = True
    total_loss = 0
    all_preds = []
    all_targets = []
    
    # 动态batch size
    current_batch_size = min(64 + epoch * 8, 128)  # 渐进增长
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # Mixup增强 (前期epochs)
        if epoch < 5 and random.random() < 0.3:
            data, target_a, target_b, lam = mixup_data(data, target)
            mixed = True
        else:
            mixed = False
        
        optimizer.zero_grad()
        
        # 混合精度训练
        with autocast():
            final_out, n1_out = model(data)
            
            if mixed:
                loss = lam * criterion(final_out, n1_out, target_a, epoch) + \
                       (1 - lam) * criterion(final_out, n1_out, target_b, epoch)
            else:
                loss = criterion(final_out, n1_out, target, epoch)
        
        # 反向传播
        scaler.scale(loss).backward()
        
        # 梯度裁剪
        scaler.unscale_(optimizer)
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        scaler.step(optimizer)
        scaler.update()
        
        total_loss += loss.item()
        
        # 收集预测
        if not mixed:
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            preds = final_out.argmax(dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.cpu().numpy())
        
        pbar.set_postfix({'loss': f'{loss.item():.4f}', 'batch': current_batch_size})
    
    if len(all_preds) > 0:
        accuracy = accuracy_score(all_targets, all_preds)
        f1 = f1_score(all_targets, all_preds, average='macro')
        class_f1 = f1_score(all_targets, all_preds, average=None)
    else:
        accuracy = f1 = 0
        class_f1 = np.zeros(5)
    
    return total_loss / len(train_loader), accuracy, f1, class_f1

def evaluate(model, data_loader, device):
    """快速评估"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            with autocast():
                final_out, n1_out = model(data)
            
            # 组合预测
            combined = 0.7 * F.softmax(final_out, dim=-1) + 0.3 * F.softmax(n1_out, dim=-1)
            preds = combined.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    # 优化配置
    config = {
        'd_model': 192,
        'n_heads': 12,
        'n_layers': 4,
        'dropout': 0.2,
        'seq_len': 5,
        'initial_batch_size': 64,
        'max_batch_size': 128,
        'learning_rate': 4e-4,
        'weight_decay': 1e-4,
        'num_epochs': 25,  # 较少epochs但更高效
        'patience': 8
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    logging.info("Strategy: Optimized training with full data")
    logging.info("Features: Mixed precision, progressive batch size, mixup augmentation")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载完整数据
    logging.info("Loading FULL datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None  # 完整数据
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器 - 初始batch size
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['initial_batch_size'],
        shuffle=True, num_workers=8, pin_memory=True,
        persistent_workers=True  # 保持workers活跃
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=128,
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=128,
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = OptimizedModel(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = AdaptiveLoss(device)
    optimizer = optim.AdamW(model.parameters(),
                           lr=config['learning_rate'],
                           weight_decay=config['weight_decay'])
    
    # 混合精度训练
    scaler = GradScaler()
    
    # 学习率调度 - Cosine退火
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=5, T_mult=2, eta_min=1e-6
    )
    
    # 训练
    best_val_acc = 0
    best_val_metrics = {}
    patience_counter = 0
    
    logging.info("STARTING OPTIMIZED TRAINING!")
    logging.info("Using mixed precision training and progressive batch size")
    
    for epoch in range(config['num_epochs']):
        # 动态调整batch size
        if epoch % 5 == 0 and epoch > 0:
            new_batch_size = min(config['initial_batch_size'] + epoch * 8, config['max_batch_size'])
            train_loader = torch.utils.data.DataLoader(
                train_dataset, batch_size=new_batch_size,
                shuffle=True, num_workers=8, pin_memory=True,
                persistent_workers=True
            )
            logging.info(f"Batch size increased to {new_batch_size}")
        
        # 训练
        train_loss, train_acc, train_f1, train_class_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch, scaler
        )
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(
            model, val_loader, device
        )
        
        # 学习率调度
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Train Class F1: W={train_class_f1[0]:.3f}, N1={train_class_f1[1]:.3f}, "
                    f"N2={train_class_f1[2]:.3f}, N3={train_class_f1[3]:.3f}, REM={train_class_f1[4]:.3f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # REM检查
        if val_class_f1[4] < 0.6:
            logging.warning(f"  ⚠️ REM F1 low: {val_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_val_metrics = {
                'epoch': epoch + 1,
                'accuracy': val_acc,
                'f1': val_f1,
                'kappa': val_kappa,
                'class_f1': val_class_f1.tolist(),
                'confusion_matrix': val_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_metrics': best_val_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Val Acc: {val_acc:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 加载最佳模型进行测试
    logging.info("\n" + "="*80)
    logging.info("Loading best model for final test...")
    checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 测试
    test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(
        model, test_loader, device
    )
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS - V14 OPTIMIZED")
    logging.info("="*80)
    logging.info(f"Best Validation Accuracy: {best_val_acc:.4f}")
    logging.info("-"*40)
    logging.info("FINAL TEST RESULTS:")
    logging.info(f"Test Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    logging.info(f"Test F1: {test_f1:.4f}")
    logging.info(f"Test Kappa: {test_kappa:.4f}")
    logging.info(f"Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
    
    # 混淆矩阵
    logging.info("\n🔄 Confusion Matrix:")
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    logging.info("       " + "  ".join([f"{name:>6}" for name in class_names]))
    for i, true_class in enumerate(class_names):
        row = test_cm[i]
        row_str = " ".join([f"{val:>6}" for val in row])
        logging.info(f"{true_class:>6} {row_str}")
    
    # 保存结果
    final_results = {
        'best_val_metrics': best_val_metrics,
        'test_metrics': {
            'accuracy': test_acc,
            'f1': test_f1,
            'kappa': test_kappa,
            'class_f1': test_class_f1.tolist(),
            'confusion_matrix': test_cm.tolist()
        },
        'config': config
    }
    
    with open(os.path.join(log_dir, 'final_results.json'), 'w') as f:
        json.dump(final_results, f, indent=2)
    
    logging.info(f"\nResults saved to {log_dir}")

if __name__ == "__main__":
    main()