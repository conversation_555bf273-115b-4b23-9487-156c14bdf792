#!/usr/bin/env python3
"""
V8 Fixed - 修复版本，正确提取特征
基于V8成功架构，修复特征提取问题
目标: 90% accuracy
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v8_fixed_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V8 Fixed - Enhanced Architecture for 90% Target")
    logging.info("="*80)
    
    return log_dir

class FeatureExtractorWrapper(nn.Module):
    """包装器，提取中间特征而非最终分类"""
    def __init__(self, backbone_model):
        super().__init__()
        self.backbone = backbone_model
        self.d_model = backbone_model.d_model
        
    def forward(self, x):
        """提取编码后的特征"""
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 重塑为 (batch*seq_len, channels, time_steps) 进行特征提取
        x_reshaped = x.view(batch_size * seq_len, time_steps, channels).transpose(1, 2)
        
        # 提取每个epoch的特征
        features = self.backbone.feature_extractor(x_reshaped)  # (batch*seq_len, d_model)
        
        # 重塑回序列形式
        features = features.view(batch_size, seq_len, self.d_model)
        
        # 添加位置编码
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.backbone.pos_encoder(features)
        features = features.transpose(0, 1)  # 转回 (batch, seq_len, d_model)
        
        # Transformer编码
        encoded_features = self.backbone.transformer_encoder(features)
        
        return encoded_features  # (batch, seq_len, d_model)

class V8FixedModel(nn.Module):
    """V8修复模型 - 多策略融合"""
    def __init__(self, config):
        super().__init__()
        
        # 创建原始backbone
        backbone = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len']
        )
        
        # 使用包装器提取特征
        self.feature_extractor = FeatureExtractorWrapper(backbone)
        
        # 主分类头
        self.main_head = nn.Sequential(
            nn.LayerNorm(config['d_model']),
            nn.Dropout(config['dropout']),
            nn.Linear(config['d_model'], config['d_model'] // 2),
            nn.ReLU(),
            nn.Dropout(config['dropout'] * 0.5),
            nn.Linear(config['d_model'] // 2, 5)
        )
        
        # 辅助分类头（专注困难类别）
        self.aux_head = nn.Sequential(
            nn.LayerNorm(config['d_model']),
            nn.Linear(config['d_model'], 64),
            nn.ReLU(),
            nn.Dropout(config['dropout']),
            nn.Linear(64, 5)
        )
        
        # 粗分类头（3类）
        self.coarse_head = nn.Sequential(
            nn.Linear(config['d_model'], 32),
            nn.ReLU(),
            nn.Linear(32, 3)  # Wake, NREM, REM
        )
        
        # 融合层
        self.fusion = nn.Sequential(
            nn.Linear(5 + 5 + 3, 16),
            nn.ReLU(),
            nn.Dropout(config['dropout'] * 0.5),
            nn.Linear(16, 5)
        )
        
        self.d_model = config['d_model']
        
    def forward(self, x):
        """前向传播"""
        # 提取特征 (batch, seq_len, d_model)
        features = self.feature_extractor(x)
        
        # 使用中间时间步的特征进行分类
        center_features = features[:, features.shape[1]//2, :]  # (batch, d_model)
        
        # 多个分类头
        main_logits = self.main_head(center_features)
        aux_logits = self.aux_head(center_features)
        coarse_logits = self.coarse_head(center_features)
        
        # 融合所有预测
        combined = torch.cat([main_logits, aux_logits, coarse_logits], dim=-1)
        final_logits = self.fusion(combined)
        
        # 残差连接
        final_logits = final_logits + main_logits * 0.5
        
        return final_logits, aux_logits, coarse_logits

class EnhancedFocalLoss(nn.Module):
    """增强的Focal Loss"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 类别权重 - 强调N1和REM
        self.class_weights = torch.tensor([3.0, 4.0, 1.0, 1.0, 3.0]).to(device)
        self.gamma = 2.0
        
    def forward(self, final_logits, aux_logits, coarse_logits, targets):
        """计算多任务损失"""
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        # 主损失 - Focal Loss
        ce_loss = F.cross_entropy(final_logits, targets, weight=self.class_weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = ((1 - pt) ** self.gamma) * ce_loss
        main_loss = focal_loss.mean()
        
        # 辅助损失
        aux_loss = F.cross_entropy(aux_logits, targets, weight=self.class_weights)
        
        # 粗分类损失
        coarse_targets = targets.clone()
        coarse_targets[targets == 1] = 1  # N1 -> NREM
        coarse_targets[targets == 2] = 1  # N2 -> NREM
        coarse_targets[targets == 3] = 1  # N3 -> NREM
        coarse_targets[targets == 0] = 0  # W -> Wake
        coarse_targets[targets == 4] = 2  # REM -> REM
        coarse_loss = F.cross_entropy(coarse_logits, coarse_targets)
        
        # 组合损失
        total_loss = main_loss + 0.3 * aux_loss + 0.2 * coarse_loss
        
        return total_loss

def mixup_data(x, y, alpha=0.2):
    """Mixup数据增强"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).to(x.device)

    mixed_x = lam * x + (1 - lam) * x[index]
    y_a, y_b = y, y[index]
    
    return mixed_x, y_a, y_b, lam

def train_epoch(model, train_loader, criterion, optimizer, device, use_mixup=True):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc='Training')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # Mixup增强（概率性）
        if use_mixup and random.random() < 0.3:
            data, target_a, target_b, lam = mixup_data(data, target)
            
            optimizer.zero_grad()
            final_logits, aux_logits, coarse_logits = model(data)
            
            # Mixup损失
            loss_a = criterion(final_logits, aux_logits, coarse_logits, target_a)
            loss_b = criterion(final_logits, aux_logits, coarse_logits, target_b)
            loss = lam * loss_a + (1 - lam) * loss_b
        else:
            optimizer.zero_grad()
            final_logits, aux_logits, coarse_logits = model(data)
            loss = criterion(final_logits, aux_logits, coarse_logits, target)
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        preds = final_logits.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy() if not use_mixup else target_a.cpu().numpy())
        
        pbar.set_postfix({'loss': loss.item()})
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate(model, data_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            final_logits, _, _ = model(data)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            preds = final_logits.argmax(dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    # V8 Fixed配置
    config = {
        'd_model': 256,
        'n_heads': 16,   
        'n_layers': 6,   
        'dropout': 0.12,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 2e-4,
        'weight_decay': 3e-5,
        'num_epochs': 60,
        'patience': 12
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = V8FixedModel(config).to(device)
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = EnhancedFocalLoss(device)
    optimizer = optim.AdamW(model.parameters(), 
                           lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'])
    
    # 学习率调度 - OneCycleLR
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'] * 10,
        epochs=config['num_epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.3,
        anneal_strategy='cos'
    )
    
    # 训练
    best_test_acc = 0
    best_test_metrics = {}
    patience_counter = 0
    
    logging.info("Starting V8 Fixed training...")
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device,
            use_mixup=(epoch > 3)  # 前几个epoch不用mixup
        )
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(model, val_loader, device)
        
        # 测试
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(model, test_loader, device)
        
        # 学习率调度
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Test: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_test_metrics = {
                'accuracy': test_acc,
                'f1': test_f1,
                'kappa': test_kappa,
                'class_f1': test_class_f1.tolist(),
                'confusion_matrix': test_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_metrics': best_test_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Test Acc: {test_acc:.4f})")
            
            if test_acc >= 0.87:
                logging.info("  🎯 Reached 87% target!")
                os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '🎯 V8 Fixed reached 87%: {test_acc:.4f}'")
            
            if test_acc >= 0.90:
                logging.info("  🎉 ACHIEVED 90% TARGET!")
                os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '✅ V8 Fixed ACHIEVED 90%: {test_acc:.4f}'")
                break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS (V8 Fixed)")
    logging.info("="*80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test F1: {best_test_metrics['f1']:.4f}")
    logging.info(f"Best Test Kappa: {best_test_metrics['kappa']:.4f}")
    
    if best_test_acc >= 0.90:
        logging.info("✅ SUCCESS: Achieved 90% target!")
    else:
        gap = 0.90 - best_test_acc
        logging.info(f"Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
    
    # 保存结果
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(best_test_metrics, f, indent=2)

if __name__ == "__main__":
    main()