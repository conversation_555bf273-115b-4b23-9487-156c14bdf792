2025-08-12 18:15:17,346 - INFO - ================================================================================
2025-08-12 18:15:17,346 - INFO - 🧪 TESTING BEST MODELS
2025-08-12 18:15:17,346 - INFO - ================================================================================
2025-08-12 18:15:17,375 - INFO - Device: cuda
2025-08-12 18:15:17,375 - INFO - Loading test dataset...
2025-08-12 18:15:17,857 - INFO - 从 10 个文件加载了 11995 个epochs, 创建了 11955 个序列
2025-08-12 18:15:17,857 - INFO - 创建序列数据集: 11955个序列, 序列长度=5, 通道数=3, 总epochs=11995
2025-08-12 18:15:17,858 - INFO - Test dataset size: 11955
2025-08-12 18:15:17,858 - INFO - 
============================================================
2025-08-12 18:15:17,858 - INFO - Testing model: ../logs/v8_proper_eval_20250812_155847
2025-08-12 18:15:17,858 - INFO - ============================================================
2025-08-12 18:15:18,158 - INFO - Model config: {'d_model': 256, 'n_heads': 16, 'n_layers': 6, 'dropout': 0.2, 'seq_len': 5, 'batch_size': 32, 'learning_rate': 0.0002, 'weight_decay': 0.0001, 'num_epochs': 100, 'patience': 20}
2025-08-12 18:15:18,215 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-12 18:15:18,241 - INFO - 
Validation metrics:
2025-08-12 18:15:18,241 - INFO -   Epoch: 27
2025-08-12 18:15:18,241 - INFO -   Val Acc: 0.8680
2025-08-12 18:15:18,241 - INFO -   Val F1: 0.8160
2025-08-12 18:15:18,241 - INFO -   Val Kappa: 0.8155
2025-08-12 18:15:18,241 - INFO - 
Evaluating on test set with TTA...
2025-08-12 18:15:28,185 - INFO - 
================================================================================
2025-08-12 18:15:28,185 - INFO - 📊 DETAILED TEST RESULTS
2025-08-12 18:15:28,185 - INFO - ================================================================================
2025-08-12 18:15:28,186 - INFO - 
🎯 Overall Metrics:
2025-08-12 18:15:28,186 - INFO -   Accuracy: 0.8444 (84.44%)
2025-08-12 18:15:28,186 - INFO -   Macro F1: 0.7879
2025-08-12 18:15:28,186 - INFO -   Cohen's Kappa: 0.7894
2025-08-12 18:15:28,186 - INFO - 
🔄 Confusion Matrix:
2025-08-12 18:15:28,186 - INFO -          Wake      N1      N2      N3     REM
2025-08-12 18:15:28,186 - INFO -   Wake   2465    214    180      9    157
2025-08-12 18:15:28,186 - INFO -     N1     59    319    140      3    123
2025-08-12 18:15:28,186 - INFO -     N2     32     84   4060     69    202
2025-08-12 18:15:28,186 - INFO -     N3      9      2    332   1516      1
2025-08-12 18:15:28,186 - INFO -    REM     28     44    172      0   1735
2025-08-12 18:15:28,186 - INFO - Total samples: 11955
2025-08-12 18:15:28,186 - INFO - 
📈 Per-Class Metrics:
2025-08-12 18:15:28,186 - INFO -   Wake: F1=0.878, Prec=0.951, Recall=0.815, Support=3025
2025-08-12 18:15:28,187 - INFO -     N1: F1=0.488, Prec=0.481, Recall=0.495, Support=644
2025-08-12 18:15:28,187 - INFO -     N2: F1=0.870, Prec=0.831, Recall=0.913, Support=4447
2025-08-12 18:15:28,187 - INFO -     N3: F1=0.877, Prec=0.949, Recall=0.815, Support=1860
2025-08-12 18:15:28,187 - INFO -    REM: F1=0.827, Prec=0.782, Recall=0.877, Support=1979
2025-08-12 18:15:28,187 - INFO - 
🔍 Error Analysis:
2025-08-12 18:15:28,187 - INFO - Total errors: 1860 (15.56%)
2025-08-12 18:15:28,187 - INFO - 
📊 Major Confusion Pairs:
2025-08-12 18:15:28,187 - INFO -   N3 → N2: 332 errors (17.8% of N3)
2025-08-12 18:15:28,187 - INFO -   Wake → N1: 214 errors (7.1% of Wake)
2025-08-12 18:15:28,187 - INFO -   N2 → REM: 202 errors (4.5% of N2)
2025-08-12 18:15:28,187 - INFO -   Wake → N2: 180 errors (6.0% of Wake)
2025-08-12 18:15:28,187 - INFO -   REM → N2: 172 errors (8.7% of REM)
2025-08-12 18:15:28,187 - INFO -   Wake → REM: 157 errors (5.2% of Wake)
2025-08-12 18:15:28,188 - INFO -   N1 → N2: 140 errors (21.7% of N1)
2025-08-12 18:15:28,188 - INFO -   N1 → REM: 123 errors (19.1% of N1)
2025-08-12 18:15:28,188 - INFO -   N2 → N1: 84 errors (1.9% of N2)
2025-08-12 18:15:28,188 - INFO -   N2 → N3: 69 errors (1.6% of N2)
2025-08-12 18:15:28,188 - INFO - 
📋 Performance Summary:
2025-08-12 18:15:28,188 - INFO -   Strongest class: Wake (F1=0.878)
2025-08-12 18:15:28,188 - INFO -   Weakest class: N1 (F1=0.488)
2025-08-12 18:15:28,188 - INFO -   F1 variance: 0.151
2025-08-12 18:15:28,188 - INFO - 
🎯 Target Comparison:
2025-08-12 18:15:28,188 - INFO -   Gap to 90%: 0.0556 (5.56%)
2025-08-12 18:15:28,189 - INFO -   Required improvement: 6.58% relative increase
2025-08-12 18:15:28,189 - INFO - 
💡 Improvement Suggestions:
2025-08-12 18:15:28,189 - INFO -   ⚠️ N1 stage critically weak (F1=0.488)
2025-08-12 18:15:28,189 - INFO -      N1 mainly confused with: N2 (140 times)
2025-08-12 18:15:28,189 - INFO -      Suggestions:
2025-08-12 18:15:28,189 - INFO -        - Increase N1 class weight to 15-20
2025-08-12 18:15:28,189 - INFO -        - Add specialized N1 detection branch
2025-08-12 18:15:28,189 - INFO -        - Use focal loss with higher gamma (3.0)
2025-08-12 18:15:28,189 - INFO -        - Augment N1 samples more aggressively
2025-08-12 18:15:28,189 - INFO -   ⚠️ High class imbalance (F1 std=0.151)
2025-08-12 18:15:28,189 - INFO -      Suggestions:
2025-08-12 18:15:28,189 - INFO -        - Use balanced sampling strategy
2025-08-12 18:15:28,189 - INFO -        - Implement SMOTE for minority classes
2025-08-12 18:15:28,189 - INFO -        - Add class-specific loss weights
2025-08-12 18:15:28,190 - INFO -   ⚠️ Major confusion: N3 → N2 (332 errors)
2025-08-12 18:15:28,190 - INFO -      Deep sleep confusion detected
2025-08-12 18:15:28,190 - INFO -      Suggestions:
2025-08-12 18:15:28,190 - INFO -        - Extract delta wave features explicitly
2025-08-12 18:15:28,190 - INFO -        - Use multi-scale temporal modeling
2025-08-12 18:15:28,190 - INFO - 
============================================================
2025-08-12 18:15:28,190 - INFO - Testing model: ../logs/v8_extreme_20250812_160347
2025-08-12 18:15:28,190 - INFO - ============================================================
2025-08-12 18:15:28,493 - INFO - Model config: {'d_model': 384, 'n_heads': 24, 'n_layers': 8, 'dropout': 0.12, 'seq_len': 5, 'batch_size': 24, 'learning_rate': 0.0001, 'weight_decay': 5e-05, 'num_epochs': 100, 'patience': 15}
2025-08-12 18:15:28,494 - INFO - Using Extreme model architecture
2025-08-12 18:15:28,494 - INFO - 
================================================================================
2025-08-12 18:15:28,494 - INFO - 📊 FINAL SUMMARY
2025-08-12 18:15:28,494 - INFO - ================================================================================
2025-08-12 18:15:28,494 - INFO - 
🏆 Best Model: v8_proper_eval_20250812_155847
2025-08-12 18:15:28,494 - INFO -   Accuracy: 0.8444
2025-08-12 18:15:28,494 - INFO -   F1: 0.7879
2025-08-12 18:15:28,494 - INFO -   Kappa: 0.7894
2025-08-12 18:15:28,494 - INFO - 
📊 Gap to 90%: 0.0556 (5.56%)
2025-08-12 18:15:28,494 - INFO - 
🎯 Next Steps to Reach 90%:
2025-08-12 18:15:28,494 - INFO -   1. Focus on N1 stage improvement (currently weakest)
2025-08-12 18:15:28,494 - INFO -   2. Implement specialized N1 detection module
2025-08-12 18:15:28,494 - INFO -   3. Use more aggressive data augmentation
2025-08-12 18:15:28,494 - INFO -   4. Try ensemble of multiple models
2025-08-12 18:15:28,494 - INFO -   5. Implement post-processing with temporal constraints
2025-08-12 18:15:28,494 - INFO - 
Results saved to: test_results_20250812_181517.log
