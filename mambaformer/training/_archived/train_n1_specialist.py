#!/usr/bin/env python3
"""
N1 Specialist Model Training
Focus on improving N1 stage classification (currently ~50% F1)
Strategy: Train specialized model for N1 vs others, then combine
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, WeightedRandomSampler
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class N1FocusedLoss(nn.Module):
    """Custom loss heavily weighted for N1 classification"""
    def __init__(self, n1_weight=15.0):
        super().__init__()
        # Extreme weight for N1 class
        self.class_weights = torch.tensor([2.0, n1_weight, 1.0, 1.5, 2.0])
        
    def forward(self, predictions, targets):
        batch_size, seq_len = targets.shape
        n_classes = predictions.shape[-1]
        
        # Flatten
        pred_flat = predictions.reshape(-1, n_classes)
        target_flat = targets.reshape(-1)
        
        # Focal loss component for hard examples
        ce_loss = F.cross_entropy(pred_flat, target_flat, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** 2 * ce_loss
        
        # Apply class weights
        weights = self.class_weights.to(predictions.device)[target_flat]
        weighted_loss = focal_loss * weights
        
        # Extra penalty for N1 misclassification
        n1_mask = (target_flat == 1).float()
        n1_penalty = n1_mask * ce_loss * 2.0
        
        total_loss = weighted_loss + n1_penalty
        
        return total_loss.mean()


class N1AugmentedDataset(Dataset):
    """Dataset with augmented N1 samples"""
    def __init__(self, original_dataset, augment_n1_factor=3):
        self.original_dataset = original_dataset
        self.augment_factor = augment_n1_factor
        
        # Find N1 samples
        self.n1_indices = []
        self.other_indices = []
        
        for idx in range(len(original_dataset)):
            _, label = original_dataset[idx]
            # Convert to numpy if tensor
            if torch.is_tensor(label):
                label = label.numpy()
            # Check if sequence contains N1 (label == 1)
            if np.any(label == 1):
                n1_ratio = np.mean(label == 1)
                if n1_ratio > 0.3:  # At least 30% N1 in sequence
                    self.n1_indices.append(idx)
                else:
                    self.other_indices.append(idx)
            else:
                self.other_indices.append(idx)
        
        logging.info(f"Found {len(self.n1_indices)} N1-rich sequences")
        
        # Create augmented index mapping
        self.index_map = []
        # Add all original samples
        self.index_map.extend(list(range(len(original_dataset))))
        # Add augmented N1 samples
        for _ in range(augment_n1_factor - 1):
            self.index_map.extend(self.n1_indices)
        
        logging.info(f"Total samples after augmentation: {len(self.index_map)}")
    
    def __len__(self):
        return len(self.index_map)
    
    def __getitem__(self, idx):
        original_idx = self.index_map[idx]
        data, label = self.original_dataset[original_idx]
        
        # Apply augmentation if this is a repeated N1 sample
        if idx >= len(self.original_dataset):
            # Add small noise
            noise_level = np.random.uniform(0.005, 0.02)
            noise = torch.randn_like(data) * noise_level
            data = data + noise
            
            # Random scaling
            scale = np.random.uniform(0.95, 1.05)
            data = data * scale
        
        return data, label


def train_n1_specialist():
    """Train a model specialized for N1 detection"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/n1_specialist_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 N1 SPECIALIST MODEL TRAINING")
    logging.info("Current N1 F1: ~50% → Target: >70%")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Configuration - smaller, focused model
    config = {
        'd_model': 320,
        'n_heads': 20,
        'n_layers': 8,
        'dropout': 0.25,
        'seq_len': 7,
        'batch_size': 16,
        'learning_rate': 2e-5,
        'num_epochs': 30,
        'patience': 10,
        'gradient_clip': 0.5,
        'weight_decay': 0.03,
        'n1_augment_factor': 3
    }
    
    logging.info("\n📋 Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Create datasets
    train_dataset_original = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    # Create augmented dataset with more N1 samples
    train_dataset = N1AugmentedDataset(
        train_dataset_original,
        augment_n1_factor=config['n1_augment_factor']
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # Try to load pretrained weights from best model
    try:
        pretrained_path = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v17_stable_20250811_004457/best_model.pth'
        if os.path.exists(pretrained_path):
            checkpoint = torch.load(pretrained_path, map_location=device, weights_only=False)
            # Partial loading
            model_dict = model.state_dict()
            pretrained_dict = {k: v for k, v in checkpoint['model_state_dict'].items() 
                             if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(pretrained_dict)
            model.load_state_dict(model_dict)
            logging.info(f"✅ Loaded {len(pretrained_dict)}/{len(model_dict)} layers from pretrained")
    except:
        logging.info("Starting from scratch")
    
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"\nModel Parameters: {total_params:,}")
    
    # Loss and optimizer
    criterion = N1FocusedLoss(n1_weight=15.0)
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=5,
        T_mult=2,
        eta_min=1e-6
    )
    
    # Training
    best_n1_f1 = 0
    best_overall_acc = 0
    patience_counter = 0
    
    logging.info("\n🏋️ Starting N1-Focused Training...")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        # Training
        model.train()
        train_loss = 0
        train_steps = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config["num_epochs"]}')
        for data, labels in pbar:
            data = data.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            outputs, _ = model(data)
            loss = criterion(outputs, labels)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
            optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / train_steps
        scheduler.step()
        
        # Validation
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(val_loader, desc='Validating', leave=False):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics['accuracy']
        n1_metrics = val_metrics['per_class_metrics'].get('N1', {})
        n1_f1 = n1_metrics.get('f1', 0)
        
        logging.info(f"\nEpoch {epoch+1}:")
        logging.info(f"  Loss: {avg_train_loss:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f}")
        logging.info(f"  N1 F1: {n1_f1:.4f} ⭐")
        logging.info(f"  N1 Precision: {n1_metrics.get('precision', 0):.4f}")
        logging.info(f"  N1 Recall: {n1_metrics.get('recall', 0):.4f}")
        
        # Save best model based on N1 F1
        if n1_f1 > best_n1_f1:
            best_n1_f1 = n1_f1
            best_overall_acc = val_acc
            patience_counter = 0
            
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'n1_f1': n1_f1,
                'val_acc': val_acc,
                'config': config
            }
            
            checkpoint_path = os.path.join(log_dir, 'best_n1_model.pth')
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"  💾 Saved best N1 model (F1: {n1_f1:.4f})")
            
            if n1_f1 >= 0.70:
                logging.info("  🎉 N1 F1 target achieved!")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info("\n⏹️ Early stopping")
                break
    
    # Final evaluation
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL TEST EVALUATION")
    logging.info("="*80)
    
    # Load best model
    checkpoint = torch.load(checkpoint_path, map_location=device, weights_only=False)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    test_evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
    test_evaluator.total_epochs = test_dataset.total_epochs
    
    with torch.no_grad():
        batch_start_idx = 0
        for data, labels in tqdm(test_loader, desc='Testing'):
            data = data.to(device)
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
            
            if start_indices:
                valid_batch_size = len(start_indices)
                test_evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    test_metrics = test_evaluator.evaluate()
    test_acc = test_metrics['accuracy']
    test_n1_f1 = test_metrics['per_class_metrics'].get('N1', {}).get('f1', 0)
    
    logging.info("\n🎯 TEST RESULTS:")
    logging.info(f"  Overall Accuracy: {test_acc:.4f}")
    logging.info(f"  N1 F1 Score: {test_n1_f1:.4f}")
    
    # Per-class performance
    logging.info("\n📊 Per-Class Performance:")
    for class_name in ['Wake', 'N1', 'N2', 'N3', 'REM']:
        metrics = test_metrics['per_class_metrics'].get(class_name, {})
        logging.info(f"{class_name}: F1={metrics.get('f1', 0)*100:.1f}%")
    
    # Save results
    results = {
        'timestamp': timestamp,
        'config': config,
        'best_n1_f1_val': float(best_n1_f1),
        'test_accuracy': float(test_acc),
        'test_n1_f1': float(test_n1_f1),
        'test_metrics': test_metrics
    }
    
    results_file = os.path.join(log_dir, 'results.json')
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\n💾 Results saved to {results_file}")
    
    if test_n1_f1 >= 0.65:
        logging.info("\n🎉 N1 Specialist Success! Ready for ensemble integration.")
    
    return results


if __name__ == "__main__":
    results = train_n1_specialist()