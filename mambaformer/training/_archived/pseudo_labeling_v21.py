#!/usr/bin/env python3
"""
V21 Pseudo-Labeling Semi-Supervised Learning
Use high-confidence predictions to augment training data
Target: 90% Accuracy, 82% Macro F1, 0.82 Kappa
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, ConcatDataset
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix
import warnings

warnings.filterwarnings("ignore")

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class PseudoLabelDataset(Dataset):
    """Dataset with pseudo-labeled samples"""

    def __init__(self, data, pseudo_labels, confidence_threshold=0.9):
        self.data = []
        self.labels = []

        # Filter by confidence
        for i in range(len(data)):
            max_conf = np.max(pseudo_labels[i])
            if max_conf >= confidence_threshold:
                self.data.append(data[i])
                # Convert probabilities to hard labels
                self.labels.append(np.argmax(pseudo_labels[i], axis=-1))

        logging.info(
            f"Created pseudo-label dataset with {len(self.data)} high-confidence samples"
        )

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        # 确保返回PyTorch张量
        data = self.data[idx]
        label = self.labels[idx]

        # 转换为正确的tensor类型
        if not isinstance(data, torch.Tensor):
            data = torch.FloatTensor(data)
        if not isinstance(label, torch.Tensor):
            label = torch.LongTensor(label)

        return data, label


class SelfTrainingLoss(nn.Module):
    """Loss function for self-training with confidence weighting"""

    def __init__(self):
        super().__init__()
        self.ce_loss = nn.CrossEntropyLoss(reduction="none")
        # Strong class weights for challenging classes
        self.class_weights = torch.tensor([3.0, 12.0, 1.0, 1.5, 2.5])

    def forward(self, predictions, targets, is_pseudo=False, confidence=None):
        batch_size, seq_len = targets.shape
        n_classes = predictions.shape[-1]

        # Flatten
        pred_flat = predictions.reshape(-1, n_classes)
        target_flat = targets.reshape(-1)

        # Calculate base loss
        loss = self.ce_loss(pred_flat, target_flat)

        # Apply class weights
        weights = self.class_weights.to(predictions.device)[target_flat]
        weighted_loss = loss * weights

        # If pseudo-labels, weight by confidence
        if is_pseudo and confidence is not None:
            conf_flat = confidence.reshape(-1)
            # Use confidence as weight (higher confidence = higher weight)
            weighted_loss = weighted_loss * conf_flat
            # Reduce weight of pseudo-labels overall
            weighted_loss = weighted_loss * 0.5

        return weighted_loss.mean()


def generate_pseudo_labels(model, unlabeled_loader, device, threshold=0.9):
    """Generate pseudo-labels for unlabeled data"""
    model.eval()

    pseudo_data = []
    pseudo_probs = []
    high_conf_count = 0
    total_count = 0

    logging.info("Generating pseudo-labels...")

    with torch.no_grad():
        for data, _ in unlabeled_loader:
            data = data.to(device)
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)

            # Check confidence
            max_probs, _ = torch.max(probs, dim=-1)

            for i in range(data.shape[0]):
                total_count += 1
                # Average confidence across sequence
                avg_conf = max_probs[i].mean().item()

                if avg_conf >= threshold:
                    pseudo_data.append(data[i].cpu())
                    pseudo_probs.append(probs[i].cpu().numpy())
                    high_conf_count += 1

    logging.info(
        f"Generated {high_conf_count}/{total_count} high-confidence pseudo-labels ({high_conf_count/total_count*100:.1f}%)"
    )

    return pseudo_data, pseudo_probs


def train_with_pseudo_labeling():
    """Main training with pseudo-labeling"""

    # Setup logging
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/pseudo_labeling_v21_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[logging.FileHandler(log_file), logging.StreamHandler()],
    )

    logging.info("=" * 80)
    logging.info("🚀 V21 PSEUDO-LABELING SEMI-SUPERVISED LEARNING")
    logging.info("=" * 80)
    logging.info("🎯 Targets: Accuracy ≥90%, Macro F1 ≥82%, Kappa ≥0.82")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"Device: {device}")

    # Configuration
    config = {
        "d_model": 384,
        "n_heads": 24,
        "n_layers": 7,
        "dropout": 0.2,
        "seq_len": 7,
        "batch_size": 20,
        "learning_rate": 4e-5,
        "num_epochs": 50,
        "patience": 20,
        "gradient_clip": 0.5,
        "weight_decay": 0.02,
        "pseudo_threshold": 0.90,  # High confidence threshold
        "pseudo_update_epochs": 5,  # Update pseudo-labels every N epochs
        "warmup_epochs": 3,
    }

    logging.info("\n📋 Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")

    # Data paths
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"

    # Original train/val/test split
    train_files = [
        "SC4181E0.npz",
        "SC4182E0.npz",
        "SC4161E0.npz",
        "SC4162E0.npz",
        "SC4131E0.npz",
        "SC4101E0.npz",
        "SC4102E0.npz",
        "SC4111E0.npz",
        "SC4112E0.npz",
        "SC4041E0.npz",
        "SC4042E0.npz",
        "SC4191E0.npz",
        "SC4192E0.npz",
        "SC4061E0.npz",
        "SC4062E0.npz",
        "SC4091E0.npz",
        "SC4092E0.npz",
        "SC4121E0.npz",
        "SC4122E0.npz",
        "SC4141E0.npz",
        "SC4142E0.npz",
        "SC4051E0.npz",
        "SC4052E0.npz",
    ]

    val_files = [
        "SC4021E0.npz",
        "SC4022E0.npz",
        "SC4031E0.npz",
        "SC4032E0.npz",
    ]

    test_files = ["SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"]

    # We'll generate pseudo-labels from the model's predictions
    unlabeled_files = [
        
        "SC4081E0.npz",
        "SC4082E0.npz",
        "SC4151E0.npz",
        "SC4152E0.npz",
        "SC4171E0.npz",
        "SC4172E0.npz",
        "SC4071E0.npz",
        "SC4072E0.npz",
    ]

    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    unlabeled_files = [os.path.join(data_dir, f) for f in unlabeled_files]

    logging.info(f"\n📂 Data Split:")
    logging.info(f"  Train: {len(train_files)} files")
    logging.info(f"  Val: {len(val_files)} files")
    logging.info(f"  Test: {len(test_files)} files")
    logging.info(f"  Unlabeled (for pseudo): {len(unlabeled_files)} files")

    # Create datasets
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        use_channels=3,
        max_samples_per_file=None,
    )

    val_dataset = SequenceSleepDataset(
        val_files, seq_len=config["seq_len"], use_channels=3, max_samples_per_file=None
    )

    test_dataset = SequenceSleepDataset(
        test_files, seq_len=config["seq_len"], use_channels=3, max_samples_per_file=None
    )

    unlabeled_dataset = SequenceSleepDataset(
        unlabeled_files,
        seq_len=config["seq_len"],
        use_channels=3,
        max_samples_per_file=None,
    )

    # Initial data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True,
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    unlabeled_loader = DataLoader(
        unlabeled_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    # Load pre-trained model (use best available model)
    logging.info("\n📦 Loading pre-trained model for initialization...")

    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)

    # Try to load V17 (best performing model) weights
    try:
        pretrained_path = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v17_stable_20250811_004457/best_model.pth"
        if os.path.exists(pretrained_path):
            # Load with different architecture - partial loading
            checkpoint = torch.load(
                pretrained_path, map_location=device, weights_only=False
            )
            # Try to load what matches
            model_dict = model.state_dict()
            pretrained_dict = {
                k: v
                for k, v in checkpoint["model_state_dict"].items()
                if k in model_dict and v.shape == model_dict[k].shape
            }
            model_dict.update(pretrained_dict)
            model.load_state_dict(model_dict)
            logging.info(
                f"✅ Loaded {len(pretrained_dict)}/{len(model_dict)} layers from pre-trained model"
            )
        else:
            logging.info("⚠️ No pre-trained model found, training from scratch")
    except Exception as e:
        logging.info(f"⚠️ Could not load pre-trained weights: {e}")

    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"\nModel Parameters: {total_params:,}")
    logging.info(f"Model Size: {total_params * 4 / 1024 / 1024:.2f} MB")

    # Loss and optimizer
    criterion = SelfTrainingLoss()
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"],
        betas=(0.9, 0.999),
    )

    scheduler = CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2, eta_min=1e-6)

    # Training variables
    best_val_acc = 0
    best_val_f1 = 0
    best_val_kappa = 0
    best_model_state = None
    patience_counter = 0
    training_history = []
    pseudo_dataset = None

    logging.info("\n🏋️ Starting Self-Training...")
    logging.info("=" * 80)

    for epoch in range(config["num_epochs"]):
        epoch_start = datetime.now()

        # Update pseudo-labels periodically
        if epoch > 0 and epoch % config["pseudo_update_epochs"] == 0:
            logging.info("\n🔄 Updating pseudo-labels...")
            pseudo_data, pseudo_probs = generate_pseudo_labels(
                model, unlabeled_loader, device, threshold=config["pseudo_threshold"]
            )

            if len(pseudo_data) > 0:
                # Create pseudo-label dataset
                pseudo_dataset = PseudoLabelDataset(
                    pseudo_data,
                    pseudo_probs,
                    confidence_threshold=config["pseudo_threshold"],
                )

                # Combine with original training data
                combined_dataset = ConcatDataset([train_dataset, pseudo_dataset])

                # Update train loader
                train_loader = DataLoader(
                    combined_dataset,
                    batch_size=config["batch_size"],
                    shuffle=True,
                    num_workers=4,
                    pin_memory=True,
                    drop_last=True,
                )

                logging.info(
                    f"Training with {len(combined_dataset)} samples (original + pseudo)"
                )

        # Warmup
        if epoch < config["warmup_epochs"]:
            warmup_lr = config["learning_rate"] * (epoch + 1) / config["warmup_epochs"]
            for param_group in optimizer.param_groups:
                param_group["lr"] = warmup_lr
            logging.info(f"Warmup LR: {warmup_lr:.2e}")

        # Training phase
        model.train()
        train_loss = 0
        train_steps = 0

        for batch_idx, (data, labels) in enumerate(train_loader):
            data = data.to(device)
            labels = labels.to(device)

            optimizer.zero_grad()
            outputs, _ = model(data)

            # Check if this batch contains pseudo-labels
            is_pseudo = batch_idx >= len(train_dataset) // config["batch_size"]

            if is_pseudo and pseudo_dataset is not None:
                # Calculate confidence for pseudo-labels
                with torch.no_grad():
                    probs = torch.softmax(outputs, dim=-1)
                    confidence, _ = torch.max(probs, dim=-1)
                loss = criterion(outputs, labels, is_pseudo=True, confidence=confidence)
            else:
                loss = criterion(outputs, labels, is_pseudo=False)

            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), config["gradient_clip"])
            optimizer.step()

            train_loss += loss.item()
            train_steps += 1

            # Track loss (no progress bar)

        avg_train_loss = train_loss / train_steps

        # Step scheduler
        if epoch >= config["warmup_epochs"]:
            scheduler.step()

        # Validation phase
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config["seq_len"], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs

        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in val_loader:
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)

                batch_size = data.shape[0]
                start_indices = []

                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info["start_epoch_idx"])

                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices,
                    )

                batch_start_idx += batch_size

        # Calculate metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics["accuracy"]
        val_f1 = val_metrics["macro_f1"]
        val_kappa = val_metrics["kappa"]

        # Get per-class performance
        n1_f1 = val_metrics["per_class_metrics"]["N1"]["f1"]
        rem_f1 = val_metrics["per_class_metrics"]["REM"]["f1"]

        epoch_time = (datetime.now() - epoch_start).total_seconds()

        # Log results
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Loss: {avg_train_loss:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f} ({val_acc*100:.2f}%)")
        logging.info(f"  Val F1: {val_f1:.4f} ({val_f1*100:.2f}%)")
        logging.info(f"  Val Kappa: {val_kappa:.4f}")
        logging.info(f"  N1 F1: {n1_f1:.4f} | REM F1: {rem_f1:.4f}")
        logging.info(f"  LR: {optimizer.param_groups[0]['lr']:.2e}")
        logging.info(f"  Time: {epoch_time:.1f}s")

        # Check targets
        targets_met = []
        if val_acc >= 0.90:
            targets_met.append("ACC")
        if val_f1 >= 0.82:
            targets_met.append("F1")
        if val_kappa >= 0.82:
            targets_met.append("KAPPA")

        if targets_met:
            logging.info(f"  🎯 Targets met: {', '.join(targets_met)}")

        training_history.append(
            {
                "epoch": epoch + 1,
                "train_loss": avg_train_loss,
                "val_acc": val_acc,
                "val_f1": val_f1,
                "val_kappa": val_kappa,
                "n1_f1": n1_f1,
                "rem_f1": rem_f1,
                "lr": optimizer.param_groups[0]["lr"],
                "time": epoch_time,
            }
        )

        # Save best model
        improved = False
        if val_acc > best_val_acc:
            improved = True
        elif val_acc == best_val_acc and val_f1 > best_val_f1:
            improved = True

        if improved:
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_kappa = val_kappa
            best_model_state = model.state_dict().copy()
            patience_counter = 0

            checkpoint = {
                "epoch": epoch + 1,
                "model_state_dict": best_model_state,
                "optimizer_state_dict": optimizer.state_dict(),
                "val_acc": val_acc,
                "val_f1": val_f1,
                "val_kappa": val_kappa,
                "config": config,
            }

            checkpoint_path = os.path.join(log_dir, "best_model.pth")
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"  💾 Saved best model")

            # Save if all targets met
            if len(targets_met) == 3:
                success_path = os.path.join(log_dir, f"SUCCESS_epoch{epoch+1}.pth")
                torch.save(checkpoint, success_path)
                logging.info(f"  🎉 SUCCESS! All targets achieved!")

                if epoch >= 15:
                    logging.info("  ✅ Stopping - targets achieved!")
                    break
        else:
            patience_counter += 1
            if patience_counter >= config["patience"]:
                logging.info(f"\n⏹️ Early stopping triggered")
                break

    # Final test evaluation
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        logging.info("\n" + "=" * 80)
        logging.info("📊 FINAL TEST EVALUATION")
        logging.info("=" * 80)

        model.eval()
        test_evaluator = EpochLevelEvaluator(seq_len=config["seq_len"], n_classes=5)
        test_evaluator.total_epochs = test_dataset.total_epochs

        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in test_loader:
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)

                batch_size = data.shape[0]
                start_indices = []

                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info["start_epoch_idx"])

                if start_indices:
                    valid_batch_size = len(start_indices)
                    test_evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices,
                    )

                batch_start_idx += batch_size

        # Get test metrics
        test_metrics = test_evaluator.evaluate()
        test_acc = test_metrics["accuracy"]
        test_f1 = test_metrics["macro_f1"]
        test_kappa = test_metrics["kappa"]

        # Get confusion matrix
        final_preds, final_labels, _ = test_evaluator.get_final_predictions()
        cm = confusion_matrix(final_labels, final_preds)

        # Results
        logging.info("\n🎯 FINAL TEST RESULTS:")
        logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
        logging.info(f"  Macro F1: {test_f1:.4f} ({test_f1*100:.2f}%)")
        logging.info(f"  Kappa: {test_kappa:.4f}")

        # Target achievement
        logging.info("\n🎯 Target Achievement (90% / 82% / 0.82):")
        all_achieved = True

        if test_acc >= 0.90:
            logging.info(f"  ✅ Accuracy: {test_acc:.4f} ≥ 0.90")
        else:
            logging.info(
                f"  ❌ Accuracy: {test_acc:.4f} < 0.90 (gap: {0.90-test_acc:.4f})"
            )
            all_achieved = False

        if test_f1 >= 0.82:
            logging.info(f"  ✅ Macro F1: {test_f1:.4f} ≥ 0.82")
        else:
            logging.info(
                f"  ❌ Macro F1: {test_f1:.4f} < 0.82 (gap: {0.82-test_f1:.4f})"
            )
            all_achieved = False

        if test_kappa >= 0.82:
            logging.info(f"  ✅ Kappa: {test_kappa:.4f} ≥ 0.82")
        else:
            logging.info(
                f"  ❌ Kappa: {test_kappa:.4f} < 0.82 (gap: {0.82-test_kappa:.4f})"
            )
            all_achieved = False

        # Per-class metrics
        logging.info("\n📊 Per-Class Performance:")
        class_names = ["Wake", "N1", "N2", "N3", "REM"]
        for class_name in class_names:
            metrics = test_metrics["per_class_metrics"][class_name]
            logging.info(
                f"{class_name}: P={metrics['precision']*100:.1f}%, R={metrics['recall']*100:.1f}%, F1={metrics['f1']*100:.1f}%"
            )

        # Save results
        final_results = {
            "timestamp": timestamp,
            "config": config,
            "best_val_acc": float(best_val_acc),
            "best_val_f1": float(best_val_f1),
            "best_val_kappa": float(best_val_kappa),
            "test_acc": float(test_acc),
            "test_f1": float(test_f1),
            "test_kappa": float(test_kappa),
            "confusion_matrix": cm.tolist(),
            "per_class_metrics": test_metrics["per_class_metrics"],
            "training_history": training_history,
            "targets_achieved": all_achieved,
            "pseudo_labeling_enabled": True,
        }

        # Convert numpy types
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            return obj

        final_results = convert_numpy(final_results)

        results_file = os.path.join(log_dir, "results.json")
        with open(results_file, "w") as f:
            json.dump(final_results, f, indent=2)

        logging.info(f"\n💾 Results saved to {results_file}")

        if all_achieved:
            logging.info("\n" + "=" * 80)
            logging.info("🎉🎉🎉 SUCCESS! ALL TARGETS ACHIEVED! 🎉🎉🎉")
            logging.info("V21 Pseudo-Labeling Model Ready for ICASSP 2026!")
            logging.info("=" * 80)
        else:
            logging.info("\n📈 Continue optimization with other methods...")

    return final_results


if __name__ == "__main__":
    results = train_with_pseudo_labeling()
