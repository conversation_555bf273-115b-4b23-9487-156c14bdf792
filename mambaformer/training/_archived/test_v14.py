#!/usr/bin/env python3
"""测试V14模型"""

import torch
import json
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
import torch.nn as nn
import torch.nn.functional as F
from torch.cuda.amp import autocast
from tqdm import tqdm

class OptimizedModel(nn.Module):
    def __init__(self, input_channels, n_classes, d_model, n_heads, n_layers, dropout, seq_len):
        super().__init__()
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        self.n1_branch = nn.Sequential(
            nn.Linear(n_classes, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, n_classes)
        )
        self.fusion_gate = nn.Sequential(
            nn.Linear(n_classes * 2, n_classes),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        main_out, _ = self.backbone(x)
        if main_out.dim() == 3:
            center_out = main_out[:, main_out.shape[1]//2, :]
        else:
            center_out = main_out
        n1_out = self.n1_branch(center_out)
        concat_feat = torch.cat([center_out, n1_out], dim=-1)
        gate = self.fusion_gate(concat_feat)
        final_out = gate * center_out + (1 - gate) * n1_out
        return final_out, n1_out

def evaluate(model, data_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            with autocast():
                final_out, n1_out = model(data)
            combined = 0.7 * F.softmax(final_out, dim=-1) + 0.3 * F.softmax(n1_out, dim=-1)
            preds = combined.argmax(dim=1)
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

# Load model
device = torch.device('cuda')
log_dir = '../logs/v14_optimized_20250812_204543'
checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'), weights_only=False)

model = OptimizedModel(
    input_channels=3,
    n_classes=5,
    d_model=192,
    n_heads=12,
    n_layers=4,
    dropout=0.2,
    seq_len=5
).to(device)
model.load_state_dict(checkpoint['model_state_dict'])

# Load test data
data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
test_files = [
    'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
    'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
    'SC4171E0.npz', 'SC4172E0.npz'
]

test_dataset = SequenceSleepDataset(
    [os.path.join(data_dir, f) for f in test_files],
    seq_len=5,
    max_samples_per_file=None
)

test_loader = torch.utils.data.DataLoader(
    test_dataset, batch_size=128,
    shuffle=False, num_workers=4, pin_memory=True
)

# Test
test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(model, test_loader, device)

print('='*80)
print('📊 FINAL TEST RESULTS - V14 OPTIMIZED')
print('='*80)
print(f'Best Val Acc from training: {checkpoint["val_metrics"]["accuracy"]:.4f}')
print('-'*40)
print(f'Test Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)')
print(f'Test F1: {test_f1:.4f}')
print(f'Test Kappa: {test_kappa:.4f}')
print(f'Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}')
print()
print('Confusion Matrix:')
class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
print('       ' + '  '.join([f'{name:>6}' for name in class_names]))
for i, true_class in enumerate(class_names):
    row = test_cm[i]
    row_str = ' '.join([f'{val:>6}' for val in row])
    print(f'{true_class:>6} {row_str}')