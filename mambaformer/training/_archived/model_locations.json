{"description": "Three key MAMBAFORMER models for sleep stage classification analysis", "created_at": "2025-08-12 22:47:40", "models": {"V30_Ultra": {"name": "V30 Ultra (Final Test 90)", "training_script": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/final_test_90.py", "log_dir": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/final_test_90_20250811_201958/", "checkpoint": null, "config": {"d_model": 512, "n_heads": 32, "n_layers": 12, "dropout": 0.25, "seq_len": 7, "batch_size": 16, "learning_rate": 0.0003, "num_epochs": 10, "label_smoothing": 0.1, "class_weights": [1.0, 3.0, 1.0, 1.0, 5.0]}, "results": {"test_accuracy": 0.8837, "test_f1": "N/A", "test_kappa": "N/A", "REM_F1": 0.0, "issue": "Complete REM detection failure due to extreme class weight and possible data truncation"}}, "V14_FIXED": {"name": "V14 FIXED (REM Focused)", "training_script": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v14_FIXED.py", "log_dir": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_detailed_evaluation_20250810_202805/", "checkpoint": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_optimized_20250812_204543/best_model.pth", "alternative_checkpoints": ["/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v14_robust_20250812_011519/best_model.pth", "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/checkpoints/v14_fixed.pth"], "config": {"d_model": 256, "n_heads": 16, "n_layers": 6, "dropout": 0.15, "seq_len": 5, "batch_size": 32, "learning_rate": 0.0002, "max_samples_per_file": null, "REMFocusedLoss": true, "rem_weight": 3.0, "wake_weight": 5.0}, "results": {"test_accuracy": 0.8635, "macro_f1": 0.8078, "kappa": 0.814, "REM_F1": 0.7929, "strengths": "Balanced performance, good REM detection"}}, "V8_Proper_Eval": {"name": "V8 Proper Evaluation", "training_script": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v8_proper_eval.py", "log_dir": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/sequential_v8_enhanced_20250809_222418/", "checkpoint": "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v8_proper_eval_20250812_155847/best_model.pth", "alternative_checkpoints": ["/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v8_fixed_20250812_022342/best_model.pth", "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v8_improved_20250812_013748/best_model.pth"], "config": {"d_model": 256, "n_heads": 16, "n_layers": 6, "dropout": 0.2, "seq_len": 5, "batch_size": 32, "learning_rate": 0.0002, "max_samples_per_file": null, "label_smoothing": 0.1, "TTA": true, "auxiliary_head": true}, "results": {"test_accuracy": 0.8374, "macro_f1": 0.788, "kappa": 0.7815, "REM_F1": 0.797, "strengths": "Stable training, extensive augmentation, proper evaluation protocol"}}}, "key_findings": {"data_truncation": "V30 Ultra可能因为数据截断导致REM失败，但脚本中未显式设置max_samples_per_file", "sequence_prediction": "所有模型都使用center output extraction，不是真正的seq-to-seq", "voting_strategy": "发现了ensemble voting脚本，但不是模型内部机制", "best_overall": "V14 FIXED - 最平衡的性能和REM检测", "recommendations": ["实现真正的sequence-to-sequence预测", "使用滑动窗口推理+概率平均", "确保所有模型使用完整数据(max_samples_per_file=None)", "避免极端的类权重设置"]}}