#!/usr/bin/env python3
"""
V8 N1-Focus - 专门优化N1类别性能
N1是最难分类的阶段（仅45-52% F1），需要特殊处理
目标: 提升N1性能，整体达到90%
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v8_n1_focus_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 V8 N1-Focus - Optimizing N1 Classification")
    logging.info("="*80)
    
    return log_dir

class N1FocusedLoss(nn.Module):
    """N1优化损失函数 - 特别关注N1类别"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 大幅提高N1权重
        # Classes: W=0, N1=1, N2=2, N3=3, REM=4
        self.class_weights = torch.tensor([3.0, 5.0, 1.0, 1.0, 2.5]).to(device)
        
        # Focal Loss参数 - 更高的gamma让模型关注困难样本
        self.gamma = 2.5
        self.alpha = 0.3  # 平衡因子
        
    def forward(self, inputs, targets, epoch=0):
        """
        动态调整的损失函数
        早期：平衡所有类别
        后期：重点优化N1
        """
        if inputs.dim() == 3:
            inputs = inputs[:, inputs.shape[1]//2, :]
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        # 动态权重调整（随epoch增加，逐渐增加N1权重）
        n1_weight_boost = min(1.0 + epoch * 0.1, 3.0)  # 最多3倍boost
        dynamic_weights = self.class_weights.clone()
        dynamic_weights[1] *= n1_weight_boost  # N1类别
        
        # 计算Focal Loss
        ce_loss = F.cross_entropy(inputs, targets, weight=dynamic_weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = ((1 - pt) ** self.gamma) * ce_loss
        
        # 额外的N1惩罚项
        n1_mask = (targets == 1).float()
        n1_penalty = 0.0
        
        if n1_mask.sum() > 0:
            # 获取N1样本的预测概率
            probs = F.softmax(inputs, dim=-1)
            n1_probs = probs[targets == 1, 1]  # N1类别的预测概率
            
            # 惩罚低置信度的N1预测
            confidence_penalty = (1 - n1_probs).mean()
            n1_penalty = 0.2 * confidence_penalty
        
        # 混淆惩罚：惩罚N1被误分类为N2或W
        confusion_penalty = 0.0
        if n1_mask.sum() > 0:
            n1_predictions = inputs[targets == 1].argmax(dim=-1)
            # 统计N1被误分类为N2(2)或W(0)的比例
            wrong_as_n2_or_w = ((n1_predictions == 2) | (n1_predictions == 0)).float().mean()
            confusion_penalty = 0.3 * wrong_as_n2_or_w
        
        total_loss = focal_loss.mean() + n1_penalty + confusion_penalty
        
        return total_loss

class N1AugmentedDataset(torch.utils.data.Dataset):
    """N1增强数据集 - 过采样N1样本"""
    def __init__(self, base_dataset, n1_oversample_ratio=2.0):
        self.base_dataset = base_dataset
        self.n1_oversample_ratio = n1_oversample_ratio
        
        # 找出包含N1的序列
        self.n1_indices = []
        self.other_indices = []
        
        for idx in range(len(base_dataset)):
            _, label = base_dataset[idx]
            if label.dim() > 0:
                center_label = label[label.shape[0]//2] if label.dim() > 0 else label
            else:
                center_label = label
                
            if center_label == 1:  # N1
                self.n1_indices.append(idx)
            else:
                self.other_indices.append(idx)
        
        # 创建增强索引列表
        self.augmented_indices = self.other_indices.copy()
        # 过采样N1
        n1_repeat = int(n1_oversample_ratio)
        for _ in range(n1_repeat):
            self.augmented_indices.extend(self.n1_indices)
        
        # 随机打乱
        random.shuffle(self.augmented_indices)
        
    def __len__(self):
        return len(self.augmented_indices)
    
    def __getitem__(self, idx):
        real_idx = self.augmented_indices[idx]
        data, label = self.base_dataset[real_idx]
        
        # 对N1样本应用额外的数据增强
        if label.dim() > 0:
            center_label = label[label.shape[0]//2] if label.dim() > 0 else label
        else:
            center_label = label
            
        if center_label == 1:  # N1样本
            # 应用更强的数据增强
            if random.random() < 0.5:
                # 时间偏移
                shift = random.randint(-100, 100)
                data = torch.roll(data, shifts=shift, dims=-1)
            
            if random.random() < 0.3:
                # 幅度变化
                scale = 0.7 + random.random() * 0.6  # 0.7-1.3
                data = data * scale
            
            if random.random() < 0.3:
                # 添加噪声
                noise = torch.randn_like(data) * 0.015
                data = data + noise
        
        return data, label

class N1ExpertModule(nn.Module):
    """N1专家模块 - 专门识别N1特征"""
    def __init__(self, d_model):
        super().__init__()
        
        # N1特征提取器
        self.n1_feature_extractor = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, d_model // 4),
            nn.ReLU(),
            nn.Linear(d_model // 4, 32)
        )
        
        # N1 vs Others二分类器
        self.n1_detector = nn.Sequential(
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 2)  # N1 or not N1
        )
        
        # N1细分类器（区分N1与易混淆类别）
        self.n1_refiner = nn.Sequential(
            nn.Linear(32 + d_model, 64),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(64, 3)  # N1, N2, W (最容易混淆的三个)
        )
        
    def forward(self, features):
        """
        features: (batch, d_model)
        """
        n1_features = self.n1_feature_extractor(features)
        n1_detection = self.n1_detector(n1_features)
        
        # 组合特征进行细分类
        combined = torch.cat([n1_features, features], dim=-1)
        n1_refined = self.n1_refiner(combined)
        
        return n1_detection, n1_refined, n1_features

class V8N1FocusModel(nn.Module):
    """V8 N1-Focus模型"""
    def __init__(self, config):
        super().__init__()
        
        # 主干网络
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len']
        )
        
        # N1专家模块
        self.n1_expert = N1ExpertModule(config['d_model'])
        
        # 主分类器
        self.main_classifier = nn.Sequential(
            nn.Linear(config['d_model'], config['d_model'] // 2),
            nn.LayerNorm(config['d_model'] // 2),
            nn.ReLU(),
            nn.Dropout(config['dropout']),
            nn.Linear(config['d_model'] // 2, 5)
        )
        
        # 融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(5 + 3 + 32, 32),  # main + n1_refined + n1_features
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(32, 5)
        )
        
        self.d_model = config['d_model']
        
    def forward(self, x):
        """前向传播"""
        # 提取特征
        output, aux = self.backbone(x)
        
        # 获取中心时间步特征
        if output.dim() == 3:
            # 从backbone获取特征而不是分类输出
            batch_size, seq_len, _ = output.shape
            # 需要从backbone获取中间特征，而不是最终输出
            # 我们需要修改为获取中间表示
            features = output.mean(dim=1)  # 平均池化获取特征 (batch, d_model)
        else:
            features = output
        
        # 主分类
        main_logits = self.main_classifier(features)
        
        # N1专家预测
        n1_detection, n1_refined, n1_features = self.n1_expert(features)
        
        # 融合所有信息
        fusion_input = torch.cat([
            main_logits, 
            n1_refined,
            n1_features
        ], dim=-1)
        
        final_logits = self.fusion_layer(fusion_input)
        
        # 增强N1预测
        # 如果N1检测器认为是N1，增加N1类别的logit
        n1_probs = F.softmax(n1_detection, dim=-1)[:, 1]  # N1概率
        n1_boost = n1_probs.unsqueeze(1) * 2.0  # 最多2倍boost
        final_logits[:, 1] = final_logits[:, 1] + n1_boost.squeeze()
        
        return final_logits, n1_detection, n1_refined

def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    n1_correct = 0
    n1_total = 0
    
    pbar = tqdm(train_loader, desc='Training')
    for batch_idx, (data, target) in enumerate(pbar):
        data, target = data.to(device), target.to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        final_logits, n1_detection, n1_refined = model(data)
        
        # 处理目标
        if target.dim() > 1:
            target_center = target[:, target.shape[1]//2]
        else:
            target_center = target
        
        # 主损失
        main_loss = criterion(final_logits, target_center, epoch=epoch)
        
        # N1检测辅助损失
        n1_binary_target = (target_center == 1).long()  # 1 if N1, 0 otherwise
        n1_detect_loss = F.cross_entropy(n1_detection, n1_binary_target)
        
        # N1细分类辅助损失（只对N1、N2、W样本）
        mask = (target_center == 0) | (target_center == 1) | (target_center == 2)
        if mask.sum() > 0:
            # 映射到0,1,2
            refined_target = target_center[mask].clone()
            refined_target[refined_target == 0] = 0  # W->0
            refined_target[refined_target == 1] = 1  # N1->1
            refined_target[refined_target == 2] = 2  # N2->2
            n1_refine_loss = F.cross_entropy(n1_refined[mask], refined_target)
        else:
            n1_refine_loss = 0.0
        
        # 总损失
        total = main_loss + 0.2 * n1_detect_loss + 0.1 * n1_refine_loss
        
        total.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += total.item()
        
        # 收集预测
        preds = final_logits.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target_center.cpu().numpy())
        
        # 统计N1性能
        n1_mask = target_center == 1
        if n1_mask.sum() > 0:
            n1_total += n1_mask.sum().item()
            n1_correct += ((preds == 1) & n1_mask).sum().item()
        
        pbar.set_postfix({'loss': total.item(), 
                         'N1_acc': f'{n1_correct/max(n1_total,1):.3f}'})
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    # 计算N1 F1
    class_f1 = f1_score(all_targets, all_preds, average=None)
    n1_f1 = class_f1[1] if len(class_f1) > 1 else 0
    
    logging.info(f"  N1 Performance: Acc={n1_correct/max(n1_total,1):.3f}, F1={n1_f1:.3f}")
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate(model, data_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            final_logits, _, _ = model(data)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            preds = final_logits.argmax(dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    # V8 N1-Focus配置
    config = {
        'd_model': 224,
        'n_heads': 14,   
        'n_layers': 6,   
        'dropout': 0.12,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 1.5e-4,
        'weight_decay': 3e-5,
        'num_epochs': 60,
        'patience': 15,
        'n1_oversample': 2.0  # N1过采样倍数
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_base = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    # 使用N1增强数据集
    train_dataset = N1AugmentedDataset(train_base, n1_oversample_ratio=config['n1_oversample'])
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)} (augmented), Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = V8N1FocusModel(config).to(device)
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = N1FocusedLoss(device)
    optimizer = optim.AdamW(model.parameters(), 
                           lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'])
    
    # 学习率调度
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    
    # 训练
    best_test_acc = 0
    best_test_metrics = {}
    patience_counter = 0
    
    logging.info("Starting V8 N1-Focus training...")
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch
        )
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(model, val_loader, device)
        
        # 测试
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(model, test_loader, device)
        
        # 学习率调度
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Test: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # 特别关注N1性能
        n1_f1 = test_class_f1[1]
        if n1_f1 > 0.6:
            logging.info(f"  🎯 N1 F1 reached {n1_f1:.3f}!")
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_test_metrics = {
                'accuracy': test_acc,
                'f1': test_f1,
                'kappa': test_kappa,
                'class_f1': test_class_f1.tolist(),
                'confusion_matrix': test_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_metrics': best_test_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Test Acc: {test_acc:.4f})")
            
            if test_acc >= 0.87:
                logging.info("  🎯 Reached 87% target!")
                os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '🎯 V8 N1-Focus reached 87%: {test_acc:.4f}'")
            
            if test_acc >= 0.90:
                logging.info("  🎉 ACHIEVED 90% TARGET!")
                os.system(f"cd /media/main/ypf/eeg/Cross-Modal-Transformer && git add -A && git commit -m '✅ V8 N1-Focus ACHIEVED 90%: {test_acc:.4f}'")
                break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS (V8 N1-Focus)")
    logging.info("="*80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test F1: {best_test_metrics['f1']:.4f}")
    logging.info(f"Best Test Kappa: {best_test_metrics['kappa']:.4f}")
    logging.info(f"N1 F1 Score: {best_test_metrics['class_f1'][1]:.4f}")
    
    if best_test_acc >= 0.90:
        logging.info("✅ SUCCESS: Achieved 90% target!")
    else:
        gap = 0.90 - best_test_acc
        logging.info(f"Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
    
    # 保存结果
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(best_test_metrics, f, indent=2)

if __name__ == "__main__":
    main()