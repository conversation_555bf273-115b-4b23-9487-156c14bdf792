#!/usr/bin/env python3
"""
V9 N1 SIMPLE - 简化版N1专家模型
专注于N1检测分支，暂时简化注意力机制
目标：改善N1 F1从0.488到>0.6
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix, precision_recall_fscore_support
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v9_n1_simple_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 V9 N1 SIMPLE - FOCUSED N1 DETECTION")
    logging.info("="*80)
    
    return log_dir

class N1SimpleModel(nn.Module):
    """简化的N1专家模型 - 专注于N1检测"""
    def __init__(self, input_channels, n_classes, d_model, n_heads, n_layers, dropout, seq_len):
        super().__init__()
        
        # 主干网络 - 较小的模型
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=input_channels,
            n_classes=n_classes,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            dropout=dropout,
            seq_len=seq_len
        )
        
        # N1专门检测分支 - 二分类
        self.n1_detector = nn.Sequential(
            nn.Linear(n_classes, d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 2)  # N1 vs 非N1
        )
        
        # N2/N3边界检测
        self.deep_sleep_detector = nn.Sequential(
            nn.Linear(n_classes, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 2)  # N2 vs N3
        )
        
        # 阶段转换检测
        self.transition_detector = nn.Sequential(
            nn.Linear(n_classes, d_model // 4),
            nn.ReLU(),
            nn.Linear(d_model // 4, n_classes)
        )
        
        # 融合所有检测结果
        self.fusion = nn.Sequential(
            nn.Linear(n_classes + 2 + 2 + n_classes, d_model),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, n_classes)
        )
        
        # N1增强权重
        self.n1_boost = nn.Parameter(torch.tensor(2.0))
        
    def forward(self, x):
        # 主干输出
        backbone_out, _ = self.backbone(x)
        
        # 提取中心时间步
        if backbone_out.dim() == 3:
            center_out = backbone_out[:, backbone_out.shape[1]//2, :]
        else:
            center_out = backbone_out
        
        # N1检测
        n1_detection = self.n1_detector(center_out)
        n1_prob = F.softmax(n1_detection, dim=-1)[:, 1:2]  # N1概率
        
        # 深睡眠检测
        deep_sleep = self.deep_sleep_detector(center_out)
        
        # 阶段转换
        transition = self.transition_detector(center_out)
        
        # 融合所有信息
        combined = torch.cat([center_out, n1_detection, deep_sleep, transition], dim=-1)
        final_out = self.fusion(combined)
        
        # N1增强：如果N1检测分支认为是N1，大幅增强N1输出
        final_out[:, 1] = final_out[:, 1] + self.n1_boost * n1_prob.squeeze()
        
        return final_out, center_out, n1_detection, deep_sleep

class N1FocusedLoss(nn.Module):
    """极度关注N1的损失函数"""
    def __init__(self, device='cuda'):
        super().__init__()
        self.device = device
        
        # 极端N1权重
        self.class_weights = torch.tensor([3.0, 15.0, 1.0, 1.5, 2.5]).to(device)
        self.gamma = 2.5  # Focal loss
        self.label_smoothing = 0.1
        
    def forward(self, final_out, center_out, n1_detection, deep_sleep, targets, epoch=0):
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        # 动态权重
        weights = self.class_weights.clone()
        if epoch > 5:
            # N1权重递增
            weights[1] = min(20.0, weights[1] + (epoch - 5) * 0.3)
        
        # 主损失 - Focal Loss
        ce_loss = F.cross_entropy(final_out, targets, weight=weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = ((1 - pt) ** self.gamma) * ce_loss
        
        # 辅助损失
        center_loss = F.cross_entropy(center_out, targets, weight=weights)
        
        # N1专门损失
        n1_targets = (targets == 1).long()
        n1_weight = torch.tensor([1.0, 8.0]).to(self.device)  # N1权重8倍
        n1_loss = F.cross_entropy(n1_detection, n1_targets, weight=n1_weight)
        
        # 深睡眠损失
        deep_targets = (targets == 3).long()  # N3为1，其他为0
        deep_loss = F.cross_entropy(deep_sleep, deep_targets)
        
        # N1错误的额外惩罚
        n1_mask = (targets == 1).float()
        n1_pred_wrong = n1_mask * (final_out.argmax(dim=1) != 1).float()
        n1_penalty = n1_pred_wrong.sum() * 3.0
        
        # N3→N2混淆惩罚
        n3_mask = (targets == 3).float()
        n3_to_n2 = n3_mask * (final_out.argmax(dim=1) == 2).float()
        n3_penalty = n3_to_n2.sum() * 1.5
        
        # 总损失
        total_loss = (
            focal_loss.mean() +
            0.2 * center_loss +
            0.4 * n1_loss +  # N1损失权重最高
            0.1 * deep_loss +
            0.1 * n1_penalty +
            0.05 * n3_penalty
        )
        
        # 标签平滑
        if self.label_smoothing > 0:
            n_classes = final_out.size(-1)
            smooth_loss = -F.log_softmax(final_out, dim=-1).mean(dim=-1)
            total_loss = (1 - self.label_smoothing) * total_loss + self.label_smoothing * smooth_loss.mean()
        
        return total_loss

def n1_focused_augment(x, targets=None, epoch=0):
    """针对N1的数据增强"""
    if random.random() > 0.5:
        return x
    
    # 对N1样本进行更强增强
    if targets is not None:
        if targets.dim() > 1:
            targets = targets[:, targets.shape[1]//2]
        
        n1_mask = (targets == 1)
        
        for idx in torch.where(n1_mask)[0]:
            if random.random() < 0.8:  # 80%概率增强N1
                # 添加噪声
                x[idx] = x[idx] + torch.randn_like(x[idx]) * 0.02
                
                # 幅度调整
                x[idx] = x[idx] * (0.8 + random.random() * 0.4)
                
                # 时间偏移
                shift = random.randint(-50, 50)
                x[idx] = torch.roll(x[idx], shifts=shift, dims=-1)
    
    # 一般增强
    if random.random() < 0.3:
        x = x + torch.randn_like(x) * 0.01
    
    return x

def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    n1_correct = 0
    n1_total = 0
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        target = target.to(device)
        
        # N1针对性增强
        data = n1_focused_augment(data, target, epoch)
        
        optimizer.zero_grad()
        
        # 前向传播
        final_out, center_out, n1_detection, deep_sleep = model(data)
        
        # 计算损失
        loss = criterion(final_out, center_out, n1_detection, deep_sleep, target, epoch)
        
        # L2正则化
        l2_lambda = 1e-5
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        preds = final_out.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
        
        # N1统计
        n1_mask = (target == 1)
        n1_total += n1_mask.sum().item()
        n1_correct += ((preds == 1) & n1_mask).sum().item()
        
        # 显示进度
        n1_acc = n1_correct / max(n1_total, 1)
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'N1_acc': f'{n1_acc:.3f}'
        })
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    # 计算每类F1
    class_f1 = f1_score(all_targets, all_preds, average=None)
    logging.info(f"  Train N1 F1: {class_f1[1]:.3f}")
    
    return total_loss / len(train_loader), accuracy, f1

def evaluate_with_tta(model, data_loader, device, n_tta=5):
    """评估函数 - 带TTA"""
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            # TTA
            predictions = []
            for i in range(n_tta):
                if i == 0:
                    data_aug = data
                elif i == 1:
                    data_aug = data + torch.randn_like(data) * 0.002
                elif i == 2:
                    shift = random.randint(-20, 20)
                    data_aug = torch.roll(data, shifts=shift, dims=-1)
                elif i == 3:
                    data_aug = data * (0.97 + random.random() * 0.06)
                else:
                    data_aug = data + torch.randn_like(data) * 0.003
                
                final_out, center_out, n1_detection, deep_sleep = model(data_aug)
                
                # 组合输出
                combined = 0.7 * F.softmax(final_out, dim=-1) + 0.3 * F.softmax(center_out, dim=-1)
                
                # N1增强
                n1_prob = F.softmax(n1_detection, dim=-1)[:, 1]
                combined[:, 1] = combined[:, 1] * (1 + n1_prob * 0.3)
                
                predictions.append(combined)
            
            avg_probs = torch.stack(predictions).mean(dim=0)
            preds = avg_probs.argmax(dim=1)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    return np.array(all_targets), np.array(all_preds)

def main():
    # 配置 - 更小更快的模型
    config = {
        'd_model': 192,
        'n_heads': 12,
        'n_layers': 5,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 2e-4,
        'weight_decay': 1e-4,
        'num_epochs': 100,
        'patience': 15
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    logging.info("Strategy: Simplified N1 Detection Branch")
    logging.info("Focus: Improve N1 F1 from 0.488 to >0.6")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz'
    ]
    
    val_files = [
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'],
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = N1SimpleModel(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = N1FocusedLoss(device)
    optimizer = optim.AdamW(model.parameters(),
                           lr=config['learning_rate'],
                           weight_decay=config['weight_decay'])
    
    # 学习率调度
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5
    )
    
    # 训练
    best_val_acc = 0
    best_val_n1_f1 = 0
    patience_counter = 0
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    
    logging.info("STARTING SIMPLIFIED N1 TRAINING!")
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch
        )
        
        # 验证
        val_targets, val_preds = evaluate_with_tta(model, val_loader, device, n_tta=3)
        
        val_acc = accuracy_score(val_targets, val_preds)
        val_f1 = f1_score(val_targets, val_preds, average='macro')
        val_kappa = cohen_kappa_score(val_targets, val_preds)
        val_class_f1 = f1_score(val_targets, val_preds, average=None)
        
        # 学习率调度
        scheduler.step(val_acc)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # 特别关注N1
        if val_class_f1[1] > 0.6:
            logging.info(f"  🎯 N1 F1 > 0.6! Progress!")
        
        # 保存最佳模型
        if val_acc > best_val_acc or val_class_f1[1] > best_val_n1_f1:
            if val_acc > best_val_acc:
                best_val_acc = val_acc
            if val_class_f1[1] > best_val_n1_f1:
                best_val_n1_f1 = val_class_f1[1]
            
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_n1_f1': val_class_f1[1],
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Val Acc: {val_acc:.4f}, N1 F1: {val_class_f1[1]:.3f})")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 加载最佳模型测试
    logging.info("\nLoading best model for test...")
    checkpoint = torch.load(os.path.join(log_dir, 'best_model.pth'))
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # 测试
    test_targets, test_preds = evaluate_with_tta(model, test_loader, device, n_tta=5)
    
    test_acc = accuracy_score(test_targets, test_preds)
    test_f1 = f1_score(test_targets, test_preds, average='macro')
    test_kappa = cohen_kappa_score(test_targets, test_preds)
    test_class_f1 = f1_score(test_targets, test_preds, average=None)
    test_cm = confusion_matrix(test_targets, test_preds)
    
    # 结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL TEST RESULTS")
    logging.info("="*80)
    logging.info(f"Test Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
    logging.info(f"Test F1: {test_f1:.4f}")
    logging.info(f"Test Kappa: {test_kappa:.4f}")
    logging.info(f"Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
    
    # N1分析
    logging.info(f"\n🎯 N1 Analysis:")
    logging.info(f"  Previous N1 F1: 0.488")
    logging.info(f"  Current N1 F1: {test_class_f1[1]:.3f}")
    improvement = test_class_f1[1] - 0.488
    logging.info(f"  Improvement: {improvement:.3f}")
    
    # 混淆矩阵
    logging.info(f"\n🔄 Confusion Matrix:")
    logging.info("       " + "  ".join([f"{name:>6}" for name in class_names]))
    for i, true_class in enumerate(class_names):
        row = test_cm[i]
        row_str = " ".join([f"{val:>6}" for val in row])
        logging.info(f"{true_class:>6} {row_str}")
    
    # 与90%目标对比
    gap_to_90 = 0.90 - test_acc
    if test_acc >= 0.90:
        logging.info(f"\n✅ TARGET ACHIEVED! Accuracy: {test_acc:.4f}")
    else:
        logging.info(f"\nGap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")

if __name__ == "__main__":
    main()