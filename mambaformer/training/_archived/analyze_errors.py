#!/usr/bin/env python3
"""
Error Analysis for 90% Target
Analyze misclassification patterns to identify improvement opportunities
Current: 87.32% → Target: 90%
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
from collections import defaultdict
import logging
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


def analyze_confusion_patterns(y_true, y_pred, class_names):
    """Analyze confusion matrix patterns"""
    cm = confusion_matrix(y_true, y_pred)
    
    # Normalize confusion matrix
    cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
    
    # Find most common misclassifications
    misclassifications = []
    for true_class in range(len(class_names)):
        for pred_class in range(len(class_names)):
            if true_class != pred_class:
                count = cm[true_class, pred_class]
                percentage = cm_normalized[true_class, pred_class] * 100
                if count > 10:  # Significant misclassifications
                    misclassifications.append({
                        'true': class_names[true_class],
                        'predicted': class_names[pred_class],
                        'count': int(count),
                        'percentage': float(percentage)
                    })
    
    # Sort by count
    misclassifications.sort(key=lambda x: x['count'], reverse=True)
    
    return cm, cm_normalized, misclassifications


def analyze_temporal_patterns(predictions, labels, seq_len=7):
    """Analyze temporal patterns in misclassifications"""
    
    # Analyze transitions
    transition_errors = defaultdict(int)
    
    for i in range(len(labels) - 1):
        curr_label = labels[i]
        next_label = labels[i + 1]
        curr_pred = predictions[i]
        next_pred = predictions[i + 1]
        
        # Check if transition is misclassified
        if curr_label != curr_pred or next_label != next_pred:
            transition_key = f"{curr_label}->{next_label}"
            transition_errors[transition_key] += 1
    
    # Sort by frequency
    sorted_transitions = sorted(transition_errors.items(), key=lambda x: x[1], reverse=True)
    
    return sorted_transitions[:10]  # Top 10 problematic transitions


def analyze_confidence_patterns(probabilities, labels, predictions):
    """Analyze confidence patterns in correct vs incorrect predictions"""
    
    correct_mask = predictions == labels
    incorrect_mask = ~correct_mask
    
    # Get max probabilities (confidence)
    confidences = np.max(probabilities, axis=1)
    
    correct_confidences = confidences[correct_mask]
    incorrect_confidences = confidences[incorrect_mask]
    
    stats = {
        'correct_mean_confidence': float(np.mean(correct_confidences)),
        'correct_std_confidence': float(np.std(correct_confidences)),
        'incorrect_mean_confidence': float(np.mean(incorrect_confidences)),
        'incorrect_std_confidence': float(np.std(incorrect_confidences)),
        'low_confidence_errors': int(np.sum(incorrect_confidences < 0.5)),
        'high_confidence_errors': int(np.sum(incorrect_confidences > 0.8))
    }
    
    return stats, correct_confidences, incorrect_confidences


def identify_hard_samples(probabilities, labels, predictions):
    """Identify hardest samples to classify"""
    
    hard_samples = []
    
    for i in range(len(labels)):
        if predictions[i] != labels[i]:
            confidence = np.max(probabilities[i])
            entropy = -np.sum(probabilities[i] * np.log(probabilities[i] + 1e-8))
            
            hard_samples.append({
                'index': i,
                'true_label': int(labels[i]),
                'predicted_label': int(predictions[i]),
                'confidence': float(confidence),
                'entropy': float(entropy),
                'prob_distribution': probabilities[i].tolist()
            })
    
    # Sort by entropy (higher entropy = more uncertain)
    hard_samples.sort(key=lambda x: x['entropy'], reverse=True)
    
    return hard_samples[:100]  # Top 100 hardest samples


def analyze_class_specific_errors(y_true, y_pred, probabilities, class_names):
    """Analyze errors for each class"""
    
    class_analysis = {}
    
    for class_idx, class_name in enumerate(class_names):
        class_mask = y_true == class_idx
        
        if np.sum(class_mask) == 0:
            continue
        
        class_predictions = y_pred[class_mask]
        class_probs = probabilities[class_mask]
        class_labels = y_true[class_mask]
        
        # Calculate metrics
        accuracy = np.sum(class_predictions == class_labels) / len(class_labels)
        
        # Common confusions
        confusions = defaultdict(int)
        for pred in class_predictions[class_predictions != class_labels]:
            confusions[class_names[pred]] += 1
        
        # Confidence analysis
        correct_mask_class = class_predictions == class_labels
        if np.sum(correct_mask_class) > 0:
            correct_conf = np.mean(np.max(class_probs[correct_mask_class], axis=1))
        else:
            correct_conf = 0
        
        if np.sum(~correct_mask_class) > 0:
            incorrect_conf = np.mean(np.max(class_probs[~correct_mask_class], axis=1))
        else:
            incorrect_conf = 0
        
        class_analysis[class_name] = {
            'accuracy': float(accuracy),
            'total_samples': int(np.sum(class_mask)),
            'errors': int(np.sum(class_predictions != class_labels)),
            'common_confusions': dict(confusions),
            'avg_correct_confidence': float(correct_conf),
            'avg_incorrect_confidence': float(incorrect_conf)
        }
    
    return class_analysis


def generate_improvement_strategies(analysis_results):
    """Generate specific improvement strategies based on error analysis"""
    
    strategies = []
    
    # Check N1 performance
    if 'class_analysis' in analysis_results:
        n1_acc = analysis_results['class_analysis'].get('N1', {}).get('accuracy', 1.0)
        if n1_acc < 0.60:
            strategies.append({
                'priority': 'HIGH',
                'issue': f"N1 accuracy only {n1_acc:.1%}",
                'strategy': "Focus on N1-specific features and augmentation",
                'potential_gain': "1-1.5% overall accuracy"
            })
    
    # Check transition errors
    if 'temporal_patterns' in analysis_results:
        top_transition = analysis_results['temporal_patterns'][0] if analysis_results['temporal_patterns'] else None
        if top_transition and top_transition[1] > 100:
            strategies.append({
                'priority': 'MEDIUM',
                'issue': f"High transition errors: {top_transition[0]}",
                'strategy': "Implement transition-aware loss or features",
                'potential_gain': "0.5-1% overall accuracy"
            })
    
    # Check confidence patterns
    if 'confidence_stats' in analysis_results:
        high_conf_errors = analysis_results['confidence_stats'].get('high_confidence_errors', 0)
        if high_conf_errors > 500:
            strategies.append({
                'priority': 'HIGH',
                'issue': f"{high_conf_errors} high-confidence errors",
                'strategy': "Implement confidence calibration",
                'potential_gain': "0.3-0.5% overall accuracy"
            })
    
    # Check specific confusions
    if 'top_misclassifications' in analysis_results:
        top_confusion = analysis_results['top_misclassifications'][0] if analysis_results['top_misclassifications'] else None
        if top_confusion and top_confusion['percentage'] > 20:
            strategies.append({
                'priority': 'MEDIUM',
                'issue': f"{top_confusion['true']} confused as {top_confusion['predicted']} ({top_confusion['percentage']:.1f}%)",
                'strategy': f"Create specialized features for {top_confusion['true']} vs {top_confusion['predicted']}",
                'potential_gain': "0.3-0.7% overall accuracy"
            })
    
    return strategies


def perform_error_analysis():
    """Main error analysis function"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/error_analysis_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'analysis.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🔍 ERROR ANALYSIS FOR 90% TARGET")
    logging.info("Current: 87.32% → Finding path to 90%")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Best model configuration (Grid Search winner)
    model_config = {
        'name': 'GridSearch_Best',
        'models': [
            {
                'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v17_stable_20250811_004457/best_model.pth',
                'd_model': 288,
                'n_heads': 18,
                'n_layers': 6,
                'seq_len': 5,
                'weight': 0.55
            },
            {
                'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v18_fixed_20250811_014911/best_model.pth',
                'd_model': 384,
                'n_heads': 24,
                'n_layers': 7,
                'seq_len': 6,
                'weight': 0.25
            },
            {
                'path': '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v22_deep_20250811_030913/best_model.pth',
                'd_model': 448,
                'n_heads': 28,
                'n_layers': 10,
                'seq_len': 9,
                'weight': 0.20
            }
        ]
    }
    
    # Data
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
    
    # Collect predictions from ensemble
    all_predictions = []
    all_probabilities = []
    all_labels = None
    
    logging.info("\n📊 Getting ensemble predictions...")
    
    for model_info in model_config['models']:
        logging.info(f"Loading model: {model_info['path'].split('/')[-2]}")
        
        # Load model
        model = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=model_info['d_model'],
            n_heads=model_info['n_heads'],
            n_layers=model_info['n_layers'],
            dropout=0.0,
            seq_len=model_info['seq_len']
        ).to(device)
        
        checkpoint = torch.load(model_info['path'], map_location=device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # Create dataset
        test_dataset = SequenceSleepDataset(
            test_files,
            seq_len=model_info['seq_len'],
            use_channels=3,
            max_samples_per_file=None
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=32,
            shuffle=False,
            num_workers=4,
            pin_memory=True
        )
        
        # Get predictions
        evaluator = EpochLevelEvaluator(seq_len=model_info['seq_len'], n_classes=5)
        evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in test_loader:
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get final predictions
        preds, labels, probs = evaluator.get_final_predictions()
        all_predictions.append(preds)
        all_probabilities.append(probs * model_info['weight'])
        
        if all_labels is None:
            all_labels = labels
    
    # Combine predictions
    ensemble_probs = np.sum(all_probabilities, axis=0)
    ensemble_preds = np.argmax(ensemble_probs, axis=-1)
    
    # Calculate accuracy
    accuracy = np.sum(ensemble_preds == all_labels) / len(all_labels)
    logging.info(f"\n📈 Ensemble Accuracy: {accuracy:.4f} ({accuracy*100:.2f}%)")
    
    # Perform analyses
    logging.info("\n🔍 Performing Error Analysis...")
    
    # 1. Confusion Matrix Analysis
    cm, cm_norm, misclassifications = analyze_confusion_patterns(all_labels, ensemble_preds, class_names)
    
    # 2. Temporal Pattern Analysis
    temporal_patterns = analyze_temporal_patterns(ensemble_preds, all_labels)
    
    # 3. Confidence Analysis
    confidence_stats, correct_conf, incorrect_conf = analyze_confidence_patterns(
        ensemble_probs, all_labels, ensemble_preds
    )
    
    # 4. Hard Samples
    hard_samples = identify_hard_samples(ensemble_probs, all_labels, ensemble_preds)
    
    # 5. Class-Specific Analysis
    class_analysis = analyze_class_specific_errors(
        all_labels, ensemble_preds, ensemble_probs, class_names
    )
    
    # Compile results
    analysis_results = {
        'accuracy': float(accuracy),
        'total_samples': len(all_labels),
        'errors': int(np.sum(ensemble_preds != all_labels)),
        'confusion_matrix': cm.tolist(),
        'top_misclassifications': misclassifications[:10],
        'temporal_patterns': temporal_patterns,
        'confidence_stats': confidence_stats,
        'hard_samples': hard_samples[:20],  # Top 20
        'class_analysis': class_analysis
    }
    
    # Generate improvement strategies
    strategies = generate_improvement_strategies(analysis_results)
    analysis_results['improvement_strategies'] = strategies
    
    # Save results
    results_file = os.path.join(log_dir, 'error_analysis.json')
    with open(results_file, 'w') as f:
        json.dump(analysis_results, f, indent=2)
    
    # Print key findings
    logging.info("\n" + "="*80)
    logging.info("KEY FINDINGS:")
    logging.info("="*80)
    
    logging.info("\n📊 Class-Specific Performance:")
    for class_name, stats in class_analysis.items():
        logging.info(f"  {class_name}: {stats['accuracy']:.1%} ({stats['errors']}/{stats['total_samples']} errors)")
    
    logging.info("\n❌ Top Misclassifications:")
    for i, mis in enumerate(misclassifications[:5]):
        logging.info(f"  {i+1}. {mis['true']} → {mis['predicted']}: {mis['count']} ({mis['percentage']:.1f}%)")
    
    logging.info("\n🔄 Problematic Transitions:")
    for transition, count in temporal_patterns[:5]:
        logging.info(f"  {transition}: {count} errors")
    
    logging.info("\n📈 Confidence Analysis:")
    logging.info(f"  Correct predictions: {confidence_stats['correct_mean_confidence']:.3f} ± {confidence_stats['correct_std_confidence']:.3f}")
    logging.info(f"  Incorrect predictions: {confidence_stats['incorrect_mean_confidence']:.3f} ± {confidence_stats['incorrect_std_confidence']:.3f}")
    logging.info(f"  High-confidence errors: {confidence_stats['high_confidence_errors']}")
    
    logging.info("\n" + "="*80)
    logging.info("💡 IMPROVEMENT STRATEGIES:")
    logging.info("="*80)
    
    for strategy in strategies:
        logging.info(f"\n[{strategy['priority']}] {strategy['issue']}")
        logging.info(f"  Strategy: {strategy['strategy']}")
        logging.info(f"  Potential: {strategy['potential_gain']}")
    
    # Calculate path to 90%
    current_acc = accuracy
    target_acc = 0.90
    gap = target_acc - current_acc
    samples_needed = int(gap * len(all_labels))
    
    logging.info("\n" + "="*80)
    logging.info("🎯 PATH TO 90%:")
    logging.info(f"  Current: {current_acc:.4f} ({current_acc*100:.2f}%)")
    logging.info(f"  Target: {target_acc:.4f} ({target_acc*100:.2f}%)")
    logging.info(f"  Gap: {gap:.4f} ({gap*100:.2f}%)")
    logging.info(f"  Need to correct: {samples_needed} more samples")
    
    # Focus areas
    n1_potential = (0.70 - class_analysis['N1']['accuracy']) * class_analysis['N1']['total_samples'] / len(all_labels)
    logging.info(f"\n  Fixing N1 to 70%: +{n1_potential:.3f} ({n1_potential*100:.2f}%)")
    
    remaining = gap - n1_potential
    if remaining > 0:
        logging.info(f"  Still need: +{remaining:.3f} ({remaining*100:.2f}%)")
        logging.info("  Options: Calibration, transition modeling, ensemble optimization")
    
    logging.info("\n💾 Results saved to: " + results_file)
    logging.info("="*80)
    
    return analysis_results


if __name__ == "__main__":
    results = perform_error_analysis()