#!/usr/bin/env python3
"""
Pure Transformer Model for Sleep Stage Classification
Alternative architecture without Mamba components
Target: Provide diversity for ensemble to reach 90%
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class PositionalEncoding(nn.Module):
    """Positional encoding for Transformer"""
    def __init__(self, d_model, max_len=100):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        # x shape: (batch, seq_len, d_model)
        return x + self.pe[:x.size(1), :]


class PureTransformer(nn.Module):
    """Pure Transformer architecture for sleep staging"""
    def __init__(self, input_channels=3, n_classes=5, d_model=512, 
                 n_heads=16, n_layers=8, dropout=0.2, seq_len=7):
        super().__init__()
        
        self.d_model = d_model
        self.seq_len = seq_len
        
        # Feature extraction from EEG channels
        self.conv1 = nn.Conv1d(input_channels, 64, kernel_size=50, stride=6)
        self.bn1 = nn.BatchNorm1d(64)
        self.conv2 = nn.Conv1d(64, 128, kernel_size=8, stride=1)
        self.bn2 = nn.BatchNorm1d(128)
        self.conv3 = nn.Conv1d(128, 256, kernel_size=8, stride=1)
        self.bn3 = nn.BatchNorm1d(256)
        self.pool = nn.AdaptiveAvgPool1d(32)
        
        # Project to d_model
        self.input_proj = nn.Linear(256 * 32, d_model)
        
        # Positional encoding
        self.pos_encoder = PositionalEncoding(d_model)
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # Classification head
        self.dropout = nn.Dropout(dropout)
        self.fc1 = nn.Linear(d_model, d_model // 2)
        self.fc2 = nn.Linear(d_model // 2, n_classes)
        
        # Attention pooling
        self.attention_weights = nn.Linear(d_model, 1)
        
    def extract_features(self, x):
        """Extract features from raw EEG signals"""
        # Input x shape: (batch, channels, time) but may need transpose
        # Ensure correct dimension ordering for conv1d
        if x.shape[1] > x.shape[2]:  # If time > channels, need to transpose
            x = x.transpose(1, 2)  # Now (batch, channels, time)
        
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = F.relu(self.bn3(self.conv3(x)))
        x = self.pool(x)
        x = x.flatten(1)  # (batch, features)
        return x
    
    def forward(self, x):
        batch_size, seq_len, n_channels, epoch_len = x.shape
        
        # Process each epoch in the sequence
        features = []
        for i in range(seq_len):
            epoch_data = x[:, i, :, :]  # (batch, channels, time)
            epoch_features = self.extract_features(epoch_data)
            features.append(epoch_features)
        
        # Stack features
        x = torch.stack(features, dim=1)  # (batch, seq_len, features)
        
        # Project to d_model
        x = self.input_proj(x)
        
        # Add positional encoding
        x = self.pos_encoder(x)
        
        # Transformer encoding
        x = self.transformer(x)
        
        # Attention pooling over sequence
        attention_scores = self.attention_weights(x)  # (batch, seq_len, 1)
        attention_scores = F.softmax(attention_scores, dim=1)
        x = torch.sum(x * attention_scores, dim=1)  # (batch, d_model)
        
        # Classification
        x = self.dropout(x)
        x = F.relu(self.fc1(x))
        x = self.fc2(x)
        
        # For compatibility, return dummy attention weights
        return x.unsqueeze(1).repeat(1, seq_len, 1), None


class FocalLoss(nn.Module):
    """Focal loss for class imbalance"""
    def __init__(self, alpha=None, gamma=2.0):
        super().__init__()
        self.gamma = gamma
        if alpha is not None:
            self.alpha = torch.tensor(alpha)
        else:
            self.alpha = None
    
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            self.alpha = self.alpha.to(targets.device)
            focal_loss = self.alpha[targets] * focal_loss
        
        return focal_loss.mean()


def train_pure_transformer():
    """Train pure Transformer model"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/pure_transformer_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 PURE TRANSFORMER MODEL TRAINING")
    logging.info("Alternative architecture for ensemble diversity")
    logging.info("="*80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # Configuration
    config = {
        'd_model': 512,
        'n_heads': 16,
        'n_layers': 8,
        'dropout': 0.15,
        'seq_len': 7,
        'batch_size': 16,
        'learning_rate': 1e-4,
        'num_epochs': 20,  # Quick training
        'patience': 5,
        'gradient_clip': 1.0,
        'weight_decay': 0.01
    }
    
    logging.info("\n📋 Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Create datasets
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    logging.info(f"\n📂 Data Split:")
    logging.info(f"  Train: {len(train_dataset)} sequences")
    logging.info(f"  Val: {len(val_dataset)} sequences")
    logging.info(f"  Test: {len(test_dataset)} sequences")
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'],
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Model
    model = PureTransformer(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"\nModel Parameters: {total_params:,}")
    logging.info(f"Model Size: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # Loss and optimizer
    # Class weights based on distribution
    class_weights = torch.tensor([2.0, 10.0, 1.0, 1.5, 2.5]).to(device)
    criterion = nn.CrossEntropyLoss(weight=class_weights)
    
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=5,
        T_mult=1,
        eta_min=1e-6
    )
    
    # Training
    best_val_acc = 0
    best_model_state = None
    patience_counter = 0
    training_history = []
    
    logging.info("\n🏋️ Starting Training...")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        epoch_start = datetime.now()
        
        # Training phase
        model.train()
        train_loss = 0
        train_steps = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config["num_epochs"]}')
        for data, labels in pbar:
            data = data.to(device)
            labels = labels.to(device)
            
            # Flatten sequence labels
            labels = labels.reshape(-1)
            
            optimizer.zero_grad()
            outputs, _ = model(data)
            outputs = outputs.reshape(-1, 5)
            
            loss = criterion(outputs, labels)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
            optimizer.step()
            
            train_loss += loss.item()
            train_steps += 1
            
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_train_loss = train_loss / train_steps
        scheduler.step()
        
        # Validation phase
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(val_loader, desc='Validating', leave=False):
                data = data.to(device)
                outputs, _ = model(data)
                
                # Convert to probabilities
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Calculate metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics['accuracy']
        val_f1 = val_metrics['macro_f1']
        val_kappa = val_metrics['kappa']
        
        epoch_time = (datetime.now() - epoch_start).total_seconds()
        
        # Log results
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Loss: {avg_train_loss:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f} ({val_acc*100:.2f}%)")
        logging.info(f"  Val F1: {val_f1:.4f}")
        logging.info(f"  Val Kappa: {val_kappa:.4f}")
        logging.info(f"  LR: {optimizer.param_groups[0]['lr']:.2e}")
        logging.info(f"  Time: {epoch_time:.1f}s")
        
        training_history.append({
            'epoch': epoch + 1,
            'train_loss': avg_train_loss,
            'val_acc': val_acc,
            'val_f1': val_f1,
            'val_kappa': val_kappa,
            'lr': optimizer.param_groups[0]['lr'],
            'time': epoch_time
        })
        
        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': best_model_state,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_kappa': val_kappa,
                'config': config
            }
            
            checkpoint_path = os.path.join(log_dir, 'best_transformer.pth')
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"  💾 Saved best model")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info("\n⏹️ Early stopping")
                break
    
    # Test evaluation
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        logging.info("\n" + "="*80)
        logging.info("📊 TEST EVALUATION")
        logging.info("="*80)
        
        model.eval()
        test_evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        test_evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc='Testing'):
                data = data.to(device)
                outputs, _ = model(data)
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    test_evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get test metrics
        test_metrics = test_evaluator.evaluate()
        test_acc = test_metrics['accuracy']
        test_f1 = test_metrics['macro_f1']
        test_kappa = test_metrics['kappa']
        
        logging.info("\n🎯 TEST RESULTS:")
        logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
        logging.info(f"  Macro F1: {test_f1:.4f}")
        logging.info(f"  Kappa: {test_kappa:.4f}")
        
        # Save results
        results = {
            'timestamp': timestamp,
            'config': config,
            'best_val_acc': float(best_val_acc),
            'test_acc': float(test_acc),
            'test_f1': float(test_f1),
            'test_kappa': float(test_kappa),
            'training_history': training_history,
            'architecture': 'PureTransformer'
        }
        
        results_file = os.path.join(log_dir, 'results.json')
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        logging.info(f"\n💾 Results saved to {results_file}")
        
        if test_acc >= 0.85:
            logging.info("\n✅ Good performance! Ready for ensemble integration.")
    
    return results


if __name__ == "__main__":
    results = train_pure_transformer()