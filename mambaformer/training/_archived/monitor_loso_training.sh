#!/bin/bash

# Monitor script for Leave-One-Subject-Out training

LOG_DIR="../logs/v14_leave_one_subject_out_20250813_225207"
LOG_FILE="$LOG_DIR/training.log"

echo "=================================================================================="
echo "Monitoring Leave-One-Subject-Out Cross-Validation Training"
echo "Log file: $LOG_FILE"
echo "=================================================================================="

# Check if process is running
PID=$(ps aux | grep "train_v14_leave_one_subject_out.py" | grep -v grep | awk '{print $2}' | head -1)
if [ -z "$PID" ]; then
    echo "Training process is not running!"
else
    echo "Training process PID: $PID"
fi

echo ""
echo "Latest Progress:"
echo "----------------"

# Show last completed fold and current fold progress
tail -n 100 "$LOG_FILE" | grep -E "(FOLD|Best Results|Epoch.*Train)" | tail -10

echo ""
echo "Current Status:"
echo "---------------"
tail -n 5 "$LOG_FILE"

echo ""
echo "Completed Folds:"
echo "----------------"
grep "Best Results" "$LOG_FILE" | nl

echo ""
echo "Memory Usage:"
nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader

echo ""
echo "To continue monitoring: tail -f $LOG_FILE"