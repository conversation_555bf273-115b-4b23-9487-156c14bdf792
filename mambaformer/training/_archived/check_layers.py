#!/usr/bin/env python3
"""Quick script to check the number of transformer layers in each checkpoint."""

import torch
import os
from pathlib import Path

def check_transformer_layers(checkpoint_path):
    """Check the number of transformer layers in a checkpoint."""
    try:
        # Load checkpoint with weights_only=False (we trust our own checkpoints)
        checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
        
        # Get state dict
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        elif 'state_dict' in checkpoint:
            state_dict = checkpoint['state_dict']
        else:
            state_dict = checkpoint
        
        # Find transformer layer keys
        transformer_keys = [k for k in state_dict.keys() if 'transformer_encoder.layers.' in k]
        
        if not transformer_keys:
            print(f"  No transformer layers found")
            return 0
        
        # Extract layer indices
        layer_indices = set()
        for key in transformer_keys:
            # Extract layer index from keys like "transformer_encoder.layers.0.self_attn.in_proj_weight"
            parts = key.split('.')
            for i, part in enumerate(parts):
                if part == 'layers' and i + 1 < len(parts):
                    try:
                        layer_idx = int(parts[i + 1])
                        layer_indices.add(layer_idx)
                    except ValueError:
                        pass
        
        if layer_indices:
            num_layers = max(layer_indices) + 1  # +1 because indices are 0-based
            print(f"  Number of transformer layers: {num_layers}")
            print(f"  Layer indices found: {sorted(layer_indices)}")
            return num_layers
        else:
            print(f"  Could not determine number of layers")
            return 0
            
    except Exception as e:
        print(f"  Error loading checkpoint: {e}")
        return 0

def main():
    """Check all specified checkpoints."""
    base_dir = Path("../logs")
    
    checkpoints = [
        base_dir / "v17_stable_20250811_004457" / "best_model.pth",
        base_dir / "v18_fixed_20250811_014911" / "best_model.pth",
        base_dir / "v22_deep_20250811_030913" / "best_model.pth",
        base_dir / "n1_specialist_20250811_171630" / "best_n1_model.pth",
    ]
    
    print("Checking transformer layers in checkpoints:")
    print("=" * 60)
    
    for checkpoint_path in checkpoints:
        print(f"\n{checkpoint_path.name} ({checkpoint_path.parent.name}):")
        
        if not checkpoint_path.exists():
            print(f"  File not found: {checkpoint_path}")
            continue
        
        check_transformer_layers(checkpoint_path)
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()