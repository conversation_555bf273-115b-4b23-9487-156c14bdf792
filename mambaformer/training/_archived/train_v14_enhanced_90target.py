#!/usr/bin/env python3
"""
V14 Enhanced - 目标90%准确率
基于V14的成功架构，结合以下改进：
1. 更深的网络（8层）
2. 改进的损失函数
3. 动态权重调整
4. 混合精度训练
5. 更好的数据增强
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import torch.cuda.amp as amp

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset

def setup_logging():
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"../logs/v14_enhanced_90_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, "training.log")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 V14 Enhanced - Target: 90% Accuracy")
    logging.info("="*80)
    
    return log_dir

class DynamicFocalLoss(nn.Module):
    """动态调整的Focal Loss，根据训练进度自适应调整权重"""
    def __init__(self, device='cuda', initial_gamma=2.0):
        super().__init__()
        self.device = device
        self.gamma = initial_gamma
        self.epoch = 0
        
        # 初始类权重 - 根据之前的经验优化
        self.base_weights = torch.tensor([5.0, 3.0, 1.0, 1.0, 4.0]).to(device)
        
        # 动态调整参数
        self.class_performance = torch.ones(5).to(device)
        self.update_rate = 0.1
        
    def update_weights(self, confusion_mat):
        """根据混淆矩阵更新权重"""
        # 计算每个类的召回率
        recalls = confusion_mat.diagonal() / (confusion_mat.sum(axis=1) + 1e-8)
        
        # 更新性能追踪
        self.class_performance = (1 - self.update_rate) * self.class_performance + self.update_rate * torch.tensor(recalls).to(self.device)
        
        # 调整权重 - 性能差的类获得更高权重
        performance_weights = 1.0 / (self.class_performance + 0.1)
        self.dynamic_weights = self.base_weights * performance_weights
        self.dynamic_weights = self.dynamic_weights / self.dynamic_weights.mean() * 2.0  # 归一化
        
    def forward(self, inputs, targets):
        if inputs.dim() == 3:
            inputs = inputs.reshape(-1, inputs.shape[-1])
            targets = targets.reshape(-1)
        
        # 基础交叉熵
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        
        # Focal项
        p_t = torch.exp(-ce_loss)
        
        # 动态gamma - 随着训练进行逐渐降低
        current_gamma = self.gamma * (1.0 - self.epoch / 100.0)
        current_gamma = max(current_gamma, 1.0)
        
        focal_term = (1 - p_t) ** current_gamma
        
        # 应用动态权重
        if hasattr(self, 'dynamic_weights'):
            weights = self.dynamic_weights[targets]
        else:
            weights = self.base_weights[targets]
        
        loss = focal_term * ce_loss * weights
        
        return loss.mean()

class EnhancedMAMBAFORMER(nn.Module):
    """增强版MAMBAFORMER - 更深、更强大"""
    def __init__(self, config):
        super().__init__()
        
        # 基础模型 - 增加层数
        self.backbone = SequentialMAMBAFORMER_V2(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],  # 8层
            dropout=config['dropout'],
            seq_len=config['seq_len']
        )
        
        # 添加额外的特征提取层
        self.feature_enhancer = nn.Sequential(
            nn.Linear(config['d_model'], config['d_model'] * 2),
            nn.LayerNorm(config['d_model'] * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(config['d_model'] * 2, config['d_model']),
            nn.LayerNorm(config['d_model'])
        )
        
        # 多尺度特征融合
        self.multi_scale_conv = nn.ModuleList([
            nn.Conv1d(config['d_model'], config['d_model'] // 4, kernel_size=k, padding=k//2)
            for k in [1, 3, 5, 7]
        ])
        
        # 最终分类器
        self.classifier = nn.Sequential(
            nn.Linear(config['d_model'], config['d_model']),
            nn.ReLU(),
            nn.Dropout(0.15),
            nn.Linear(config['d_model'], 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 5)
        )
        
    def forward(self, x):
        # 主干网络
        features, _ = self.backbone(x)
        
        # 处理序列输出
        if features.dim() == 3:
            # 多尺度特征提取
            batch_size, seq_len, d_model = features.shape
            
            # 取中间时间步作为主特征
            features = features[:, seq_len//2, :]
        
        # 特征增强
        features = features + self.feature_enhancer(features)  # 残差连接
        
        # 分类
        output = self.classifier(features)
        
        return output

def mixup_data(x, y, alpha=0.2):
    """MixUp数据增强"""
    if alpha > 0:
        lam = np.random.beta(alpha, alpha)
    else:
        lam = 1

    batch_size = x.size()[0]
    index = torch.randperm(batch_size).to(x.device)

    mixed_x = lam * x + (1 - lam) * x[index]
    y_a, y_b = y, y[index]
    
    return mixed_x, y_a, y_b, lam

def train_epoch(model, train_loader, criterion, optimizer, device, scaler, use_mixup=True):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc='Training')
    for batch_idx, (data, target) in enumerate(pbar):
        data, target = data.to(device), target.to(device)
        
        # 处理序列标签
        if target.dim() > 1:
            target = target[:, target.shape[1]//2]
        
        optimizer.zero_grad()
        
        # MixUp增强
        if use_mixup and np.random.random() > 0.5:
            data, target_a, target_b, lam = mixup_data(data, target)
            
            with amp.autocast():
                output = model(data)
                loss = lam * criterion(output, target_a) + (1 - lam) * criterion(output, target_b)
        else:
            with amp.autocast():
                output = model(data)
                loss = criterion(output, target)
        
        # 混合精度训练
        scaler.scale(loss).backward()
        
        # 梯度裁剪
        scaler.unscale_(optimizer)
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        scaler.step(optimizer)
        scaler.update()
        
        total_loss += loss.item()
        
        # 收集预测
        preds = output.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy() if not use_mixup else target_a.cpu().numpy())
        
        # 动态更新学习率（warmup）
        if batch_idx < 100:
            lr = optimizer.param_groups[0]['lr'] * (batch_idx + 1) / 100
            for param_group in optimizer.param_groups:
                param_group['lr'] = lr
        
        pbar.set_postfix({'loss': loss.item()})
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    # 更新损失函数权重
    cm = confusion_matrix(all_targets, all_preds)
    criterion.update_weights(cm)
    
    return total_loss / len(train_loader), accuracy, f1, cm

def evaluate(model, data_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc='Evaluating'):
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1]//2]
            
            with amp.autocast():
                output = model(data)
            
            preds = output.argmax(dim=1)
            all_preds.extend(preds.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    # Per-class metrics
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm

def main():
    # Enhanced配置
    config = {
        'd_model': 320,  # 增加模型维度
        'n_heads': 20,   # 更多注意力头
        'n_layers': 8,   # 更深的网络
        'dropout': 0.12,
        'seq_len': 5,
        'batch_size': 24,  # 稍小的batch以适应更大的模型
        'learning_rate': 1.5e-4,
        'weight_decay': 5e-5,
        'num_epochs': 50,
        'patience': 15,
        'mixup_alpha': 0.2
    }
    
    log_dir = setup_logging()
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 扩展训练集 - 使用更多数据
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz', 'SC4021E0.npz', 'SC4022E0.npz'  # 添加部分验证集数据
    ]
    
    val_files = [
        'SC4031E0.npz', 'SC4032E0.npz', 'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 加载数据
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'], 
        shuffle=True, num_workers=4, pin_memory=True
    )
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'], 
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = EnhancedMAMBAFORMER(config).to(device)
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = DynamicFocalLoss(device)
    optimizer = optim.AdamW(model.parameters(), 
                           lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'])
    
    # 学习率调度
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer, 
        max_lr=config['learning_rate'] * 10,
        epochs=config['num_epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.1,
        anneal_strategy='cos'
    )
    
    # 混合精度训练
    scaler = amp.GradScaler()
    
    # 训练
    best_val_acc = 0
    best_test_acc = 0
    best_test_metrics = {}
    patience_counter = 0
    
    logging.info("Starting training with enhanced architecture...")
    for epoch in range(config['num_epochs']):
        # 更新损失函数的epoch
        criterion.epoch = epoch
        
        # 训练
        train_loss, train_acc, train_f1, train_cm = train_epoch(
            model, train_loader, criterion, optimizer, device, scaler,
            use_mixup=(epoch > 5)  # 前几个epoch不使用mixup
        )
        
        # 验证
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(model, val_loader, device)
        
        # 测试
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(model, test_loader, device)
        
        # 学习率调度
        scheduler.step()
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Test: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            best_test_acc = test_acc
            best_test_metrics = {
                'accuracy': test_acc,
                'f1': test_f1,
                'kappa': test_kappa,
                'class_f1': test_class_f1.tolist(),
                'confusion_matrix': test_cm.tolist()
            }
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_metrics': best_test_metrics,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Test Acc: {test_acc:.4f})")
            
            if test_acc >= 0.90:
                logging.info("  🎉 ACHIEVED 90% TARGET!")
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS")
    logging.info("="*80)
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    logging.info(f"Best Test F1: {best_test_metrics['f1']:.4f}")
    logging.info(f"Best Test Kappa: {best_test_metrics['kappa']:.4f}")
    
    if best_test_acc >= 0.90:
        logging.info("✅ SUCCESS: Achieved 90% target!")
    else:
        gap = 0.90 - best_test_acc
        logging.info(f"Gap to 90%: {gap:.4f} ({gap*100:.2f}%)")
    
    # 保存结果
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(best_test_metrics, f, indent=2)

if __name__ == "__main__":
    main()