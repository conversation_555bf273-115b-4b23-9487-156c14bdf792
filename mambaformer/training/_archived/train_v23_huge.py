#!/usr/bin/env python3
"""
V23 HUGE Model - Largest architecture for 90% accuracy
d_model=640 with extreme capacity
Target: 90% Accuracy, 82% Macro F1, 0.82 Kappa
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
from torch.cuda.amp import autocast, GradScaler
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import confusion_matrix
import warnings
warnings.filterwarnings('ignore')

# Add parent directory
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator


class HugeFocalLoss(nn.Module):
    """Focal loss for huge model with maximum N1 focus"""
    def __init__(self, gamma=4.5):
        super().__init__()
        self.gamma = gamma
        # Maximum weights for N1
        self.class_weights = torch.tensor([4.5, 20.0, 1.0, 2.0, 4.0])  # Wake, N1, N2, N3, REM
        
    def forward(self, inputs, targets):
        batch_size, seq_len = targets.shape
        n_classes = inputs.shape[-1]
        
        # Flatten
        inputs_flat = inputs.reshape(-1, n_classes)
        targets_flat = targets.reshape(-1)
        
        # Apply class weights
        ce_loss = F.cross_entropy(inputs_flat, targets_flat, 
                                  weight=self.class_weights.to(inputs.device), 
                                  reduction='none')
        
        # Focal loss with very high gamma
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        # Triple penalty for N1
        n1_mask = (targets_flat == 1).float()
        n1_penalty = focal_loss * n1_mask * 3.0
        
        # Heavy penalty for N1-N2 confusion
        pred_classes = torch.argmax(inputs_flat, dim=-1)
        n1_n2_confusion = ((targets_flat == 1) & (pred_classes == 2)).float()
        n1_n2_penalty = n1_n2_confusion * 6.0
        
        # Wake-N1 confusion penalty
        wake_n1_confusion = ((targets_flat == 0) & (pred_classes == 1)).float()
        wake_n1_confusion += ((targets_flat == 1) & (pred_classes == 0)).float()
        wake_penalty = wake_n1_confusion * 5.0
        
        total_loss = focal_loss + n1_penalty + n1_n2_penalty + wake_penalty
        
        return total_loss.mean()


class LabelSmoothing(nn.Module):
    """Label smoothing for better generalization"""
    def __init__(self, smoothing=0.1):
        super().__init__()
        self.smoothing = smoothing
        
    def forward(self, predictions, targets):
        n_classes = predictions.shape[-1]
        
        # Create smoothed targets
        smooth_targets = torch.zeros_like(predictions)
        smooth_targets.fill_(self.smoothing / (n_classes - 1))
        
        # Flatten
        pred_flat = predictions.reshape(-1, n_classes)
        target_flat = targets.reshape(-1)
        
        # Set true class probability
        smooth_targets_flat = smooth_targets.reshape(-1, n_classes)
        smooth_targets_flat.scatter_(1, target_flat.unsqueeze(1), 1 - self.smoothing)
        
        # KL divergence loss
        log_probs = F.log_softmax(pred_flat, dim=-1)
        loss = -(smooth_targets_flat * log_probs).sum(dim=-1)
        
        return loss.mean()


class HugeLoss(nn.Module):
    """Combined loss for huge model"""
    def __init__(self):
        super().__init__()
        self.focal = HugeFocalLoss(gamma=4.5)
        self.smooth = LabelSmoothing(smoothing=0.1)
        self.ce = nn.CrossEntropyLoss()
        
    def forward(self, predictions, targets):
        # Main focal loss
        focal_loss = self.focal(predictions, targets)
        
        # Label smoothing for regularization
        smooth_loss = self.smooth(predictions, targets) * 0.2
        
        # Standard CE for stability
        pred_flat = predictions.reshape(-1, predictions.shape[-1])
        target_flat = targets.reshape(-1)
        ce_loss = self.ce(pred_flat, target_flat) * 0.1
        
        return focal_loss + smooth_loss + ce_loss


class ExtremeBalancedDataset(Dataset):
    """Extremely balanced dataset with maximum N1 focus"""
    def __init__(self, base_dataset, n1_factor=5.0, wake_factor=2.5, rem_factor=2.0):
        self.base_dataset = base_dataset
        
        # Find samples by dominant class
        self.n1_indices = []
        self.wake_indices = []
        self.rem_indices = []
        self.n3_indices = []
        self.other_indices = []
        
        for i in range(len(base_dataset)):
            _, label = base_dataset[i]
            # Count occurrences of each class
            class_counts = np.bincount(label, minlength=5)
            dominant = np.argmax(class_counts)
            
            # Also check if N1 appears at all
            if 1 in label:
                self.n1_indices.append(i)
            elif dominant == 0:  # Wake
                self.wake_indices.append(i)
            elif dominant == 4:  # REM
                self.rem_indices.append(i)
            elif dominant == 3:  # N3
                self.n3_indices.append(i)
            else:
                self.other_indices.append(i)
        
        # Create extremely balanced dataset
        self.indices = self.other_indices.copy()
        
        # Maximum N1 oversampling
        n1_samples = int(len(self.n1_indices) * n1_factor)
        if n1_samples > 0:
            n1_selected = np.random.choice(self.n1_indices, n1_samples, replace=True)
            self.indices.extend(n1_selected.tolist())
        
        # Wake oversampling
        wake_samples = int(len(self.wake_indices) * wake_factor)
        if wake_samples > 0:
            wake_selected = np.random.choice(self.wake_indices, wake_samples, replace=True)
            self.indices.extend(wake_selected.tolist())
        
        # REM oversampling
        rem_samples = int(len(self.rem_indices) * rem_factor)
        if rem_samples > 0:
            rem_selected = np.random.choice(self.rem_indices, rem_samples, replace=True)
            self.indices.extend(rem_selected.tolist())
        
        # N3 oversampling
        n3_samples = int(len(self.n3_indices) * 1.5)
        if n3_samples > 0:
            n3_selected = np.random.choice(self.n3_indices, n3_samples, replace=True)
            self.indices.extend(n3_selected.tolist())
        
        np.random.shuffle(self.indices)
        
    def __len__(self):
        return len(self.indices)
    
    def __getitem__(self, idx):
        real_idx = self.indices[idx]
        data, label = self.base_dataset[real_idx]
        
        # Extreme augmentation
        if np.random.random() > 0.2:
            aug_type = np.random.choice(['noise', 'scale', 'shift', 'dropout', 'mixup'])
            
            if aug_type == 'noise':
                noise = torch.randn_like(data) * np.random.uniform(0.03, 0.12)
                data = data + noise
            
            elif aug_type == 'scale':
                scale = np.random.uniform(0.8, 1.2)
                data = data * scale
            
            elif aug_type == 'shift':
                shift = np.random.randint(-3, 4)
                if shift != 0:
                    data = torch.roll(data, shift, dims=-1)
            
            elif aug_type == 'dropout':
                # Random channel dropout
                if np.random.random() > 0.5:
                    channel_idx = np.random.randint(0, data.shape[0])
                    data[channel_idx] = data[channel_idx] * np.random.uniform(0.2, 0.8)
            
            elif aug_type == 'mixup':
                # Simple mixup within batch
                if idx > 0:
                    other_idx = np.random.randint(0, len(self.indices))
                    other_data, other_label = self.base_dataset[self.indices[other_idx]]
                    alpha = np.random.beta(0.2, 0.2)
                    data = alpha * data + (1 - alpha) * other_data
        
        return data, label


def train_v23():
    """Main training function for V23 HUGE model"""
    
    # Setup logging
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_dir = f"../logs/v23_huge_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 MAMBAFORMER V23 HUGE - MAXIMUM CAPACITY FOR 90%")
    logging.info("="*80)
    logging.info("🎯 Targets: Accuracy ≥90%, Macro F1 ≥82%, Kappa ≥0.82")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # V23 HUGE Configuration
    config = {
        'd_model': 640,      # HUGE model
        'n_heads': 40,       # Maximum heads
        'n_layers': 8,       # Deep enough
        'dropout': 0.28,     # Higher dropout for huge model
        'seq_len': 10,       # Maximum context
        'batch_size': 8,     # Small batch for huge model
        'learning_rate': 2e-5,
        'num_epochs': 80,    
        'patience': 35,
        'gradient_clip': 0.25,
        'weight_decay': 0.04,
        'n1_oversample': 5.0,  # Maximum N1 focus
        'wake_oversample': 2.5,
        'rem_oversample': 2.0,
        'warmup_epochs': 6,
        'mixed_precision': True,
        'gradient_accumulation': 2  # For effective batch size of 16
    }
    
    logging.info("\n📋 V23 HUGE Configuration:")
    for key, value in config.items():
        logging.info(f"  {key}: {value}")
    
    # Data paths
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    logging.info(f"\n📂 Data Split:")
    logging.info(f"  Train: {len(train_files)} files")
    logging.info(f"  Val: {len(val_files)} files")
    logging.info(f"  Test: {len(test_files)} files")
    
    # Create datasets
    logging.info("\n📊 Loading datasets with EXTREME balancing...")
    
    base_train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    # Extreme balancing
    train_dataset = ExtremeBalancedDataset(
        base_train_dataset, 
        n1_factor=config['n1_oversample'],
        wake_factor=config['wake_oversample'],
        rem_factor=config['rem_oversample']
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config['seq_len'],
        use_channels=3,
        max_samples_per_file=None
    )
    
    logging.info(f"Train samples: {len(train_dataset)} (with EXTREME balancing)")
    logging.info(f"Val samples: {val_dataset.total_epochs} epochs")
    logging.info(f"Test samples: {test_dataset.total_epochs} epochs")
    
    # Data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config['batch_size'],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config['batch_size'] * 2,  # Larger batch for validation
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config['batch_size'] * 2,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Create HUGE model
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    # Try to load best weights for initialization
    try:
        # Try V17 first (best single model)
        pretrained_path = '/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/v17_stable_20250811_004457/best_model.pth'
        if os.path.exists(pretrained_path):
            checkpoint = torch.load(pretrained_path, map_location=device, weights_only=False)
            # Partial loading
            model_dict = model.state_dict()
            pretrained_dict = {k: v for k, v in checkpoint['model_state_dict'].items() 
                             if k in model_dict and v.shape == model_dict[k].shape}
            model_dict.update(pretrained_dict)
            model.load_state_dict(model_dict)
            logging.info(f"✅ Loaded {len(pretrained_dict)}/{len(model_dict)} layers from V17")
    except Exception as e:
        logging.info(f"⚠️ Could not load pretrained weights: {e}")
    
    total_params = sum(p.numel() for p in model.parameters())
    logging.info(f"\nModel Parameters: {total_params:,}")
    logging.info(f"Model Size: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    # Loss and optimizer
    criterion = HugeLoss()
    
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999),
        eps=1e-8
    )
    
    # Scheduler with restarts
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=25,
        T_mult=2,
        eta_min=1e-8
    )
    
    # Mixed precision
    scaler = GradScaler() if config['mixed_precision'] else None
    
    # Training variables
    best_val_acc = 0
    best_val_f1 = 0
    best_val_kappa = 0
    best_model_state = None
    patience_counter = 0
    training_history = []
    
    logging.info("\n🏋️ Starting HUGE Model Training...")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        epoch_start = datetime.now()
        
        # Warmup
        if epoch < config['warmup_epochs']:
            warmup_lr = config['learning_rate'] * (epoch + 1) / config['warmup_epochs']
            for param_group in optimizer.param_groups:
                param_group['lr'] = warmup_lr
            logging.info(f"Warmup LR: {warmup_lr:.2e}")
        
        # Training phase
        model.train()
        train_loss = 0
        train_steps = 0
        accumulation_steps = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config["num_epochs"]}')
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)
            
            if config['mixed_precision'] and scaler is not None:
                with autocast():
                    outputs, _ = model(data)
                    loss = criterion(outputs, labels)
                    loss = loss / config['gradient_accumulation']  # Scale loss
                
                scaler.scale(loss).backward()
                accumulation_steps += 1
                
                # Update weights every gradient_accumulation steps
                if accumulation_steps % config['gradient_accumulation'] == 0:
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
                    scaler.step(optimizer)
                    scaler.update()
                    optimizer.zero_grad()
                    accumulation_steps = 0
            else:
                outputs, _ = model(data)
                loss = criterion(outputs, labels)
                loss = loss / config['gradient_accumulation']
                loss.backward()
                accumulation_steps += 1
                
                if accumulation_steps % config['gradient_accumulation'] == 0:
                    torch.nn.utils.clip_grad_norm_(model.parameters(), config['gradient_clip'])
                    optimizer.step()
                    optimizer.zero_grad()
                    accumulation_steps = 0
            
            train_loss += loss.item() * config['gradient_accumulation']
            train_steps += 1
            
            current_lr = optimizer.param_groups[0]['lr']
            pbar.set_postfix({'loss': f'{loss.item()*config["gradient_accumulation"]:.4f}', 
                            'lr': f'{current_lr:.2e}'})
        
        avg_train_loss = train_loss / train_steps
        
        # Step scheduler
        if epoch >= config['warmup_epochs']:
            scheduler.step()
        
        # Validation phase
        model.eval()
        evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        evaluator.total_epochs = val_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(val_loader, desc='Validating', leave=False):
                data = data.to(device)
                
                if config['mixed_precision']:
                    with autocast():
                        outputs, _ = model(data)
                else:
                    outputs, _ = model(data)
                
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(val_dataset):
                        seq_info = val_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Calculate metrics
        val_metrics = evaluator.evaluate()
        val_acc = val_metrics['accuracy']
        val_f1 = val_metrics['macro_f1']
        val_kappa = val_metrics['kappa']
        
        # Get per-class performance
        n1_f1 = val_metrics['per_class_metrics']['N1']['f1']
        rem_f1 = val_metrics['per_class_metrics']['REM']['f1']
        wake_f1 = val_metrics['per_class_metrics']['Wake']['f1']
        
        epoch_time = (datetime.now() - epoch_start).total_seconds()
        
        # Log results
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}:")
        logging.info(f"  Loss: {avg_train_loss:.4f}")
        logging.info(f"  Val Acc: {val_acc:.4f} ({val_acc*100:.2f}%)")
        logging.info(f"  Val F1: {val_f1:.4f} ({val_f1*100:.2f}%)")
        logging.info(f"  Val Kappa: {val_kappa:.4f}")
        logging.info(f"  Wake F1: {wake_f1:.4f} | N1 F1: {n1_f1:.4f} | REM F1: {rem_f1:.4f}")
        logging.info(f"  LR: {current_lr:.2e}")
        logging.info(f"  Time: {epoch_time:.1f}s")
        
        # Check targets
        targets_met = []
        if val_acc >= 0.90:
            targets_met.append("ACC")
        if val_f1 >= 0.82:
            targets_met.append("F1")
        if val_kappa >= 0.82:
            targets_met.append("KAPPA")
        
        if targets_met:
            logging.info(f"  🎯 Targets met: {', '.join(targets_met)}")
        
        training_history.append({
            'epoch': epoch + 1,
            'train_loss': avg_train_loss,
            'val_acc': val_acc,
            'val_f1': val_f1,
            'val_kappa': val_kappa,
            'n1_f1': n1_f1,
            'rem_f1': rem_f1,
            'wake_f1': wake_f1,
            'lr': current_lr,
            'time': epoch_time
        })
        
        # Save best model
        improved = False
        if val_acc > best_val_acc:
            improved = True
        elif val_acc == best_val_acc and val_f1 > best_val_f1:
            improved = True
        
        if improved:
            best_val_acc = val_acc
            best_val_f1 = val_f1
            best_val_kappa = val_kappa
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            
            checkpoint = {
                'epoch': epoch + 1,
                'model_state_dict': best_model_state,
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'val_f1': val_f1,
                'val_kappa': val_kappa,
                'config': config
            }
            
            checkpoint_path = os.path.join(log_dir, 'best_model.pth')
            torch.save(checkpoint, checkpoint_path)
            logging.info(f"  💾 Saved best model")
            
            # Save if all targets met
            if len(targets_met) == 3:
                success_path = os.path.join(log_dir, f'SUCCESS_epoch{epoch+1}.pth')
                torch.save(checkpoint, success_path)
                logging.info(f"  🎉 SUCCESS! All targets achieved!")
                
                if epoch >= 30:  # At least 30 epochs
                    logging.info("  ✅ Stopping - targets achieved!")
                    break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"\n⏹️ Early stopping triggered")
                break
    
    # Final test evaluation
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        logging.info("\n" + "="*80)
        logging.info("📊 FINAL TEST EVALUATION")
        logging.info("="*80)
        
        model.eval()
        test_evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
        test_evaluator.total_epochs = test_dataset.total_epochs
        
        with torch.no_grad():
            batch_start_idx = 0
            for data, labels in tqdm(test_loader, desc='Testing'):
                data = data.to(device)
                
                if config['mixed_precision']:
                    with autocast():
                        outputs, _ = model(data)
                else:
                    outputs, _ = model(data)
                
                probs = torch.softmax(outputs, dim=-1)
                
                batch_size = data.shape[0]
                start_indices = []
                
                for i in range(batch_size):
                    seq_idx = batch_start_idx + i
                    if seq_idx < len(test_dataset):
                        seq_info = test_dataset.get_sequence_info(seq_idx)
                        if seq_info:
                            start_indices.append(seq_info['start_epoch_idx'])
                
                if start_indices:
                    valid_batch_size = len(start_indices)
                    test_evaluator.add_batch_predictions(
                        probs[:valid_batch_size].cpu().numpy(),
                        labels[:valid_batch_size].cpu().numpy(),
                        start_indices
                    )
                
                batch_start_idx += batch_size
        
        # Get test metrics
        test_metrics = test_evaluator.evaluate()
        test_acc = test_metrics['accuracy']
        test_f1 = test_metrics['macro_f1']
        test_kappa = test_metrics['kappa']
        
        # Get confusion matrix
        final_preds, final_labels, _ = test_evaluator.get_final_predictions()
        cm = confusion_matrix(final_labels, final_preds)
        
        # Results
        logging.info("\n🎯 FINAL TEST RESULTS:")
        logging.info(f"  Accuracy: {test_acc:.4f} ({test_acc*100:.2f}%)")
        logging.info(f"  Macro F1: {test_f1:.4f} ({test_f1*100:.2f}%)")
        logging.info(f"  Kappa: {test_kappa:.4f}")
        
        # Target achievement
        logging.info("\n🎯 Target Achievement (90% / 82% / 0.82):")
        all_achieved = True
        
        if test_acc >= 0.90:
            logging.info(f"  ✅ Accuracy: {test_acc:.4f} ≥ 0.90")
        else:
            logging.info(f"  ❌ Accuracy: {test_acc:.4f} < 0.90 (gap: {0.90-test_acc:.4f})")
            all_achieved = False
        
        if test_f1 >= 0.82:
            logging.info(f"  ✅ Macro F1: {test_f1:.4f} ≥ 0.82")
        else:
            logging.info(f"  ❌ Macro F1: {test_f1:.4f} < 0.82 (gap: {0.82-test_f1:.4f})")
            all_achieved = False
        
        if test_kappa >= 0.82:
            logging.info(f"  ✅ Kappa: {test_kappa:.4f} ≥ 0.82")
        else:
            logging.info(f"  ❌ Kappa: {test_kappa:.4f} < 0.82 (gap: {0.82-test_kappa:.4f})")
            all_achieved = False
        
        # Per-class metrics
        logging.info("\n📊 Per-Class Performance:")
        class_names = ['Wake', 'N1', 'N2', 'N3', 'REM']
        for class_name in class_names:
            metrics = test_metrics['per_class_metrics'][class_name]
            logging.info(f"{class_name}: P={metrics['precision']*100:.1f}%, R={metrics['recall']*100:.1f}%, F1={metrics['f1']*100:.1f}%")
        
        # Save results
        final_results = {
            'timestamp': timestamp,
            'config': config,
            'model_size': f"{total_params * 4 / 1024 / 1024:.2f} MB",
            'total_params': total_params,
            'best_val_acc': float(best_val_acc),
            'best_val_f1': float(best_val_f1),
            'best_val_kappa': float(best_val_kappa),
            'test_acc': float(test_acc),
            'test_f1': float(test_f1),
            'test_kappa': float(test_kappa),
            'confusion_matrix': cm.tolist(),
            'per_class_metrics': test_metrics['per_class_metrics'],
            'training_history': training_history,
            'targets_achieved': all_achieved,
            'model_type': 'HUGE',
            'd_model': config['d_model']
        }
        
        # Convert numpy types
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {k: convert_numpy(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            return obj
        
        final_results = convert_numpy(final_results)
        
        results_file = os.path.join(log_dir, 'final_results.json')
        with open(results_file, 'w') as f:
            json.dump(final_results, f, indent=2)
        
        logging.info(f"\n💾 Results saved to {results_file}")
        
        if all_achieved:
            logging.info("\n" + "="*80)
            logging.info("🎉🎉🎉 SUCCESS! ALL TARGETS ACHIEVED! 🎉🎉🎉")
            logging.info("V23 HUGE Model Ready for ICASSP 2026!")
            logging.info("="*80)
        else:
            logging.info("\n📈 Continue with hierarchical ensemble of all models...")
    
    return final_results


if __name__ == "__main__":
    results = train_v23()