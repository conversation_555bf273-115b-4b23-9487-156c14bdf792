#!/usr/bin/env python3
"""
🚀 ADVANCED ENSEMBLE WITH MULTIPLE STRATEGIES
Combining all successful models with adaptive strategies
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from pathlib import Path
import sys
import json
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
import logging
from datetime import datetime
from torch.utils.data import DataLoader

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PureTransformerModel(nn.Module):
    """Pure Transformer architecture for diversity"""
    def __init__(self, input_dim=3, d_model=256, n_heads=16, n_layers=6, num_classes=5, seq_len=7, dropout=0.15):
        super().__init__()
        self.d_model = d_model
        self.seq_len = seq_len
        
        # Feature extraction
        self.feature_extractor = nn.Sequential(
            nn.Conv1d(input_dim, 64, kernel_size=3, padding=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv1d(128, d_model, kernel_size=3, padding=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU()
        )
        
        # Positional encoding
        self.pos_encoding = nn.Parameter(torch.randn(1, seq_len, d_model))
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, num_classes)
        )
        
    def forward(self, x):
        # x shape: [batch, seq_len, channels]
        # Transpose for Conv1d: [batch, channels, seq_len]
        x = x.transpose(1, 2)
        
        # Feature extraction
        features = self.feature_extractor(x)
        
        # Transpose back: [batch, seq_len, d_model]
        features = features.transpose(1, 2)
        
        # Add positional encoding
        features = features + self.pos_encoding
        
        # Transformer encoding
        encoded = self.transformer_encoder(features)
        
        # Global pooling
        pooled = encoded.mean(dim=1)
        
        # Classification
        output = self.classifier(pooled)
        
        return output

class AdvancedEnsemble:
    def __init__(self, device='cuda'):
        self.device = device
        self.models = {}
        self.weights = {}
        self.strategies = []
        
    def load_model(self, name, checkpoint_path, config):
        """Load a model from checkpoint"""
        logger.info(f"Loading {name} from {checkpoint_path}")
        
        if 'pure_transformer' in name.lower():
            model = PureTransformerModel(
                d_model=config.get('d_model', 256),
                n_heads=config.get('n_heads', 16),
                n_layers=config.get('n_layers', 6),
                dropout=config.get('dropout', 0.15)
            ).to(self.device)
        else:
            # Create SequentialMAMBAFORMER_V2 model
            model = SequentialMAMBAFORMER_V2(
                input_channels=3,
                n_classes=5,
                d_model=config.get('d_model', 128),
                n_heads=config.get('n_heads', 8),
                n_layers=config.get('n_layers', 4),
                dropout=config.get('dropout', 0.15),
                seq_len=7
            ).to(self.device)
        
        checkpoint = torch.load(checkpoint_path, map_location=self.device, weights_only=False)
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model.eval()
        self.models[name] = model
        
    def predict_with_tta(self, model, x, num_augmentations=5):
        """Test-Time Augmentation with noise and dropout"""
        predictions = []
        
        with torch.no_grad():
            # Original prediction
            output = model(x)
            if isinstance(output, tuple):
                output = output[0]
                if output.dim() == 3:
                    output = output.mean(dim=1)
            pred = F.softmax(output, dim=1)
            predictions.append(pred)
            
            # Augmented predictions
            for _ in range(num_augmentations - 1):
                # Add small noise
                noise = torch.randn_like(x) * 0.01
                augmented = x + noise
                output = model(augmented)
                if isinstance(output, tuple):
                    output = output[0]
                    if output.dim() == 3:
                        output = output.mean(dim=1)
                pred = F.softmax(output, dim=1)
                predictions.append(pred)
        
        return torch.stack(predictions).mean(dim=0)
    
    def get_ensemble_predictions(self, test_loader, strategy='weighted_vote'):
        """Get predictions using specified strategy"""
        all_predictions = {}
        all_labels = []
        
        # First collect all labels
        for batch_idx, (data, labels) in enumerate(test_loader):
            # Handle sequence labels - may be (batch, seq_len)
            if labels.dim() > 1:
                # Use middle epoch for evaluation
                labels = labels[:, labels.shape[1]//2]
            all_labels.extend(labels.numpy())
        
        # Collect predictions from all models
        for name, model in self.models.items():
            model_preds = []
            
            for batch_idx, (data, labels) in enumerate(test_loader):
                data = data.to(self.device)
                
                # Use TTA for critical models
                if 'n1' in name.lower() or 'transformer' in name.lower():
                    probs = self.predict_with_tta(model, data, num_augmentations=3)
                else:
                    with torch.no_grad():
                        output = model(data)
                        # Handle tuple output from SequentialMAMBAFORMER_V2
                        if isinstance(output, tuple):
                            output = output[0]  # First element is predictions
                            # If it's (batch, seq, classes), average over sequence
                            if output.dim() == 3:
                                output = output.mean(dim=1)
                        probs = F.softmax(output, dim=1)
                
                model_preds.append(probs.cpu().numpy())
            
            all_predictions[name] = np.concatenate(model_preds)
        
        all_labels = np.array(all_labels)
        
        # Apply strategy
        if strategy == 'weighted_vote':
            ensemble_probs = np.zeros_like(list(all_predictions.values())[0])
            for name, preds in all_predictions.items():
                weight = self.weights.get(name, 1.0 / len(self.models))
                ensemble_probs += weight * preds
            ensemble_preds = ensemble_probs.argmax(axis=1)
            
        elif strategy == 'confidence_weighted':
            # Weight by prediction confidence
            ensemble_probs = np.zeros_like(list(all_predictions.values())[0])
            for name, preds in all_predictions.items():
                confidence = np.max(preds, axis=1, keepdims=True)
                weighted_preds = preds * confidence
                ensemble_probs += weighted_preds
            ensemble_preds = ensemble_probs.argmax(axis=1)
            
        elif strategy == 'n1_boosted':
            # Give extra weight to models that predict N1 well
            ensemble_probs = np.zeros_like(list(all_predictions.values())[0])
            n1_boost_factor = 2.0
            
            for name, preds in all_predictions.items():
                weight = self.weights.get(name, 1.0 / len(self.models))
                if 'n1' in name.lower():
                    # Boost N1 predictions
                    preds_copy = preds.copy()
                    preds_copy[:, 1] *= n1_boost_factor  # N1 is class 1
                    # Renormalize
                    preds_copy = preds_copy / preds_copy.sum(axis=1, keepdims=True)
                    ensemble_probs += weight * preds_copy
                else:
                    ensemble_probs += weight * preds
            
            ensemble_preds = ensemble_probs.argmax(axis=1)
            
        elif strategy == 'oracle_selection':
            # Select best model per sample based on confidence
            ensemble_preds = np.zeros(len(all_labels), dtype=int)
            
            for i in range(len(all_labels)):
                best_confidence = 0
                best_pred = 0
                
                for name, preds in all_predictions.items():
                    confidence = np.max(preds[i])
                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_pred = np.argmax(preds[i])
                
                ensemble_preds[i] = best_pred
        
        else:
            raise ValueError(f"Unknown strategy: {strategy}")
        
        return ensemble_preds, all_labels
    
    def evaluate_strategy(self, test_loader, strategy):
        """Evaluate a specific ensemble strategy"""
        predictions, labels = self.get_ensemble_predictions(test_loader, strategy)
        
        accuracy = accuracy_score(labels, predictions)
        macro_f1 = f1_score(labels, predictions, average='macro')
        kappa = cohen_kappa_score(labels, predictions)
        
        # Per-class F1
        class_f1 = f1_score(labels, predictions, average=None)
        
        return {
            'strategy': strategy,
            'accuracy': accuracy,
            'macro_f1': macro_f1,
            'kappa': kappa,
            'class_f1': class_f1.tolist(),
            'n1_f1': class_f1[1]  # N1 is class 1
        }

def main():
    # Configuration - using actual available models with correct dimensions
    configs = {
        'v17_stable': {
            'checkpoint': '../logs/v17_stable_20250811_004457/best_model.pth',
            'd_model': 288,  # Correct dimension from checkpoint
            'n_heads': 18,
            'n_layers': 6,  # Confirmed 6 layers
            'weight': 0.35  # High weight for stable model
        },
        'v18_fixed': {
            'checkpoint': '../logs/v18_fixed_20250811_014911/best_model.pth',
            'd_model': 384,  # Correct dimension from checkpoint
            'n_heads': 16,
            'n_layers': 7,  # Confirmed 7 layers (not 8)
            'weight': 0.30
        },
        'v22_deep': {
            'checkpoint': '../logs/v22_deep_20250811_030913/best_model.pth',
            'd_model': 448,  # Correct dimension from checkpoint
            'n_heads': 16,
            'n_layers': 10,  # Confirmed 10 layers (deep model)
            'weight': 0.25
        },
        # Skip pure transformer due to architecture mismatch
        # 'pure_transformer': {
        #     'checkpoint': '../logs/pure_transformer_20250811_172628/best_transformer.pth',
        #     'd_model': 256,
        #     'n_heads': 16,
        #     'n_layers': 6,
        #     'dropout': 0.15,
        #     'weight': 0.15  # 80.26% accuracy
        # },
        'n1_specialist': {
            'checkpoint': '../logs/n1_specialist_20250811_171630/best_n1_model.pth',
            'd_model': 320,  # Correct dimension from checkpoint
            'n_heads': 20,
            'n_layers': 8,  # Confirmed 8 layers
            'weight': 0.10  # Specialist for N1
        }
    }
    
    # Create ensemble
    ensemble = AdvancedEnsemble()
    
    # Load models
    for name, config in configs.items():
        checkpoint_path = Path(config['checkpoint'])
        if checkpoint_path.exists():
            ensemble.load_model(name, checkpoint_path, config)
            ensemble.weights[name] = config['weight']
        else:
            logger.warning(f"Checkpoint not found: {checkpoint_path}")
    
    # Normalize weights
    total_weight = sum(ensemble.weights.values())
    for name in ensemble.weights:
        ensemble.weights[name] /= total_weight
    
    logger.info(f"\nNormalized weights: {ensemble.weights}")
    
    # Load test data
    # Data paths  
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4151E0.npz', 'SC4152E0.npz', 'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # Create test dataset
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=7,
        is_training=False
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=32,
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    # Test different strategies
    strategies = [
        'weighted_vote',
        'confidence_weighted',
        'n1_boosted',
        'oracle_selection'
    ]
    
    results = []
    best_result = None
    best_accuracy = 0
    
    logger.info("\n" + "="*80)
    logger.info("🧪 TESTING ENSEMBLE STRATEGIES")
    logger.info("="*80)
    
    for strategy in strategies:
        logger.info(f"\nTesting strategy: {strategy}")
        result = ensemble.evaluate_strategy(test_loader, strategy)
        results.append(result)
        
        logger.info(f"  Accuracy: {result['accuracy']:.4f} ({result['accuracy']*100:.2f}%)")
        logger.info(f"  Macro F1: {result['macro_f1']:.4f}")
        logger.info(f"  Kappa: {result['kappa']:.4f}")
        logger.info(f"  N1 F1: {result['n1_f1']:.4f}")
        
        if result['accuracy'] > best_accuracy:
            best_accuracy = result['accuracy']
            best_result = result
    
    # Save results
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = Path(f'../logs/advanced_ensemble_{timestamp}')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    with open(output_dir / 'results.json', 'w') as f:
        json.dump({
            'models': list(configs.keys()),
            'weights': ensemble.weights,
            'strategies': results,
            'best_result': best_result
        }, f, indent=2)
    
    logger.info("\n" + "="*80)
    logger.info("🏆 BEST RESULT")
    logger.info("="*80)
    logger.info(f"Strategy: {best_result['strategy']}")
    logger.info(f"Accuracy: {best_result['accuracy']:.4f} ({best_result['accuracy']*100:.2f}%)")
    logger.info(f"Macro F1: {best_result['macro_f1']:.4f}")
    logger.info(f"Kappa: {best_result['kappa']:.4f}")
    logger.info(f"\nClass-wise F1: {best_result['class_f1']}")
    
    gap_to_90 = 0.90 - best_result['accuracy']
    logger.info(f"\n📊 Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
    
if __name__ == '__main__':
    main()