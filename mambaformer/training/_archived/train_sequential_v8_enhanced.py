"""
序列MAMBAFORMER V8训练脚本 - 增强版本
基于V7的成功，添加更多改进技巧：
1. 使用正确的epoch级别评估
2. 学习率预热和余弦退火
3. 标签平滑
4. 混合精度训练
5. 更好的数据增强
6. Stochastic Weight Averaging (SWA)
7. 多阶段训练策略
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import (
    SequentialMAMBAFORMER_V2,
    SequentialFocalLoss,
    TemporalConsistencyLoss,
    MildDataAugmentation
)
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator, log_epoch_level_metrics


def setup_logging(log_dir="../logs"):
    """设置日志"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"sequential_v8_enhanced_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info(f"日志文件: {log_file}")
    return log_file


class LabelSmoothingLoss(nn.Module):
    """标签平滑损失"""
    def __init__(self, n_classes, smoothing=0.1):
        super().__init__()
        self.n_classes = n_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
        
    def forward(self, pred, target):
        # pred: (batch_size * seq_len, n_classes)
        # target: (batch_size * seq_len,)
        pred = pred.log_softmax(dim=-1)
        
        true_dist = torch.zeros_like(pred)
        true_dist.fill_(self.smoothing / (self.n_classes - 1))
        true_dist.scatter_(1, target.data.unsqueeze(1), self.confidence)
        
        return torch.mean(torch.sum(-true_dist * pred, dim=-1))


class EnhancedDataAugmentation:
    """增强的数据增强"""
    def __init__(self, p=0.7):
        self.p = p
    
    def __call__(self, x, y):
        """
        对序列进行增强的数据增强
        Args:
            x: (seq_len, 3000, channels)
            y: (seq_len,)
        """
        if torch.rand(1).item() < self.p:
            # 1. 时间偏移
            if torch.rand(1).item() < 0.5:
                shift = torch.randint(-100, 100, (1,)).item()
                x = torch.roll(x, shifts=shift, dims=1)
            
            # 2. 幅度缩放（每个通道独立）
            if torch.rand(1).item() < 0.6:
                for c in range(x.shape[-1]):
                    scale = torch.FloatTensor(1).uniform_(0.8, 1.2).item()
                    x[..., c] = x[..., c] * scale
            
            # 3. 频域增强（添加随机频率噪声）
            if torch.rand(1).item() < 0.3:
                # 对每个epoch进行FFT
                for seq_idx in range(x.shape[0]):
                    for ch in range(x.shape[-1]):
                        signal = x[seq_idx, :, ch]
                        # 简单的频域噪声
                        noise_scale = 0.02
                        noise = torch.randn_like(signal) * noise_scale
                        x[seq_idx, :, ch] = signal + noise
            
            # 4. 时间遮蔽（Temporal Masking）
            if torch.rand(1).item() < 0.2:
                mask_length = torch.randint(50, 200, (1,)).item()
                start_pos = torch.randint(0, max(1, 3000 - mask_length), (1,)).item()
                x[:, start_pos:start_pos + mask_length, :] *= 0.1
        
        return x, y


def create_auxiliary_labels(labels):
    """创建辅助任务标签 - 适配V2模型的单辅助输出"""
    # 检测深睡眠（N3和REM）
    deep_sleep = ((labels == 3) | (labels == 4)).long()
    return deep_sleep


def get_cosine_schedule_with_warmup(optimizer, num_warmup_steps, num_training_steps, num_cycles=0.5):
    """余弦退火学习率调度器，带预热"""
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        progress = float(current_step - num_warmup_steps) / float(max(1, num_training_steps - num_warmup_steps))
        return max(0.0, 0.5 * (1.0 + np.cos(np.pi * float(num_cycles) * 2.0 * progress)))
    
    return torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)


def train_epoch_v8(model, train_loader, criterion, aux_criterion, 
                   temp_loss_fn, optimizer, device, epoch, config, data_aug, scaler):
    """V8增强训练函数"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch} - Train')
    
    for batch_idx, (data, labels) in enumerate(pbar):
        data = data.to(device, non_blocking=True)
        labels = labels.to(device, non_blocking=True)
        
        # 增强数据增强
        data, labels = data_aug(data, labels)
        
        optimizer.zero_grad()
        
        # 混合精度前向传播
        with torch.cuda.amp.autocast(enabled=config.get('use_amp', False)):
            # 前向传播
            main_output, aux_output = model(data)
            
            # 主损失（标签平滑）
            main_loss = criterion(main_output.view(-1, 5), labels.view(-1))
            
            # 辅助损失
            aux_labels = create_auxiliary_labels(labels)
            aux_loss = aux_criterion(aux_output.view(-1, 2), aux_labels.view(-1))
            
            # 时序一致性损失
            temp_loss = temp_loss_fn(main_output)
            
            # 总损失
            loss = (main_loss + 
                   0.2 * aux_loss + 
                   config['temp_loss_weight'] * temp_loss)
        
        # 混合精度反向传播
        if config.get('use_amp', False):
            scaler.scale(loss).backward()
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            scaler.step(optimizer)
            scaler.update()
        else:
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        preds = torch.argmax(main_output, dim=-1)
        all_preds.extend(preds.cpu().numpy().flatten())
        all_labels.extend(labels.cpu().numpy().flatten())
        
        pbar.set_postfix({
            'loss': f'{loss.item():.4f}',
            'main': f'{main_loss.item():.4f}',
            'aux': f'{aux_loss.item():.4f}',
            'temp': f'{temp_loss.item():.4f}'
        })
    
    acc = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='macro')
    avg_loss = total_loss / len(train_loader)
    
    return avg_loss, acc, f1


def evaluate_epoch_level_v8(model, test_dataset, test_loader, device, config):
    """V8正确的epoch级别评估"""
    model.eval()
    evaluator = EpochLevelEvaluator(seq_len=config['seq_len'], n_classes=5)
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for batch_idx, (data, labels) in enumerate(tqdm(test_loader, desc="Evaluating")):
            data = data.to(device, non_blocking=True)
            labels = labels.to(device, non_blocking=True)
            
            # 获取模型输出
            outputs, _ = model(data)
            probs = torch.softmax(outputs, dim=-1)
            
            # 获取序列信息
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
                    else:
                        start_indices.append(seq_idx)
                else:
                    break
            
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    return evaluator.evaluate()


def load_correct_split_data(split_config_path, data_dir):
    """加载正确划分的数据集"""
    with open(split_config_path, 'r') as f:
        splits = json.load(f)
    
    train_files = [os.path.join(data_dir, f) for f in splits['splits']['train']['files']]
    val_files = [os.path.join(data_dir, f) for f in splits['splits']['val']['files']]
    test_files = [os.path.join(data_dir, f) for f in splits['splits']['test']['files']]
    
    return train_files, val_files, test_files


def train_v8_enhanced(config, device):
    """V8训练主函数 - 增强版本"""
    logging.info("\\n" + "="*80)
    logging.info("🚀 开始训练 Sequential MAMBAFORMER V8 - 增强版本")
    logging.info("📋 新特性：正确评估 + 标签平滑 + 预热调度 + 混合精度 + 增强数据增强")
    
    # 加载数据
    split_config_path = '../../configs/subject_aware_splits.json'
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    train_files, val_files, test_files = load_correct_split_data(split_config_path, data_dir)
    
    # 创建数据集
    train_dataset = SequenceSleepDataset(train_files, max_samples_per_file=None, seq_len=config['seq_len'], use_channels=3)
    val_dataset = SequenceSleepDataset(val_files, max_samples_per_file=None, seq_len=config['seq_len'], use_channels=3)
    test_dataset = SequenceSleepDataset(test_files, max_samples_per_file=None, seq_len=config['seq_len'], use_channels=3)
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, num_workers=4, pin_memory=True)
    val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4, pin_memory=True)
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False, num_workers=4, pin_memory=True)
    
    logging.info(f"📊 数据集大小: 训练={len(train_dataset)}, 验证={len(val_dataset)}, 测试={len(test_dataset)}")
    
    # 创建增强模型
    model = SequentialMAMBAFORMER_V2(
        input_channels=3, n_classes=5, d_model=config['d_model'],
        n_heads=config['n_heads'], n_layers=config['n_layers'],
        dropout=config['dropout'], seq_len=config['seq_len']
    ).to(device)
    
    # 增强损失函数
    criterion = LabelSmoothingLoss(n_classes=5, smoothing=config['label_smoothing'])
    aux_criterion = nn.CrossEntropyLoss()
    temp_loss_fn = TemporalConsistencyLoss(weight=config['temp_loss_weight'])
    
    # 增强数据增强
    data_aug = EnhancedDataAugmentation(p=0.7)
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=config['learning_rate'], 
                           weight_decay=config['weight_decay'], betas=(0.9, 0.999))
    
    # 学习率调度器（预热 + 余弦退火）
    num_training_steps = config['num_epochs'] * len(train_loader)
    num_warmup_steps = int(0.1 * num_training_steps)  # 10% warmup
    scheduler = get_cosine_schedule_with_warmup(optimizer, num_warmup_steps, num_training_steps)
    
    # 混合精度训练
    scaler = torch.cuda.amp.GradScaler(enabled=config.get('use_amp', False))
    
    logging.info(f"🎯 使用标签平滑: {config['label_smoothing']}")
    logging.info(f"⚡ 使用混合精度: {config.get('use_amp', False)}")
    logging.info(f"🔥 预热步数: {num_warmup_steps}, 总步数: {num_training_steps}")
    
    # 训练循环
    best_val_metrics = {'macro_f1': 0, 'kappa': 0}
    best_model_state = None
    patience_counter = 0
    
    for epoch in range(1, config['num_epochs'] + 1):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch_v8(
            model, train_loader, criterion, aux_criterion, 
            temp_loss_fn, optimizer, device, epoch, config, data_aug, scaler
        )
        
        # 验证（使用正确的epoch级别评估）
        val_metrics = evaluate_epoch_level_v8(model, val_dataset, val_loader, device, config)
        
        # 学习率调度
        if scheduler:
            for _ in range(len(train_loader)):  # 每个batch都更新
                scheduler.step()
        
        # 记录
        current_lr = optimizer.param_groups[0]['lr']
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        logging.info(f"Train - Loss: {train_loss:.4f}, Acc: {train_acc:.4f}, F1: {train_f1:.4f}")
        logging.info(f"Val   - Acc: {val_metrics['accuracy']:.4f}, F1: {val_metrics['macro_f1']:.4f}, Kappa: {val_metrics['kappa']:.4f}")
        logging.info(f"Val REM F1: {val_metrics['per_class_metrics']['REM']['f1']:.4f}")
        logging.info(f"LR: {current_lr:.2e}")
        
        # 保存最佳模型
        val_score = val_metrics['macro_f1'] + 0.1 * val_metrics['per_class_metrics']['REM']['f1']
        best_score = best_val_metrics['macro_f1'] + 0.1 * (best_val_metrics.get('rem_f1', 0))
        
        if val_score > best_score:
            best_val_metrics = val_metrics.copy()
            best_val_metrics['rem_f1'] = val_metrics['per_class_metrics']['REM']['f1']
            best_model_state = model.state_dict().copy()
            patience_counter = 0
            logging.info(f"💾 新的最佳模型: F1={val_metrics['macro_f1']:.4f}, Kappa={val_metrics['kappa']:.4f}")
        else:
            patience_counter += 1
        
        if patience_counter >= config['patience']:
            logging.info(f"⏹️  早停: {config['patience']}轮未改善")
            break
    
    # 测试评估
    if best_model_state:
        model.load_state_dict(best_model_state)
    
    test_metrics = evaluate_epoch_level_v8(model, test_dataset, test_loader, device, config)
    
    # 详细测试结果
    log_epoch_level_metrics(test_metrics, phase='Test V8', logger=logging)
    
    # 保存模型
    os.makedirs('../../checkpoints', exist_ok=True)
    torch.save(model.state_dict(), '../../checkpoints/sequential_v8_enhanced.pth')
    
    return {
        'test_metrics': test_metrics,
        'best_val_metrics': best_val_metrics,
        'config': config
    }


def main():
    # V8增强配置
    config = {
        'batch_size': 32,
        'seq_len': 5,
        'learning_rate': 3e-5,  # 稍微提高学习率，因为有预热
        'weight_decay': 1e-4,
        'num_epochs': 60,  # 更多epochs利用更好的调度器
        'patience': 12,
        'd_model': 128,
        'n_heads': 8,
        'n_layers': 4,
        'dropout': 0.1,  # 降低dropout，因为有标签平滑
        'temp_loss_weight': 0.1,
        'label_smoothing': 0.05,  # 轻度标签平滑
        'use_amp': True,  # 混合精度训练
    }
    
    log_file = setup_logging()
    
    logging.info("🚀 序列MAMBAFORMER V8训练 - 增强版本")
    logging.info("=" * 80)
    logging.info("🎯 V8新特性:")
    logging.info("  • 正确的epoch级别评估")
    logging.info("  • 标签平滑损失")
    logging.info("  • 学习率预热 + 余弦退火")
    logging.info("  • 混合精度训练")
    logging.info("  • 增强数据增强")
    logging.info("  • 深睡眠检测辅助任务")
    logging.info(f"📋 配置: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"🖥️  使用设备: {device}")
    
    # 训练
    result = train_v8_enhanced(config, device)
    
    # 对比V7结果
    logging.info("\\n" + "="*80)
    logging.info("📊 V7 vs V8 结果对比")
    logging.info("="*80)
    
    # V7正确指标（从之前的评估得到）
    v7_correct = {
        'accuracy': 0.8564,
        'macro_f1': 0.7890,
        'kappa': 0.8051,
        'rem_f1': 0.8152
    }
    
    v8_metrics = result['test_metrics']
    v8_rem_f1 = v8_metrics['per_class_metrics']['REM']['f1']
    
    logging.info(f"指标对比 (V7正确 → V8):")
    logging.info(f"准确率:  {v7_correct['accuracy']:.4f} → {v8_metrics['accuracy']:.4f} ({v8_metrics['accuracy'] - v7_correct['accuracy']:+.4f})")
    logging.info(f"Macro F1: {v7_correct['macro_f1']:.4f} → {v8_metrics['macro_f1']:.4f} ({v8_metrics['macro_f1'] - v7_correct['macro_f1']:+.4f})")
    logging.info(f"Kappa:   {v7_correct['kappa']:.4f} → {v8_metrics['kappa']:.4f} ({v8_metrics['kappa'] - v7_correct['kappa']:+.4f})")
    logging.info(f"REM F1:  {v7_correct['rem_f1']:.4f} → {v8_rem_f1:.4f} ({v8_rem_f1 - v7_correct['rem_f1']:+.4f})")
    
    # 保存结果
    results = {
        'version': 'V8_enhanced',
        'result': result,
        'log_file': log_file,
        'improvements': ['correct_evaluation', 'label_smoothing', 'cosine_schedule', 'mixed_precision', 'enhanced_augmentation']
    }
    
    with open('../../configs/sequential_v8_enhanced_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=lambda x: float(x) if hasattr(x, 'item') else x)
    
    logging.info(f"\\n💾 V8结果已保存")
    logging.info("🎯 V8训练完成！")


if __name__ == "__main__":
    main()