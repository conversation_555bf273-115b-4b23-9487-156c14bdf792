#!/usr/bin/env python3
"""
V14 Context-Only 实现
使用序列上下文，但只预测中心位置
这是更合理的方法

核心思想：
1. 输入5个连续epochs
2. 只预测中心位置的标签
3. 利用Transformer建模序列依赖
4. 推理时使用滑动窗口+投票
"""

import os
import sys
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
from collections import Counter

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.sequence_dataset import SequenceSleepDataset


class SequentialFeatureExtractor(nn.Module):
    """特征提取器 - 从V14 FIXED继承"""
    def __init__(self, input_channels=3, d_model=192):
        super().__init__()
        
        self.conv_layers = nn.Sequential(
            # 第一层
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6, padding=24),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=8, stride=8),
            nn.Dropout(0.2),
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=8, stride=1, padding=4),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.MaxPool1d(kernel_size=4, stride=4),
            nn.Dropout(0.2),
            
            # 第三层
            nn.Conv1d(128, d_model, kernel_size=4, stride=1, padding=2),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
    def forward(self, x):
        # x: (batch, seq_len, time_steps, channels)
        batch_size, seq_len, time_steps, channels = x.shape
        
        # Reshape for conv processing
        x = x.view(batch_size * seq_len, channels, time_steps)
        
        # Extract features
        x = self.conv_layers(x)
        
        # Global average pooling
        x = F.adaptive_avg_pool1d(x, 1).squeeze(-1)
        
        # Reshape back
        x = x.view(batch_size, seq_len, -1)
        
        return x


class ContextOnlyMAMBAFORMER(nn.Module):
    """
    使用序列上下文但只预测中心位置
    """
    def __init__(self, input_channels=3, n_classes=5, d_model=192, 
                 n_heads=12, n_layers=4, dropout=0.15, seq_len=5):
        super().__init__()
        
        self.seq_len = seq_len
        self.d_model = d_model
        self.n_classes = n_classes
        
        # 特征提取器
        self.feature_extractor = SequentialFeatureExtractor(input_channels, d_model)
        
        # Positional encoding
        self.pos_encoder = nn.Parameter(torch.randn(1, seq_len, d_model) * 0.02)
        
        # Transformer encoder - 处理序列信息
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='gelu',
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 只对中心位置的特征进行分类
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # N1专门分支
        self.n1_branch = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, n_classes)
        )
        
        # 融合门控
        self.fusion_gate = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Linear(d_model // 4, 1),
            nn.Sigmoid()
        )
        
        self._init_weights()
        
    def _init_weights(self):
        """初始化权重"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
    
    def forward(self, x):
        """
        输入: (batch, seq_len, time_steps, channels)
        输出: (batch, n_classes) - 只预测中心位置！
        """
        batch_size = x.shape[0]
        
        # 特征提取
        features = self.feature_extractor(x)  # (batch, seq_len, d_model)
        
        # 添加位置编码
        features = features + self.pos_encoder
        
        # Transformer处理 - 建模序列间的依赖关系
        encoded = self.transformer(features)  # (batch, seq_len, d_model)
        
        # 只取中心位置的编码特征
        center_idx = self.seq_len // 2
        center_features = encoded[:, center_idx, :]  # (batch, d_model)
        
        # 分类
        main_out = self.classifier(center_features)  # (batch, n_classes)
        n1_out = self.n1_branch(center_features)  # (batch, n_classes)
        
        # 融合
        gate = self.fusion_gate(center_features)  # (batch, 1)
        final_out = gate * main_out + (1 - gate) * n1_out
        
        return final_out, n1_out


class REMFocusedLoss(nn.Module):
    """REM和N1聚焦的损失函数"""
    def __init__(self, rem_weight=2.5, n1_weight=3.5, gamma=2.0):
        super().__init__()
        self.rem_weight = rem_weight
        self.n1_weight = n1_weight
        self.gamma = gamma
        
        # 类别权重 [Wake, N1, N2, N3, REM]
        self.class_weights = torch.tensor([2.0, n1_weight, 1.0, 1.0, rem_weight])
        
    def forward(self, predictions, targets):
        """
        predictions: (batch, n_classes)
        targets: (batch,)
        """
        # 移动class_weights到正确的设备
        if self.class_weights.device != predictions.device:
            self.class_weights = self.class_weights.to(predictions.device)
        
        # Focal loss
        ce_loss = F.cross_entropy(predictions, targets, weight=self.class_weights, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        return focal_loss.mean()


def train_epoch(model, train_loader, criterion, optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}')
    for batch_idx, (data, target) in enumerate(pbar):
        data = data.to(device)
        
        # 只使用中心位置的标签
        if target.dim() == 2:  # (batch, seq_len)
            center_idx = target.shape[1] // 2
            target = target[:, center_idx]
        
        target = target.to(device)
        
        # 数据增强
        if np.random.random() < 0.3:
            noise = torch.randn_like(data) * 0.005
            data = data + noise
        
        optimizer.zero_grad()
        
        # 前向传播
        predictions, n1_predictions = model(data)
        
        # 计算损失
        main_loss = criterion(predictions, target)
        n1_loss = criterion(n1_predictions, target) * 0.3
        
        loss = main_loss + n1_loss
        
        # L2正则化
        l2_lambda = 1e-5
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        
        optimizer.step()
        
        total_loss += loss.item()
        
        # 收集预测
        preds = predictions.argmax(dim=1)
        all_preds.extend(preds.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
        
        # 更新进度条
        acc = (preds == target).float().mean().item()
        pbar.set_postfix({'loss': f'{loss.item():.4f}', 'acc': f'{acc:.4f}'})
    
    # 计算epoch指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, f1


def evaluate_with_voting(model, data_loader, device, use_voting=True):
    """
    评估模型
    如果use_voting=True，使用滑动窗口投票
    """
    model.eval()
    
    if not use_voting:
        # 标准评估
        all_preds = []
        all_targets = []
        
        with torch.no_grad():
            for data, target in tqdm(data_loader, desc='Standard Evaluation'):
                data = data.to(device)
                
                predictions, _ = model(data)
                preds = predictions.argmax(dim=1)
                
                if target.dim() == 2:
                    center_idx = target.shape[1] // 2
                    target = target[:, center_idx]
                
                all_preds.extend(preds.cpu().numpy())
                all_targets.extend(target.numpy())
    else:
        # 投票评估 - 对每个样本多次预测
        all_preds = []
        all_targets = []
        
        with torch.no_grad():
            for data, target in tqdm(data_loader, desc='Voting Evaluation'):
                data = data.to(device)
                batch_size = data.shape[0]
                
                # 每个样本的多次预测
                for b in range(batch_size):
                    sample_data = data[b:b+1]
                    
                    # 收集多个预测（通过轻微扰动）
                    predictions_list = []
                    
                    # 原始预测
                    pred, _ = model(sample_data)
                    predictions_list.append(pred.argmax(dim=1).item())
                    
                    # 添加轻微噪声获得更多预测
                    for _ in range(4):
                        noise = torch.randn_like(sample_data) * 0.002
                        noisy_data = sample_data + noise
                        pred, _ = model(noisy_data)
                        predictions_list.append(pred.argmax(dim=1).item())
                    
                    # 投票
                    final_pred = Counter(predictions_list).most_common(1)[0][0]
                    all_preds.append(final_pred)
                    
                    # 获取真实标签
                    if target.dim() == 2:
                        center_idx = target.shape[1] // 2
                        true_label = target[b, center_idx].item()
                    else:
                        true_label = target[b].item()
                    
                    all_targets.append(true_label)
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_preds)
    f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, f1, kappa, class_f1, cm


def main():
    # 配置（基于V14 FIXED的最佳配置）
    config = {
        'd_model': 192,
        'n_heads': 12,
        'n_layers': 4,
        'dropout': 0.15,
        'seq_len': 5,
        'batch_size': 32,
        'learning_rate': 2e-4,
        'weight_decay': 1e-4,
        'num_epochs': 50,
        'patience': 10,
        'rem_weight': 2.5,
        'n1_weight': 3.5,
    }
    
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f'../logs/v14_context_only_{timestamp}'
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'training.log')
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🚀 V14 Context-Only Training")
    logging.info("="*80)
    logging.info("✅ 使用序列上下文，但只预测中心位置")
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Device: {device}")
    
    # 数据路径
    data_dir = '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/'
    
    # 数据分割（与V14 FIXED相同）
    train_files = [
        'SC4181E0.npz', 'SC4182E0.npz', 'SC4161E0.npz', 'SC4162E0.npz',
        'SC4131E0.npz', 'SC4101E0.npz', 'SC4102E0.npz', 'SC4111E0.npz',
        'SC4112E0.npz', 'SC4041E0.npz', 'SC4042E0.npz', 'SC4191E0.npz',
        'SC4192E0.npz', 'SC4061E0.npz', 'SC4062E0.npz', 'SC4091E0.npz',
        'SC4092E0.npz', 'SC4121E0.npz', 'SC4122E0.npz', 'SC4141E0.npz',
        'SC4142E0.npz', 'SC4051E0.npz', 'SC4052E0.npz', 'SC4081E0.npz',
        'SC4082E0.npz'
    ]
    
    val_files = [
        'SC4021E0.npz', 'SC4022E0.npz', 'SC4031E0.npz', 'SC4032E0.npz'
    ]
    
    test_files = [
        'SC4001E0.npz', 'SC4002E0.npz', 'SC4011E0.npz', 'SC4012E0.npz',
        'SC4071E0.npz', 'SC4072E0.npz', 'SC4151E0.npz', 'SC4152E0.npz',
        'SC4171E0.npz', 'SC4172E0.npz'
    ]
    
    # 创建数据集
    logging.info("Loading datasets...")
    train_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in train_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,
        is_training=True
    )
    
    val_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in val_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,
        is_training=False
    )
    
    test_dataset = SequenceSleepDataset(
        [os.path.join(data_dir, f) for f in test_files],
        seq_len=config['seq_len'],
        max_samples_per_file=None,
        is_training=False
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # 数据加载器
    train_loader = torch.utils.data.DataLoader(
        train_dataset, batch_size=config['batch_size'],
        shuffle=True, num_workers=4, pin_memory=True, drop_last=True
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset, batch_size=config['batch_size'],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # 创建模型
    model = ContextOnlyMAMBAFORMER(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    logging.info(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数
    criterion = REMFocusedLoss(
        rem_weight=config['rem_weight'],
        n1_weight=config['n1_weight']
    )
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='max', factor=0.5, patience=5, min_lr=1e-6
    )
    
    # 训练循环
    best_val_acc = 0
    best_test_acc = 0
    patience_counter = 0
    
    logging.info("\n" + "="*80)
    logging.info("Starting Training!")
    logging.info("="*80)
    
    for epoch in range(config['num_epochs']):
        # 训练
        train_loss, train_acc, train_f1 = train_epoch(
            model, train_loader, criterion, optimizer, device, epoch
        )
        
        # 标准评估（快速）
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate_with_voting(
            model, val_loader, device, use_voting=False
        )
        
        # 调整学习率
        scheduler.step(val_acc)
        current_lr = optimizer.param_groups[0]['lr']
        
        # 记录
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']} (LR: {current_lr:.2e}):")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  Val Class F1: W={val_class_f1[0]:.3f}, N1={val_class_f1[1]:.3f}, "
                    f"N2={val_class_f1[2]:.3f}, N3={val_class_f1[3]:.3f}, REM={val_class_f1[4]:.3f}")
        
        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            
            # 使用投票评估测试集
            logging.info("  Evaluating on test set with voting...")
            test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate_with_voting(
                model, test_loader, device, use_voting=True
            )
            best_test_acc = test_acc
            
            # 保存模型
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config
            }, os.path.join(log_dir, 'best_model.pth'))
            
            logging.info(f"  💾 Saved best model (Val: {val_acc:.4f}, Test with voting: {test_acc:.4f})")
            logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                        f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
            
            if test_acc >= 0.90:
                logging.info("\n" + "="*80)
                logging.info("🎉 ACHIEVED 90% TEST ACCURACY!")
                logging.info("="*80)
                break
        else:
            patience_counter += 1
            if patience_counter >= config['patience']:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
    
    # 最终结果
    logging.info("\n" + "="*80)
    logging.info("📊 FINAL RESULTS")
    logging.info("="*80)
    logging.info(f"Best Val Accuracy: {best_val_acc:.4f}")
    logging.info(f"Best Test Accuracy (with voting): {best_test_acc:.4f} ({best_test_acc*100:.2f}%)")
    
    gap_to_90 = 0.90 - best_test_acc
    if best_test_acc >= 0.90:
        logging.info(f"✅ TARGET ACHIEVED!")
    else:
        logging.info(f"Gap to 90%: {gap_to_90:.4f} ({gap_to_90*100:.2f}%)")
    
    # 保存结果
    results = {
        'best_val_acc': float(best_val_acc),
        'best_test_acc': float(best_test_acc),
        'config': config
    }
    
    with open(os.path.join(log_dir, 'results.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"\nResults saved to {log_dir}")


if __name__ == '__main__':
    main()