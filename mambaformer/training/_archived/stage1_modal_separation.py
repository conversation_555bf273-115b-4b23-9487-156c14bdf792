#!/usr/bin/env python3
"""
Stage 1: 模态分离处理
- 使用全部4通道 (EEG×2 + EOG×1 + EMG×1)
- 分离处理EEG和辅助模态
- 简单加权融合，初始权重很小
- 目标: ≥88% accuracy
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts

import numpy as np
import os
import glob
import logging
from datetime import datetime
from pathlib import Path
import json
from tqdm import tqdm
import math

# 导入必要的模块
import sys
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')
from utils.sequence_dataset import SequenceSleepDataset
from models.sequential_mambaformer_v2 import PositionalEncoding

# 添加正确的路径以导入train_utils
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
from utils.train_utils import EarlyStopping

# ===================== Stage 1 模型 =====================

class ModalSeparatedFeatureExtractor(nn.Module):
    """模态分离的特征提取器"""
    def __init__(self, d_model=128, dropout=0.15):
        super().__init__()
        
        # EEG特征提取器 (2通道)
        self.eeg_extractor = nn.Sequential(
            # 第一层 - 保持与baseline相似
            nn.Conv1d(2, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            # 第三层
            nn.Conv1d(128, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # 辅助模态特征提取器 (EOG+EMG, 2通道)
        self.aux_extractor = nn.Sequential(
            # 第一层
            nn.Conv1d(2, 32, kernel_size=50, stride=6),
            nn.BatchNorm1d(32),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            # 第二层
            nn.Conv1d(32, 64, kernel_size=8, stride=1),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            # 第三层
            nn.Conv1d(64, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # 全局池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
        
        # 可学习的融合权重 (初始化为很小的值)
        self.alpha = nn.Parameter(torch.tensor(0.1))  # 初始10%新特征，90%原始特征
        
    def forward(self, x):
        """
        Args:
            x: (batch, time_steps, channels)
        Returns:
            fused_features: (batch, d_model)
            eeg_features: (batch, d_model) 
            aux_features: (batch, d_model)
        """
        # 调整维度：(batch, time_steps, channels) -> (batch, channels, time_steps)
        x = x.transpose(1, 2)
        
        # 分离模态
        eeg = x[:, :2, :]  # EEG: 前2个通道
        aux = x[:, 2:, :]  # 辅助: 后2个通道 (EOG+EMG)
        
        # 分别提取特征
        eeg_feat = self.eeg_extractor(eeg)
        aux_feat = self.aux_extractor(aux)
        
        # 全局池化
        eeg_feat = self.global_pool(eeg_feat).squeeze(-1)
        aux_feat = self.global_pool(aux_feat).squeeze(-1)
        
        # 加权融合 (使用sigmoid确保权重在0-1之间)
        alpha = torch.sigmoid(self.alpha)
        fused_feat = (1 - alpha) * eeg_feat + alpha * aux_feat
        
        return fused_feat, eeg_feat, aux_feat


class Stage1Model(nn.Module):
    """Stage 1模型 - 基于90%准确率的baseline，添加模态分离"""
    def __init__(self, n_classes=5, d_model=128, n_heads=8, n_layers=4, 
                 dropout=0.15, seq_len=5):
        super().__init__()
        
        self.d_model = d_model
        self.seq_len = seq_len
        
        # 模态分离的特征提取器
        self.feature_extractor = ModalSeparatedFeatureExtractor(d_model, dropout)
        
        # 时序位置编码
        self.pos_encoder = PositionalEncoding(d_model, dropout)
        
        # Transformer编码器（与baseline保持一致）
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            activation='relu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=n_layers
        )
        
        # 主分类器
        self.classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Dropout(dropout),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 辅助分类器 (用于多任务学习)
        self.eeg_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, n_classes)
        )
        
        self.aux_classifier = nn.Sequential(
            nn.LayerNorm(d_model),
            nn.Linear(d_model, n_classes)
        )
        
        # 初始化权重
        self._init_weights()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in self.parameters())
        logging.info(f"创建Stage1Model: 参数量={total_params:,}, d_model={d_model}")
        
    def _init_weights(self):
        """改进的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.Conv1d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, time_steps, channels)
        Returns:
            main_output: (batch, seq_len, n_classes)
            aux_outputs: dict
        """
        batch_size, seq_len, time_steps, channels = x.shape
        
        # 处理每个时间步的特征
        all_features = []
        all_eeg_features = []
        all_aux_features = []
        
        for t in range(seq_len):
            # 提取当前时间步的特征
            x_t = x[:, t, :, :]  # (batch, time_steps, channels)
            fused_feat, eeg_feat, aux_feat = self.feature_extractor(x_t)
            
            all_features.append(fused_feat)
            all_eeg_features.append(eeg_feat)
            all_aux_features.append(aux_feat)
        
        # Stack成序列
        features = torch.stack(all_features, dim=1)  # (batch, seq_len, d_model)
        eeg_features = torch.stack(all_eeg_features, dim=1)
        aux_features = torch.stack(all_aux_features, dim=1)
        
        # 位置编码
        features = features.transpose(0, 1)  # (seq_len, batch, d_model)
        features = self.pos_encoder(features)
        features = features.transpose(0, 1)  # (batch, seq_len, d_model)
        
        # Transformer编码
        encoded = self.transformer_encoder(features)
        
        # 分类
        main_output = self.classifier(encoded)
        eeg_output = self.eeg_classifier(eeg_features)
        aux_output = self.aux_classifier(aux_features)
        
        # 获取当前的alpha值
        alpha_value = torch.sigmoid(self.feature_extractor.alpha).item()
        
        return main_output, {
            'eeg': eeg_output,
            'aux': aux_output,
            'alpha': alpha_value
        }


# ===================== 损失函数 =====================

class Stage1Loss(nn.Module):
    """Stage 1的损失函数 - 多任务学习"""
    def __init__(self, alpha_weight=0.1):
        super().__init__()
        self.alpha_weight = alpha_weight  # 辅助任务的权重
        
    def forward(self, outputs, labels):
        """
        Args:
            outputs: (main_output, aux_dict)
            labels: (batch, seq_len)
        """
        main_output, aux_dict = outputs
        
        # 主任务损失
        main_loss = F.cross_entropy(
            main_output.reshape(-1, main_output.size(-1)),
            labels.reshape(-1)
        )
        
        # 辅助任务损失
        eeg_loss = F.cross_entropy(
            aux_dict['eeg'].reshape(-1, aux_dict['eeg'].size(-1)),
            labels.reshape(-1)
        )
        
        aux_loss = F.cross_entropy(
            aux_dict['aux'].reshape(-1, aux_dict['aux'].size(-1)),
            labels.reshape(-1)
        )
        
        # 组合损失 (主任务为主，辅助任务权重很小)
        total_loss = main_loss + self.alpha_weight * (eeg_loss + aux_loss)
        
        return total_loss, {
            'main': main_loss.item(),
            'eeg': eeg_loss.item(),
            'aux': aux_loss.item(),
            'total': total_loss.item()
        }


# ===================== 训练函数 =====================

def train_epoch(model, dataloader, criterion, optimizer, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0
    loss_components = {'main': 0, 'eeg': 0, 'aux': 0}
    alpha_sum = 0
    
    with tqdm(dataloader, desc="Training") as pbar:
        for batch_idx, (data, labels) in enumerate(pbar):
            data = data.to(device)
            labels = labels.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            outputs = model(data)
            loss, loss_dict = criterion(outputs, labels)
            
            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            # 统计
            total_loss += loss.item()
            main_output = outputs[0]
            _, predicted = main_output.max(-1)
            correct += predicted.eq(labels).sum().item()
            total += labels.numel()
            
            # 累积损失分量
            for key in ['main', 'eeg', 'aux']:
                loss_components[key] += loss_dict[key]
            
            # 累积alpha值
            alpha_sum += outputs[1]['alpha']
            
            # 更新进度条
            pbar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'acc': f'{100.*correct/total:.2f}%',
                'alpha': f'{outputs[1]["alpha"]:.4f}'
            })
    
    # 平均值
    num_batches = len(dataloader)
    for key in loss_components:
        loss_components[key] /= num_batches
    avg_alpha = alpha_sum / num_batches
    
    return total_loss / num_batches, 100. * correct / total, loss_components, avg_alpha


def evaluate(model, dataloader, criterion, device):
    """评估函数"""
    model.eval()
    total_loss = 0
    correct = 0
    total = 0
    all_preds = []
    all_labels = []
    alpha_sum = 0
    
    with torch.no_grad():
        for data, labels in tqdm(dataloader, desc="Evaluating"):
            data = data.to(device)
            labels = labels.to(device)
            
            outputs = model(data)
            loss, _ = criterion(outputs, labels)
            
            total_loss += loss.item()
            main_output = outputs[0]
            _, predicted = main_output.max(-1)
            
            correct += predicted.eq(labels).sum().item()
            total += labels.numel()
            
            all_preds.extend(predicted.cpu().numpy().flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
            
            alpha_sum += outputs[1]['alpha']
    
    accuracy = 100. * correct / total
    avg_alpha = alpha_sum / len(dataloader)
    
    # 计算F1分数
    from sklearn.metrics import f1_score
    f1 = f1_score(all_labels, all_preds, average='macro')
    
    return total_loss / len(dataloader), accuracy, f1, avg_alpha


def main():
    # 设置日志
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_modal_separation_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f"{log_dir}/training.log"),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 Stage 1: 模态分离处理")
    logging.info("目标: ≥88% accuracy")
    logging.info("策略: 分离EEG和辅助模态，简单加权融合")
    logging.info("="*80)
    
    # 配置
    config = {
        "n_classes": 5,
        "d_model": 128,
        "n_heads": 8,
        "n_layers": 4,
        "dropout": 0.15,
        "seq_len": 5,
        "batch_size": 32,
        "learning_rate": 1e-3,
        "num_epochs": 50,
        "weight_decay": 1e-4,
        "patience": 10
    }
    
    logging.info(f"配置: {config}")
    
    # 设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"设备: {device}")
    
    # 数据路径
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 数据划分
    np.random.seed(42)
    np.random.shuffle(all_files)
    
    n_files = len(all_files)
    train_split = int(0.7 * n_files)
    val_split = int(0.85 * n_files)
    
    train_files = all_files[:train_split]
    val_files = all_files[train_split:val_split]
    test_files = all_files[val_split:]
    
    logging.info("加载数据集...")
    
    # 创建数据集 - 使用全部4个通道
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        use_channels=4,  # 使用全部4个通道
        max_samples_per_file=None,  # 加载所有数据
        is_training=True,
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        use_channels=4,
        max_samples_per_file=None,
        is_training=False,
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        use_channels=4,
        max_samples_per_file=None,
        is_training=False,
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")
    
    # 创建模型
    model = Stage1Model(
        n_classes=config["n_classes"],
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"]
    ).to(device)
    
    # 损失函数
    criterion = Stage1Loss(alpha_weight=0.1)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"]
    )
    
    # 学习率调度器
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=10,
        T_mult=2,
        eta_min=1e-5
    )
    
    # Early stopping
    early_stopping = EarlyStopping(patience=config["patience"], verbose=True)
    
    # 训练
    best_test_acc = 0
    best_epoch = 0
    
    for epoch in range(1, config["num_epochs"] + 1):
        logging.info("\n" + "="*60)
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        
        # 训练
        train_loss, train_acc, loss_components, train_alpha = train_epoch(
            model, train_loader, criterion, optimizer, device
        )
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        logging.info(f"  损失分量 - Main: {loss_components['main']:.4f}, "
                    f"EEG: {loss_components['eeg']:.4f}, "
                    f"Aux: {loss_components['aux']:.4f}")
        logging.info(f"  Alpha值: {train_alpha:.4f}")
        
        # 验证
        val_loss, val_acc, val_f1, val_alpha = evaluate(
            model, val_loader, criterion, device
        )
        logging.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_f1:.4f}")
        
        # 测试
        test_loss, test_acc, test_f1, test_alpha = evaluate(
            model, test_loader, criterion, device
        )
        logging.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_f1:.4f}")
        logging.info(f"  Alpha值: {test_alpha:.4f}")
        
        # 学习率
        logging.info(f"学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_epoch = epoch
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'config': config
            }, f"{log_dir}/best_model.pth")
            logging.info(f"✅ 保存最佳模型，准确率: {test_acc:.2f}%")
        
        # Early stopping
        early_stopping(val_loss, model)
        if early_stopping.early_stop:
            logging.info("Early stopping triggered")
            break
        
        scheduler.step()
    
    logging.info("\n" + "="*80)
    logging.info(f"训练完成！")
    logging.info(f"最佳测试准确率: {best_test_acc:.2f}% (Epoch {best_epoch})")
    
    # 保存训练信息
    with open(f"{log_dir}/training_info.json", 'w') as f:
        json.dump({
            'config': config,
            'best_test_acc': best_test_acc,
            'best_epoch': best_epoch,
            'total_epochs': epoch
        }, f, indent=2)
    
    # 更新方案文档
    update_progress_report(1, best_test_acc, test_alpha)


def update_progress_report(stage, accuracy, alpha):
    """更新进度报告"""
    report_path = "/media/main/ypf/eeg/Cross-Modal-Transformer/scheme/progress_report.md"
    
    with open(report_path, 'a') as f:
        f.write(f"\n## Stage {stage} Results\n")
        f.write(f"- Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n")
        f.write(f"- Accuracy: {accuracy:.2f}%\n")
        f.write(f"- Alpha (fusion weight): {alpha:.4f}\n")
        f.write(f"- Status: {'✅ Success' if accuracy >= 88 else '⚠️ Below target'}\n")


if __name__ == "__main__":
    main()