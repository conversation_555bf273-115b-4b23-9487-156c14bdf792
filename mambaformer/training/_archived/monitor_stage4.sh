#!/bin/bash
# 监控Stage 4的5分类准确率

echo "监控Stage 4训练进度 (5分类准确率)"
echo "================================"

while true; do
    # 获取最新的epoch和测试准确率
    latest=$(grep -E "测试.*Acc:|Epoch [0-9]+/" stage4_training_v2.out | tail -2)
    
    if [ ! -z "$latest" ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S')"
        echo "$latest"
        echo "---"
    fi
    
    # 检查是否达到目标
    accuracy=$(echo "$latest" | grep -oE "Acc: [0-9]+\.[0-9]+%" | grep -oE "[0-9]+\.[0-9]+")
    if [ ! -z "$accuracy" ]; then
        if (( $(echo "$accuracy >= 85.0" | bc -l) )); then
            echo "🎉 达到目标准确率 85%!"
        fi
    fi
    
    sleep 60
done