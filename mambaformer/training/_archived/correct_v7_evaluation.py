"""
正确的V7 epoch级别评估
使用训练好的V7模型，进行正确的epoch级别聚合评估
"""

import sys
import os
import json
import torch
import numpy as np
from tqdm import tqdm
import logging

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.sequential_mambaformer_v2 import SequentialMAMBAFORMER_V2
from utils.sequence_dataset import SequenceSleepDataset
from utils.epoch_level_evaluation import EpochLevelEvaluator, log_epoch_level_metrics
from utils.enhanced_metrics import get_comprehensive_metrics


def load_v7_model(checkpoint_path, config, device):
    """加载训练好的V7模型"""
    model = SequentialMAMBAFORMER_V2(
        input_channels=3,
        n_classes=5,
        d_model=config['d_model'],
        n_heads=config['n_heads'],
        n_layers=config['n_layers'],
        dropout=config['dropout'],
        seq_len=config['seq_len']
    ).to(device)
    
    model.load_state_dict(torch.load(checkpoint_path, map_location=device))
    model.eval()
    return model


def correct_epoch_evaluation(model, test_dataset, test_loader, device):
    """进行正确的epoch级别评估"""
    print("🔄 开始进行正确的epoch级别评估...")
    
    evaluator = EpochLevelEvaluator(seq_len=5, n_classes=5)
    
    with torch.no_grad():
        batch_start_idx = 0
        
        for batch_idx, (data, labels) in enumerate(tqdm(test_loader, desc="Evaluating")):
            data = data.to(device)
            labels = labels.to(device)
            
            # 获取模型输出
            outputs, _ = model(data)  # (batch_size, seq_len, n_classes)
            probs = torch.softmax(outputs, dim=-1)
            
            # 获取这个批次的序列信息
            batch_size = data.shape[0]
            start_indices = []
            
            for i in range(batch_size):
                seq_idx = batch_start_idx + i
                if seq_idx < len(test_dataset):
                    seq_info = test_dataset.get_sequence_info(seq_idx)
                    if seq_info:
                        start_indices.append(seq_info['start_epoch_idx'])
                    else:
                        # fallback: 估算起始索引
                        start_indices.append(seq_idx)
                else:
                    break
            
            # 只处理有效的序列
            if start_indices:
                valid_batch_size = len(start_indices)
                evaluator.add_batch_predictions(
                    probs[:valid_batch_size].cpu().numpy(),
                    labels[:valid_batch_size].cpu().numpy(),
                    start_indices
                )
            
            batch_start_idx += batch_size
    
    # 计算最终指标
    metrics = evaluator.evaluate()
    return metrics


def main():
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    print("=" * 80)
    print("🎯 V7模型正确的Epoch级别评估")
    print("=" * 80)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 1. 加载V7配置和模型
    with open('../../configs/sequential_v7_balanced_results.json', 'r') as f:
        v7_results = json.load(f)
    
    config = v7_results['config']
    checkpoint_path = '../../checkpoints/sequential_v7_balanced.pth'
    
    if not os.path.exists(checkpoint_path):
        print(f"❌ 模型文件不存在: {checkpoint_path}")
        return
    
    print("📥 加载V7模型...")
    model = load_v7_model(checkpoint_path, config, device)
    print("✅ 模型加载完成")
    
    # 2. 创建测试数据集
    with open('../../configs/subject_aware_splits.json', 'r') as f:
        splits = json.load(f)
    
    test_files = [os.path.join('/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/', f) 
                  for f in splits['splits']['test']['files']]
    
    print("📊 创建测试数据集...")
    test_dataset = SequenceSleepDataset(
        test_files,
        max_samples_per_file=None,  # 使用完整数据
        seq_len=config['seq_len'],
        use_channels=3
    )
    
    test_loader = torch.utils.data.DataLoader(
        test_dataset,
        batch_size=32,  # 与V7训练时相同
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )
    
    print(f"✅ 数据集创建完成:")
    print(f"  - 测试序列数: {len(test_dataset)}")
    print(f"  - 总epochs: {test_dataset.get_total_epochs()}")
    print(f"  - 批次数: {len(test_loader)}")
    
    # 3. 进行正确的评估
    print("\n" + "=" * 60)
    print("🧮 进行正确的epoch级别评估")
    print("=" * 60)
    
    correct_metrics = correct_epoch_evaluation(model, test_dataset, test_loader, device)
    
    # 4. 展示正确结果
    log_epoch_level_metrics(correct_metrics, phase='Test (Correct)', logger=logging)
    
    # 5. 对比V7原始结果
    print("\n" + "=" * 80)
    print("📊 结果对比：原始V7 vs 正确评估")
    print("=" * 80)
    
    v7_original = v7_results['result']
    
    print("指标对比：")
    print(f"{'指标':<15} {'原始V7':<12} {'正确评估':<12} {'差异':<10}")
    print("-" * 55)
    print(f"{'Accuracy':<15} {v7_original['test_acc']:<12.4f} {correct_metrics['accuracy']:<12.4f} {correct_metrics['accuracy'] - v7_original['test_acc']:<10.4f}")
    print(f"{'Macro F1':<15} {v7_original['test_macro_f1']:<12.4f} {correct_metrics['macro_f1']:<12.4f} {correct_metrics['macro_f1'] - v7_original['test_macro_f1']:<10.4f}")
    print(f"{'Kappa':<15} {v7_original['test_kappa']:<12.4f} {correct_metrics['kappa']:<12.4f} {correct_metrics['kappa'] - v7_original['test_kappa']:<10.4f}")
    print(f"{'REM F1':<15} {v7_original['rem_f1']:<12.4f} {correct_metrics['per_class_metrics']['REM']['f1']:<12.4f} {correct_metrics['per_class_metrics']['REM']['f1'] - v7_original['rem_f1']:<10.4f}")
    
    # 混淆矩阵对比
    print(f"\n混淆矩阵对比：")
    print(f"原始V7总数: {np.array(v7_original['confusion_matrix']).sum()}")
    print(f"正确评估总数: {correct_metrics['confusion_matrix'].sum()}")
    
    # 6. 保存正确结果
    correct_results = {
        'method': 'epoch_level_evaluation',
        'model': 'sequential_v7_balanced',
        'config': config,
        'correct_metrics': {
            'accuracy': float(correct_metrics['accuracy']),
            'macro_f1': float(correct_metrics['macro_f1']),
            'kappa': float(correct_metrics['kappa']),
            'confusion_matrix': correct_metrics['confusion_matrix'].tolist(),
            'per_class_metrics': {
                k: {kk: float(vv) if isinstance(vv, np.number) else vv 
                    for kk, vv in v.items()}
                for k, v in correct_metrics['per_class_metrics'].items()
            },
            'total_epochs': correct_metrics['total_epochs'],
            'avg_predictions_per_epoch': float(correct_metrics['avg_predictions_per_epoch'])
        },
        'comparison_with_original': {
            'original_total_predictions': np.array(v7_original['confusion_matrix']).sum(),
            'correct_total_epochs': correct_metrics['total_epochs'],
            'evaluation_method_was_wrong': True
        }
    }
    
    output_path = '../../configs/sequential_v7_correct_evaluation_results.json'
    with open(output_path, 'w') as f:
        json.dump(correct_results, f, indent=2)
    
    print(f"\n💾 正确评估结果已保存至: {output_path}")
    
    # 7. 最终结论
    print("\n" + "=" * 80)
    print("🎯 最终结论")
    print("=" * 80)
    
    acc_diff = correct_metrics['accuracy'] - v7_original['test_acc']
    f1_diff = correct_metrics['macro_f1'] - v7_original['test_macro_f1']
    kappa_diff = correct_metrics['kappa'] - v7_original['test_kappa']
    rem_f1_diff = correct_metrics['per_class_metrics']['REM']['f1'] - v7_original['rem_f1']
    
    print(f"✅ 已完成V7模型的正确epoch级别评估")
    print(f"📊 正确的测试指标：")
    print(f"   - 准确率: {correct_metrics['accuracy']:.4f} (差异: {acc_diff:+.4f})")
    print(f"   - Macro F1: {correct_metrics['macro_f1']:.4f} (差异: {f1_diff:+.4f})")
    print(f"   - Kappa: {correct_metrics['kappa']:.4f} (差异: {kappa_diff:+.4f})")
    print(f"   - REM F1: {correct_metrics['per_class_metrics']['REM']['f1']:.4f} (差异: {rem_f1_diff:+.4f})")
    
    if abs(acc_diff) > 0.01 or abs(f1_diff) > 0.01:
        print(f"\n⚠️  指标差异较大，说明原始评估确实有问题")
        print(f"📈 应该使用这些正确的指标来评价模型性能")
    else:
        print(f"\n✅ 指标差异很小，说明虽然评估方法不同，但结果相近")


if __name__ == "__main__":
    main()