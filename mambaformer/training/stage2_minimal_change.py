#!/usr/bin/env python3
"""
Stage 2: 最小化改动策略
核心思想：在Stage 1的基础上只添加一个轻量级的注意力增强模块
不改变原有架构，只是增强特征
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import CosineAnnealingLR
import logging
from datetime import datetime
from tqdm import tqdm
from sklearn.metrics import (
    confusion_matrix,
    classification_report,
    accuracy_score,
    f1_score,
    cohen_kappa_score,
)
import warnings

warnings.filterwarnings("ignore")

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.progressive_mambaformer_v1_fixed import ProgressiveMAMBAFORMER_V1_Fixed
from utils.sequence_dataset import SequenceSleepDataset


class AttentionEnhancement(nn.Module):
    """
    极简的注意力增强模块
    只在最后添加一个额外的注意力层，不改变原有结构
    """
    def __init__(self, d_model=512, n_heads=8, dropout=0.1):
        super().__init__()
        # 额外的自注意力层
        self.extra_attention = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        self.norm = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
        # 学习率为0的残差权重，确保开始时完全保留原始特征
        self.alpha = nn.Parameter(torch.zeros(1))
        
    def forward(self, x):
        # x: [batch_size, seq_len, d_model]
        
        # 额外的自注意力
        attn_out, _ = self.extra_attention(x, x, x)
        
        # 极小的权重混合，开始时alpha=0，完全是原始特征
        enhanced = x + torch.sigmoid(self.alpha) * 0.1 * self.dropout(attn_out)
        enhanced = self.norm(enhanced)
        
        return enhanced


def train_one_epoch(model, attention_module, data_loader, criterion, optimizer, device, epoch, config):
    """训练一个epoch"""
    model.eval()  # 基础模型保持eval模式
    attention_module.train()  # 只训练注意力模块
    
    running_loss = 0.0
    correct_predictions = 0
    total_predictions = 0
    
    progress_bar = tqdm(data_loader, desc=f"Epoch {epoch+1}/{config['num_epochs']}")
    
    for batch_idx, (data, target) in enumerate(progress_bar):
        data, target = data.to(device), target.to(device)
        data = data.permute(0, 1, 3, 2)
        
        optimizer.zero_grad()
        
        # 通过基础模型（不计算梯度）
        with torch.no_grad():
            # 重塑数据
            batch_size, seq_len, channels, time_steps = data.shape
            x_reshaped = data.view(batch_size * seq_len, channels, time_steps)
            
            # 提取特征
            base_features = model.feature_extractor(x_reshaped)  # (batch*seq_len, d_model)
            base_features = base_features.view(batch_size, seq_len, model.d_model)
            
            # 添加位置编码
            base_features = base_features.transpose(0, 1)  # (seq_len, batch, d_model)
            base_features = model.pos_encoder(base_features)
            base_features = base_features.transpose(0, 1)  # (batch, seq_len, d_model)
            
            # 通过前11层encoder
            for i in range(11):
                base_features = model.transformer_encoder.layers[i](base_features)
        
        # 应用注意力增强（只这部分计算梯度）
        enhanced_features = attention_module(base_features)
        
        # 通过最后一层encoder
        with torch.no_grad():
            final_features = model.transformer_encoder.layers[11](enhanced_features)
        
        # 需要梯度来计算损失
        final_features.requires_grad_(True)
        
        # 分类器
        main_output = model.classifier(final_features)
        auxiliary_output = model.auxiliary_classifier(final_features) if hasattr(model, 'auxiliary_classifier') else None
        
        # 计算准确率
        batch_size, seq_len, n_classes = main_output.shape
        _, predicted = torch.max(main_output[:, seq_len//2, :], dim=-1)
        true_labels = target[:, seq_len//2]
        correct_predictions += (predicted == true_labels).sum().item()
        total_predictions += true_labels.size(0)
        
        # 计算损失
        main_output_flat = main_output.reshape(-1, n_classes)
        target_flat = target.reshape(-1)
        
        main_loss = criterion(main_output_flat, target_flat)
        
        if auxiliary_output is not None:
            aux_output_flat = auxiliary_output.reshape(-1, n_classes)
            aux_loss = criterion(aux_output_flat, target_flat)
            total_loss = 0.9 * main_loss + 0.1 * aux_loss
        else:
            total_loss = main_loss
        
        total_loss.backward()
        
        # 只更新注意力模块的梯度
        torch.nn.utils.clip_grad_norm_(attention_module.parameters(), config['gradient_clip'])
        
        optimizer.step()
        
        running_loss += total_loss.item()
        
        current_acc = 100 * correct_predictions / total_predictions
        alpha_value = torch.sigmoid(attention_module.alpha).item()
        
        progress_bar.set_postfix({
            'loss': f'{running_loss/(batch_idx+1):.4f}',
            'acc': f'{current_acc:.2f}%',
            'alpha': f'{alpha_value:.4f}',
            'lr': f'{optimizer.param_groups[0]["lr"]:.7f}'
        })
    
    epoch_loss = running_loss / len(data_loader)
    epoch_acc = 100 * correct_predictions / total_predictions
    
    return epoch_loss, epoch_acc


def evaluate(model, attention_module, data_loader, device):
    """评估模型"""
    model.eval()
    attention_module.eval()
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in tqdm(data_loader, desc="Evaluating"):
            data = data.to(device)
            data = data.permute(0, 1, 3, 2)
            
            # 通过基础模型
            batch_size, seq_len, channels, time_steps = data.shape
            x_reshaped = data.view(batch_size * seq_len, channels, time_steps)
            
            # 提取特征
            base_features = model.feature_extractor(x_reshaped)
            base_features = base_features.view(batch_size, seq_len, model.d_model)
            
            # 添加位置编码
            base_features = base_features.transpose(0, 1)
            base_features = model.pos_encoder(base_features)
            base_features = base_features.transpose(0, 1)
            
            # 通过前11层
            for i in range(11):
                base_features = model.transformer_encoder.layers[i](base_features)
            
            # 应用注意力增强
            enhanced_features = attention_module(base_features)
            
            # 最后一层
            final_features = model.transformer_encoder.layers[11](enhanced_features)
            
            # 分类
            main_output = model.classifier(final_features)
            
            seq_len = main_output.size(1)
            predictions = torch.argmax(main_output[:, seq_len//2, :], dim=-1)
            targets = target[:, seq_len//2]
            
            all_predictions.extend(predictions.cpu().numpy())
            all_targets.extend(targets.numpy())
    
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    
    accuracy = accuracy_score(all_targets, all_predictions)
    f1_macro = f1_score(all_targets, all_predictions, average='macro')
    kappa = cohen_kappa_score(all_targets, all_predictions)
    
    report = classification_report(all_targets, all_predictions, 
                                 target_names=['W', 'N1', 'N2', 'N3', 'REM'],
                                 output_dict=True)
    
    cm = confusion_matrix(all_targets, all_predictions)
    
    return {
        'accuracy': accuracy,
        'f1_macro': f1_macro,
        'kappa': kappa,
        'classification_report': report,
        'confusion_matrix': cm
    }


def main():
    config = {
        "d_model": 512,
        "n_heads": 32,
        "n_layers": 12,
        "dropout": 0.15,
        "seq_len": 7,
        "batch_size": 16,
        "learning_rate": 1e-4,  # 只训练一个小模块，可以用稍高的学习率
        "num_epochs": 10,
        "gradient_clip": 0.5,
        "weight_decay": 0.0,  # 不要weight decay
    }

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_dir = f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage2_minimal_{timestamp}"
    os.makedirs(log_dir, exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler(os.path.join(log_dir, "training.log")),
            logging.StreamHandler(),
        ],
    )

    logging.info("="*80)
    logging.info("🎯 Stage 2: 最小化改动策略 - 注意力增强")
    logging.info("="*80)
    logging.info(f"配置: {config}")

    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    logging.info(f"Device: {device}")

    # 数据准备
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"
    
    train_files = [
        "SC4181E0.npz", "SC4182E0.npz", "SC4161E0.npz", "SC4162E0.npz",
        "SC4131E0.npz", "SC4101E0.npz", "SC4102E0.npz", "SC4111E0.npz",
        "SC4112E0.npz", "SC4041E0.npz", "SC4042E0.npz", "SC4191E0.npz",
        "SC4192E0.npz", "SC4061E0.npz", "SC4062E0.npz", "SC4091E0.npz",
        "SC4092E0.npz", "SC4121E0.npz", "SC4122E0.npz", "SC4141E0.npz",
        "SC4142E0.npz", "SC4051E0.npz", "SC4052E0.npz", "SC4081E0.npz",
        "SC4082E0.npz", "SC4151E0.npz", "SC4152E0.npz", "SC4171E0.npz",
        "SC4172E0.npz",
    ]
    val_files = [
        "SC4021E0.npz", "SC4022E0.npz", "SC4031E0.npz",
        "SC4032E0.npz", "SC4071E0.npz", "SC4072E0.npz",
    ]
    test_files = [
        "SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"
    ]

    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]

    logging.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=200,
        is_training=True,
    )

    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=200,
        is_training=False,
    )

    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=200,
        is_training=False,
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )

    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")

    # 加载Stage 1的最佳模型
    base_model = ProgressiveMAMBAFORMER_V1_Fixed(
        input_channels=3,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_layers=config["n_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
    ).to(device)
    
    # 加载Stage 1权重
    stage1_path = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage1_simple_20250815_042107/best_model.pth"
    if os.path.exists(stage1_path):
        logging.info(f"加载Stage 1最佳模型: {stage1_path}")
        checkpoint = torch.load(stage1_path)
        base_model.load_state_dict(checkpoint['model_state_dict'])
        logging.info(f"✅ 成功加载Stage 1模型")
    else:
        logging.error("找不到Stage 1的模型！")
        return
    
    # 冻结基础模型的所有参数
    for param in base_model.parameters():
        param.requires_grad = False
    
    # 创建注意力增强模块
    attention_module = AttentionEnhancement(
        d_model=config["d_model"],
        n_heads=8,  # 使用较少的头
        dropout=config["dropout"]
    ).to(device)
    
    # 统计参数
    attention_params = sum(p.numel() for p in attention_module.parameters())
    total_params = sum(p.numel() for p in base_model.parameters()) + attention_params
    logging.info(f"注意力模块参数: {attention_params:,}")
    logging.info(f"总参数: {total_params:,}")
    logging.info(f"可训练比例: {100*attention_params/total_params:.2f}%")
    
    # 评估初始性能
    logging.info("\n评估初始性能（应该等于Stage 1的88%）...")
    initial_metrics = evaluate(base_model, attention_module, test_loader, device)
    initial_acc = initial_metrics['accuracy'] * 100
    logging.info(f"初始测试准确率: {initial_acc:.2f}%")
    
    if abs(initial_acc - 88.0) > 1.0:
        logging.warning(f"⚠️ 初始准确率与Stage 1不符！期望~88%，实际{initial_acc:.2f}%")
    
    # 优化器 - 只优化注意力模块
    optimizer = optim.AdamW(
        attention_module.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"]
    )
    
    # 学习率调度器
    scheduler = CosineAnnealingLR(optimizer, T_max=config["num_epochs"], eta_min=1e-6)
    
    # 损失函数
    criterion = nn.CrossEntropyLoss()
    
    best_test_acc = initial_acc
    best_epoch = -1
    patience = 5
    patience_counter = 0
    
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_acc': [],
        'test_acc': []
    }
    
    for epoch in range(config["num_epochs"]):
        logging.info(f"\n{'='*60}")
        logging.info(f"Epoch {epoch+1}/{config['num_epochs']}")
        
        # 训练
        train_loss, train_acc = train_one_epoch(
            base_model, attention_module, train_loader, 
            criterion, optimizer, device, epoch, config
        )
        
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc:.2f}%")
        
        # 验证
        val_metrics = evaluate(base_model, attention_module, val_loader, device)
        val_acc = val_metrics['accuracy'] * 100
        
        # 测试
        test_metrics = evaluate(base_model, attention_module, test_loader, device)
        test_acc = test_metrics['accuracy'] * 100
        
        logging.info(f"验证 - Acc: {val_acc:.2f}%, F1: {val_metrics['f1_macro']:.4f}")
        logging.info(f"测试 - Acc: {test_acc:.2f}%, F1: {test_metrics['f1_macro']:.4f}")
        
        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_acc'].append(val_acc)
        history['test_acc'].append(test_acc)
        
        # 保存最佳模型
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_epoch = epoch
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'base_model_state_dict': base_model.state_dict(),
                'attention_module_state_dict': attention_module.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_acc': val_acc,
                'test_acc': test_acc,
                'config': config,
                'history': history,
            }, os.path.join(log_dir, 'best_model.pth'))
            logging.info(f"✅ 保存最佳模型 (Test: {test_acc:.2f}%)")
            
            if test_acc >= 89.0:
                logging.info(f"🎉 达到目标！测试准确率: {test_acc:.2f}% >= 89%")
                break
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logging.info(f"Early stopping at epoch {epoch+1}")
                break
        
        # 调整学习率
        scheduler.step()
        
        # 显示alpha值
        alpha_value = torch.sigmoid(attention_module.alpha).item()
        logging.info(f"注意力权重(alpha): {alpha_value:.4f}")
    
    logging.info(f"\n{'='*60}")
    logging.info(f"训练完成! 最佳测试准确率: {best_test_acc:.2f}% (Epoch {best_epoch+1})")
    logging.info(f"相对Stage 1的改进: {best_test_acc - initial_acc:+.2f}%")
    
    # 保存结果
    result_summary = {
        'stage': 'Stage 2 Minimal',
        'initial_accuracy': initial_acc,
        'final_accuracy': best_test_acc,
        'improvement': best_test_acc - initial_acc,
        'best_epoch': best_epoch + 1,
        'config': config
    }
    
    with open(os.path.join(log_dir, 'result_summary.json'), 'w') as f:
        json.dump(result_summary, f, indent=2)
    
    return best_test_acc


if __name__ == "__main__":
    final_acc = main()
    print(f"\n最终准确率: {final_acc:.2f}%")