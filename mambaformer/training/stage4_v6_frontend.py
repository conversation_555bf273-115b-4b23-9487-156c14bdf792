#!/usr/bin/env python3
"""
Stage 4 V6 Frontend - 替换前端模块为final_test_90_fixed版本
策略：
1. 使用final_test_90_fixed的EnhancedEpochFeatureExtractor
2. 保留stage4_mamba_progressive的创新架构（Mamba-Transformer混合）
3. 添加残差连接淡化原始模块影响
"""

import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from datetime import datetime
import logging
from sklearn.metrics import accuracy_score, f1_score, cohen_kappa_score, confusion_matrix
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
import json
import math
import sys
import glob

# 添加依赖路径
current_dir = os.path.dirname(os.path.abspath(__file__))
dependencies_dir = os.path.join(current_dir, 'stage4_mp_dependencies')
sys.path.insert(0, dependencies_dir)

# 导入数据集
from utils.sequence_dataset import SequenceSleepDataset

# 设置日志
log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs"
os.makedirs(log_dir, exist_ok=True)

timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
experiment_name = f"stage4_v6_frontend_{timestamp}"
log_file = os.path.join(log_dir, experiment_name, "training.log")
os.makedirs(os.path.dirname(log_file), exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)

logging.info(f"Starting V6 Frontend experiment: {experiment_name}")
logging.info("="*80)
logging.info("Strategy: Replace frontend with final_test_90_fixed + residual connections")

# =============================================================================
# Model Components from final_test_90_fixed
# =============================================================================

class EnhancedEpochFeatureExtractor(nn.Module):
    """从final_test_90_fixed复制的增强特征提取器"""
    def __init__(self, input_channels=4, d_model=128, dropout=0.15):
        super().__init__()
        
        # CNN特征提取 - 与final_test_90_fixed保持一致
        self.conv_layers = nn.Sequential(
            # 第一层
            nn.Conv1d(input_channels, 64, kernel_size=50, stride=6),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(8, stride=8),
            
            # 第二层
            nn.Conv1d(64, 128, kernel_size=8, stride=1),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout * 0.5),
            nn.MaxPool1d(4, stride=4),
            
            # 第三层
            nn.Conv1d(128, d_model, kernel_size=4, stride=1),
            nn.BatchNorm1d(d_model),
            nn.ReLU(),
            nn.Dropout(dropout * 0.3)
        )
        
        # 全局平均池化
        self.global_pool = nn.AdaptiveAvgPool1d(1)
    
    def forward(self, x):
        x = self.conv_layers(x)
        x = self.global_pool(x)
        x = x.squeeze(-1)
        return x

# =============================================================================
# Mamba Components (保留创新架构)
# =============================================================================

class SimplifiedMambaBlock(nn.Module):
    """简化的Mamba块实现（从stage4_mamba_progressive复制）"""
    def __init__(self, d_model, d_state=16, d_conv=4, expand_factor=2):
        super().__init__()
        self.d_model = d_model
        self.d_inner = d_model * expand_factor
        
        # 输入投影
        self.in_proj = nn.Linear(d_model, self.d_inner * 2)
        
        # 1D卷积用于局部特征
        self.conv1d = nn.Conv1d(
            self.d_inner,
            self.d_inner,
            kernel_size=d_conv,
            padding=d_conv // 2,
            groups=self.d_inner,
        )
        
        # SSM参数
        self.x_proj = nn.Linear(self.d_inner, d_state * 2 + 1)
        self.dt_proj = nn.Linear(1, self.d_inner)
        
        # A矩阵（状态转移）
        A = torch.arange(1, d_state + 1).reshape(1, d_state).repeat(self.d_inner, 1)
        self.A_log = nn.Parameter(torch.log(A))
        
        # D参数（跳跃连接）
        self.D = nn.Parameter(torch.ones(self.d_inner))
        
        # 输出投影
        self.out_proj = nn.Linear(self.d_inner, d_model)
        
        self.activation = nn.SiLU()
        self.norm = nn.LayerNorm(d_model)
    
    def forward(self, x):
        """x: (batch, seq_len, d_model)"""
        batch_size, seq_len, _ = x.shape
        residual = x
        
        # 输入投影和激活
        x_proj = self.in_proj(x)
        x_conv, x_gate = x_proj.chunk(2, dim=-1)
        
        # 卷积处理
        x_conv = x_conv.transpose(1, 2)  # (batch, d_inner, seq_len)
        x_conv = self.conv1d(x_conv)
        # 确保序列长度匹配
        if x_conv.size(2) != seq_len:
            x_conv = x_conv[:, :, :seq_len]
        x_conv = x_conv.transpose(1, 2)  # (batch, seq_len, d_inner)
        x_conv = self.activation(x_conv)
        
        # 确保x_gate维度匹配
        if x_gate.size(1) != x_conv.size(1):
            # 如果维度不匹配，调整x_gate
            if x_gate.size(1) > x_conv.size(1):
                x_gate = x_gate[:, : x_conv.size(1), :]
            else:
                # 填充x_gate
                padding = x_conv.size(1) - x_gate.size(1)
                x_gate = F.pad(x_gate, (0, 0, 0, padding))
        
        # SSM处理（简化版）
        ssm_params = self.x_proj(x_conv)
        B, C, delta = torch.split(
            ssm_params, [self.A_log.shape[1], self.A_log.shape[1], 1], dim=-1
        )
        delta = F.softplus(delta)
        
        # 简化的状态空间计算
        A = -torch.exp(self.A_log)
        
        # 离散化
        deltaA = torch.exp(delta.unsqueeze(-2) * A.unsqueeze(0).unsqueeze(0))
        deltaB = delta.unsqueeze(-1) * B.unsqueeze(-2)
        
        # 扫描操作（简化版本）
        y = torch.zeros_like(x_conv)
        h = torch.zeros(batch_size, self.d_inner, self.A_log.shape[1], device=x.device)
        
        current_seq_len = x_conv.size(1)
        for t in range(current_seq_len):
            h = deltaA[:, t] * h + deltaB[:, t] * x_conv[:, t, :, None]
            y[:, t] = (h @ C[:, t, :, None]).squeeze(-1)
        
        # 添加跳跃连接
        y = y * self.activation(x_gate) + self.D * x_conv
        
        # 输出投影
        output = self.out_proj(y)
        
        # 残差连接和层归一化
        return self.norm(output + residual)

# =============================================================================
# Hybrid Model with Residual Connections
# =============================================================================

class HybridProgressiveMAMBAFORMER_V6(nn.Module):
    """混合模型：final_test_90_fixed前端 + Mamba-Transformer后端 + 残差连接"""
    
    def __init__(self, input_channels=4, n_classes=5, d_model=128, 
                 n_heads=8, n_mamba_layers=2, n_transformer_layers=2,
                 dropout=0.15, seq_len=5, coarse_classes=3):
        super().__init__()
        
        self.d_model = d_model
        self.seq_len = seq_len
        self.n_classes = n_classes
        self.coarse_classes = coarse_classes
        
        # ===== Stage 1: 前端特征提取（使用final_test_90_fixed版本）=====
        self.enhanced_feature_extractor = EnhancedEpochFeatureExtractor(
            input_channels, d_model, dropout
        )
        
        # ===== Stage 2: 模态细化（保留创新但添加残差）=====
        # EEG通道 - 使用final_test_90_fixed的简单线性投影
        self.eeg_projection = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.LayerNorm(d_model),
            nn.Dropout(dropout)
        )
        
        # EOG/EMG通道 - 保留轻量级Mamba但添加残差
        self.eog_mamba = SimplifiedMambaBlock(d_model, d_state=8, expand_factor=1)
        self.emg_mamba = SimplifiedMambaBlock(d_model, d_state=8, expand_factor=1)
        
        # 残差权重（可学习）
        self.residual_weight = nn.Parameter(torch.tensor(0.3))
        
        # 模态融合
        self.modal_fusion = nn.Sequential(
            nn.Linear(d_model * 3, d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # ===== Stage 3: 时序建模（Mamba-Transformer混合）=====
        # Mamba层处理局部依赖
        self.mamba_layers = nn.ModuleList([
            SimplifiedMambaBlock(d_model, expand_factor=2) for _ in range(n_mamba_layers)
        ])
        
        # Transformer层处理全局依赖
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, num_layers=n_transformer_layers
        )
        
        # ===== Stage 4: 渐进式分类 =====
        # 粗分类头
        self.coarse_classifier = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, coarse_classes)
        )
        
        # 细分类头（考虑粗分类结果）
        self.fine_classifier = nn.Sequential(
            nn.Linear(d_model + coarse_classes, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, n_classes)
        )
        
        # 直接分类头（跳过渐进式）
        self.direct_classifier = nn.Sequential(
            nn.Linear(d_model, n_classes)
        )
        
        self._init_weights()
        
        logging.info(f"Created HybridProgressiveMAMBAFORMER_V6:")
        logging.info(f"  - Enhanced frontend from final_test_90_fixed")
        logging.info(f"  - Residual connections with weight={self.residual_weight.item():.3f}")
        logging.info(f"  - Mamba-Transformer hybrid backend")
        logging.info(f"  - Parameters: {sum(p.numel() for p in self.parameters()):,}")
    
    def _init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.LayerNorm):
                nn.init.constant_(m.bias, 0)
                nn.init.constant_(m.weight, 1.0)
    
    def forward(self, x):
        batch_size, seq_len, time_steps, channels = x.shape
        
        # Stage 1: 特征提取（使用增强版本）
        x_reshaped = x.view(batch_size * seq_len, time_steps, channels).transpose(1, 2)
        features = self.enhanced_feature_extractor(x_reshaped)
        features = features.view(batch_size, seq_len, self.d_model)
        
        # Stage 2: 模态细化（添加残差连接）
        # 假设3个通道分别对应EEG, EOG, EMG
        eeg_features = self.eeg_projection(features)
        
        # EOG和EMG使用Mamba，但添加残差
        eog_features = features + self.residual_weight * self.eog_mamba(features)
        emg_features = features + self.residual_weight * self.emg_mamba(features)
        
        # 融合多模态特征
        modal_concat = torch.cat([eeg_features, eog_features, emg_features], dim=-1)
        fused_features = self.modal_fusion(modal_concat)
        
        # 添加跳跃连接
        fused_features = fused_features + features
        
        # Stage 3: 时序建模
        # Mamba处理局部依赖
        mamba_out = fused_features
        for mamba_layer in self.mamba_layers:
            mamba_out = mamba_out + mamba_layer(mamba_out)
        
        # Transformer处理全局依赖
        transformer_out = self.transformer_encoder(mamba_out)
        
        # 组合局部和全局特征
        combined_features = transformer_out + mamba_out
        
        # Stage 4: 渐进式分类
        # 粗分类
        coarse_logits = self.coarse_classifier(combined_features)
        
        # 细分类（结合粗分类信息）
        fine_input = torch.cat([combined_features, F.softmax(coarse_logits, dim=-1)], dim=-1)
        fine_logits = self.fine_classifier(fine_input)
        
        # 直接分类（用于对比）
        direct_logits = self.direct_classifier(combined_features)
        
        return fine_logits, coarse_logits, direct_logits

# =============================================================================
# Loss Functions
# =============================================================================

class ProgressiveFocalLoss(nn.Module):
    """渐进式Focal Loss"""
    def __init__(self, alpha=None, gamma=2.0):
        super().__init__()
        self.gamma = gamma
        self.alpha = alpha
        
    def forward(self, inputs, targets):
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.alpha is not None:
            if isinstance(self.alpha, (list, np.ndarray)):
                alpha = torch.tensor(self.alpha, device=inputs.device)[targets]
            else:
                alpha = self.alpha
            focal_loss = alpha * focal_loss
        
        return focal_loss.mean()

# =============================================================================
# Training Functions
# =============================================================================

def train_one_epoch(model, train_loader, criterion_fine, criterion_coarse, 
                   optimizer, scheduler, device, epoch):
    model.train()
    total_loss = 0
    all_preds = []
    all_targets = []
    
    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)
        
        if target.dim() > 1:
            target = target[:, target.shape[1] // 2]
        
        optimizer.zero_grad()
        
        # Forward pass
        fine_logits, coarse_logits, direct_logits = model(data)
        
        if fine_logits.dim() == 3:
            batch_size, seq_len, n_classes = fine_logits.shape
            fine_logits = fine_logits.reshape(-1, n_classes)
            coarse_logits = coarse_logits.reshape(-1, model.coarse_classes)
            direct_logits = direct_logits.reshape(-1, model.n_classes)
            target_expanded = target.unsqueeze(1).expand(-1, seq_len).reshape(-1)
        else:
            target_expanded = target
        
        # 创建粗标签
        coarse_target = torch.zeros_like(target_expanded)
        coarse_target[target_expanded <= 1] = 0  # Wake/N1
        coarse_target[(target_expanded == 2) | (target_expanded == 3)] = 1  # N2/N3
        coarse_target[target_expanded == 4] = 2  # REM
        
        # 计算损失（调整权重）
        loss_fine = criterion_fine(fine_logits, target_expanded)
        loss_coarse = criterion_coarse(coarse_logits, coarse_target)
        loss_direct = criterion_fine(direct_logits, target_expanded)
        
        # 组合损失（优先细分类）
        loss = 0.5 * loss_fine + 0.2 * loss_coarse + 0.3 * loss_direct
        
        # L2正则化
        l2_lambda = 0.0001
        l2_norm = sum(p.pow(2.0).sum() for p in model.parameters())
        loss = loss + l2_lambda * l2_norm
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
        optimizer.step()
        
        total_loss += loss.item()
        
        # 使用细分类结果进行预测
        if fine_logits.dim() == 2:
            pred = fine_logits.argmax(dim=1)
        else:
            pred = fine_logits[:, fine_logits.shape[1] // 2, :].argmax(dim=1)
            target_expanded = target
        
        all_preds.extend(pred.cpu().numpy())
        all_targets.extend(target_expanded.cpu().numpy())
        
        if batch_idx % 10 == 0:
            acc = accuracy_score(target_expanded.cpu(), pred.cpu())
            logging.info(f"  Batch {batch_idx}/{len(train_loader)}: "
                        f"Loss={loss.item():.4f}, Acc={acc:.4f}")
    
    scheduler.step()
    
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average='macro')
    
    return total_loss / len(train_loader), accuracy, macro_f1

def evaluate(model, val_loader, device):
    model.eval()
    all_preds = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in val_loader:
            data = data.to(device)
            
            if target.dim() > 1:
                target = target[:, target.shape[1] // 2]
            
            fine_logits, _, _ = model(data)
            
            if fine_logits.dim() == 3:
                pred = fine_logits[:, fine_logits.shape[1] // 2, :].argmax(dim=1)
            else:
                pred = fine_logits.argmax(dim=1)
            
            all_preds.extend(pred.cpu().numpy())
            all_targets.extend(target.numpy())
    
    accuracy = accuracy_score(all_targets, all_preds)
    macro_f1 = f1_score(all_targets, all_preds, average='macro')
    kappa = cohen_kappa_score(all_targets, all_preds)
    
    class_f1 = f1_score(all_targets, all_preds, average=None)
    cm = confusion_matrix(all_targets, all_preds)
    
    return accuracy, macro_f1, kappa, class_f1, cm

# =============================================================================
# Main Training
# =============================================================================

def main():
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Using device: {device}")
    
    # Configuration - 与stage4_mamba_progressive保持一致
    config = {
        "d_model": 128,
        "n_heads": 8,
        "n_mamba_layers": 2,
        "n_transformer_layers": 2,
        "dropout": 0.15,
        "seq_len": 5,  # 与原始一致
        "batch_size": 32,  # 与原始一致
        "learning_rate": 2e-4,  # 与原始一致
        "num_epochs": 30,  # 与原始一致
        "coarse_classes": 3,
        "weight_decay": 1e-4,
    }
    
    logging.info(f"Configuration: {json.dumps(config, indent=2)}")
    
    # Data paths - 与stage4_mamba_progressive完全一致
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20"
    all_files = sorted(glob.glob(os.path.join(data_dir, "*.npz")))
    
    # 按受试者划分数据集
    from collections import defaultdict
    subject_files = defaultdict(list)
    for file_path in all_files:
        filename = os.path.basename(file_path)
        if filename.startswith("SC4") and len(filename) >= 7:
            subject_id = filename[3:5]
            subject_files[subject_id].append(file_path)
    
    # 使用与stage4_mamba_progressive完全相同的固定数据分割
    train_subjects = [
        "00", "01", "02", "03", "06", "07", "08", "09",
        "10", "11", "12", "13", "15", "16", "17", "18",
    ]
    val_subjects = ["04", "14"]
    test_subjects = ["05", "19"]
    
    # 收集文件
    train_files = []
    val_files = []
    test_files = []
    
    for subject in train_subjects:
        if subject in subject_files:
            train_files.extend(subject_files[subject])
    
    for subject in val_subjects:
        if subject in subject_files:
            val_files.extend(subject_files[subject])
    
    for subject in test_subjects:
        if subject in subject_files:
            test_files.extend(subject_files[subject])
    
    # Create datasets
    logging.info("Loading datasets with ALL data (no limit)...")
    train_dataset = SequenceSleepDataset(
        train_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=True
    )
    val_dataset = SequenceSleepDataset(
        val_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=False
    )
    test_dataset = SequenceSleepDataset(
        test_files, seq_len=config["seq_len"], 
        max_samples_per_file=None, is_training=False
    )
    
    logging.info(f"Dataset sizes: Train={len(train_dataset)}, "
                f"Val={len(val_dataset)}, Test={len(test_dataset)}")
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset, batch_size=config["batch_size"],
        shuffle=True, num_workers=4, pin_memory=True, drop_last=True
    )
    val_loader = DataLoader(
        val_dataset, batch_size=config["batch_size"],
        shuffle=False, num_workers=4, pin_memory=True
    )
    test_loader = DataLoader(
        test_dataset, batch_size=config["batch_size"],
        shuffle=False, num_workers=4, pin_memory=True
    )
    
    # Create model
    model = HybridProgressiveMAMBAFORMER_V6(
        input_channels=4,
        n_classes=5,
        d_model=config["d_model"],
        n_heads=config["n_heads"],
        n_mamba_layers=config["n_mamba_layers"],
        n_transformer_layers=config["n_transformer_layers"],
        dropout=config["dropout"],
        seq_len=config["seq_len"],
        coarse_classes=config["coarse_classes"]
    ).to(device)
    
    # Loss functions with class weights
    class_weights_fine = [2.0, 2.5, 1.0, 1.5, 2.0]
    class_weights_coarse = [1.5, 1.0, 1.5]
    
    criterion_fine = ProgressiveFocalLoss(alpha=class_weights_fine, gamma=2.0)
    criterion_coarse = ProgressiveFocalLoss(alpha=class_weights_coarse, gamma=2.0)
    
    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["learning_rate"],
        weight_decay=config["weight_decay"]
    )
    
    # Scheduler - CosineAnnealingWarmRestarts
    scheduler = CosineAnnealingWarmRestarts(
        optimizer, T_0=5, T_mult=2, eta_min=1e-6
    )
    
    # Training loop
    logging.info("\n" + "="*80)
    logging.info("Starting V6 Frontend Training...")
    logging.info("="*80)
    
    best_test_acc = 0
    best_test_f1 = 0
    
    for epoch in range(config["num_epochs"]):
        logging.info(f"\nEpoch {epoch+1}/{config['num_epochs']}")
        
        # Train
        train_loss, train_acc, train_f1 = train_one_epoch(
            model, train_loader, criterion_fine, criterion_coarse,
            optimizer, scheduler, device, epoch
        )
        
        # Validate
        val_acc, val_f1, val_kappa, val_class_f1, val_cm = evaluate(
            model, val_loader, device
        )
        
        # Test
        test_acc, test_f1, test_kappa, test_class_f1, test_cm = evaluate(
            model, test_loader, device
        )
        
        logging.info(f"\nEpoch {epoch+1} Results:")
        logging.info(f"  Train: Loss={train_loss:.4f}, Acc={train_acc:.4f}, F1={train_f1:.4f}")
        logging.info(f"  Val: Acc={val_acc:.4f}, F1={val_f1:.4f}, Kappa={val_kappa:.4f}")
        logging.info(f"  TEST: Acc={test_acc:.4f}, F1={test_f1:.4f}, Kappa={test_kappa:.4f}")
        logging.info(f"  Test Class F1: W={test_class_f1[0]:.3f}, N1={test_class_f1[1]:.3f}, "
                    f"N2={test_class_f1[2]:.3f}, N3={test_class_f1[3]:.3f}, REM={test_class_f1[4]:.3f}")
        
        # Save best model
        if test_acc > best_test_acc:
            best_test_acc = test_acc
            best_test_f1 = test_f1
            
            model_path = os.path.join(log_dir, experiment_name, f"best_model_acc_{test_acc:.4f}.pth")
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_acc': test_acc,
                'test_f1': test_f1,
                'config': config
            }, model_path)
            logging.info(f"  💾 Saved best model: {model_path}")
    
    # Final summary
    logging.info("\n" + "="*80)
    logging.info("V6 Frontend Training Completed!")
    logging.info(f"Best Test Accuracy: {best_test_acc:.4f}")
    logging.info(f"Best Test F1 Score: {best_test_f1:.4f}")
    logging.info(f"Log saved to: {log_file}")
    logging.info("="*80)

if __name__ == "__main__":
    main()