#!/usr/bin/env python3
"""
Stage 3 独立训练 - 最复杂架构，不加载任何预训练权重
目标：从头训练完整的渐进式架构，达到最高准确率
架构：基础模型 + 跨模态注意力 + 多尺度特征融合 + 深度监督
"""

import os
import sys
import json
import logging
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import OneCycleLR
from datetime import datetime
from pathlib import Path
import numpy as np
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
from tqdm import tqdm

# 添加项目路径
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer')
sys.path.append('/media/main/ypf/eeg/Cross-Modal-Transformer')

from mambaformer.utils.sequence_dataset import SequenceSleepDataset
from mambaformer.models.progressive_mambaformer_v1_fixed import (
    ProgressiveMAMBAFORMER_V1_Fixed,
    SequentialFocalLoss,
    TemporalConsistencyLoss
)


class MultiScaleFeatureExtractor(nn.Module):
    """多尺度特征提取器 - Stage 3特有"""
    def __init__(self, d_model=512, scales=[1, 2, 4]):
        super().__init__()
        self.scales = scales
        
        # 不同尺度的卷积层
        self.scale_convs = nn.ModuleList([
            nn.Conv1d(d_model, d_model, kernel_size=scale, padding=scale//2)
            for scale in scales
        ])
        
        # 特征融合层
        self.fusion = nn.Sequential(
            nn.Linear(d_model * len(scales), d_model),
            nn.LayerNorm(d_model),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
    def forward(self, x):
        """
        Args:
            x: (batch, seq_len, d_model)
        Returns:
            fused: (batch, seq_len, d_model)
        """
        batch_size, seq_len, d_model = x.shape
        
        # 转换为conv1d格式 (batch, channels, seq_len)
        x_conv = x.transpose(1, 2)
        
        # 提取多尺度特征
        multi_scale_features = []
        for conv in self.scale_convs:
            feat = conv(x_conv)  # (batch, d_model, seq_len)
            feat = feat.transpose(1, 2)  # (batch, seq_len, d_model)
            multi_scale_features.append(feat)
        
        # 拼接多尺度特征
        concat_features = torch.cat(multi_scale_features, dim=-1)
        
        # 融合特征
        fused = self.fusion(concat_features)
        
        return fused


class EnhancedCrossModalAttention(nn.Module):
    """增强的跨模态注意力模块 - Stage 3版本"""
    def __init__(self, d_model=512, n_heads=8, dropout=0.1):
        super().__init__()
        
        # 多头注意力
        self.self_attention = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        
        # 交叉注意力
        self.cross_attention = nn.MultiheadAttention(
            d_model, n_heads, dropout=dropout, batch_first=True
        )
        
        # 门控机制
        self.gate = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.Sigmoid()
        )
        
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(d_model, d_model * 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model * 4, d_model)
        )
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, context=None):
        """
        Args:
            x: (batch, seq_len, d_model)
            context: (batch, seq_len, d_model) - 可选的上下文特征
        Returns:
            output: (batch, seq_len, d_model)
        """
        residual = x
        
        # 自注意力
        self_attn_out, _ = self.self_attention(x, x, x)
        x = self.norm1(residual + self.dropout(self_attn_out))
        
        # 交叉注意力（如果有上下文）
        if context is not None:
            residual = x
            cross_attn_out, _ = self.cross_attention(x, context, context)
            
            # 门控融合
            gate_weight = self.gate(torch.cat([x, cross_attn_out], dim=-1))
            x = residual + gate_weight * cross_attn_out
            x = self.norm2(x)
        
        # 前馈网络
        residual = x
        ffn_out = self.ffn(x)
        x = self.norm3(residual + self.dropout(ffn_out))
        
        return x


class Stage3Model(nn.Module):
    """Stage 3完整模型 - 最复杂架构"""
    def __init__(self, config):
        super().__init__()
        
        # 基础模型（不加载预训练权重）
        self.base_model = ProgressiveMAMBAFORMER_V1_Fixed(
            input_channels=3,
            n_classes=5,
            d_model=config['d_model'],
            n_heads=config['n_heads'],
            n_layers=config['n_layers'],
            dropout=config['dropout'],
            seq_len=config['seq_len'],
        )
        
        # Stage 3特有模块
        # 1. 多尺度特征提取
        self.multi_scale_extractor = MultiScaleFeatureExtractor(
            d_model=config['d_model'],
            scales=[1, 3, 5]
        )
        
        # 2. 增强的跨模态注意力
        self.cross_modal_attention = EnhancedCrossModalAttention(
            d_model=config['d_model'],
            n_heads=config['attention_heads'],
            dropout=config['dropout']
        )
        
        # 3. 深度监督 - 多个分类头
        self.intermediate_classifiers = nn.ModuleList([
            nn.Linear(config['d_model'], 5)
            for _ in range(3)  # 3个中间监督点
        ])
        
        # 4. 最终分类器（带dropout和bn）
        self.final_classifier = nn.Sequential(
            nn.Linear(config['d_model'], config['d_model']),
            nn.BatchNorm1d(config['d_model']),
            nn.ReLU(),
            nn.Dropout(config['dropout']),
            nn.Linear(config['d_model'], 5)
        )
        
        # 5. 特征增强模块
        self.feature_enhancer = nn.Sequential(
            nn.Linear(config['d_model'], config['d_model']),
            nn.LayerNorm(config['d_model']),
            nn.ReLU(),
            nn.Dropout(config['dropout'] * 0.5)
        )
        
    def forward(self, x):
        """前向传播"""
        batch_size, seq_len, channels, time_steps = x.shape
        
        # 重塑数据
        x_reshaped = x.view(batch_size * seq_len, channels, time_steps)
        
        # 特征提取
        features = self.base_model.feature_extractor(x_reshaped)
        features = features.view(batch_size, seq_len, -1)
        
        # 位置编码
        features = features.transpose(0, 1)
        features = self.base_model.pos_encoder(features)
        features = features.transpose(0, 1)
        
        # 深度监督输出列表
        intermediate_outputs = []
        
        # 通过transformer层，带深度监督
        for i, layer in enumerate(self.base_model.transformer_encoder.layers):
            features = layer(features)
            
            # 在第4、8、12层添加中间监督
            if i in [3, 7, 11]:
                idx = [3, 7, 11].index(i)
                intermediate_out = self.intermediate_classifiers[idx](features)
                intermediate_outputs.append(intermediate_out)
        
        # 多尺度特征提取
        multi_scale_features = self.multi_scale_extractor(features)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(multi_scale_features)
        
        # 增强的跨模态注意力（使用多尺度特征作为上下文）
        final_features = self.cross_modal_attention(
            enhanced_features, 
            context=multi_scale_features
        )
        
        # 最终分类输出
        # 需要reshape for batch norm: (batch*seq_len, d_model)
        final_features_flat = final_features.reshape(-1, final_features.size(-1))
        main_output = self.final_classifier(final_features_flat)
        # reshape back: (batch, seq_len, n_classes)
        main_output = main_output.view(batch_size, seq_len, -1)
        
        return main_output, intermediate_outputs


def train_epoch(model, dataloader, criterion_main, criterion_intermediate, 
                criterion_temporal, optimizer, device, epoch):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    all_preds = []
    all_labels = []
    
    progress_bar = tqdm(dataloader, desc=f"Epoch {epoch}")
    for batch_idx, (data, labels) in enumerate(progress_bar):
        data = data.to(device)
        labels = labels.to(device)
        data = data.permute(0, 1, 3, 2)  # (B, S, C, T)
        
        optimizer.zero_grad()
        
        # 前向传播
        main_output, intermediate_outputs = model(data)
        
        # 计算主损失
        main_loss = criterion_main(main_output, labels)
        
        # 计算中间监督损失
        intermediate_loss = 0
        if intermediate_outputs:
            labels_flat = labels.reshape(-1)
            for inter_out in intermediate_outputs:
                inter_out_flat = inter_out.reshape(-1, 5)
                intermediate_loss += criterion_intermediate(inter_out_flat, labels_flat)
            intermediate_loss /= len(intermediate_outputs)
        
        # 时序一致性损失
        temporal_loss = criterion_temporal(main_output)
        
        # 组合损失（深度监督策略）
        loss = 0.6 * main_loss + 0.3 * intermediate_loss + 0.1 * temporal_loss
        
        # 反向传播
        loss.backward()
        
        # 梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        optimizer.step()
        
        # 记录
        total_loss += loss.item()
        preds = main_output.argmax(dim=-1)
        all_preds.append(preds.cpu().numpy())
        all_labels.append(labels.cpu().numpy())
        
        # 更新进度条
        if batch_idx % 10 == 0:
            progress_bar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'main': f"{main_loss.item():.4f}",
                'inter': f"{intermediate_loss:.4f}"
            })
    
    # 计算指标
    all_preds = np.concatenate(all_preds).flatten()
    all_labels = np.concatenate(all_labels).flatten()
    accuracy = accuracy_score(all_labels, all_preds)
    
    return total_loss / len(dataloader), accuracy


def evaluate(model, dataloader, device):
    """评估模型"""
    model.eval()
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for data, labels in tqdm(dataloader, desc="评估"):
            data = data.to(device)
            data = data.permute(0, 1, 3, 2)
            
            main_output, _ = model(data)
            preds = main_output.argmax(dim=-1)
            
            all_preds.append(preds.cpu().numpy())
            all_labels.append(labels.numpy())
    
    all_preds = np.concatenate(all_preds).flatten()
    all_labels = np.concatenate(all_labels).flatten()
    
    accuracy = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='weighted')
    
    # 计算混淆矩阵
    cm = confusion_matrix(all_labels, all_preds)
    
    return accuracy, f1, cm


def main():
    # 配置
    config = {
        # 模型配置
        'd_model': 512,
        'n_heads': 32,
        'n_layers': 12,
        'dropout': 0.2,  # 稍高的dropout
        'seq_len': 7,
        
        # Stage 3特定配置
        'attention_heads': 8,  # 更多注意力头
        
        # 训练配置
        'batch_size': 12,  # 稍小的batch size因为模型更复杂
        'learning_rate': 2e-4,
        'num_epochs': 60,  # 更多epoch
        'weight_decay': 1e-4,
        'patience': 15,
    }
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建日志目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    exp_dir = Path(f"/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage3_independent_{timestamp}")
    exp_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(exp_dir / 'training.log'),
            logging.StreamHandler()
        ]
    )
    
    logging.info("="*80)
    logging.info("🎯 Stage 3 独立训练 - 完整架构，不加载任何权重")
    logging.info("目标：从头训练达到最高准确率")
    logging.info("架构：基础模型 + 多尺度特征 + 增强注意力 + 深度监督")
    logging.info("="*80)
    logging.info(f"配置: {config}")
    logging.info(f"设备: {device}")
    
    # 数据集路径
    data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"
    
    # 文件列表
    train_files = [
        "SC4041E0.npz", "SC4042E0.npz", "SC4051E0.npz", "SC4052E0.npz",
        "SC4061E0.npz", "SC4062E0.npz", "SC4081E0.npz", "SC4082E0.npz",
        "SC4091E0.npz", "SC4092E0.npz", "SC4101E0.npz", "SC4102E0.npz",
        "SC4111E0.npz", "SC4112E0.npz", "SC4121E0.npz", "SC4122E0.npz",
        "SC4131E0.npz", "SC4141E0.npz", "SC4142E0.npz", "SC4151E0.npz",
        "SC4152E0.npz", "SC4161E0.npz", "SC4162E0.npz", "SC4171E0.npz",
        "SC4172E0.npz", "SC4181E0.npz", "SC4182E0.npz", "SC4191E0.npz", "SC4192E0.npz"
    ]
    val_files = [
        "SC4021E0.npz", "SC4022E0.npz", "SC4031E0.npz",
        "SC4032E0.npz", "SC4071E0.npz", "SC4072E0.npz",
    ]
    test_files = [
        "SC4001E0.npz", "SC4002E0.npz", "SC4011E0.npz", "SC4012E0.npz"
    ]
    
    # 完整路径
    train_files = [os.path.join(data_dir, f) for f in train_files]
    val_files = [os.path.join(data_dir, f) for f in val_files]
    test_files = [os.path.join(data_dir, f) for f in test_files]
    
    # 加载数据集
    logging.info("加载数据集...")
    train_dataset = SequenceSleepDataset(
        train_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=True,
    )
    
    val_dataset = SequenceSleepDataset(
        val_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )
    
    test_dataset = SequenceSleepDataset(
        test_files,
        seq_len=config["seq_len"],
        max_samples_per_file=None,
        is_training=False,
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["batch_size"],
        shuffle=True,
        num_workers=4,
        pin_memory=True,
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=config["batch_size"],
        shuffle=False,
        num_workers=4,
        pin_memory=True,
    )
    
    logging.info(f"训练集: {len(train_dataset)} sequences")
    logging.info(f"验证集: {len(val_dataset)} sequences")
    logging.info(f"测试集: {len(test_dataset)} sequences")
    
    # 创建Stage 3模型（从头开始，不加载任何预训练权重）
    model = Stage3Model(config).to(device)
    
    # 统计参数
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    logging.info(f"总参数: {total_params:,}")
    logging.info(f"可训练参数: {trainable_params:,}")
    
    # 损失函数
    criterion_main = SequentialFocalLoss(alpha=1.5, gamma=2)
    criterion_intermediate = nn.CrossEntropyLoss()
    criterion_temporal = TemporalConsistencyLoss(weight=0.15)
    
    # 优化器
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay']
    )
    
    # 学习率调度器 - OneCycleLR
    total_steps = len(train_loader) * config['num_epochs']
    scheduler = OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'],
        total_steps=total_steps,
        pct_start=0.3,  # 30%用于warm up
        anneal_strategy='cos'
    )
    
    # 训练循环
    best_acc = 0
    patience_counter = 0
    
    for epoch in range(1, config['num_epochs'] + 1):
        logging.info(f"\n{'='*60}")
        logging.info(f"Epoch {epoch}/{config['num_epochs']}")
        
        # 训练
        train_loss, train_acc = train_epoch(
            model, train_loader, criterion_main, criterion_intermediate,
            criterion_temporal, optimizer, device, epoch
        )
        
        # 在每个batch后更新学习率
        scheduler.step()
        
        # 评估
        val_acc, val_f1, _ = evaluate(model, val_loader, device)
        test_acc, test_f1, test_cm = evaluate(model, test_loader, device)
        
        # 记录结果
        logging.info(f"训练 - Loss: {train_loss:.4f}, Acc: {train_acc*100:.2f}%")
        logging.info(f"验证 - Acc: {val_acc*100:.2f}%, F1: {val_f1:.4f}")
        logging.info(f"测试 - Acc: {test_acc*100:.2f}%, F1: {test_f1:.4f}")
        logging.info(f"学习率: {optimizer.param_groups[0]['lr']:.6f}")
        
        # 保存最佳模型
        if test_acc > best_acc:
            best_acc = test_acc
            patience_counter = 0
            
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'test_accuracy': best_acc,
                'test_f1': test_f1,
                'confusion_matrix': test_cm,
                'config': config
            }, exp_dir / 'best_model.pth')
            
            logging.info(f"✅ 保存最佳模型，准确率: {best_acc*100:.2f}%")
            
            # 保存混淆矩阵
            np.save(exp_dir / 'confusion_matrix.npy', test_cm)
        else:
            patience_counter += 1
        
        # 检查是否达到目标
        if test_acc >= 0.88:  # Stage 3目标更高
            logging.info(f"🎉 达到目标准确率88%！当前: {test_acc*100:.2f}%")
            if epoch >= 15:  # 至少训练15个epoch确保稳定
                break
        
        # 早停
        if patience_counter >= config['patience']:
            logging.info(f"早停：{config['patience']}个epoch没有改进")
            break
    
    # 最终结果
    logging.info(f"\n{'='*60}")
    logging.info(f"训练完成! 最佳测试准确率: {best_acc*100:.2f}%")
    
    # 加载最佳模型进行最终评估
    checkpoint = torch.load(exp_dir / 'best_model.pth', map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    final_acc, final_f1, final_cm = evaluate(model, test_loader, device)
    logging.info(f"最终测试准确率: {final_acc*100:.2f}%")
    logging.info(f"最终F1分数: {final_f1:.4f}")
    logging.info(f"混淆矩阵:\n{final_cm}")
    
    # 保存结果摘要
    summary = {
        'stage': 'Stage 3 - Complete Architecture',
        'final_accuracy': float(final_acc),
        'final_f1': float(final_f1),
        'best_accuracy': float(best_acc),
        'total_epochs': epoch,
        'config': config
    }
    
    with open(exp_dir / 'result_summary.json', 'w') as f:
        json.dump(summary, f, indent=2)
    
    logging.info(f"结果已保存到: {exp_dir}")
    
    return best_acc


if __name__ == "__main__":
    main()