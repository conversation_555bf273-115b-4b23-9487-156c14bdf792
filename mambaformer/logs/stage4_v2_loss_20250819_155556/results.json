{"best_test_accuracy": 0.8380386329866271, "best_test_f1": 0.7570441788360209, "final_test_accuracy": 0.8312460199533008, "final_test_f1": 0.7509032281973437, "final_test_kappa": 0.7587715738786993, "final_test_class_f1": [0.8528138528138528, 0.40061162079510704, 0.8990362551629187, 0.7796143250688705, 0.8224400871459695], "confusion_matrix": [[788, 97, 49, 1, 43], [44, 131, 78, 0, 95], [5, 36, 1959, 23, 77], [0, 0, 136, 283, 0], [33, 42, 36, 0, 755]], "config": {"n_classes": 5, "d_model": 128, "n_heads": 8, "n_mamba_layers": 2, "n_transformer_layers": 2, "dropout": 0.15, "seq_len": 5, "batch_size": 32, "learning_rate": 0.0002, "num_epochs": 5, "weight_decay": 0.0001, "patience": 3, "coarse_weight": 0.2, "uncertainty_weight": 0.05, "l2_lambda": 0.0001}}