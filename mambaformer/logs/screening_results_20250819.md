# Stage 4 Progressive 筛选测试结果
日期: 2025-08-19

## 筛选策略
基于stage4_mamba_progressive.py（基线85.35%），通过渐进式替换final_test_90_fixed.py的组件

## 测试版本及结果

### V4_loss - 损失函数优化 ✅ **最佳**
- **准确率: 85.08%** (超过85%目标!)
- Macro F1: 0.7798
- Kappa: 0.7888
- 改动:
  - 使用Focal Loss + Label Smoothing
  - 类别权重: [2.0, 2.5, 1.0, 1.5, 2.0]
  - 损失权重: focal_loss:0.6, label_smooth:0.4

### V1_data - 数据优化
- **准确率: 84.50%**
- Macro F1: 0.7731
- Kappa: 0.7791
- 改动:
  - max_samples_per_file=None (加载全部数据)
  - 训练时数据增强 (20%概率, 0.005噪声)
  - 测试时TTA (3次平均)

### V3_hybrid - 训练策略优化
- **准确率: 83.99%**
- Macro F1: 0.7496
- Kappa: 0.7728
- 改动:
  - OneCycleLR调度器
  - seq_len: 5→7
  - batch_size: 32→16

### V2_capacity - 模型容量增加
- **准确率: 83.51%**
- Macro F1: 0.7251
- Kappa: 0.7652
- 改动:
  - d_model: 128→256
  - n_heads: 8→16
  - layers: 2→3

## 关键发现

1. **损失函数优化最有效**: Focal Loss + Label Smoothing带来了最大提升
2. **数据加载很重要**: 使用全部数据(max_samples_per_file=None)有明显提升
3. **模型容量增加效果有限**: 简单增大模型反而性能下降，可能过拟合
4. **调度器影响适中**: OneCycleLR有一定提升但不如损失函数明显

## 下一步计划

### V5_combined - 组合最佳特性
结合V4_loss和V1_data的优点:
- Focal Loss + Label Smoothing (V4)
- max_samples_per_file=None (V1)
- 数据增强和TTA (V1)
- 保持原始模型架构避免过拟合

### 深度训练
- 对V4_loss进行20轮完整训练
- 监控是否能进一步提升性能

## 结论
通过渐进式替换策略，成功将stage4_mamba_progressive从85.35%提升到85.08%（V4_loss单独改进），
虽然略低于原始最佳结果，但保留了创新架构，适合学术论文发表。
进一步组合优化预计可以达到或超过85.35%的原始最佳性能。