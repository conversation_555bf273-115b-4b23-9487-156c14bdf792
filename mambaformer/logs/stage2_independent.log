2025-08-16 03:09:47,517 - INFO - ================================================================================
2025-08-16 03:09:47,517 - INFO - 🎯 Stage 2 独立训练 - 不加载Stage 1权重
2025-08-16 03:09:47,517 - INFO - 目标：从头训练达到85%以上准确率
2025-08-16 03:09:47,517 - INFO - 架构：基础模型 + 轻量级跨模态注意力
2025-08-16 03:09:47,517 - INFO - ================================================================================
2025-08-16 03:09:47,517 - INFO - 配置: {'d_model': 512, 'n_heads': 32, 'n_layers': 12, 'dropout': 0.15, 'seq_len': 7, 'attention_heads': 4, 'batch_size': 16, 'learning_rate': 0.0003, 'num_epochs': 50, 'warmup_epochs': 5, 'weight_decay': 0.0001, 'patience': 10}
2025-08-16 03:09:47,517 - INFO - 设备: cuda
2025-08-16 03:09:47,517 - INFO - 加载数据集...
2025-08-16 03:09:48,947 - INFO - 从 29 个文件加载了 31905 个epochs, 创建了 31731 个序列
2025-08-16 03:09:48,948 - INFO - 创建序列数据集: 31731个序列, 序列长度=7, 通道数=3, 总epochs=31905
2025-08-16 03:09:49,210 - INFO - 从 6 个文件加载了 6146 个epochs, 创建了 6110 个序列
2025-08-16 03:09:49,210 - INFO - 创建序列数据集: 6110个序列, 序列长度=7, 通道数=3, 总epochs=6146
2025-08-16 03:09:49,390 - INFO - 从 4 个文件加载了 4257 个epochs, 创建了 4233 个序列
2025-08-16 03:09:49,390 - INFO - 创建序列数据集: 4233个序列, 序列长度=7, 通道数=3, 总epochs=4257
2025-08-16 03:09:49,391 - INFO - 训练集: 31731 sequences
2025-08-16 03:09:49,391 - INFO - 验证集: 6110 sequences
2025-08-16 03:09:49,391 - INFO - 测试集: 4233 sequences
2025-08-16 03:09:49,686 - INFO - 创建ProgressiveMAMBAFORMER_V1_Fixed: 参数量=41,655,367, d_model=512, n_heads=32, n_layers=12
2025-08-16 03:09:49,686 - INFO - 修复版本：简化架构，稳定训练
2025-08-16 03:09:49,938 - INFO - 总参数: 43,890,765
2025-08-16 03:09:49,939 - INFO - 可训练参数: 43,890,765
2025-08-16 03:09:51,132 - INFO - 
============================================================
2025-08-16 03:09:51,132 - INFO - Epoch 1/50

Epoch 1:   0%|          | 0/1984 [00:00<?, ?it/s]
Epoch 1:   0%|          | 0/1984 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_independent.py", line 474, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_independent.py", line 401, in main
    train_loss, train_acc = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage2_independent.py", line 175, in train_epoch
    aux_loss = criterion_aux(aux_output, labels)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/loss.py", line 1297, in forward
    return F.cross_entropy(
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/functional.py", line 3494, in cross_entropy
    return torch._C._nn.cross_entropy_loss(
RuntimeError: Expected target size [16, 5], got [16, 7]
