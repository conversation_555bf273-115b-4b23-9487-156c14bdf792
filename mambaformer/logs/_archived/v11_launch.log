2025-08-09 22:54:37,585 - INFO - 日志文件: ../logs/multimodal_v11_complete_20250809_225437.log
2025-08-09 22:54:37,585 - INFO - 🚀 多模态MAMBAFORMER V11训练 - 完整三模态融合
2025-08-09 22:54:37,585 - INFO - ================================================================================
2025-08-09 22:54:37,585 - INFO - 🎯 V11新特性:
2025-08-09 22:54:37,585 - INFO -   • 完整EEG+EOG+EMG三模态融合
2025-08-09 22:54:37,585 - INFO -   • 分层跨模态注意力机制
2025-08-09 22:54:37,585 - INFO -   • 自适应模态权重调整
2025-08-09 22:54:37,585 - INFO -   • 多任务辅助学习
2025-08-09 22:54:37,585 - INFO -   • 模态一致性约束
2025-08-09 22:54:37,585 - INFO -   • 睡眠阶段特定模态权重
2025-08-09 22:54:37,585 - INFO - 📋 配置: {
  "batch_size": 24,
  "seq_len": 5,
  "learning_rate": 1.5e-05,
  "weight_decay": 0.00015,
  "num_epochs": 80,
  "patience": 15,
  "d_model": 128,
  "n_heads": 8,
  "n_layers": 4,
  "dropout": 0.15,
  "temp_loss_weight": 0.08,
  "label_smoothing": 0.06,
  "use_amp": true,
  "use_channels": 2,
  "num_workers": 4
}
2025-08-09 22:54:37,614 - INFO - 🖥️  使用设备: cuda
2025-08-09 22:54:37,614 - INFO - 
================================================================================
2025-08-09 22:54:37,614 - INFO - 🚀 开始训练 MultiModal MAMBAFORMER V11 - 完整三模态阶段
2025-08-09 22:54:37,614 - INFO - 📋 EEG+EOG+EMG完整融合：分层注意力 + 自适应权重 + 多任务学习
2025-08-09 22:54:42,525 - WARNING - 加载文件 /media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4132E0.npz 失败: 文件不存在
2025-08-09 22:54:43,353 - INFO - 创建多模态序列数据集: 27761个序列, 序列长度=5, EEG×2+EOG×1+EMG×1
2025-08-09 22:54:44,299 - INFO - 创建多模态序列数据集: 4677个序列, 序列长度=5, EEG×2+EOG×1+EMG×1
2025-08-09 22:54:46,208 - INFO - 创建多模态序列数据集: 9714个序列, 序列长度=5, EEG×2+EOG×1+EMG×1
2025-08-09 22:54:46,208 - INFO - 训练集统计:
2025-08-09 22:54:46,246 - INFO -   total_sequences: 27761
2025-08-09 22:54:46,246 - INFO -   total_epochs: 27869
2025-08-09 22:54:46,246 - INFO -   seq_len: 5
2025-08-09 22:54:46,246 - INFO -   label_distribution: {0: 23970, 1: 9815, 2: 61010, 3: 17750, 4: 26260}
2025-08-09 22:54:46,246 - INFO -   channel_info: {'use_channels': 2, 'use_eog': True, 'use_emg': True, 'config': {'eeg_channels': [0, 1], 'eog_channels': [2], 'emg_channels': [3], 'description': 'EEG×2+EOG×1+EMG×1'}}
2025-08-09 22:54:46,246 - INFO - 📊 数据集大小: 训练=27761, 验证=4677, 测试=9714
2025-08-09 22:54:46,283 - INFO - 创建MultiModalMAMBAFORMER (EEG_EOG_EMG): 参数量=1,191,115
2025-08-09 22:54:47,602 - INFO - 🎯 完整多模态配置: EEG+EOG+EMG分层融合
2025-08-09 22:54:47,602 - INFO - 🧠 自适应模态权重: 根据睡眠阶段动态调整
2025-08-09 22:54:47,602 - INFO - 📚 多任务学习: 主任务+3个辅助任务
2025-08-09 22:54:47,602 - INFO - ⚡ 混合精度: True
2025-08-09 22:54:47,602 - INFO - 🚀 学习率调度: 预热13884步, 总92560步

Epoch 1 - Train:   0%|          | 0/1157 [00:00<?, ?it/s]
Epoch 1 - Train:   0%|          | 0/1157 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_multimodal_v11_complete.py", line 546, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_multimodal_v11_complete.py", line 458, in main
    result = train_v11_complete(config, device)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_multimodal_v11_complete.py", line 363, in train_v11_complete
    train_loss, train_metrics = train_epoch_v11(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_multimodal_v11_complete.py", line 167, in train_epoch_v11
    main_output, aux_outputs, modality_features = model(eeg_data, eog_data, emg_data, return_features=True)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/models/multimodal_mambaformer.py", line 386, in forward
    epoch_feat, attn_weights = self.epoch_encoder(eeg_t, eog_t, emg_t, return_modal_feats=return_features)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
TypeError: forward() got an unexpected keyword argument 'return_modal_feats'
