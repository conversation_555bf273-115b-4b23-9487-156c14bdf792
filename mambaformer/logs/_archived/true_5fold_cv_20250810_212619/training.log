2025-08-10 21:26:19,629 - INFO - ================================================================================
2025-08-10 21:26:19,629 - INFO - 🚀 MAMBAFORMER V14 - TRUE 5-FOLD CROSS-VALIDATION
2025-08-10 21:26:19,629 - INFO - ================================================================================
2025-08-10 21:26:19,629 - INFO - ⚠️ Each fold trains an INDEPENDENT model from scratch
2025-08-10 21:26:19,629 - INFO - ⚠️ NO data leakage - strict subject-level splits
2025-08-10 21:26:19,800 - INFO - Device: cuda
2025-08-10 21:26:19,800 - INFO - 
Configuration:
2025-08-10 21:26:19,800 - INFO -   d_model: 256
2025-08-10 21:26:19,800 - INFO -   n_heads: 16
2025-08-10 21:26:19,800 - INFO -   n_layers: 6
2025-08-10 21:26:19,800 - INFO -   dropout: 0.15
2025-08-10 21:26:19,800 - INFO -   seq_len: 5
2025-08-10 21:26:19,800 - INFO -   batch_size: 32
2025-08-10 21:26:19,800 - INFO -   num_workers: 4
2025-08-10 21:26:19,800 - INFO -   learning_rate: 0.0001
2025-08-10 21:26:19,800 - INFO -   num_epochs: 20
2025-08-10 21:26:19,800 - INFO -   patience: 5
2025-08-10 21:26:19,800 - INFO - 
Total subjects: 20
2025-08-10 21:26:19,801 - INFO - 
================================================================================
2025-08-10 21:26:19,801 - INFO - 🔄 FOLD 1/5 - INDEPENDENT TRAINING
2025-08-10 21:26:19,801 - INFO - ================================================================================
2025-08-10 21:26:19,801 - INFO - Train subjects (16): ['02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '16', '18', '19']
2025-08-10 21:26:19,801 - INFO - Test subjects (4): ['00', '01', '15', '17']
2025-08-10 21:26:19,801 - INFO - Train files: 31
2025-08-10 21:26:19,801 - INFO - Test files: 8
2025-08-10 21:26:19,802 - INFO - Final train subjects: ['18', '16', '13', '10', '11', '04', '19', '06', '09', '12', '14', '05', '08']
2025-08-10 21:26:19,802 - INFO - Validation subjects: ['02', '03', '07']
2025-08-10 21:26:20,958 - INFO - 从 25 个文件加载了 26416 个epochs, 创建了 26316 个序列
2025-08-10 21:26:20,958 - INFO - 创建序列数据集: 26316个序列, 序列长度=5, 通道数=3, 总epochs=26416
2025-08-10 21:26:21,207 - INFO - 从 6 个文件加载了 6146 个epochs, 创建了 6122 个序列
2025-08-10 21:26:21,207 - INFO - 创建序列数据集: 6122个序列, 序列长度=5, 通道数=3, 总epochs=6146
2025-08-10 21:26:21,726 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9714 个序列
2025-08-10 21:26:21,726 - INFO - 创建序列数据集: 9714个序列, 序列长度=5, 通道数=3, 总epochs=9746
2025-08-10 21:26:21,726 - INFO - Train samples: 26416 epochs
2025-08-10 21:26:21,726 - INFO - Val samples: 6146 epochs
2025-08-10 21:26:21,726 - INFO - Test samples: 9746 epochs
2025-08-10 21:26:21,794 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-10 21:26:21,975 - INFO - ✅ Created new model for fold 1
2025-08-10 21:27:03,603 - INFO - Fold 1 Epoch 1: Loss=0.9535, Val_Acc=0.8249, Val_F1=0.7701, Val_Kappa=0.7593, Time=40.5s
2025-08-10 21:27:03,720 - INFO - 💾 Saved best model for fold 1: F1=0.7701
2025-08-10 21:27:44,598 - INFO - Fold 1 Epoch 2: Loss=0.6389, Val_Acc=0.8765, Val_F1=0.8297, Val_Kappa=0.8298, Time=40.9s
2025-08-10 21:27:44,974 - INFO - 💾 Saved best model for fold 1: F1=0.8297
2025-08-10 21:28:27,839 - INFO - Fold 1 Epoch 3: Loss=0.5648, Val_Acc=0.8573, Val_F1=0.8073, Val_Kappa=0.8017, Time=42.9s
2025-08-10 21:29:31,283 - INFO - Fold 1 Epoch 4: Loss=0.5363, Val_Acc=0.8744, Val_F1=0.8315, Val_Kappa=0.8288, Time=63.4s
2025-08-10 21:29:31,643 - INFO - 💾 Saved best model for fold 1: F1=0.8315
2025-08-10 21:30:34,818 - INFO - Fold 1 Epoch 5: Loss=0.4954, Val_Acc=0.8570, Val_F1=0.8072, Val_Kappa=0.8038, Time=63.2s
2025-08-10 21:31:38,218 - INFO - Fold 1 Epoch 6: Loss=0.4713, Val_Acc=0.8339, Val_F1=0.7908, Val_Kappa=0.7754, Time=63.4s
2025-08-10 21:32:41,522 - INFO - Fold 1 Epoch 7: Loss=0.4436, Val_Acc=0.8659, Val_F1=0.8189, Val_Kappa=0.8157, Time=63.3s
2025-08-10 21:33:45,934 - INFO - Fold 1 Epoch 8: Loss=0.4225, Val_Acc=0.8734, Val_F1=0.8252, Val_Kappa=0.8249, Time=64.4s
2025-08-10 21:34:49,220 - INFO - Fold 1 Epoch 9: Loss=0.3876, Val_Acc=0.8604, Val_F1=0.8191, Val_Kappa=0.8101, Time=63.3s
2025-08-10 21:34:49,220 - INFO - ⏹️ Early stopping triggered for fold 1
2025-08-10 21:34:49,223 - INFO - 📊 Evaluating best model on test set...
2025-08-10 21:34:51,777 - INFO - 
📊 Fold 1 Test Results:
2025-08-10 21:34:51,777 - INFO -   Accuracy: 0.8447 (84.47%)
2025-08-10 21:34:51,777 - INFO -   Macro F1: 0.7956 (79.56%)
2025-08-10 21:34:51,777 - INFO -   Kappa: 0.7952
2025-08-10 21:34:51,777 - INFO -   Training time: 8.5 minutes
2025-08-10 21:34:51,874 - INFO - 
================================================================================
2025-08-10 21:34:51,874 - INFO - 🔄 FOLD 2/5 - INDEPENDENT TRAINING
2025-08-10 21:34:51,874 - INFO - ================================================================================
2025-08-10 21:34:51,874 - INFO - Train subjects (16): ['00', '01', '02', '04', '06', '07', '09', '10', '12', '13', '14', '15', '16', '17', '18', '19']
2025-08-10 21:34:51,874 - INFO - Test subjects (4): ['03', '05', '08', '11']
2025-08-10 21:34:51,875 - INFO - Train files: 31
2025-08-10 21:34:51,875 - INFO - Test files: 8
2025-08-10 21:34:51,875 - INFO - Final train subjects: ['09', '15', '02', '10', '13', '17', '04', '18', '14', '07', '01', '00', '06']
2025-08-10 21:34:51,875 - INFO - Validation subjects: ['19', '12', '16']
2025-08-10 21:34:53,135 - INFO - 从 25 个文件加载了 27624 个epochs, 创建了 27524 个序列
2025-08-10 21:34:53,136 - INFO - 创建序列数据集: 27524个序列, 序列长度=5, 通道数=3, 总epochs=27624
2025-08-10 21:34:53,406 - INFO - 从 6 个文件加载了 6985 个epochs, 创建了 6961 个序列
2025-08-10 21:34:53,406 - INFO - 创建序列数据集: 6961个序列, 序列长度=5, 通道数=3, 总epochs=6985
2025-08-10 21:34:53,710 - INFO - 从 8 个文件加载了 7699 个epochs, 创建了 7667 个序列
2025-08-10 21:34:53,710 - INFO - 创建序列数据集: 7667个序列, 序列长度=5, 通道数=3, 总epochs=7699
2025-08-10 21:34:53,710 - INFO - Train samples: 27624 epochs
2025-08-10 21:34:53,711 - INFO - Val samples: 6985 epochs
2025-08-10 21:34:53,711 - INFO - Test samples: 7699 epochs
2025-08-10 21:34:53,767 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-10 21:34:53,777 - INFO - ✅ Created new model for fold 2
2025-08-10 21:36:21,927 - INFO - Fold 2 Epoch 1: Loss=0.9225, Val_Acc=0.7701, Val_F1=0.7004, Val_Kappa=0.6751, Time=88.1s
2025-08-10 21:36:22,042 - INFO - 💾 Saved best model for fold 2: F1=0.7004
2025-08-10 21:37:25,217 - INFO - Fold 2 Epoch 2: Loss=0.6245, Val_Acc=0.8296, Val_F1=0.7651, Val_Kappa=0.7653, Time=63.2s
2025-08-10 21:37:25,616 - INFO - 💾 Saved best model for fold 2: F1=0.7651
2025-08-10 21:38:30,314 - INFO - Fold 2 Epoch 3: Loss=0.5491, Val_Acc=0.8365, Val_F1=0.7763, Val_Kappa=0.7726, Time=64.7s
2025-08-10 21:38:30,692 - INFO - 💾 Saved best model for fold 2: F1=0.7763
2025-08-10 21:39:35,376 - INFO - Fold 2 Epoch 4: Loss=0.5081, Val_Acc=0.8392, Val_F1=0.7796, Val_Kappa=0.7775, Time=64.7s
2025-08-10 21:39:35,772 - INFO - 💾 Saved best model for fold 2: F1=0.7796
2025-08-10 21:40:24,727 - INFO - Fold 2 Epoch 5: Loss=0.4707, Val_Acc=0.8178, Val_F1=0.7548, Val_Kappa=0.7438, Time=49.0s
2025-08-10 21:41:08,546 - INFO - Fold 2 Epoch 6: Loss=0.4569, Val_Acc=0.8255, Val_F1=0.7601, Val_Kappa=0.7570, Time=43.8s
2025-08-10 21:41:52,052 - INFO - Fold 2 Epoch 7: Loss=0.4273, Val_Acc=0.8336, Val_F1=0.7671, Val_Kappa=0.7686, Time=43.5s
2025-08-10 21:42:35,800 - INFO - Fold 2 Epoch 8: Loss=0.4163, Val_Acc=0.8451, Val_F1=0.7862, Val_Kappa=0.7838, Time=43.7s
2025-08-10 21:42:36,181 - INFO - 💾 Saved best model for fold 2: F1=0.7862
2025-08-10 21:43:21,605 - INFO - Fold 2 Epoch 9: Loss=0.4010, Val_Acc=0.8491, Val_F1=0.7884, Val_Kappa=0.7888, Time=45.4s
2025-08-10 21:43:21,948 - INFO - 💾 Saved best model for fold 2: F1=0.7884
2025-08-10 21:44:05,636 - INFO - Fold 2 Epoch 10: Loss=0.3839, Val_Acc=0.8630, Val_F1=0.7956, Val_Kappa=0.8080, Time=43.7s
2025-08-10 21:44:06,074 - INFO - 💾 Saved best model for fold 2: F1=0.7956
2025-08-10 21:44:49,533 - INFO - Fold 2 Epoch 11: Loss=0.3754, Val_Acc=0.8481, Val_F1=0.7935, Val_Kappa=0.7912, Time=43.5s
2025-08-10 21:45:32,747 - INFO - Fold 2 Epoch 12: Loss=0.3632, Val_Acc=0.8460, Val_F1=0.7845, Val_Kappa=0.7866, Time=43.2s
2025-08-10 21:46:16,482 - INFO - Fold 2 Epoch 13: Loss=0.3499, Val_Acc=0.8452, Val_F1=0.7716, Val_Kappa=0.7809, Time=43.7s
2025-08-10 21:46:59,239 - INFO - Fold 2 Epoch 14: Loss=0.3415, Val_Acc=0.8186, Val_F1=0.7388, Val_Kappa=0.7431, Time=42.8s
2025-08-10 21:47:42,532 - INFO - Fold 2 Epoch 15: Loss=0.3006, Val_Acc=0.8514, Val_F1=0.7897, Val_Kappa=0.7928, Time=43.3s
2025-08-10 21:47:42,532 - INFO - ⏹️ Early stopping triggered for fold 2
2025-08-10 21:47:42,535 - INFO - 📊 Evaluating best model on test set...
2025-08-10 21:47:44,607 - INFO - 
📊 Fold 2 Test Results:
2025-08-10 21:47:44,607 - INFO -   Accuracy: 0.7556 (75.56%)
2025-08-10 21:47:44,607 - INFO -   Macro F1: 0.7060 (70.60%)
2025-08-10 21:47:44,607 - INFO -   Kappa: 0.6771
2025-08-10 21:47:44,607 - INFO -   Training time: 12.9 minutes
2025-08-10 21:47:44,690 - INFO - 
================================================================================
2025-08-10 21:47:44,690 - INFO - 🔄 FOLD 3/5 - INDEPENDENT TRAINING
2025-08-10 21:47:44,690 - INFO - ================================================================================
2025-08-10 21:47:44,690 - INFO - Train subjects (16): ['00', '01', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '14', '15', '17', '19']
2025-08-10 21:47:44,690 - INFO - Test subjects (4): ['02', '13', '16', '18']
2025-08-10 21:47:44,690 - INFO - Train files: 32
2025-08-10 21:47:44,690 - INFO - Test files: 7
2025-08-10 21:47:44,690 - INFO - Final train subjects: ['09', '15', '11', '03', '08', '14', '19', '00', '17', '12', '01', '04', '05']
2025-08-10 21:47:44,690 - INFO - Validation subjects: ['06', '10', '07']
2025-08-10 21:47:45,969 - INFO - 从 26 个文件加载了 28911 个epochs, 创建了 28807 个序列
2025-08-10 21:47:45,969 - INFO - 创建序列数据集: 28807个序列, 序列长度=5, 通道数=3, 总epochs=28911
2025-08-10 21:47:46,216 - INFO - 从 6 个文件加载了 6304 个epochs, 创建了 6280 个序列
2025-08-10 21:47:46,216 - INFO - 创建序列数据集: 6280个序列, 序列长度=5, 通道数=3, 总epochs=6304
2025-08-10 21:47:46,496 - INFO - 从 7 个文件加载了 7093 个epochs, 创建了 7065 个序列
2025-08-10 21:47:46,496 - INFO - 创建序列数据集: 7065个序列, 序列长度=5, 通道数=3, 总epochs=7093
2025-08-10 21:47:46,496 - INFO - Train samples: 28911 epochs
2025-08-10 21:47:46,496 - INFO - Val samples: 6304 epochs
2025-08-10 21:47:46,496 - INFO - Test samples: 7093 epochs
2025-08-10 21:47:46,551 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-10 21:47:46,562 - INFO - ✅ Created new model for fold 3
2025-08-10 21:49:18,494 - INFO - Fold 3 Epoch 1: Loss=0.9628, Val_Acc=0.8261, Val_F1=0.7900, Val_Kappa=0.7597, Time=91.9s
2025-08-10 21:49:18,610 - INFO - 💾 Saved best model for fold 3: F1=0.7900
2025-08-10 21:50:26,901 - INFO - Fold 3 Epoch 2: Loss=0.6608, Val_Acc=0.8767, Val_F1=0.8385, Val_Kappa=0.8282, Time=68.3s
2025-08-10 21:50:27,284 - INFO - 💾 Saved best model for fold 3: F1=0.8385
2025-08-10 21:51:33,049 - INFO - Fold 3 Epoch 3: Loss=0.5793, Val_Acc=0.8837, Val_F1=0.8432, Val_Kappa=0.8355, Time=65.8s
2025-08-10 21:51:33,418 - INFO - 💾 Saved best model for fold 3: F1=0.8432
2025-08-10 21:52:40,296 - INFO - Fold 3 Epoch 4: Loss=0.5336, Val_Acc=0.8945, Val_F1=0.8586, Val_Kappa=0.8514, Time=66.9s
2025-08-10 21:52:40,691 - INFO - 💾 Saved best model for fold 3: F1=0.8586
2025-08-10 21:53:47,743 - INFO - Fold 3 Epoch 5: Loss=0.5095, Val_Acc=0.8902, Val_F1=0.8557, Val_Kappa=0.8447, Time=67.1s
2025-08-10 21:54:56,487 - INFO - Fold 3 Epoch 6: Loss=0.4868, Val_Acc=0.8867, Val_F1=0.8508, Val_Kappa=0.8428, Time=68.7s
2025-08-10 21:55:42,340 - INFO - Fold 3 Epoch 7: Loss=0.4572, Val_Acc=0.8939, Val_F1=0.8621, Val_Kappa=0.8521, Time=45.9s
2025-08-10 21:55:42,736 - INFO - 💾 Saved best model for fold 3: F1=0.8621
2025-08-10 21:56:27,971 - INFO - Fold 3 Epoch 8: Loss=0.4517, Val_Acc=0.8852, Val_F1=0.8527, Val_Kappa=0.8401, Time=45.2s
2025-08-10 21:57:13,786 - INFO - Fold 3 Epoch 9: Loss=0.4248, Val_Acc=0.8844, Val_F1=0.8542, Val_Kappa=0.8402, Time=45.8s
2025-08-10 21:58:01,504 - INFO - Fold 3 Epoch 10: Loss=0.4104, Val_Acc=0.8806, Val_F1=0.8483, Val_Kappa=0.8318, Time=47.7s
2025-08-10 21:58:46,841 - INFO - Fold 3 Epoch 11: Loss=0.3996, Val_Acc=0.8750, Val_F1=0.8420, Val_Kappa=0.8268, Time=45.3s
2025-08-10 21:59:32,730 - INFO - Fold 3 Epoch 12: Loss=0.3561, Val_Acc=0.8869, Val_F1=0.8562, Val_Kappa=0.8430, Time=45.9s
2025-08-10 21:59:32,731 - INFO - ⏹️ Early stopping triggered for fold 3
2025-08-10 21:59:32,733 - INFO - 📊 Evaluating best model on test set...
2025-08-10 21:59:34,913 - INFO - 
📊 Fold 3 Test Results:
2025-08-10 21:59:34,913 - INFO -   Accuracy: 0.8343 (83.43%)
2025-08-10 21:59:34,913 - INFO -   Macro F1: 0.8061 (80.61%)
2025-08-10 21:59:34,913 - INFO -   Kappa: 0.7772
2025-08-10 21:59:34,914 - INFO -   Training time: 11.8 minutes
2025-08-10 21:59:34,914 - INFO -   ✅ Targets met: F1
2025-08-10 21:59:35,000 - INFO - 
================================================================================
2025-08-10 21:59:35,001 - INFO - 🔄 FOLD 4/5 - INDEPENDENT TRAINING
2025-08-10 21:59:35,001 - INFO - ================================================================================
2025-08-10 21:59:35,001 - INFO - Train subjects (16): ['00', '01', '02', '03', '05', '06', '07', '08', '10', '11', '13', '14', '15', '16', '17', '18']
2025-08-10 21:59:35,001 - INFO - Test subjects (4): ['04', '09', '12', '19']
2025-08-10 21:59:35,001 - INFO - Train files: 31
2025-08-10 21:59:35,001 - INFO - Test files: 8
2025-08-10 21:59:35,001 - INFO - Final train subjects: ['02', '13', '08', '18', '01', '05', '16', '06', '00', '15', '03', '17', '14']
2025-08-10 21:59:35,001 - INFO - Validation subjects: ['11', '10', '07']
2025-08-10 21:59:36,063 - INFO - 从 25 个文件加载了 26623 个epochs, 创建了 26523 个序列
2025-08-10 21:59:36,063 - INFO - 创建序列数据集: 26523个序列, 序列长度=5, 通道数=3, 总epochs=26623
2025-08-10 21:59:36,305 - INFO - 从 6 个文件加载了 6175 个epochs, 创建了 6151 个序列
2025-08-10 21:59:36,306 - INFO - 创建序列数据集: 6151个序列, 序列长度=5, 通道数=3, 总epochs=6175
2025-08-10 21:59:36,675 - INFO - 从 8 个文件加载了 9510 个epochs, 创建了 9478 个序列
2025-08-10 21:59:36,675 - INFO - 创建序列数据集: 9478个序列, 序列长度=5, 通道数=3, 总epochs=9510
2025-08-10 21:59:36,675 - INFO - Train samples: 26623 epochs
2025-08-10 21:59:36,675 - INFO - Val samples: 6175 epochs
2025-08-10 21:59:36,675 - INFO - Test samples: 9510 epochs
2025-08-10 21:59:36,730 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-10 21:59:36,741 - INFO - ✅ Created new model for fold 4
2025-08-10 22:00:18,260 - INFO - Fold 4 Epoch 1: Loss=0.9332, Val_Acc=0.8133, Val_F1=0.7686, Val_Kappa=0.7365, Time=41.5s
2025-08-10 22:00:18,376 - INFO - 💾 Saved best model for fold 4: F1=0.7686
2025-08-10 22:01:01,044 - INFO - Fold 4 Epoch 2: Loss=0.6228, Val_Acc=0.8089, Val_F1=0.7662, Val_Kappa=0.7338, Time=42.7s
2025-08-10 22:01:43,698 - INFO - Fold 4 Epoch 3: Loss=0.5435, Val_Acc=0.8081, Val_F1=0.7723, Val_Kappa=0.7338, Time=42.7s
2025-08-10 22:01:44,070 - INFO - 💾 Saved best model for fold 4: F1=0.7723
2025-08-10 22:02:28,847 - INFO - Fold 4 Epoch 4: Loss=0.5040, Val_Acc=0.7903, Val_F1=0.7482, Val_Kappa=0.7047, Time=44.8s
2025-08-10 22:03:13,419 - INFO - Fold 4 Epoch 5: Loss=0.4697, Val_Acc=0.8071, Val_F1=0.7724, Val_Kappa=0.7333, Time=44.6s
2025-08-10 22:03:13,819 - INFO - 💾 Saved best model for fold 4: F1=0.7724
2025-08-10 22:03:55,124 - INFO - Fold 4 Epoch 6: Loss=0.4405, Val_Acc=0.8092, Val_F1=0.7707, Val_Kappa=0.7333, Time=41.3s
2025-08-10 22:04:38,241 - INFO - Fold 4 Epoch 7: Loss=0.4283, Val_Acc=0.7851, Val_F1=0.7509, Val_Kappa=0.7051, Time=43.1s
2025-08-10 22:05:21,110 - INFO - Fold 4 Epoch 8: Loss=0.4102, Val_Acc=0.8104, Val_F1=0.7733, Val_Kappa=0.7350, Time=42.9s
2025-08-10 22:05:21,502 - INFO - 💾 Saved best model for fold 4: F1=0.7733
2025-08-10 22:06:04,887 - INFO - Fold 4 Epoch 9: Loss=0.3921, Val_Acc=0.8084, Val_F1=0.7693, Val_Kappa=0.7333, Time=43.4s
2025-08-10 22:06:47,433 - INFO - Fold 4 Epoch 10: Loss=0.3784, Val_Acc=0.8045, Val_F1=0.7671, Val_Kappa=0.7273, Time=42.5s
2025-08-10 22:07:30,844 - INFO - Fold 4 Epoch 11: Loss=0.3648, Val_Acc=0.7862, Val_F1=0.7495, Val_Kappa=0.7043, Time=43.4s
2025-08-10 22:08:14,578 - INFO - Fold 4 Epoch 12: Loss=0.3501, Val_Acc=0.7932, Val_F1=0.7556, Val_Kappa=0.7150, Time=43.7s
2025-08-10 22:08:58,909 - INFO - Fold 4 Epoch 13: Loss=0.3192, Val_Acc=0.7798, Val_F1=0.7459, Val_Kappa=0.6977, Time=44.3s
2025-08-10 22:08:58,909 - INFO - ⏹️ Early stopping triggered for fold 4
2025-08-10 22:08:58,913 - INFO - 📊 Evaluating best model on test set...
2025-08-10 22:09:01,893 - INFO - 
📊 Fold 4 Test Results:
2025-08-10 22:09:01,894 - INFO -   Accuracy: 0.8426 (84.26%)
2025-08-10 22:09:01,894 - INFO -   Macro F1: 0.7980 (79.80%)
2025-08-10 22:09:01,894 - INFO -   Kappa: 0.7806
2025-08-10 22:09:01,894 - INFO -   Training time: 9.4 minutes
2025-08-10 22:09:01,978 - INFO - 
================================================================================
2025-08-10 22:09:01,978 - INFO - 🔄 FOLD 5/5 - INDEPENDENT TRAINING
2025-08-10 22:09:01,978 - INFO - ================================================================================
2025-08-10 22:09:01,978 - INFO - Train subjects (16): ['00', '01', '02', '03', '04', '05', '08', '09', '11', '12', '13', '15', '16', '17', '18', '19']
2025-08-10 22:09:01,979 - INFO - Test subjects (4): ['06', '07', '10', '14']
2025-08-10 22:09:01,979 - INFO - Train files: 31
2025-08-10 22:09:01,979 - INFO - Test files: 8
2025-08-10 22:09:01,979 - INFO - Final train subjects: ['09', '18', '19', '12', '15', '00', '02', '03', '13', '04', '11', '05', '17']
2025-08-10 22:09:01,979 - INFO - Validation subjects: ['01', '16', '08']
2025-08-10 22:09:03,145 - INFO - 从 25 个文件加载了 27424 个epochs, 创建了 27324 个序列
2025-08-10 22:09:03,145 - INFO - 创建序列数据集: 27324个序列, 序列长度=5, 通道数=3, 总epochs=27424
2025-08-10 22:09:03,430 - INFO - 从 6 个文件加载了 6624 个epochs, 创建了 6600 个序列
2025-08-10 22:09:03,430 - INFO - 创建序列数据集: 6600个序列, 序列长度=5, 通道数=3, 总epochs=6624
2025-08-10 22:09:03,980 - INFO - 从 8 个文件加载了 8260 个epochs, 创建了 8228 个序列
2025-08-10 22:09:03,981 - INFO - 创建序列数据集: 8228个序列, 序列长度=5, 通道数=3, 总epochs=8260
2025-08-10 22:09:03,981 - INFO - Train samples: 27424 epochs
2025-08-10 22:09:03,981 - INFO - Val samples: 6624 epochs
2025-08-10 22:09:03,981 - INFO - Test samples: 8260 epochs
2025-08-10 22:09:04,029 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-10 22:09:04,112 - INFO - ✅ Created new model for fold 5
2025-08-10 22:09:48,149 - INFO - Fold 5 Epoch 1: Loss=0.9886, Val_Acc=0.8403, Val_F1=0.7914, Val_Kappa=0.7818, Time=44.0s
2025-08-10 22:09:48,275 - INFO - 💾 Saved best model for fold 5: F1=0.7914
2025-08-10 22:10:31,757 - INFO - Fold 5 Epoch 2: Loss=0.6702, Val_Acc=0.8644, Val_F1=0.8198, Val_Kappa=0.8153, Time=43.5s
2025-08-10 22:10:32,164 - INFO - 💾 Saved best model for fold 5: F1=0.8198
2025-08-10 22:11:15,627 - INFO - Fold 5 Epoch 3: Loss=0.5969, Val_Acc=0.8685, Val_F1=0.8250, Val_Kappa=0.8214, Time=43.5s
2025-08-10 22:11:16,016 - INFO - 💾 Saved best model for fold 5: F1=0.8250
2025-08-10 22:12:00,318 - INFO - Fold 5 Epoch 4: Loss=0.5543, Val_Acc=0.8312, Val_F1=0.7926, Val_Kappa=0.7737, Time=44.3s
2025-08-10 22:12:44,481 - INFO - Fold 5 Epoch 5: Loss=0.5146, Val_Acc=0.8533, Val_F1=0.8091, Val_Kappa=0.8025, Time=44.2s
2025-08-10 22:13:28,417 - INFO - Fold 5 Epoch 6: Loss=0.5016, Val_Acc=0.8495, Val_F1=0.7989, Val_Kappa=0.7942, Time=43.9s
2025-08-10 22:14:12,083 - INFO - Fold 5 Epoch 7: Loss=0.4730, Val_Acc=0.8573, Val_F1=0.8125, Val_Kappa=0.8060, Time=43.7s
2025-08-10 22:14:55,518 - INFO - Fold 5 Epoch 8: Loss=0.4212, Val_Acc=0.8410, Val_F1=0.7963, Val_Kappa=0.7836, Time=43.4s
2025-08-10 22:14:55,519 - INFO - ⏹️ Early stopping triggered for fold 5
2025-08-10 22:14:55,521 - INFO - 📊 Evaluating best model on test set...
2025-08-10 22:14:57,909 - INFO - 
📊 Fold 5 Test Results:
2025-08-10 22:14:57,909 - INFO -   Accuracy: 0.8800 (88.00%)
2025-08-10 22:14:57,909 - INFO -   Macro F1: 0.8385 (83.85%)
2025-08-10 22:14:57,909 - INFO -   Kappa: 0.8312
2025-08-10 22:14:57,909 - INFO -   Training time: 5.9 minutes
2025-08-10 22:14:57,909 - INFO -   ✅ Targets met: Acc, F1, Kappa
2025-08-10 22:14:58,021 - INFO - 
================================================================================
2025-08-10 22:14:58,021 - INFO - 📊 TRUE 5-FOLD CROSS-VALIDATION RESULTS
2025-08-10 22:14:58,021 - INFO - ================================================================================
2025-08-10 22:14:58,021 - INFO - 
Per-Fold Test Results:
2025-08-10 22:14:58,021 - INFO - Fold 1: Acc=0.8447, F1=0.7956, Kappa=0.7952
2025-08-10 22:14:58,021 - INFO - Fold 2: Acc=0.7556, F1=0.7060, Kappa=0.6771
2025-08-10 22:14:58,021 - INFO - Fold 3: Acc=0.8343, F1=0.8061, Kappa=0.7772
2025-08-10 22:14:58,021 - INFO - Fold 4: Acc=0.8426, F1=0.7980, Kappa=0.7806
2025-08-10 22:14:58,021 - INFO - Fold 5: Acc=0.8800, F1=0.8385, Kappa=0.8312
2025-08-10 22:14:58,021 - INFO - 
📊 Average Metrics (Mean ± Std):
2025-08-10 22:14:58,022 - INFO -   Accuracy: 0.8314 ± 0.0411 (83.14% ± 4.11%)
2025-08-10 22:14:58,022 - INFO -   Macro F1: 0.7889 ± 0.0442 (78.89% ± 4.42%)
2025-08-10 22:14:58,022 - INFO -   Kappa: 0.7723 ± 0.0513
2025-08-10 22:14:58,022 - INFO - 
📈 95% Confidence Intervals:
2025-08-10 22:14:58,022 - INFO -   Accuracy: [0.7510, 0.9119]
2025-08-10 22:14:58,022 - INFO -   Macro F1: [0.7022, 0.8755]
2025-08-10 22:14:58,022 - INFO -   Kappa: [0.6717, 0.8728]
2025-08-10 22:14:58,022 - INFO - 
🎯 Target Achievement (Based on Average):
2025-08-10 22:14:58,022 - INFO -   ❌ Accuracy: 0.8314 < 0.87 (gap: 0.0386)
2025-08-10 22:14:58,022 - INFO -   ❌ Macro F1: 0.7889 < 0.80 (gap: 0.0111)
2025-08-10 22:14:58,022 - INFO -   ❌ Kappa: 0.7723 < 0.80 (gap: 0.0277)
2025-08-10 22:14:58,022 - INFO - 
⏱️ Total training time: 0.8 hours
