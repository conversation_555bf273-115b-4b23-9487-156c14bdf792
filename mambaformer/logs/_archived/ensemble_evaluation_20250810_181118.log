2025-08-10 18:11:18,292 - INFO - ================================================================================
2025-08-10 18:11:18,292 - INFO - 🚀 MAMBAFORMER Ensemble Evaluation (V13 + V14 Fixed)
2025-08-10 18:11:18,292 - INFO - ================================================================================
2025-08-10 18:11:18,321 - INFO - 🔧 Device: cuda
2025-08-10 18:11:18,529 - INFO - 从 4 个文件加载了 4693 个epochs, 创建了 4677 个序列
2025-08-10 18:11:18,530 - INFO - 创建序列数据集: 4677个序列, 序列长度=5, 通道数=3, 总epochs=4693
2025-08-10 18:11:18,531 - INFO - 📊 Validation set: 4693 epochs
2025-08-10 18:11:18,531 - INFO - 
🔍 Getting validation predictions from V13...
2025-08-10 18:11:18,603 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=8, n_layers=6
2025-08-10 18:11:20,240 - INFO - 
🔍 Getting validation predictions from V14...
2025-08-10 18:11:20,296 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-10 18:11:21,423 - INFO - 
⚖️ Optimizing ensemble weights on validation set...
2025-08-10 18:11:21,424 - INFO - 
  Strategy: combined
2025-08-10 18:11:21,463 - INFO -     Weights: V13=0.500, V14=0.500
2025-08-10 18:11:21,463 - INFO -     Val Acc: 0.8486, F1: 0.7730, Kappa: 0.7934
2025-08-10 18:11:21,463 - INFO - 
  Strategy: f1
2025-08-10 18:11:21,494 - INFO -     Weights: V13=0.500, V14=0.500
2025-08-10 18:11:21,494 - INFO -     Val Acc: 0.8486, F1: 0.7730, Kappa: 0.7934
2025-08-10 18:11:21,494 - INFO - 
  Strategy: accuracy
2025-08-10 18:11:21,525 - INFO -     Weights: V13=0.500, V14=0.500
2025-08-10 18:11:21,525 - INFO -     Val Acc: 0.8486, F1: 0.7730, Kappa: 0.7934
2025-08-10 18:11:21,525 - INFO - 
  Strategy: kappa
2025-08-10 18:11:21,558 - INFO -     Weights: V13=0.500, V14=0.500
2025-08-10 18:11:21,558 - INFO -     Val Acc: 0.8486, F1: 0.7730, Kappa: 0.7934
2025-08-10 18:11:22,101 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9714 个序列
2025-08-10 18:11:22,101 - INFO - 创建序列数据集: 9714个序列, 序列长度=5, 通道数=3, 总epochs=9746
2025-08-10 18:11:22,101 - INFO - 
📊 Test set: 9746 epochs
2025-08-10 18:11:22,101 - INFO - 
🔍 Getting test predictions from V13...
2025-08-10 18:11:22,159 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=8, n_layers=6
2025-08-10 18:11:24,253 - INFO - 
🔍 Getting test predictions from V14...
2025-08-10 18:11:24,313 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-10 18:11:26,407 - INFO - 
================================================================================
2025-08-10 18:11:26,408 - INFO - 📊 TEST SET RESULTS
2025-08-10 18:11:26,408 - INFO - ================================================================================
2025-08-10 18:11:26,434 - INFO - 
📊 Strategy: combined
2025-08-10 18:11:26,434 - INFO -    Weights: V13=0.500, V14=0.500
2025-08-10 18:11:26,434 - INFO -    Test Accuracy: 0.8464 (84.64%)
2025-08-10 18:11:26,434 - INFO -    Test Macro F1: 0.7951 (79.51%)
2025-08-10 18:11:26,434 - INFO -    Test Kappa: 0.7941
2025-08-10 18:11:26,435 - INFO -    ⚠️ Below all targets
2025-08-10 18:11:26,452 - INFO - 
📊 Strategy: f1
2025-08-10 18:11:26,452 - INFO -    Weights: V13=0.500, V14=0.500
2025-08-10 18:11:26,452 - INFO -    Test Accuracy: 0.8464 (84.64%)
2025-08-10 18:11:26,452 - INFO -    Test Macro F1: 0.7951 (79.51%)
2025-08-10 18:11:26,452 - INFO -    Test Kappa: 0.7941
2025-08-10 18:11:26,452 - INFO -    ⚠️ Below all targets
2025-08-10 18:11:26,468 - INFO - 
📊 Strategy: accuracy
2025-08-10 18:11:26,468 - INFO -    Weights: V13=0.500, V14=0.500
2025-08-10 18:11:26,469 - INFO -    Test Accuracy: 0.8464 (84.64%)
2025-08-10 18:11:26,469 - INFO -    Test Macro F1: 0.7951 (79.51%)
2025-08-10 18:11:26,469 - INFO -    Test Kappa: 0.7941
2025-08-10 18:11:26,469 - INFO -    ⚠️ Below all targets
2025-08-10 18:11:26,484 - INFO - 
📊 Strategy: kappa
2025-08-10 18:11:26,484 - INFO -    Weights: V13=0.500, V14=0.500
2025-08-10 18:11:26,484 - INFO -    Test Accuracy: 0.8464 (84.64%)
2025-08-10 18:11:26,484 - INFO -    Test Macro F1: 0.7951 (79.51%)
2025-08-10 18:11:26,485 - INFO -    Test Kappa: 0.7941
2025-08-10 18:11:26,485 - INFO -    ⚠️ Below all targets
2025-08-10 18:11:26,501 - INFO - 
📊 Strategy: equal
2025-08-10 18:11:26,501 - INFO -    Weights: V13=0.500, V14=0.500
2025-08-10 18:11:26,501 - INFO -    Test Accuracy: 0.8464 (84.64%)
2025-08-10 18:11:26,501 - INFO -    Test Macro F1: 0.7951 (79.51%)
2025-08-10 18:11:26,501 - INFO -    Test Kappa: 0.7941
2025-08-10 18:11:26,501 - INFO -    ⚠️ Below all targets
2025-08-10 18:11:26,502 - INFO - 
📊 Results saved to ../../configs/ensemble_fixed_results.json
2025-08-10 18:11:26,502 - INFO - 
================================================================================
2025-08-10 18:11:26,502 - INFO - 🏆 BEST STRATEGY: combined
2025-08-10 18:11:26,502 - INFO - ================================================================================
2025-08-10 18:11:26,502 - INFO - Weights: V13=0.500, V14=0.500
2025-08-10 18:11:26,502 - INFO - Test Accuracy: 0.8464 (84.64%)
2025-08-10 18:11:26,502 - INFO - Test Macro F1: 0.7951 (79.51%)
2025-08-10 18:11:26,502 - INFO - Test Kappa: 0.7941
2025-08-10 18:11:26,502 - INFO - 
✅ Ensemble evaluation complete!
