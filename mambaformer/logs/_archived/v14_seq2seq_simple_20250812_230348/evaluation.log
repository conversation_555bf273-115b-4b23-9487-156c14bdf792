2025-08-12 23:03:48,079 - INFO - ================================================================================
2025-08-12 23:03:48,079 - INFO - 🔬 V14 Sliding Window Inference Evaluation
2025-08-12 23:03:48,079 - INFO - ================================================================================
2025-08-12 23:03:48,248 - INFO - Device: cuda
2025-08-12 23:03:48,248 - INFO - 
============================================================
2025-08-12 23:03:48,248 - INFO - Testing: V14 Optimized
2025-08-12 23:03:48,248 - INFO - ============================================================
2025-08-12 23:03:48,531 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=1,983,191, d_model=192, n_heads=12, n_layers=4
2025-08-12 23:03:48,539 - ERROR - Error testing V14 Optimized: Error(s) in loading state_dict for SequentialMAMBAFORMER_V2:
	Missing key(s) in state_dict: "feature_extractor.conv_layers.0.weight", "feature_extractor.conv_layers.0.bias", "feature_extractor.conv_layers.1.weight", "feature_extractor.conv_layers.1.bias", "feature_extractor.conv_layers.1.running_mean", "feature_extractor.conv_layers.1.running_var", "feature_extractor.conv_layers.5.weight", "feature_extractor.conv_layers.5.bias", "feature_extractor.conv_layers.6.weight", "feature_extractor.conv_layers.6.bias", "feature_extractor.conv_layers.6.running_mean", "feature_extractor.conv_layers.6.running_var", "feature_extractor.conv_layers.10.weight", "feature_extractor.conv_layers.10.bias", "feature_extractor.conv_layers.11.weight", "feature_extractor.conv_layers.11.bias", "feature_extractor.conv_layers.11.running_mean", "feature_extractor.conv_layers.11.running_var", "pos_encoder.pe", "transformer_encoder.layers.0.self_attn.in_proj_weight", "transformer_encoder.layers.0.self_attn.in_proj_bias", "transformer_encoder.layers.0.self_attn.out_proj.weight", "transformer_encoder.layers.0.self_attn.out_proj.bias", "transformer_encoder.layers.0.linear1.weight", "transformer_encoder.layers.0.linear1.bias", "transformer_encoder.layers.0.linear2.weight", "transformer_encoder.layers.0.linear2.bias", "transformer_encoder.layers.0.norm1.weight", "transformer_encoder.layers.0.norm1.bias", "transformer_encoder.layers.0.norm2.weight", "transformer_encoder.layers.0.norm2.bias", "transformer_encoder.layers.1.self_attn.in_proj_weight", "transformer_encoder.layers.1.self_attn.in_proj_bias", "transformer_encoder.layers.1.self_attn.out_proj.weight", "transformer_encoder.layers.1.self_attn.out_proj.bias", "transformer_encoder.layers.1.linear1.weight", "transformer_encoder.layers.1.linear1.bias", "transformer_encoder.layers.1.linear2.weight", "transformer_encoder.layers.1.linear2.bias", "transformer_encoder.layers.1.norm1.weight", "transformer_encoder.layers.1.norm1.bias", "transformer_encoder.layers.1.norm2.weight", "transformer_encoder.layers.1.norm2.bias", "transformer_encoder.layers.2.self_attn.in_proj_weight", "transformer_encoder.layers.2.self_attn.in_proj_bias", "transformer_encoder.layers.2.self_attn.out_proj.weight", "transformer_encoder.layers.2.self_attn.out_proj.bias", "transformer_encoder.layers.2.linear1.weight", "transformer_encoder.layers.2.linear1.bias", "transformer_encoder.layers.2.linear2.weight", "transformer_encoder.layers.2.linear2.bias", "transformer_encoder.layers.2.norm1.weight", "transformer_encoder.layers.2.norm1.bias", "transformer_encoder.layers.2.norm2.weight", "transformer_encoder.layers.2.norm2.bias", "transformer_encoder.layers.3.self_attn.in_proj_weight", "transformer_encoder.layers.3.self_attn.in_proj_bias", "transformer_encoder.layers.3.self_attn.out_proj.weight", "transformer_encoder.layers.3.self_attn.out_proj.bias", "transformer_encoder.layers.3.linear1.weight", "transformer_encoder.layers.3.linear1.bias", "transformer_encoder.layers.3.linear2.weight", "transformer_encoder.layers.3.linear2.bias", "transformer_encoder.layers.3.norm1.weight", "transformer_encoder.layers.3.norm1.bias", "transformer_encoder.layers.3.norm2.weight", "transformer_encoder.layers.3.norm2.bias", "classifier.0.weight", "classifier.0.bias", "classifier.2.weight", "classifier.2.bias", "classifier.5.weight", "classifier.5.bias", "auxiliary_head.0.weight", "auxiliary_head.0.bias", "auxiliary_head.2.weight", "auxiliary_head.2.bias", "auxiliary_head.5.weight", "auxiliary_head.5.bias". 
	Unexpected key(s) in state_dict: "backbone.feature_extractor.conv_layers.0.weight", "backbone.feature_extractor.conv_layers.0.bias", "backbone.feature_extractor.conv_layers.1.weight", "backbone.feature_extractor.conv_layers.1.bias", "backbone.feature_extractor.conv_layers.1.running_mean", "backbone.feature_extractor.conv_layers.1.running_var", "backbone.feature_extractor.conv_layers.1.num_batches_tracked", "backbone.feature_extractor.conv_layers.5.weight", "backbone.feature_extractor.conv_layers.5.bias", "backbone.feature_extractor.conv_layers.6.weight", "backbone.feature_extractor.conv_layers.6.bias", "backbone.feature_extractor.conv_layers.6.running_mean", "backbone.feature_extractor.conv_layers.6.running_var", "backbone.feature_extractor.conv_layers.6.num_batches_tracked", "backbone.feature_extractor.conv_layers.10.weight", "backbone.feature_extractor.conv_layers.10.bias", "backbone.feature_extractor.conv_layers.11.weight", "backbone.feature_extractor.conv_layers.11.bias", "backbone.feature_extractor.conv_layers.11.running_mean", "backbone.feature_extractor.conv_layers.11.running_var", "backbone.feature_extractor.conv_layers.11.num_batches_tracked", "backbone.pos_encoder.pe", "backbone.transformer_encoder.layers.0.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.0.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.0.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.0.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.0.linear1.weight", "backbone.transformer_encoder.layers.0.linear1.bias", "backbone.transformer_encoder.layers.0.linear2.weight", "backbone.transformer_encoder.layers.0.linear2.bias", "backbone.transformer_encoder.layers.0.norm1.weight", "backbone.transformer_encoder.layers.0.norm1.bias", "backbone.transformer_encoder.layers.0.norm2.weight", "backbone.transformer_encoder.layers.0.norm2.bias", "backbone.transformer_encoder.layers.1.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.1.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.1.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.1.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.1.linear1.weight", "backbone.transformer_encoder.layers.1.linear1.bias", "backbone.transformer_encoder.layers.1.linear2.weight", "backbone.transformer_encoder.layers.1.linear2.bias", "backbone.transformer_encoder.layers.1.norm1.weight", "backbone.transformer_encoder.layers.1.norm1.bias", "backbone.transformer_encoder.layers.1.norm2.weight", "backbone.transformer_encoder.layers.1.norm2.bias", "backbone.transformer_encoder.layers.2.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.2.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.2.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.2.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.2.linear1.weight", "backbone.transformer_encoder.layers.2.linear1.bias", "backbone.transformer_encoder.layers.2.linear2.weight", "backbone.transformer_encoder.layers.2.linear2.bias", "backbone.transformer_encoder.layers.2.norm1.weight", "backbone.transformer_encoder.layers.2.norm1.bias", "backbone.transformer_encoder.layers.2.norm2.weight", "backbone.transformer_encoder.layers.2.norm2.bias", "backbone.transformer_encoder.layers.3.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.3.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.3.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.3.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.3.linear1.weight", "backbone.transformer_encoder.layers.3.linear1.bias", "backbone.transformer_encoder.layers.3.linear2.weight", "backbone.transformer_encoder.layers.3.linear2.bias", "backbone.transformer_encoder.layers.3.norm1.weight", "backbone.transformer_encoder.layers.3.norm1.bias", "backbone.transformer_encoder.layers.3.norm2.weight", "backbone.transformer_encoder.layers.3.norm2.bias", "backbone.classifier.0.weight", "backbone.classifier.0.bias", "backbone.classifier.2.weight", "backbone.classifier.2.bias", "backbone.classifier.5.weight", "backbone.classifier.5.bias", "backbone.auxiliary_head.0.weight", "backbone.auxiliary_head.0.bias", "backbone.auxiliary_head.2.weight", "backbone.auxiliary_head.2.bias", "backbone.auxiliary_head.5.weight", "backbone.auxiliary_head.5.bias", "n1_branch.0.weight", "n1_branch.0.bias", "n1_branch.1.weight", "n1_branch.1.bias", "n1_branch.4.weight", "n1_branch.4.bias", "fusion_gate.0.weight", "fusion_gate.0.bias". 
2025-08-12 23:03:48,539 - INFO - 
============================================================
2025-08-12 23:03:48,539 - INFO - Testing: V14 Robust
2025-08-12 23:03:48,539 - INFO - ============================================================
2025-08-12 23:03:48,738 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=7,836,409, d_model=280, n_heads=14, n_layers=8
2025-08-12 23:03:48,757 - ERROR - Error testing V14 Robust: Error(s) in loading state_dict for SequentialMAMBAFORMER_V2:
	Missing key(s) in state_dict: "feature_extractor.conv_layers.0.weight", "feature_extractor.conv_layers.0.bias", "feature_extractor.conv_layers.1.weight", "feature_extractor.conv_layers.1.bias", "feature_extractor.conv_layers.1.running_mean", "feature_extractor.conv_layers.1.running_var", "feature_extractor.conv_layers.5.weight", "feature_extractor.conv_layers.5.bias", "feature_extractor.conv_layers.6.weight", "feature_extractor.conv_layers.6.bias", "feature_extractor.conv_layers.6.running_mean", "feature_extractor.conv_layers.6.running_var", "feature_extractor.conv_layers.10.weight", "feature_extractor.conv_layers.10.bias", "feature_extractor.conv_layers.11.weight", "feature_extractor.conv_layers.11.bias", "feature_extractor.conv_layers.11.running_mean", "feature_extractor.conv_layers.11.running_var", "pos_encoder.pe", "transformer_encoder.layers.0.self_attn.in_proj_weight", "transformer_encoder.layers.0.self_attn.in_proj_bias", "transformer_encoder.layers.0.self_attn.out_proj.weight", "transformer_encoder.layers.0.self_attn.out_proj.bias", "transformer_encoder.layers.0.linear1.weight", "transformer_encoder.layers.0.linear1.bias", "transformer_encoder.layers.0.linear2.weight", "transformer_encoder.layers.0.linear2.bias", "transformer_encoder.layers.0.norm1.weight", "transformer_encoder.layers.0.norm1.bias", "transformer_encoder.layers.0.norm2.weight", "transformer_encoder.layers.0.norm2.bias", "transformer_encoder.layers.1.self_attn.in_proj_weight", "transformer_encoder.layers.1.self_attn.in_proj_bias", "transformer_encoder.layers.1.self_attn.out_proj.weight", "transformer_encoder.layers.1.self_attn.out_proj.bias", "transformer_encoder.layers.1.linear1.weight", "transformer_encoder.layers.1.linear1.bias", "transformer_encoder.layers.1.linear2.weight", "transformer_encoder.layers.1.linear2.bias", "transformer_encoder.layers.1.norm1.weight", "transformer_encoder.layers.1.norm1.bias", "transformer_encoder.layers.1.norm2.weight", "transformer_encoder.layers.1.norm2.bias", "transformer_encoder.layers.2.self_attn.in_proj_weight", "transformer_encoder.layers.2.self_attn.in_proj_bias", "transformer_encoder.layers.2.self_attn.out_proj.weight", "transformer_encoder.layers.2.self_attn.out_proj.bias", "transformer_encoder.layers.2.linear1.weight", "transformer_encoder.layers.2.linear1.bias", "transformer_encoder.layers.2.linear2.weight", "transformer_encoder.layers.2.linear2.bias", "transformer_encoder.layers.2.norm1.weight", "transformer_encoder.layers.2.norm1.bias", "transformer_encoder.layers.2.norm2.weight", "transformer_encoder.layers.2.norm2.bias", "transformer_encoder.layers.3.self_attn.in_proj_weight", "transformer_encoder.layers.3.self_attn.in_proj_bias", "transformer_encoder.layers.3.self_attn.out_proj.weight", "transformer_encoder.layers.3.self_attn.out_proj.bias", "transformer_encoder.layers.3.linear1.weight", "transformer_encoder.layers.3.linear1.bias", "transformer_encoder.layers.3.linear2.weight", "transformer_encoder.layers.3.linear2.bias", "transformer_encoder.layers.3.norm1.weight", "transformer_encoder.layers.3.norm1.bias", "transformer_encoder.layers.3.norm2.weight", "transformer_encoder.layers.3.norm2.bias", "transformer_encoder.layers.4.self_attn.in_proj_weight", "transformer_encoder.layers.4.self_attn.in_proj_bias", "transformer_encoder.layers.4.self_attn.out_proj.weight", "transformer_encoder.layers.4.self_attn.out_proj.bias", "transformer_encoder.layers.4.linear1.weight", "transformer_encoder.layers.4.linear1.bias", "transformer_encoder.layers.4.linear2.weight", "transformer_encoder.layers.4.linear2.bias", "transformer_encoder.layers.4.norm1.weight", "transformer_encoder.layers.4.norm1.bias", "transformer_encoder.layers.4.norm2.weight", "transformer_encoder.layers.4.norm2.bias", "transformer_encoder.layers.5.self_attn.in_proj_weight", "transformer_encoder.layers.5.self_attn.in_proj_bias", "transformer_encoder.layers.5.self_attn.out_proj.weight", "transformer_encoder.layers.5.self_attn.out_proj.bias", "transformer_encoder.layers.5.linear1.weight", "transformer_encoder.layers.5.linear1.bias", "transformer_encoder.layers.5.linear2.weight", "transformer_encoder.layers.5.linear2.bias", "transformer_encoder.layers.5.norm1.weight", "transformer_encoder.layers.5.norm1.bias", "transformer_encoder.layers.5.norm2.weight", "transformer_encoder.layers.5.norm2.bias", "transformer_encoder.layers.6.self_attn.in_proj_weight", "transformer_encoder.layers.6.self_attn.in_proj_bias", "transformer_encoder.layers.6.self_attn.out_proj.weight", "transformer_encoder.layers.6.self_attn.out_proj.bias", "transformer_encoder.layers.6.linear1.weight", "transformer_encoder.layers.6.linear1.bias", "transformer_encoder.layers.6.linear2.weight", "transformer_encoder.layers.6.linear2.bias", "transformer_encoder.layers.6.norm1.weight", "transformer_encoder.layers.6.norm1.bias", "transformer_encoder.layers.6.norm2.weight", "transformer_encoder.layers.6.norm2.bias", "transformer_encoder.layers.7.self_attn.in_proj_weight", "transformer_encoder.layers.7.self_attn.in_proj_bias", "transformer_encoder.layers.7.self_attn.out_proj.weight", "transformer_encoder.layers.7.self_attn.out_proj.bias", "transformer_encoder.layers.7.linear1.weight", "transformer_encoder.layers.7.linear1.bias", "transformer_encoder.layers.7.linear2.weight", "transformer_encoder.layers.7.linear2.bias", "transformer_encoder.layers.7.norm1.weight", "transformer_encoder.layers.7.norm1.bias", "transformer_encoder.layers.7.norm2.weight", "transformer_encoder.layers.7.norm2.bias", "classifier.0.weight", "classifier.0.bias", "classifier.2.weight", "classifier.2.bias", "classifier.5.weight", "classifier.5.bias", "auxiliary_head.0.weight", "auxiliary_head.0.bias", "auxiliary_head.2.weight", "auxiliary_head.2.bias", "auxiliary_head.5.weight", "auxiliary_head.5.bias". 
	Unexpected key(s) in state_dict: "backbone.feature_extractor.conv_layers.0.weight", "backbone.feature_extractor.conv_layers.0.bias", "backbone.feature_extractor.conv_layers.1.weight", "backbone.feature_extractor.conv_layers.1.bias", "backbone.feature_extractor.conv_layers.1.running_mean", "backbone.feature_extractor.conv_layers.1.running_var", "backbone.feature_extractor.conv_layers.1.num_batches_tracked", "backbone.feature_extractor.conv_layers.5.weight", "backbone.feature_extractor.conv_layers.5.bias", "backbone.feature_extractor.conv_layers.6.weight", "backbone.feature_extractor.conv_layers.6.bias", "backbone.feature_extractor.conv_layers.6.running_mean", "backbone.feature_extractor.conv_layers.6.running_var", "backbone.feature_extractor.conv_layers.6.num_batches_tracked", "backbone.feature_extractor.conv_layers.10.weight", "backbone.feature_extractor.conv_layers.10.bias", "backbone.feature_extractor.conv_layers.11.weight", "backbone.feature_extractor.conv_layers.11.bias", "backbone.feature_extractor.conv_layers.11.running_mean", "backbone.feature_extractor.conv_layers.11.running_var", "backbone.feature_extractor.conv_layers.11.num_batches_tracked", "backbone.pos_encoder.pe", "backbone.transformer_encoder.layers.0.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.0.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.0.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.0.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.0.linear1.weight", "backbone.transformer_encoder.layers.0.linear1.bias", "backbone.transformer_encoder.layers.0.linear2.weight", "backbone.transformer_encoder.layers.0.linear2.bias", "backbone.transformer_encoder.layers.0.norm1.weight", "backbone.transformer_encoder.layers.0.norm1.bias", "backbone.transformer_encoder.layers.0.norm2.weight", "backbone.transformer_encoder.layers.0.norm2.bias", "backbone.transformer_encoder.layers.1.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.1.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.1.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.1.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.1.linear1.weight", "backbone.transformer_encoder.layers.1.linear1.bias", "backbone.transformer_encoder.layers.1.linear2.weight", "backbone.transformer_encoder.layers.1.linear2.bias", "backbone.transformer_encoder.layers.1.norm1.weight", "backbone.transformer_encoder.layers.1.norm1.bias", "backbone.transformer_encoder.layers.1.norm2.weight", "backbone.transformer_encoder.layers.1.norm2.bias", "backbone.transformer_encoder.layers.2.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.2.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.2.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.2.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.2.linear1.weight", "backbone.transformer_encoder.layers.2.linear1.bias", "backbone.transformer_encoder.layers.2.linear2.weight", "backbone.transformer_encoder.layers.2.linear2.bias", "backbone.transformer_encoder.layers.2.norm1.weight", "backbone.transformer_encoder.layers.2.norm1.bias", "backbone.transformer_encoder.layers.2.norm2.weight", "backbone.transformer_encoder.layers.2.norm2.bias", "backbone.transformer_encoder.layers.3.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.3.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.3.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.3.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.3.linear1.weight", "backbone.transformer_encoder.layers.3.linear1.bias", "backbone.transformer_encoder.layers.3.linear2.weight", "backbone.transformer_encoder.layers.3.linear2.bias", "backbone.transformer_encoder.layers.3.norm1.weight", "backbone.transformer_encoder.layers.3.norm1.bias", "backbone.transformer_encoder.layers.3.norm2.weight", "backbone.transformer_encoder.layers.3.norm2.bias", "backbone.transformer_encoder.layers.4.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.4.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.4.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.4.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.4.linear1.weight", "backbone.transformer_encoder.layers.4.linear1.bias", "backbone.transformer_encoder.layers.4.linear2.weight", "backbone.transformer_encoder.layers.4.linear2.bias", "backbone.transformer_encoder.layers.4.norm1.weight", "backbone.transformer_encoder.layers.4.norm1.bias", "backbone.transformer_encoder.layers.4.norm2.weight", "backbone.transformer_encoder.layers.4.norm2.bias", "backbone.transformer_encoder.layers.5.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.5.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.5.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.5.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.5.linear1.weight", "backbone.transformer_encoder.layers.5.linear1.bias", "backbone.transformer_encoder.layers.5.linear2.weight", "backbone.transformer_encoder.layers.5.linear2.bias", "backbone.transformer_encoder.layers.5.norm1.weight", "backbone.transformer_encoder.layers.5.norm1.bias", "backbone.transformer_encoder.layers.5.norm2.weight", "backbone.transformer_encoder.layers.5.norm2.bias", "backbone.transformer_encoder.layers.6.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.6.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.6.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.6.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.6.linear1.weight", "backbone.transformer_encoder.layers.6.linear1.bias", "backbone.transformer_encoder.layers.6.linear2.weight", "backbone.transformer_encoder.layers.6.linear2.bias", "backbone.transformer_encoder.layers.6.norm1.weight", "backbone.transformer_encoder.layers.6.norm1.bias", "backbone.transformer_encoder.layers.6.norm2.weight", "backbone.transformer_encoder.layers.6.norm2.bias", "backbone.transformer_encoder.layers.7.self_attn.in_proj_weight", "backbone.transformer_encoder.layers.7.self_attn.in_proj_bias", "backbone.transformer_encoder.layers.7.self_attn.out_proj.weight", "backbone.transformer_encoder.layers.7.self_attn.out_proj.bias", "backbone.transformer_encoder.layers.7.linear1.weight", "backbone.transformer_encoder.layers.7.linear1.bias", "backbone.transformer_encoder.layers.7.linear2.weight", "backbone.transformer_encoder.layers.7.linear2.bias", "backbone.transformer_encoder.layers.7.norm1.weight", "backbone.transformer_encoder.layers.7.norm1.bias", "backbone.transformer_encoder.layers.7.norm2.weight", "backbone.transformer_encoder.layers.7.norm2.bias", "backbone.classifier.0.weight", "backbone.classifier.0.bias", "backbone.classifier.2.weight", "backbone.classifier.2.bias", "backbone.classifier.5.weight", "backbone.classifier.5.bias", "backbone.auxiliary_head.0.weight", "backbone.auxiliary_head.0.bias", "backbone.auxiliary_head.2.weight", "backbone.auxiliary_head.2.bias", "backbone.auxiliary_head.5.weight", "backbone.auxiliary_head.5.bias", "extra_classifier.0.weight", "extra_classifier.0.bias", "extra_classifier.1.weight", "extra_classifier.1.bias", "extra_classifier.4.weight", "extra_classifier.4.bias", "extra_classifier.7.weight", "extra_classifier.7.bias". 
2025-08-12 23:03:48,757 - INFO - 
============================================================
2025-08-12 23:03:48,757 - INFO - Testing: V14 Progressive
2025-08-12 23:03:48,757 - INFO - ============================================================
2025-08-12 23:03:48,906 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-12 23:03:48,920 - ERROR - Error testing V14 Progressive: Error(s) in loading state_dict for SequentialMAMBAFORMER_V2:
	Missing key(s) in state_dict: "feature_extractor.conv_layers.0.weight", "feature_extractor.conv_layers.0.bias", "feature_extractor.conv_layers.1.weight", "feature_extractor.conv_layers.1.bias", "feature_extractor.conv_layers.1.running_mean", "feature_extractor.conv_layers.1.running_var", "feature_extractor.conv_layers.5.weight", "feature_extractor.conv_layers.5.bias", "feature_extractor.conv_layers.6.weight", "feature_extractor.conv_layers.6.bias", "feature_extractor.conv_layers.6.running_mean", "feature_extractor.conv_layers.6.running_var", "feature_extractor.conv_layers.10.weight", "feature_extractor.conv_layers.10.bias", "feature_extractor.conv_layers.11.weight", "feature_extractor.conv_layers.11.bias", "feature_extractor.conv_layers.11.running_mean", "feature_extractor.conv_layers.11.running_var", "pos_encoder.pe", "transformer_encoder.layers.0.self_attn.in_proj_weight", "transformer_encoder.layers.0.self_attn.in_proj_bias", "transformer_encoder.layers.0.self_attn.out_proj.weight", "transformer_encoder.layers.0.self_attn.out_proj.bias", "transformer_encoder.layers.0.linear1.weight", "transformer_encoder.layers.0.linear1.bias", "transformer_encoder.layers.0.linear2.weight", "transformer_encoder.layers.0.linear2.bias", "transformer_encoder.layers.0.norm1.weight", "transformer_encoder.layers.0.norm1.bias", "transformer_encoder.layers.0.norm2.weight", "transformer_encoder.layers.0.norm2.bias", "transformer_encoder.layers.1.self_attn.in_proj_weight", "transformer_encoder.layers.1.self_attn.in_proj_bias", "transformer_encoder.layers.1.self_attn.out_proj.weight", "transformer_encoder.layers.1.self_attn.out_proj.bias", "transformer_encoder.layers.1.linear1.weight", "transformer_encoder.layers.1.linear1.bias", "transformer_encoder.layers.1.linear2.weight", "transformer_encoder.layers.1.linear2.bias", "transformer_encoder.layers.1.norm1.weight", "transformer_encoder.layers.1.norm1.bias", "transformer_encoder.layers.1.norm2.weight", "transformer_encoder.layers.1.norm2.bias", "transformer_encoder.layers.2.self_attn.in_proj_weight", "transformer_encoder.layers.2.self_attn.in_proj_bias", "transformer_encoder.layers.2.self_attn.out_proj.weight", "transformer_encoder.layers.2.self_attn.out_proj.bias", "transformer_encoder.layers.2.linear1.weight", "transformer_encoder.layers.2.linear1.bias", "transformer_encoder.layers.2.linear2.weight", "transformer_encoder.layers.2.linear2.bias", "transformer_encoder.layers.2.norm1.weight", "transformer_encoder.layers.2.norm1.bias", "transformer_encoder.layers.2.norm2.weight", "transformer_encoder.layers.2.norm2.bias", "transformer_encoder.layers.3.self_attn.in_proj_weight", "transformer_encoder.layers.3.self_attn.in_proj_bias", "transformer_encoder.layers.3.self_attn.out_proj.weight", "transformer_encoder.layers.3.self_attn.out_proj.bias", "transformer_encoder.layers.3.linear1.weight", "transformer_encoder.layers.3.linear1.bias", "transformer_encoder.layers.3.linear2.weight", "transformer_encoder.layers.3.linear2.bias", "transformer_encoder.layers.3.norm1.weight", "transformer_encoder.layers.3.norm1.bias", "transformer_encoder.layers.3.norm2.weight", "transformer_encoder.layers.3.norm2.bias", "transformer_encoder.layers.4.self_attn.in_proj_weight", "transformer_encoder.layers.4.self_attn.in_proj_bias", "transformer_encoder.layers.4.self_attn.out_proj.weight", "transformer_encoder.layers.4.self_attn.out_proj.bias", "transformer_encoder.layers.4.linear1.weight", "transformer_encoder.layers.4.linear1.bias", "transformer_encoder.layers.4.linear2.weight", "transformer_encoder.layers.4.linear2.bias", "transformer_encoder.layers.4.norm1.weight", "transformer_encoder.layers.4.norm1.bias", "transformer_encoder.layers.4.norm2.weight", "transformer_encoder.layers.4.norm2.bias", "transformer_encoder.layers.5.self_attn.in_proj_weight", "transformer_encoder.layers.5.self_attn.in_proj_bias", "transformer_encoder.layers.5.self_attn.out_proj.weight", "transformer_encoder.layers.5.self_attn.out_proj.bias", "transformer_encoder.layers.5.linear1.weight", "transformer_encoder.layers.5.linear1.bias", "transformer_encoder.layers.5.linear2.weight", "transformer_encoder.layers.5.linear2.bias", "transformer_encoder.layers.5.norm1.weight", "transformer_encoder.layers.5.norm1.bias", "transformer_encoder.layers.5.norm2.weight", "transformer_encoder.layers.5.norm2.bias", "classifier.0.weight", "classifier.0.bias", "classifier.2.weight", "classifier.2.bias", "classifier.5.weight", "classifier.5.bias", "auxiliary_head.0.weight", "auxiliary_head.0.bias", "auxiliary_head.2.weight", "auxiliary_head.2.bias", "auxiliary_head.5.weight", "auxiliary_head.5.bias". 
	Unexpected key(s) in state_dict: "encoder.feature_extractor.conv_layers.0.weight", "encoder.feature_extractor.conv_layers.0.bias", "encoder.feature_extractor.conv_layers.1.weight", "encoder.feature_extractor.conv_layers.1.bias", "encoder.feature_extractor.conv_layers.1.running_mean", "encoder.feature_extractor.conv_layers.1.running_var", "encoder.feature_extractor.conv_layers.1.num_batches_tracked", "encoder.feature_extractor.conv_layers.5.weight", "encoder.feature_extractor.conv_layers.5.bias", "encoder.feature_extractor.conv_layers.6.weight", "encoder.feature_extractor.conv_layers.6.bias", "encoder.feature_extractor.conv_layers.6.running_mean", "encoder.feature_extractor.conv_layers.6.running_var", "encoder.feature_extractor.conv_layers.6.num_batches_tracked", "encoder.feature_extractor.conv_layers.10.weight", "encoder.feature_extractor.conv_layers.10.bias", "encoder.feature_extractor.conv_layers.11.weight", "encoder.feature_extractor.conv_layers.11.bias", "encoder.feature_extractor.conv_layers.11.running_mean", "encoder.feature_extractor.conv_layers.11.running_var", "encoder.feature_extractor.conv_layers.11.num_batches_tracked", "encoder.pos_encoder.pe", "encoder.transformer_encoder.layers.0.self_attn.in_proj_weight", "encoder.transformer_encoder.layers.0.self_attn.in_proj_bias", "encoder.transformer_encoder.layers.0.self_attn.out_proj.weight", "encoder.transformer_encoder.layers.0.self_attn.out_proj.bias", "encoder.transformer_encoder.layers.0.linear1.weight", "encoder.transformer_encoder.layers.0.linear1.bias", "encoder.transformer_encoder.layers.0.linear2.weight", "encoder.transformer_encoder.layers.0.linear2.bias", "encoder.transformer_encoder.layers.0.norm1.weight", "encoder.transformer_encoder.layers.0.norm1.bias", "encoder.transformer_encoder.layers.0.norm2.weight", "encoder.transformer_encoder.layers.0.norm2.bias", "encoder.transformer_encoder.layers.1.self_attn.in_proj_weight", "encoder.transformer_encoder.layers.1.self_attn.in_proj_bias", "encoder.transformer_encoder.layers.1.self_attn.out_proj.weight", "encoder.transformer_encoder.layers.1.self_attn.out_proj.bias", "encoder.transformer_encoder.layers.1.linear1.weight", "encoder.transformer_encoder.layers.1.linear1.bias", "encoder.transformer_encoder.layers.1.linear2.weight", "encoder.transformer_encoder.layers.1.linear2.bias", "encoder.transformer_encoder.layers.1.norm1.weight", "encoder.transformer_encoder.layers.1.norm1.bias", "encoder.transformer_encoder.layers.1.norm2.weight", "encoder.transformer_encoder.layers.1.norm2.bias", "encoder.transformer_encoder.layers.2.self_attn.in_proj_weight", "encoder.transformer_encoder.layers.2.self_attn.in_proj_bias", "encoder.transformer_encoder.layers.2.self_attn.out_proj.weight", "encoder.transformer_encoder.layers.2.self_attn.out_proj.bias", "encoder.transformer_encoder.layers.2.linear1.weight", "encoder.transformer_encoder.layers.2.linear1.bias", "encoder.transformer_encoder.layers.2.linear2.weight", "encoder.transformer_encoder.layers.2.linear2.bias", "encoder.transformer_encoder.layers.2.norm1.weight", "encoder.transformer_encoder.layers.2.norm1.bias", "encoder.transformer_encoder.layers.2.norm2.weight", "encoder.transformer_encoder.layers.2.norm2.bias", "encoder.transformer_encoder.layers.3.self_attn.in_proj_weight", "encoder.transformer_encoder.layers.3.self_attn.in_proj_bias", "encoder.transformer_encoder.layers.3.self_attn.out_proj.weight", "encoder.transformer_encoder.layers.3.self_attn.out_proj.bias", "encoder.transformer_encoder.layers.3.linear1.weight", "encoder.transformer_encoder.layers.3.linear1.bias", "encoder.transformer_encoder.layers.3.linear2.weight", "encoder.transformer_encoder.layers.3.linear2.bias", "encoder.transformer_encoder.layers.3.norm1.weight", "encoder.transformer_encoder.layers.3.norm1.bias", "encoder.transformer_encoder.layers.3.norm2.weight", "encoder.transformer_encoder.layers.3.norm2.bias", "encoder.transformer_encoder.layers.4.self_attn.in_proj_weight", "encoder.transformer_encoder.layers.4.self_attn.in_proj_bias", "encoder.transformer_encoder.layers.4.self_attn.out_proj.weight", "encoder.transformer_encoder.layers.4.self_attn.out_proj.bias", "encoder.transformer_encoder.layers.4.linear1.weight", "encoder.transformer_encoder.layers.4.linear1.bias", "encoder.transformer_encoder.layers.4.linear2.weight", "encoder.transformer_encoder.layers.4.linear2.bias", "encoder.transformer_encoder.layers.4.norm1.weight", "encoder.transformer_encoder.layers.4.norm1.bias", "encoder.transformer_encoder.layers.4.norm2.weight", "encoder.transformer_encoder.layers.4.norm2.bias", "encoder.transformer_encoder.layers.5.self_attn.in_proj_weight", "encoder.transformer_encoder.layers.5.self_attn.in_proj_bias", "encoder.transformer_encoder.layers.5.self_attn.out_proj.weight", "encoder.transformer_encoder.layers.5.self_attn.out_proj.bias", "encoder.transformer_encoder.layers.5.linear1.weight", "encoder.transformer_encoder.layers.5.linear1.bias", "encoder.transformer_encoder.layers.5.linear2.weight", "encoder.transformer_encoder.layers.5.linear2.bias", "encoder.transformer_encoder.layers.5.norm1.weight", "encoder.transformer_encoder.layers.5.norm1.bias", "encoder.transformer_encoder.layers.5.norm2.weight", "encoder.transformer_encoder.layers.5.norm2.bias", "encoder.auxiliary_head.0.weight", "encoder.auxiliary_head.0.bias", "encoder.auxiliary_head.2.weight", "encoder.auxiliary_head.2.bias", "encoder.auxiliary_head.5.weight", "encoder.auxiliary_head.5.bias", "coarse_classifier.0.weight", "coarse_classifier.0.bias", "coarse_classifier.3.weight", "coarse_classifier.3.bias", "fine_classifier.0.weight", "fine_classifier.0.bias", "fine_classifier.3.weight", "fine_classifier.3.bias", "fine_classifier.6.weight", "fine_classifier.6.bias". 
