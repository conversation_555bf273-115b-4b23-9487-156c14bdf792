python ~/eeg/Cross-Modal-Transformer/mambaformer/training/pseudo_labeling_v21.py
2025-08-14 17:10:47,455 - INFO - ================================================================================
2025-08-14 17:10:47,455 - INFO - 🚀 V21 PSEUDO-LABELING SEMI-SUPERVISED LEARNING
2025-08-14 17:10:47,455 - INFO - ================================================================================
2025-08-14 17:10:47,455 - INFO - 🎯 Targets: Accuracy ≥90%, Macro F1 ≥82%, Kappa ≥0.82
2025-08-14 17:10:47,487 - INFO - Device: cuda
2025-08-14 17:10:47,487 - INFO - 
📋 Configuration:
2025-08-14 17:10:47,487 - INFO -   d_model: 384
2025-08-14 17:10:47,487 - INFO -   n_heads: 24
2025-08-14 17:10:47,487 - INFO -   n_layers: 7
2025-08-14 17:10:47,487 - INFO -   dropout: 0.2
2025-08-14 17:10:47,487 - INFO -   seq_len: 7
2025-08-14 17:10:47,487 - INFO -   batch_size: 20
2025-08-14 17:10:47,487 - INFO -   learning_rate: 4e-05
2025-08-14 17:10:47,487 - INFO -   num_epochs: 50
2025-08-14 17:10:47,487 - INFO -   patience: 20
2025-08-14 17:10:47,487 - INFO -   gradient_clip: 0.5
2025-08-14 17:10:47,487 - INFO -   weight_decay: 0.02
2025-08-14 17:10:47,487 - INFO -   pseudo_threshold: 0.9
2025-08-14 17:10:47,488 - INFO -   pseudo_update_epochs: 5
2025-08-14 17:10:47,488 - INFO -   warmup_epochs: 3
2025-08-14 17:10:47,488 - INFO - 
📂 Data Split:
2025-08-14 17:10:47,488 - INFO -   Train: 25 files
2025-08-14 17:10:47,488 - INFO -   Val: 6 files
2025-08-14 17:10:47,488 - INFO -   Test: 8 files
2025-08-14 17:10:47,488 - INFO -   Unlabeled (for pseudo): 6 files
2025-08-14 17:10:48,757 - INFO - 从 25 个文件加载了 26416 个epochs, 创建了 26266 个序列
2025-08-14 17:10:48,758 - INFO - 创建序列数据集: 26266个序列, 序列长度=7, 通道数=3, 总epochs=26416
2025-08-14 17:10:49,017 - INFO - 从 6 个文件加载了 6146 个epochs, 创建了 6110 个序列
2025-08-14 17:10:49,017 - INFO - 创建序列数据集: 6110个序列, 序列长度=7, 通道数=3, 总epochs=6146
2025-08-14 17:10:49,419 - INFO - 从 8 个文件加载了 9746 个epochs, 创建了 9698 个序列
2025-08-14 17:10:49,419 - INFO - 创建序列数据集: 9698个序列, 序列长度=7, 通道数=3, 总epochs=9746
2025-08-14 17:10:49,678 - INFO - 从 6 个文件加载了 6146 个epochs, 创建了 6110 个序列
2025-08-14 17:10:49,678 - INFO - 创建序列数据集: 6110个序列, 序列长度=7, 通道数=3, 总epochs=6146
2025-08-14 17:10:49,679 - INFO - 
📦 Loading pre-trained model for initialization...
2025-08-14 17:10:49,806 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=12,808,295, d_model=384, n_heads=24, n_layers=7
2025-08-14 17:10:50,018 - INFO - ⚠️ No pre-trained model found, training from scratch
2025-08-14 17:10:50,018 - INFO - 
Model Parameters: 12,808,295
2025-08-14 17:10:50,019 - INFO - Model Size: 48.86 MB
2025-08-14 17:10:51,200 - INFO - 
🏋️ Starting Self-Training...
2025-08-14 17:10:51,200 - INFO - ================================================================================
2025-08-14 17:10:51,200 - INFO - Warmup LR: 1.33e-05
Epoch 1/50: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████| 1313/1313 [01:10<00:00, 18.71it/s, loss=1.0683]
2025-08-14 17:12:03,943 - INFO -                                                                                                                                          
Epoch 1/50:
2025-08-14 17:12:03,943 - INFO -   Loss: 2.2883
2025-08-14 17:12:03,943 - INFO -   Val Acc: 0.8466 (84.66%)
2025-08-14 17:12:03,943 - INFO -   Val F1: 0.8142 (81.42%)
2025-08-14 17:12:03,944 - INFO -   Val Kappa: 0.7931
2025-08-14 17:12:03,944 - INFO -   N1 F1: 0.5510 | REM F1: 0.8330
2025-08-14 17:12:03,944 - INFO -   LR: 1.33e-05
2025-08-14 17:12:03,944 - INFO -   Time: 72.7s
2025-08-14 17:12:04,351 - INFO -   💾 Saved best model
2025-08-14 17:12:04,351 - INFO - Warmup LR: 2.67e-05
Epoch 2/50: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████| 1313/1313 [01:09<00:00, 18.80it/s, loss=1.1499]
2025-08-14 17:13:16,746 - INFO -                                                                                                                                          
Epoch 2/50:
2025-08-14 17:13:16,746 - INFO -   Loss: 1.6186
2025-08-14 17:13:16,746 - INFO -   Val Acc: 0.8759 (87.59%)
2025-08-14 17:13:16,747 - INFO -   Val F1: 0.8422 (84.22%)
2025-08-14 17:13:16,747 - INFO -   Val Kappa: 0.8300
2025-08-14 17:13:16,747 - INFO -   N1 F1: 0.5890 | REM F1: 0.8787
2025-08-14 17:13:16,747 - INFO -   LR: 2.67e-05
2025-08-14 17:13:16,747 - INFO -   Time: 72.4s
2025-08-14 17:13:16,747 - INFO -   🎯 Targets met: F1, KAPPA
2025-08-14 17:13:17,681 - INFO -   💾 Saved best model
2025-08-14 17:13:17,682 - INFO - Warmup LR: 4.00e-05
Epoch 3/50: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████| 1313/1313 [01:08<00:00, 19.23it/s, loss=2.5943]
2025-08-14 17:14:28,563 - INFO -                                                                                                                                          
Epoch 3/50:
2025-08-14 17:14:28,564 - INFO -   Loss: 1.4013
2025-08-14 17:14:28,564 - INFO -   Val Acc: 0.8487 (84.87%)
2025-08-14 17:14:28,564 - INFO -   Val F1: 0.8191 (81.91%)
2025-08-14 17:14:28,564 - INFO -   Val Kappa: 0.7949
2025-08-14 17:14:28,564 - INFO -   N1 F1: 0.5640 | REM F1: 0.8223
2025-08-14 17:14:28,564 - INFO -   LR: 4.00e-05
2025-08-14 17:14:28,564 - INFO -   Time: 70.9s
Epoch 4/50: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████| 1313/1313 [01:09<00:00, 18.77it/s, loss=1.3193]
2025-08-14 17:15:41,060 - INFO -                                                                                                                                          
Epoch 4/50:
2025-08-14 17:15:41,060 - INFO -   Loss: 1.2368
2025-08-14 17:15:41,060 - INFO -   Val Acc: 0.8775 (87.75%)
2025-08-14 17:15:41,060 - INFO -   Val F1: 0.8473 (84.73%)
2025-08-14 17:15:41,060 - INFO -   Val Kappa: 0.8332
2025-08-14 17:15:41,060 - INFO -   N1 F1: 0.6169 | REM F1: 0.8776
2025-08-14 17:15:41,060 - INFO -   LR: 3.90e-05
2025-08-14 17:15:41,060 - INFO -   Time: 72.5s
2025-08-14 17:15:41,061 - INFO -   🎯 Targets met: F1, KAPPA
2025-08-14 17:15:42,005 - INFO -   💾 Saved best model
Epoch 5/50: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████| 1313/1313 [01:09<00:00, 18.91it/s, loss=0.6470]
2025-08-14 17:16:53,974 - INFO -                                                                                                                                          
Epoch 5/50:
2025-08-14 17:16:53,975 - INFO -   Loss: 1.1513
2025-08-14 17:16:53,975 - INFO -   Val Acc: 0.8516 (85.16%)
2025-08-14 17:16:53,975 - INFO -   Val F1: 0.8183 (81.83%)
2025-08-14 17:16:53,975 - INFO -   Val Kappa: 0.7974
2025-08-14 17:16:53,975 - INFO -   N1 F1: 0.5778 | REM F1: 0.7766
2025-08-14 17:16:53,975 - INFO -   LR: 3.63e-05
2025-08-14 17:16:53,975 - INFO -   Time: 72.0s
2025-08-14 17:16:53,975 - INFO - 
🔄 Updating pseudo-labels...
2025-08-14 17:16:53,976 - INFO - Generating pseudo-labels...
Pseudo-labeling: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 306/306 [00:04<00:00, 72.50it/s]
2025-08-14 17:16:58,197 - INFO - Generated 3655/6110 high-confidence pseudo-labels (59.8%)
2025-08-14 17:16:58,241 - INFO - Created pseudo-label dataset with 3655 high-confidence samples
2025-08-14 17:16:58,242 - INFO - Training with 29921 samples (original + pseudo)
Epoch 6/50:   0%|                                                                                                           | 1/1496 [00:00<09:00,  2.77it/s, loss=0.6780]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/pseudo_labeling_v21.py", line 673, in <module>
    results = train_with_pseudo_labeling()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/pseudo_labeling_v21.py", line 384, in train_with_pseudo_labeling
    for batch_idx, (data, labels) in enumerate(pbar):
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/tqdm/std.py", line 1181, in __iter__
    for obj in iterable:
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
    data = self._next_data()
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/dataloader.py", line 1515, in _next_data
    return self._process_data(data, worker_id)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/dataloader.py", line 1550, in _process_data
    data.reraise()
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/_utils.py", line 750, in reraise
    raise exception
AttributeError: Caught AttributeError in DataLoader worker process 1.
Original Traceback (most recent call last):
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/_utils/worker.py", line 349, in _worker_loop
    data = fetcher.fetch(index)  # type: ignore[possibly-undefined]
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
    return self.collate_fn(data)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/_utils/collate.py", line 398, in default_collate
    return collate(batch, collate_fn_map=default_collate_fn_map)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/_utils/collate.py", line 211, in collate
    return [
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/_utils/collate.py", line 212, in <listcomp>
    collate(samples, collate_fn_map=collate_fn_map)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/_utils/collate.py", line 155, in collate
    return collate_fn_map[elem_type](batch, collate_fn_map=collate_fn_map)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/_utils/collate.py", line 269, in collate_tensor_fn
    numel = sum(x.numel() for x in batch)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/utils/data/_utils/collate.py", line 269, in <genexpr>
    numel = sum(x.numel() for x in batch)
AttributeError: 'numpy.ndarray' object has no attribute 'numel'