# V21 训练错误分析与修复报告

## 错误摘要

**错误类型**: `AttributeError: 'numpy.ndarray' object has no attribute 'numel'`  
**发生时间**: 2025-08-14 17:16:58  
**错误位置**: 第6个epoch开始时，在更新伪标签后的DataLoader中

## 问题分析

### 1. 错误触发流程
1. 前5个epoch正常训练，验证准确率达到85.16%
2. 第5个epoch结束后触发伪标签更新（每5个epoch更新一次）
3. 成功生成3655个高置信度伪标签（59.8%的无标签数据）
4. 创建包含29921个样本的新数据集（原始26266 + 伪标签3655）
5. 第6个epoch开始时，DataLoader的worker进程在处理数据时崩溃

### 2. 根本原因

`PseudoLabelDataset`类的`__getitem__`方法返回了混合类型的数据：
- **数据(data)**：PyTorch张量（从`data[i].cpu()`而来）
- **标签(labels)**：NumPy数组（从`np.argmax(pseudo_labels[i], axis=-1)`而来）

PyTorch的DataLoader期望所有数据都是张量类型，以便能够调用`.numel()`方法进行批处理。

### 3. 代码问题定位

文件：`pseudo_labeling_v21.py`

**问题代码（原始）**：
```python
# 第52-53行
def __getitem__(self, idx):
    return self.data[idx], self.labels[idx]
```

**生成伪标签时的数据类型**：
```python
# 第126-127行
pseudo_data.append(data[i].cpu())  # PyTorch tensor
pseudo_probs.append(probs[i].cpu().numpy())  # NumPy array
```

**创建标签时**：
```python
# 第45行
self.labels.append(np.argmax(pseudo_labels[i], axis=-1))  # NumPy array
```

## 修复方案

### 已实施的修复

修改`PseudoLabelDataset`的`__getitem__`方法，确保始终返回PyTorch张量：

```python
def __getitem__(self, idx):
    # 确保返回PyTorch张量
    data = self.data[idx]
    label = self.labels[idx]
    
    # 转换为正确的tensor类型
    if not isinstance(data, torch.Tensor):
        data = torch.FloatTensor(data)
    if not isinstance(label, torch.Tensor):
        label = torch.LongTensor(label)
    
    return data, label
```

### 修复验证

创建了测试脚本`test_pseudo_fix.py`，验证：
- ✅ 单个数据项返回正确的张量类型
- ✅ DataLoader（单进程）正常工作
- ✅ DataLoader（多进程）正常工作

## 额外改进建议

### 1. 数据类型一致性
建议在`generate_pseudo_labels`函数中统一数据格式：
```python
def generate_pseudo_labels(model, unlabeled_loader, device, threshold=0.9):
    # ...
    if avg_conf >= threshold:
        pseudo_data.append(data[i].cpu())  # 保持为tensor
        pseudo_probs.append(probs[i].cpu())  # 保持为tensor，不转numpy
```

### 2. 增加数据验证
在创建混合数据集前添加类型检查：
```python
# 在train_with_pseudo_labeling函数中
if epoch > 0 and (epoch + 1) % config['pseudo_update_epochs'] == 0:
    # 验证数据类型
    assert all(isinstance(d, torch.Tensor) for d in pseudo_data), "Data must be tensors"
    # ...
```

### 3. 改进日志记录
添加更详细的伪标签生成日志：
```python
logging.info(f"Pseudo-labeling statistics:")
logging.info(f"  - Total unlabeled samples: {total_count}")
logging.info(f"  - High confidence samples: {high_conf_count} ({high_conf_count/total_count*100:.1f}%)")
logging.info(f"  - Average confidence: {avg_confidence:.3f}")
logging.info(f"  - Data types: data={type(pseudo_data[0])}, labels={type(pseudo_probs[0])}")
```

### 4. 性能优化建议

根据训练日志观察：
- 前5个epoch表现良好，Val F1达到84.73%（第4个epoch）
- 建议调整伪标签阈值（当前0.9可能过高）
- 考虑渐进式降低阈值：开始0.95，逐步降至0.85

### 5. 鲁棒性增强
```python
class PseudoLabelDataset(Dataset):
    def __init__(self, data, pseudo_labels, confidence_threshold=0.9):
        # 添加输入验证
        assert len(data) == len(pseudo_labels), "Data and labels must have same length"
        
        self.data = []
        self.labels = []
        
        for i in range(len(data)):
            max_conf = np.max(pseudo_labels[i])
            if max_conf >= confidence_threshold:
                # 统一转换为tensor
                if isinstance(data[i], np.ndarray):
                    self.data.append(torch.from_numpy(data[i]).float())
                elif isinstance(data[i], torch.Tensor):
                    self.data.append(data[i].float())
                else:
                    raise TypeError(f"Unsupported data type: {type(data[i])}")
                
                # 处理标签
                if isinstance(pseudo_labels[i], np.ndarray):
                    label = torch.from_numpy(np.argmax(pseudo_labels[i], axis=-1)).long()
                else:
                    label = torch.argmax(pseudo_labels[i], dim=-1).long()
                self.labels.append(label)
```

## 运行建议

1. **立即重新运行训练**：修复已经应用，可以安全地重新启动训练
2. **监控第6个epoch**：特别关注伪标签更新后的训练稳定性
3. **保存检查点**：在每个伪标签更新前保存模型，以便回滚
4. **调整超参数**：如果问题持续，考虑降低`pseudo_threshold`或增加`pseudo_update_epochs`

## 总结

问题已成功定位并修复。错误源于数据类型不一致，修复确保了`PseudoLabelDataset`始终返回PyTorch张量。建议实施额外的改进措施以提高训练的鲁棒性和性能。