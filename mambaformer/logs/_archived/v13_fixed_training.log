WARNING:root:hmmlearn not installed. HMM post-processing will be disabled.

Epoch 1:   0%|          | 0/868 [00:00<?, ?it/s]
Epoch 1:   0%|          | 0/868 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_FIXED.py", line 403, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_FIXED.py", line 399, in main
    train_v13_fixed(config, device)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_FIXED.py", line 285, in train_v13_fixed
    train_loss, train_acc, train_f1, train_kappa = train_epoch_v13(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_FIXED.py", line 84, in train_epoch_v13
    main_loss = criterion(outputs.view(-1, 5), labels.view(-1))
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/models/sequential_mambaformer_v2.py", line 214, in forward
    focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
RuntimeError: The size of tensor a (5) must match the size of tensor b (160) at non-singleton dimension 0
