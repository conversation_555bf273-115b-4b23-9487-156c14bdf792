WARNING:root:hmmlearn not installed. HMM post-processing will be disabled.
WARNING:root:加载文件 /media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4132E0.npz 失败: [Errno 2] No such file or directory: '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4132E0.npz'

Epoch 1 - Train:   0%|          | 0/124 [00:00<?, ?it/s]
Epoch 1 - Train:   0%|          | 0/124 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py", line 564, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py", line 483, in main
    test_metrics, pp_metrics = train_v13(config, device)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py", line 356, in train_v13
    train_loss, train_acc, train_f1, train_kappa = train_epoch_v13(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py", line 148, in train_epoch_v13
    main_loss = criterion(main_output, labels_flat)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py", line 69, in forward
    ce_loss = nn.functional.cross_entropy(input, target, reduction='none')
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/functional.py", line 3494, in cross_entropy
    return torch._C._nn.cross_entropy_loss(
ValueError: Expected input batch_size (32) to match target batch_size (160).
