WARNING:root:hmmlearn not installed. HMM post-processing will be disabled.
🚀 Final Target Achievement Script
================================================================================
🎯 Targets: ACC=87%, Kappa=0.8, MF1=80%
Device: cuda
Test dataset size: 1168 sequences
Loading V7...
✅ V7 loaded
Loading V8...
✅ V8 loaded
Loading V13...
✅ V13 loaded
Loading V14...
✅ V14 loaded

Evaluating V7...

  0%|          | 0/37 [00:00<?, ?it/s]
  3%|▎         | 1/37 [00:00<00:16,  2.21it/s]
 54%|█████▍    | 20/37 [00:00<00:00, 47.26it/s]
100%|██████████| 37/37 [00:00<00:00, 77.20it/s]
100%|██████████| 37/37 [00:00<00:00, 52.89it/s]
V7: ACC=0.8533, F1=0.6922, Kappa=0.7919
  REM F1: 0.1905, Wake F1: 0.9384

Evaluating V8...

  0%|          | 0/37 [00:00<?, ?it/s]
  3%|▎         | 1/37 [00:00<00:05,  6.47it/s]
 41%|████      | 15/37 [00:00<00:00, 70.51it/s]
 95%|█████████▍| 35/37 [00:00<00:00, 121.43it/s]
100%|██████████| 37/37 [00:00<00:00, 91.27it/s] 
V8: ACC=0.8458, F1=0.7055, Kappa=0.7837
  REM F1: 0.2500, Wake F1: 0.8941

Evaluating V13...

  0%|          | 0/37 [00:00<?, ?it/s]
  3%|▎         | 1/37 [00:00<00:04,  7.57it/s]
 43%|████▎     | 16/37 [00:00<00:00, 80.66it/s]
 86%|████████▋ | 32/37 [00:00<00:00, 113.30it/s]
100%|██████████| 37/37 [00:00<00:00, 89.27it/s] 
V13: ACC=0.7958, F1=0.5841, Kappa=0.7050
  REM F1: 0.0000, Wake F1: 0.9654

Evaluating V14...

  0%|          | 0/37 [00:00<?, ?it/s]
  3%|▎         | 1/37 [00:00<00:05,  6.98it/s]
 38%|███▊      | 14/37 [00:00<00:00, 67.50it/s]
 81%|████████  | 30/37 [00:00<00:00, 103.28it/s]
100%|██████████| 37/37 [00:00<00:00, 84.87it/s] 
V14: ACC=0.8358, F1=0.6207, Kappa=0.7639
  REM F1: 0.0000, Wake F1: 0.9489

🔍 Optimizing ensemble weights...

🎉 TARGET ACHIEVED with weights: {'V7': 0.14285714285714285, 'V8': 0.0, 'V13': 0.0, 'V14': 0.8571428571428572}
ACC=0.8733, F1=0.8340, Kappa=0.8162

Per-class F1 scores:
  REM: 0.9540
  N1: 0.6038
  N2: 0.8354
  N3: 0.8679
  Wake: 0.9091
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/achieve_final_target.py", line 350, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/achieve_final_target.py", line 344, in main
    json.dump(results, f, indent=2)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/__init__.py", line 179, in dump
    for chunk in iterable:
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/encoder.py", line 431, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/encoder.py", line 438, in _iterencode
    o = _default(o)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/json/encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type bool_ is not JSON serializable
