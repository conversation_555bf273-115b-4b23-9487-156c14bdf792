WARNING:root:hmmlearn not installed. HMM post-processing will be disabled.
WARNING:root:加载文件 /media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4132E0.npz 失败: [Errno 2] No such file or directory: '/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/SC4132E0.npz'

Epoch 1 - Train:   0%|          | 0/124 [00:00<?, ?it/s]
Epoch 1 - Train:   0%|          | 0/124 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py", line 561, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py", line 480, in main
    test_metrics, pp_metrics = train_v13(config, device)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py", line 353, in train_v13
    train_loss, train_acc, train_f1, train_kappa = train_epoch_v13(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py", line 145, in train_epoch_v13
    main_loss = criterion(main_output, labels)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/train_v13_simple.py", line 77, in forward
    loss = alpha_t * loss
RuntimeError: The size of tensor a (160) must match the size of tensor b (5) at non-singleton dimension 1
