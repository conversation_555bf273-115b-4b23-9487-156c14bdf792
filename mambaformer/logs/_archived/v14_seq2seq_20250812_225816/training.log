2025-08-12 22:58:16,884 - INFO - ================================================================================
2025-08-12 22:58:16,884 - INFO - 🚀 V14 Sequence-to-Sequence Training
2025-08-12 22:58:16,884 - INFO - ================================================================================
2025-08-12 22:58:16,884 - INFO - Configuration: {
  "d_model": 256,
  "n_heads": 16,
  "n_layers": 6,
  "dropout": 0.15,
  "seq_len": 5,
  "batch_size": 32,
  "learning_rate": 0.0002,
  "weight_decay": 0.0001,
  "num_epochs": 50,
  "patience": 15,
  "rem_weight": 2.5,
  "n1_weight": 4.0
}
2025-08-12 22:58:17,059 - INFO - Device: cuda
2025-08-12 22:58:17,059 - INFO - Loading datasets...
2025-08-12 22:58:18,074 - INFO - 从 24 个文件加载了 25362 个epochs, 创建了 25266 个序列
2025-08-12 22:58:18,074 - INFO - 创建序列数据集: 25266个序列, 序列长度=5, 通道数=3, 总epochs=25362
2025-08-12 22:58:18,372 - INFO - 从 5 个文件加载了 4951 个epochs, 创建了 4931 个序列
2025-08-12 22:58:18,372 - INFO - 创建序列数据集: 4931个序列, 序列长度=5, 通道数=3, 总epochs=4951
2025-08-12 22:58:18,848 - INFO - 从 10 个文件加载了 11995 个epochs, 创建了 11955 个序列
2025-08-12 22:58:18,849 - INFO - 创建序列数据集: 11955个序列, 序列长度=5, 通道数=3, 总epochs=11995
2025-08-12 22:58:18,849 - INFO - Dataset sizes: Train=25266, Val=4931, Test=11955
2025-08-12 22:58:18,909 - INFO - 创建SequentialMAMBAFORMER_V2: 参数量=4,997,255, d_model=256, n_heads=16, n_layers=6
2025-08-12 22:58:18,911 - INFO - 创建Seq2SeqMAMBAFORMER: 参数量=5,082,258
2025-08-12 22:58:19,093 - INFO - Model parameters: 5,082,258
2025-08-12 22:58:19,156 - WARNING - Could not load V14 weights: Error(s) in loading state_dict for Seq2SeqMAMBAFORMER:
	size mismatch for backbone.feature_extractor.conv_layers.10.weight: copying a param with shape torch.Size([192, 128, 4]) from checkpoint, the shape in current model is torch.Size([256, 128, 4]).
	size mismatch for backbone.feature_extractor.conv_layers.10.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.feature_extractor.conv_layers.11.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.feature_extractor.conv_layers.11.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.feature_extractor.conv_layers.11.running_mean: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.feature_extractor.conv_layers.11.running_var: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.pos_encoder.pe: copying a param with shape torch.Size([5000, 1, 192]) from checkpoint, the shape in current model is torch.Size([5000, 1, 256]).
	size mismatch for backbone.transformer_encoder.layers.0.self_attn.in_proj_weight: copying a param with shape torch.Size([576, 192]) from checkpoint, the shape in current model is torch.Size([768, 256]).
	size mismatch for backbone.transformer_encoder.layers.0.self_attn.in_proj_bias: copying a param with shape torch.Size([576]) from checkpoint, the shape in current model is torch.Size([768]).
	size mismatch for backbone.transformer_encoder.layers.0.self_attn.out_proj.weight: copying a param with shape torch.Size([192, 192]) from checkpoint, the shape in current model is torch.Size([256, 256]).
	size mismatch for backbone.transformer_encoder.layers.0.self_attn.out_proj.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.0.linear1.weight: copying a param with shape torch.Size([768, 192]) from checkpoint, the shape in current model is torch.Size([1024, 256]).
	size mismatch for backbone.transformer_encoder.layers.0.linear1.bias: copying a param with shape torch.Size([768]) from checkpoint, the shape in current model is torch.Size([1024]).
	size mismatch for backbone.transformer_encoder.layers.0.linear2.weight: copying a param with shape torch.Size([192, 768]) from checkpoint, the shape in current model is torch.Size([256, 1024]).
	size mismatch for backbone.transformer_encoder.layers.0.linear2.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.0.norm1.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.0.norm1.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.0.norm2.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.0.norm2.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.1.self_attn.in_proj_weight: copying a param with shape torch.Size([576, 192]) from checkpoint, the shape in current model is torch.Size([768, 256]).
	size mismatch for backbone.transformer_encoder.layers.1.self_attn.in_proj_bias: copying a param with shape torch.Size([576]) from checkpoint, the shape in current model is torch.Size([768]).
	size mismatch for backbone.transformer_encoder.layers.1.self_attn.out_proj.weight: copying a param with shape torch.Size([192, 192]) from checkpoint, the shape in current model is torch.Size([256, 256]).
	size mismatch for backbone.transformer_encoder.layers.1.self_attn.out_proj.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.1.linear1.weight: copying a param with shape torch.Size([768, 192]) from checkpoint, the shape in current model is torch.Size([1024, 256]).
	size mismatch for backbone.transformer_encoder.layers.1.linear1.bias: copying a param with shape torch.Size([768]) from checkpoint, the shape in current model is torch.Size([1024]).
	size mismatch for backbone.transformer_encoder.layers.1.linear2.weight: copying a param with shape torch.Size([192, 768]) from checkpoint, the shape in current model is torch.Size([256, 1024]).
	size mismatch for backbone.transformer_encoder.layers.1.linear2.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.1.norm1.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.1.norm1.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.1.norm2.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.1.norm2.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.2.self_attn.in_proj_weight: copying a param with shape torch.Size([576, 192]) from checkpoint, the shape in current model is torch.Size([768, 256]).
	size mismatch for backbone.transformer_encoder.layers.2.self_attn.in_proj_bias: copying a param with shape torch.Size([576]) from checkpoint, the shape in current model is torch.Size([768]).
	size mismatch for backbone.transformer_encoder.layers.2.self_attn.out_proj.weight: copying a param with shape torch.Size([192, 192]) from checkpoint, the shape in current model is torch.Size([256, 256]).
	size mismatch for backbone.transformer_encoder.layers.2.self_attn.out_proj.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.2.linear1.weight: copying a param with shape torch.Size([768, 192]) from checkpoint, the shape in current model is torch.Size([1024, 256]).
	size mismatch for backbone.transformer_encoder.layers.2.linear1.bias: copying a param with shape torch.Size([768]) from checkpoint, the shape in current model is torch.Size([1024]).
	size mismatch for backbone.transformer_encoder.layers.2.linear2.weight: copying a param with shape torch.Size([192, 768]) from checkpoint, the shape in current model is torch.Size([256, 1024]).
	size mismatch for backbone.transformer_encoder.layers.2.linear2.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.2.norm1.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.2.norm1.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.2.norm2.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.2.norm2.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.3.self_attn.in_proj_weight: copying a param with shape torch.Size([576, 192]) from checkpoint, the shape in current model is torch.Size([768, 256]).
	size mismatch for backbone.transformer_encoder.layers.3.self_attn.in_proj_bias: copying a param with shape torch.Size([576]) from checkpoint, the shape in current model is torch.Size([768]).
	size mismatch for backbone.transformer_encoder.layers.3.self_attn.out_proj.weight: copying a param with shape torch.Size([192, 192]) from checkpoint, the shape in current model is torch.Size([256, 256]).
	size mismatch for backbone.transformer_encoder.layers.3.self_attn.out_proj.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.3.linear1.weight: copying a param with shape torch.Size([768, 192]) from checkpoint, the shape in current model is torch.Size([1024, 256]).
	size mismatch for backbone.transformer_encoder.layers.3.linear1.bias: copying a param with shape torch.Size([768]) from checkpoint, the shape in current model is torch.Size([1024]).
	size mismatch for backbone.transformer_encoder.layers.3.linear2.weight: copying a param with shape torch.Size([192, 768]) from checkpoint, the shape in current model is torch.Size([256, 1024]).
	size mismatch for backbone.transformer_encoder.layers.3.linear2.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.3.norm1.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.3.norm1.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.3.norm2.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.transformer_encoder.layers.3.norm2.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.classifier.0.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.classifier.0.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.classifier.2.weight: copying a param with shape torch.Size([96, 192]) from checkpoint, the shape in current model is torch.Size([128, 256]).
	size mismatch for backbone.classifier.2.bias: copying a param with shape torch.Size([96]) from checkpoint, the shape in current model is torch.Size([128]).
	size mismatch for backbone.classifier.5.weight: copying a param with shape torch.Size([5, 96]) from checkpoint, the shape in current model is torch.Size([5, 128]).
	size mismatch for backbone.auxiliary_head.0.weight: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.auxiliary_head.0.bias: copying a param with shape torch.Size([192]) from checkpoint, the shape in current model is torch.Size([256]).
	size mismatch for backbone.auxiliary_head.2.weight: copying a param with shape torch.Size([48, 192]) from checkpoint, the shape in current model is torch.Size([64, 256]).
	size mismatch for backbone.auxiliary_head.2.bias: copying a param with shape torch.Size([48]) from checkpoint, the shape in current model is torch.Size([64]).
	size mismatch for backbone.auxiliary_head.5.weight: copying a param with shape torch.Size([2, 48]) from checkpoint, the shape in current model is torch.Size([2, 64]).
2025-08-12 22:58:20,262 - INFO - 
================================================================================
2025-08-12 22:58:20,262 - INFO - Starting Training with Sequence-to-Sequence!
2025-08-12 22:58:20,262 - INFO - ================================================================================
