2025-08-16 18:36:50,445 - INFO - ================================================================================
2025-08-16 18:36:50,445 - INFO - 🎯 Stage 4: Mamba-Transformer混合架构 + 渐进式分类策略
2025-08-16 18:36:50,445 - INFO - 核心创新：
2025-08-16 18:36:50,445 - INFO - 1. Mamba组件用于长程依赖建模
2025-08-16 18:36:50,445 - INFO - 2. 模态内特征精炼（EEG局部注意力，EOG/EMG轻量级Mamba）
2025-08-16 18:36:50,445 - INFO - 3. 渐进式分类策略（粗分类→细分类）
2025-08-16 18:36:50,446 - INFO - 4. 不确定性估计和置信度决策
2025-08-16 18:36:50,446 - INFO - 目标: ≥85% accuracy
2025-08-16 18:36:50,446 - INFO - ================================================================================
2025-08-16 18:36:50,446 - INFO - 配置: {'n_classes': 5, 'd_model': 128, 'n_heads': 8, 'n_mamba_layers': 2, 'n_transformer_layers': 2, 'dropout': 0.15, 'seq_len': 5, 'batch_size': 32, 'learning_rate': 0.001, 'num_epochs': 100, 'weight_decay': 0.0001, 'patience': 15, 'coarse_weight': 0.3, 'uncertainty_weight': 0.1}
2025-08-16 18:36:50,477 - INFO - 设备: cuda
2025-08-16 18:36:50,478 - INFO - 加载数据集...
2025-08-16 18:36:51,838 - INFO - 从 27 个文件加载了 29454 个epochs, 创建了 29346 个序列
2025-08-16 18:36:51,838 - INFO - 创建序列数据集: 29346个序列, 序列长度=5, 通道数=4, 总epochs=29454
2025-08-16 18:36:52,120 - INFO - 从 6 个文件加载了 6505 个epochs, 创建了 6481 个序列
2025-08-16 18:36:52,121 - INFO - 创建序列数据集: 6481个序列, 序列长度=5, 通道数=4, 总epochs=6505
2025-08-16 18:36:52,484 - INFO - 从 6 个文件加载了 6349 个epochs, 创建了 6325 个序列
2025-08-16 18:36:52,485 - INFO - 创建序列数据集: 6325个序列, 序列长度=5, 通道数=4, 总epochs=6349
2025-08-16 18:36:52,485 - INFO - 训练集: 29346 sequences
2025-08-16 18:36:52,485 - INFO - 验证集: 6481 sequences
2025-08-16 18:36:52,485 - INFO - 测试集: 6325 sequences
2025-08-16 18:36:52,533 - INFO - 创建Stage4MambaProgressiveModel: 参数量=1,173,230
2025-08-16 18:36:53,803 - INFO - 
============================================================
2025-08-16 18:36:53,803 - INFO - Epoch 1/100

Training:   0%|          | 0/918 [00:00<?, ?it/s]
Training:   0%|          | 0/918 [00:00<?, ?it/s]
Traceback (most recent call last):
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py", line 979, in <module>
    main()
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py", line 906, in main
    train_loss, train_acc, loss_components = train_epoch(
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py", line 650, in train_epoch
    outputs = model(data, return_uncertainty=True)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py", line 511, in forward
    eeg_refined, eog_refined, emg_refined = self.modal_refinement(
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py", line 201, in forward
    eog_refined = self.eog_refinement(eog_feat)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/media/main/ypf/miniconda3/envs/sl/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/training/stage4_mamba_progressive.py", line 112, in forward
    y = y * self.activation(x_gate)
RuntimeError: The size of tensor a (11) must match the size of tensor b (10) at non-singleton dimension 1
