{"best_test_accuracy": 0.8495011674803651, "best_test_f1": 0.7762112477818689, "final_test_accuracy": 0.8272129059647633, "final_test_f1": 0.7588624964702351, "final_test_kappa": 0.7606385512004513, "final_test_class_f1": [0.8655256723716381, 0.37388724035608306, 0.8919528703935824, 0.8727705112960761, 0.7901761879337961], "confusion_matrix": [[885, 43, 25, 4, 21], [91, 126, 28, 0, 103], [11, 116, 1779, 51, 143], [0, 0, 52, 367, 0], [80, 41, 5, 0, 740]], "config": {"n_classes": 5, "d_model": 128, "n_heads": 8, "n_mamba_layers": 2, "n_transformer_layers": 2, "dropout": 0.2, "seq_len": 5, "batch_size": 16, "learning_rate": 0.0002, "num_epochs": 15, "weight_decay": 0.0001, "patience": 3, "coarse_weight": 0.3, "uncertainty_weight": 0.1}}