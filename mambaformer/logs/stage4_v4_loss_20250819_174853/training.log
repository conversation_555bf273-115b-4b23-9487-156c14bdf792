2025-08-19 17:48:53,860 - INFO - ================================================================================
2025-08-19 17:48:53,860 - INFO - 🎯 Stage 4 V4: 损失函数优化版本
2025-08-19 17:48:53,860 - INFO - 核心创新：
2025-08-19 17:48:53,860 - INFO - 1. Mamba组件用于长程依赖建模
2025-08-19 17:48:53,860 - INFO - 2. 模态内特征精炼（EEG局部注意力，EOG/EMG轻量级Mamba）
2025-08-19 17:48:53,860 - INFO - 3. 渐进式分类策略（粗分类→细分类）
2025-08-19 17:48:53,860 - INFO - 4. 不确定性估计和置信度决策
2025-08-19 17:48:53,860 - INFO - 目标: ≥85% accuracy
2025-08-19 17:48:53,860 - INFO - ================================================================================
2025-08-19 17:48:53,860 - INFO - 配置: {'n_classes': 5, 'd_model': 128, 'n_heads': 8, 'n_mamba_layers': 2, 'n_transformer_layers': 2, 'dropout': 0.15, 'seq_len': 5, 'batch_size': 32, 'learning_rate': 0.0002, 'num_epochs': 5, 'weight_decay': 0.0001, 'patience': 10, 'coarse_weight': 0.3, 'uncertainty_weight': 0.1}
2025-08-19 17:48:53,889 - INFO - 设备: cuda
2025-08-19 17:48:53,890 - INFO - 训练集受试者: ['00', '01', '02', '03', '06', '07', '08', '09', '10', '11', '12', '13', '15', '16', '17', '18']
2025-08-19 17:48:53,890 - INFO - 验证集受试者: ['04', '14']
2025-08-19 17:48:53,890 - INFO - 测试集受试者: ['05', '19']
2025-08-19 17:48:53,890 - INFO - 训练文件数: 31
2025-08-19 17:48:53,890 - INFO - 验证文件数: 4
2025-08-19 17:48:53,890 - INFO - 测试文件数: 4
2025-08-19 17:48:53,890 - INFO - 加载数据集...
2025-08-19 17:48:56,673 - INFO - 从 31 个文件加载了 33190 个epochs, 创建了 33066 个序列
2025-08-19 17:48:56,673 - INFO - 创建序列数据集: 33066个序列, 序列长度=5, 通道数=4, 总epochs=33190
2025-08-19 17:48:57,027 - INFO - 从 4 个文件加载了 4391 个epochs, 创建了 4375 个序列
2025-08-19 17:48:57,027 - INFO - 创建序列数据集: 4375个序列, 序列长度=5, 通道数=4, 总epochs=4391
2025-08-19 17:48:57,430 - INFO - 从 4 个文件加载了 4727 个epochs, 创建了 4711 个序列
2025-08-19 17:48:57,430 - INFO - 创建序列数据集: 4711个序列, 序列长度=5, 通道数=4, 总epochs=4727
2025-08-19 17:48:57,431 - INFO - 训练集: 33066 sequences
2025-08-19 17:48:57,431 - INFO - 验证集: 4375 sequences
2025-08-19 17:48:57,431 - INFO - 测试集: 4711 sequences
2025-08-19 17:48:57,498 - INFO - 创建Stage4MambaProgressiveModel: 参数量=1,173,230
2025-08-19 17:48:59,009 - INFO - 
Epoch 1/5
2025-08-19 17:52:12,091 - INFO - 训练 - Loss: 0.5733, Acc: 82.91%
2025-08-19 17:52:19,775 - INFO - 验证 - Acc: 82.74%, Macro F1: 0.7934
2025-08-19 17:52:28,259 - INFO - 测试 - Acc: 81.43%, Macro F1: 0.7393, Kappa: 0.7442
2025-08-19 17:52:28,259 - INFO - ✅ 新的最佳结果: Epoch 1, Acc=81.43%, F1=0.7393
2025-08-19 17:52:28,274 - INFO - 
详细分类报告:
              precision    recall  f1-score   support

        Wake      0.850     0.856     0.853       978
          N1      0.379     0.302     0.336       348
          N2      0.937     0.824     0.877      2100
          N3      0.743     0.979     0.844       419
         REM      0.717     0.871     0.787       866

    accuracy                          0.814      4711
   macro avg      0.725     0.766     0.739      4711
weighted avg      0.820     0.814     0.812      4711

2025-08-19 17:52:28,277 - INFO - 
混淆矩阵:
2025-08-19 17:52:28,277 - INFO -      W   N1   N2   N3  REM
2025-08-19 17:52:28,278 - INFO - Wake [837  41  49   8  43]
2025-08-19 17:52:28,278 - INFO - N1  [ 87 105  37   0 119]
2025-08-19 17:52:28,278 - INFO - N2  [   8   93 1730  134  135]
2025-08-19 17:52:28,278 - INFO - N3  [  0   0   9 410   0]
2025-08-19 17:52:28,279 - INFO - REM [ 53  38  21   0 754]
2025-08-19 17:52:28,279 - INFO - 学习率: 0.000195
2025-08-19 17:52:28,331 - INFO - 
Epoch 2/5
2025-08-19 17:55:40,733 - INFO - 训练 - Loss: 0.4709, Acc: 86.70%
2025-08-19 17:55:48,311 - INFO - 验证 - Acc: 84.43%, Macro F1: 0.8058
2025-08-19 17:55:56,354 - INFO - 测试 - Acc: 82.57%, Macro F1: 0.7648, Kappa: 0.7576
2025-08-19 17:55:56,354 - INFO - ✅ 新的最佳结果: Epoch 2, Acc=82.57%, F1=0.7648
2025-08-19 17:55:56,371 - INFO - 
详细分类报告:
              precision    recall  f1-score   support

        Wake      0.942     0.768     0.846       978
          N1      0.394     0.460     0.424       348
          N2      0.924     0.883     0.903      2100
          N3      0.850     0.895     0.872       419
         REM      0.707     0.865     0.778       866

    accuracy                          0.826      4711
   macro avg      0.764     0.774     0.765      4711
weighted avg      0.842     0.826     0.830      4711

2025-08-19 17:55:56,374 - INFO - 
混淆矩阵:
2025-08-19 17:55:56,374 - INFO -      W   N1   N2   N3  REM
2025-08-19 17:55:56,374 - INFO - Wake [751  89  42   3  93]
2025-08-19 17:55:56,375 - INFO - N1  [ 37 160  46   0 105]
2025-08-19 17:55:56,375 - INFO - N2  [   4   65 1855   63  113]
2025-08-19 17:55:56,375 - INFO - N3  [  0   0  44 375   0]
2025-08-19 17:55:56,375 - INFO - REM [  5  92  20   0 749]
2025-08-19 17:55:56,375 - INFO - 学习率: 0.000181
2025-08-19 17:55:56,423 - INFO - 
Epoch 3/5
2025-08-19 17:59:05,793 - INFO - 训练 - Loss: 0.4416, Acc: 87.50%
2025-08-19 17:59:13,397 - INFO - 验证 - Acc: 84.91%, Macro F1: 0.8057
2025-08-19 17:59:21,598 - INFO - 测试 - Acc: 83.21%, Macro F1: 0.7634, Kappa: 0.7631
2025-08-19 17:59:21,598 - INFO - ✅ 新的最佳结果: Epoch 3, Acc=83.21%, F1=0.7634
2025-08-19 17:59:21,614 - INFO - 
详细分类报告:
              precision    recall  f1-score   support

        Wake      0.937     0.763     0.841       978
          N1      0.425     0.437     0.431       348
          N2      0.897     0.917     0.907      2100
          N3      0.880     0.790     0.833       419
         REM      0.741     0.885     0.806       866

    accuracy                          0.832      4711
   macro avg      0.776     0.758     0.763      4711
weighted avg      0.840     0.832     0.833      4711

2025-08-19 17:59:21,618 - INFO - 
混淆矩阵:
2025-08-19 17:59:21,618 - INFO -      W   N1   N2   N3  REM
2025-08-19 17:59:21,618 - INFO - Wake [746 102  58   7  65]
2025-08-19 17:59:21,618 - INFO - N1  [ 38 152  56   0 102]
2025-08-19 17:59:21,618 - INFO - N2  [   3   33 1925   38  101]
2025-08-19 17:59:21,618 - INFO - N3  [  0   0  88 331   0]
2025-08-19 17:59:21,619 - INFO - REM [  9  71  20   0 766]
2025-08-19 17:59:21,619 - INFO - 学习率: 0.000159
2025-08-19 17:59:21,619 - INFO - 
Epoch 4/5
2025-08-19 18:02:29,580 - INFO - 训练 - Loss: 0.4162, Acc: 88.51%
2025-08-19 18:02:37,018 - INFO - 验证 - Acc: 86.47%, Macro F1: 0.8233
2025-08-19 18:02:45,061 - INFO - 测试 - Acc: 85.08%, Macro F1: 0.7798, Kappa: 0.7902
2025-08-19 18:02:45,061 - INFO - ✅ 新的最佳结果: Epoch 4, Acc=85.08%, F1=0.7798
2025-08-19 18:02:45,076 - INFO - 
详细分类报告:
              precision    recall  f1-score   support

        Wake      0.895     0.864     0.879       978
          N1      0.466     0.397     0.429       348
          N2      0.922     0.908     0.915      2100
          N3      0.875     0.833     0.853       419
         REM      0.767     0.888     0.823       866

    accuracy                          0.851      4711
   macro avg      0.785     0.778     0.780      4711
weighted avg      0.850     0.851     0.849      4711

2025-08-19 18:02:45,080 - INFO - 
混淆矩阵:
2025-08-19 18:02:45,080 - INFO -      W   N1   N2   N3  REM
2025-08-19 18:02:45,081 - INFO - Wake [845  56  24   3  50]
2025-08-19 18:02:45,081 - INFO - N1  [ 69 138  52   0  89]
2025-08-19 18:02:45,081 - INFO - N2  [   9   42 1907   47   95]
2025-08-19 18:02:45,081 - INFO - N3  [  2   0  68 349   0]
2025-08-19 18:02:45,081 - INFO - REM [ 19  60  18   0 769]
2025-08-19 18:02:45,081 - INFO - 学习率: 0.000131
2025-08-19 18:02:45,129 - INFO - 
Epoch 5/5
2025-08-19 18:05:52,956 - INFO - 训练 - Loss: 0.3993, Acc: 89.33%
2025-08-19 18:06:00,284 - INFO - 验证 - Acc: 85.19%, Macro F1: 0.8090
2025-08-19 18:06:08,217 - INFO - 测试 - Acc: 85.01%, Macro F1: 0.7844, Kappa: 0.7888
2025-08-19 18:06:08,217 - INFO - 学习率: 0.000101
2025-08-19 18:06:08,217 - INFO - 
================================================================================
2025-08-19 18:06:08,217 - INFO - 最佳结果: Epoch 4
2025-08-19 18:06:08,218 - INFO - 最佳测试准确率: 85.08%
2025-08-19 18:06:08,218 - INFO - 最佳测试Macro F1: 0.7798
2025-08-19 18:06:08,218 - INFO - ================================================================================
2025-08-19 18:06:08,218 - INFO - 
================================================================================
2025-08-19 18:06:08,218 - INFO - 🎯 Evaluation Results (最终评估结果)
2025-08-19 18:06:08,218 - INFO - ================================================================================
2025-08-19 18:06:20,948 - INFO - 
🔄 Confusion Matrix:
2025-08-19 18:06:20,948 - INFO -        Wake     N1     N2     N3    REM
2025-08-19 18:06:20,948 - INFO - Wake    843     89     22      2     22
2025-08-19 18:06:20,949 - INFO - N1       43    168     54      0     83
2025-08-19 18:06:20,949 - INFO - N2        9     56   1917     33     85
2025-08-19 18:06:20,949 - INFO - N3        0      0    103    315      1
2025-08-19 18:06:20,949 - INFO - REM      30     55     19      0    762
2025-08-19 18:06:20,949 - INFO - 
📈 Per-Class Metrics:
2025-08-19 18:06:20,968 - INFO - Wake : F1=0.886, Prec=0.911, Recall=0.862, Support=978
2025-08-19 18:06:20,968 - INFO - N1   : F1=0.469, Prec=0.457, Recall=0.483, Support=348
2025-08-19 18:06:20,968 - INFO - N2   : F1=0.910, Prec=0.906, Recall=0.913, Support=2100
2025-08-19 18:06:20,968 - INFO - N3   : F1=0.819, Prec=0.900, Recall=0.752, Support=419
2025-08-19 18:06:20,968 - INFO - REM  : F1=0.838, Prec=0.800, Recall=0.880, Support=866
2025-08-19 18:06:20,973 - INFO - 
📊 Overall Metrics:
2025-08-19 18:06:20,973 - INFO - Accuracy: 0.8501 (85.01%)
2025-08-19 18:06:20,973 - INFO - Macro F1: 0.7844
2025-08-19 18:06:20,973 - INFO - Weighted F1: 0.8509
2025-08-19 18:06:20,973 - INFO - Cohen's Kappa: 0.7888
2025-08-19 18:06:20,977 - INFO - 
🎯 Coarse Classification Results:
2025-08-19 18:06:20,977 - INFO - Coarse Accuracy: 0.7211 (72.11%)
2025-08-19 18:06:20,977 - INFO - Coarse Confusion Matrix:
2025-08-19 18:06:20,977 - INFO -        Wake   NREM    REM
2025-08-19 18:06:20,978 - INFO - Wake     14    964      0
2025-08-19 18:06:20,978 - INFO - NREM      0   2849     18
2025-08-19 18:06:20,978 - INFO - REM       0    332    534
2025-08-19 18:06:20,978 - INFO - 
================================================================================
2025-08-19 18:06:20,978 - INFO - ✅ Stage 4 成功达到目标!
2025-08-19 18:06:20,978 - INFO - ================================================================================
