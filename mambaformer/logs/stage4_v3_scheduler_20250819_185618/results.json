{"best_test_accuracy": 0.8509870515814052, "best_test_f1": 0.7846137705646689, "final_test_accuracy": 0.8380386329866271, "final_test_f1": 0.7791882475377896, "final_test_kappa": 0.7735730333115063, "final_test_class_f1": [0.868995633187773, 0.4697986577181208, 0.8997342353225417, 0.8398486759142497, 0.8175640355462624], "confusion_matrix": [[796, 97, 37, 4, 44], [41, 175, 43, 0, 89], [11, 59, 1862, 37, 131], [1, 0, 84, 333, 1], [5, 66, 13, 0, 782]], "config": {"n_classes": 5, "d_model": 128, "n_heads": 8, "n_mamba_layers": 2, "n_transformer_layers": 2, "dropout": 0.2, "seq_len": 5, "batch_size": 16, "learning_rate": 0.0002, "num_epochs": 15, "weight_decay": 0.0001, "patience": 10, "coarse_weight": 0.3, "uncertainty_weight": 0.1}}