# 关键差异分析: final_test_90_fixed.py vs stage4_mamba_progressive.py

## 1. 模型架构差异

### final_test_90_fixed.py (SequentialMAMBAFORMER_V2)
- **模型规模**: d_model=512, n_heads=32, n_layers=12 (大模型)
- **架构**: 纯Transformer架构，没有Mamba组件
- **序列建模**: seq_len=7 (更长的时间窗口)
- **特征提取**: 相对简单的CNN特征提取器
- **融合策略**: 简单的特征拼接和线性投影

### stage4_mamba_progressive.py (Stage4MambaProgressiveModel) 
- **模型规模**: d_model=128, n_heads=8, n_layers=2+2 (小模型)
- **架构创新**:
  - Mamba-Transformer混合架构
  - 模态内特征精炼(EEG局部注意力, EOG/EMG轻量级Mamba)
  - EEG中心的跨模态注意力
  - 自适应门控融合
- **渐进式分类**: 粗分类(3类)→细分类(5类)
- **不确定性估计**: MC Dropout和温度缩放

## 2. 训练策略差异

### final_test_90_fixed.py
- **批次大小**: 16 (较小)
- **学习率**: 2e-4
- **训练轮数**: 15轮
- **调度器**: OneCycleLR (warmup=0.2, cosine退火)
- **正则化**: dropout=0.20, L2正则化=0.0001
- **数据增强**: 
  - 训练时20%概率添加噪声(0.005强度)
  - 测试时TTA: 3次预测平均(噪声0.003)

### stage4_mamba_progressive.py
- **批次大小**: 32 (较大)
- **学习率**: 2e-4 (相同)
- **训练轮数**: 30轮 (更多)
- **调度器**: CosineAnnealingWarmRestarts
- **正则化**: dropout=0.15 (较小)
- **无数据增强**

## 3. 损失函数差异

### final_test_90_fixed.py
- Focal Loss (alpha=[2.0, 2.5, 1.0, 1.5, 2.0], gamma=2.0)
- Label Smoothing (smoothing=0.1)
- 组合权重: 0.6*focal + 0.4*smooth

### stage4_mamba_progressive.py
- 相同的Focal + Label Smoothing组合
- 额外的粗分类损失 (权重0.3)
- 不确定性正则化损失 (权重0.1)

## 4. 数据处理差异

### final_test_90_fixed.py
- 固定的训练/验证/测试分割
- max_samples_per_file=None (使用全部数据)
- 训练集: 30个文件, 验证集: 6个文件, 测试集: 4个文件

### stage4_mamba_progressive.py
- 按受试者ID分割数据
- 训练: 16个受试者, 验证: 2个受试者, 测试: 2个受试者
- 同样使用全部数据

## 5. 性能对比

### final_test_90_fixed.py
- **目标**: 90%准确率
- **预期性能**: 接近或超过90%
- **优势**: 大模型容量，充分的正则化和数据增强

### stage4_mamba_progressive.py
- **目标**: 85%准确率
- **当前性能**: 低于目标
- **问题**: 模型容量不足，缺少数据增强

## 关键改进方向

1. **模型容量**: 增加stage4的d_model (128→256或384)
2. **网络深度**: 增加Mamba和Transformer层数
3. **数据增强**: 添加训练时噪声增强和测试时TTA
4. **批次大小**: 减小到16以提高泛化
5. **正则化**: 增加dropout (0.15→0.20)
6. **训练轮数**: 可以保持30轮或适当减少
7. **损失权重**: 调整渐进式损失的权重平衡