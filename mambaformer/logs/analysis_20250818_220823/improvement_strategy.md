# 渐进式改进策略

## 优先级排序 (按预期影响大小)

### 第一轮并行测试 (3个变体，各训练5轮快速筛选)

1. **V1: 增强模型容量** (预期影响: ★★★★★)
   - d_model: 128 → 256
   - n_mamba_layers: 2 → 3
   - n_transformer_layers: 2 → 3
   - 保持其他不变

2. **V2: 添加数据增强和TTA** (预期影响: ★★★★☆)
   - 保持原始模型架构
   - 训练时: 20%概率添加噪声(强度0.01)
   - 测试时: 3次TTA平均(噪声0.005)
   - dropout: 0.15 → 0.20

3. **V3: 优化训练策略** (预期影响: ★★★☆☆)
   - batch_size: 32 → 16
   - 添加L2正则化 (weight=0.0001)
   - 使用OneCycleLR调度器替代CosineAnnealingWarmRestarts
   - 添加梯度累积(accumulation_steps=2)

### 第二轮深度优化 (基于第一轮最佳结果)

4. **组合最佳特性**
   - 合并第一轮中表现最好的2-3个改进
   - 微调超参数
   - 训练15-20轮

5. **序列长度调整**
   - seq_len: 5 → 7 (如果模型容量足够)
   - 相应调整位置编码

6. **损失函数优化**
   - 调整渐进式损失权重
   - coarse_weight: 0.3 → 0.2
   - uncertainty_weight: 0.1 → 0.05

### 第三轮精细调优

7. **模型架构微调**
   - 如果仍未达标，进一步增加模型规模
   - d_model → 384或512
   - 考虑移除某些创新组件如果它们影响性能

8. **训练技巧**
   - 混合精度训练
   - 更激进的数据增强
   - 集成学习(多模型投票)

## 实施计划

### Phase 1: 快速筛选 (预计2小时)
- 并行运行V1, V2, V3
- 每个变体训练5轮
- 记录验证集准确率和损失曲线
- 选择表现最好的1-2个方向

### Phase 2: 深度优化 (预计3小时)
- 基于Phase 1结果组合特性
- 训练15-20轮
- 详细记录各类别F1分数
- 保存最佳模型

### Phase 3: 最终冲刺 (预计2小时)
- 如未达到目标，采用更激进策略
- 考虑牺牲部分创新性换取性能
- 确保至少达到85%准确率

## 成功标准

- **最低目标**: 85% 测试准确率
- **理想目标**: 88% 测试准确率
- **保持创新**: 尽可能保留Mamba混合架构和渐进式分类

## 版本命名规则

- stage4_v1_capacity.py (容量增强版)
- stage4_v2_augmentation.py (数据增强版)
- stage4_v3_training.py (训练策略版)
- stage4_v4_combined.py (组合优化版)
- stage4_v5_final.py (最终版本)