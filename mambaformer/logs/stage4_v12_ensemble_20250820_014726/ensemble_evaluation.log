2025-08-20 01:47:26,413 - INFO - Starting V12 Ensemble experiment: stage4_v12_ensemble_20250820_014726
2025-08-20 01:47:26,413 - INFO - ================================================================================
2025-08-20 01:47:26,414 - INFO - Strategy: Ensemble multiple best models for improved accuracy
2025-08-20 01:47:26,588 - INFO - Using device: cuda
2025-08-20 01:47:26,590 - INFO - Found 5 models for ensemble:
2025-08-20 01:47:26,590 - INFO - Loading checkpoint: /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v6_frontend_20250819_225757/best_model_acc_0.8559.pth
2025-08-20 01:47:26,885 - INFO -   Model test accuracy: 0.8559
2025-08-20 01:47:26,886 - INFO -   Detected architecture: d_model=128, n_layers=2
2025-08-20 01:47:26,894 - INFO - Starting V8 Final Architecture experiment: stage4_v8_final_architecture_20250820_014726
2025-08-20 01:47:26,895 - INFO - ================================================================================
2025-08-20 01:47:26,895 - INFO - Strategy: Use complete final_test_90_fixed architecture with progressive classification
2025-08-20 01:47:26,924 - INFO - Created ProgressiveSequentialMAMBAFORMER_V8:
2025-08-20 01:47:26,925 - INFO -   - Complete architecture from final_test_90_fixed
2025-08-20 01:47:26,925 - INFO -   - Progressive classification as innovation
2025-08-20 01:47:26,925 - INFO -   - d_model=128, n_heads=32, n_layers=2
2025-08-20 01:47:26,925 - INFO -   - Parameters: 760,944
2025-08-20 01:47:26,931 - WARNING - Failed to load model /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v6_frontend_20250819_225757/best_model_acc_0.8559.pth: Error(s) in loading state_dict for ProgressiveSequentialMAMBAFORMER_V8:
	size mismatch for coarse_classifier.0.weight: copying a param with shape torch.Size([64, 128]) from checkpoint, the shape in current model is torch.Size([128]).
	size mismatch for coarse_classifier.0.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([128]).
	size mismatch for fine_classifier.0.weight: copying a param with shape torch.Size([64, 131]) from checkpoint, the shape in current model is torch.Size([131]).
	size mismatch for fine_classifier.0.bias: copying a param with shape torch.Size([64]) from checkpoint, the shape in current model is torch.Size([131]).
2025-08-20 01:47:26,931 - INFO - Loading checkpoint: /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v9_improved_progressive_20250820_001529/best_model_acc_0.8512.pth
2025-08-20 01:47:27,328 - INFO -   Model test accuracy: 0.8512
2025-08-20 01:47:27,329 - INFO -   Detected architecture: d_model=512, n_layers=12
2025-08-20 01:47:27,593 - INFO - Created ProgressiveSequentialMAMBAFORMER_V8:
2025-08-20 01:47:27,593 - INFO -   - Complete architecture from final_test_90_fixed
2025-08-20 01:47:27,593 - INFO -   - Progressive classification as innovation
2025-08-20 01:47:27,593 - INFO -   - d_model=512, n_heads=32, n_layers=12
2025-08-20 01:47:27,593 - INFO -   - Parameters: 38,899,664
2025-08-20 01:47:27,657 - WARNING - Failed to load model /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v9_improved_progressive_20250820_001529/best_model_acc_0.8512.pth: Error(s) in loading state_dict for ProgressiveSequentialMAMBAFORMER_V8:
	size mismatch for coarse_classifier.5.weight: copying a param with shape torch.Size([4, 256]) from checkpoint, the shape in current model is torch.Size([3, 256]).
	size mismatch for coarse_classifier.5.bias: copying a param with shape torch.Size([4]) from checkpoint, the shape in current model is torch.Size([3]).
	size mismatch for fine_classifier.0.weight: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([515]).
	size mismatch for fine_classifier.0.bias: copying a param with shape torch.Size([512]) from checkpoint, the shape in current model is torch.Size([515]).
	size mismatch for fine_classifier.2.weight: copying a param with shape torch.Size([256, 512]) from checkpoint, the shape in current model is torch.Size([256, 515]).
	size mismatch for auxiliary_head.2.weight: copying a param with shape torch.Size([2, 512]) from checkpoint, the shape in current model is torch.Size([128, 512]).
	size mismatch for auxiliary_head.2.bias: copying a param with shape torch.Size([2]) from checkpoint, the shape in current model is torch.Size([128]).
2025-08-20 01:47:27,657 - INFO - Loading checkpoint: /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v7_aggressive_20250819_231643/best_model_acc_0.8609.pth
2025-08-20 01:47:27,931 - INFO -   Model test accuracy: 0.8609
2025-08-20 01:47:27,932 - INFO -   Detected architecture: d_model=512, n_layers=8
2025-08-20 01:47:28,126 - INFO - Created ProgressiveSequentialMAMBAFORMER_V8:
2025-08-20 01:47:28,126 - INFO -   - Complete architecture from final_test_90_fixed
2025-08-20 01:47:28,126 - INFO -   - Progressive classification as innovation
2025-08-20 01:47:28,126 - INFO -   - d_model=512, n_heads=32, n_layers=8
2025-08-20 01:47:28,126 - INFO -   - Parameters: 26,290,128
2025-08-20 01:47:28,175 - INFO - Loading checkpoint: /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v8_final_architecture_20250819_232253/best_model_acc_0.8595.pth
2025-08-20 01:47:28,520 - INFO -   Model test accuracy: 0.8595
2025-08-20 01:47:28,522 - INFO -   Detected architecture: d_model=512, n_layers=12
2025-08-20 01:47:28,795 - INFO - Created ProgressiveSequentialMAMBAFORMER_V8:
2025-08-20 01:47:28,796 - INFO -   - Complete architecture from final_test_90_fixed
2025-08-20 01:47:28,796 - INFO -   - Progressive classification as innovation
2025-08-20 01:47:28,796 - INFO -   - d_model=512, n_heads=32, n_layers=12
2025-08-20 01:47:28,796 - INFO -   - Parameters: 38,899,664
2025-08-20 01:47:28,859 - INFO - Loading checkpoint: /media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs/stage4_v10_optimized_20250820_005622/best_model_acc_0.8548.pth
2025-08-20 01:47:29,179 - INFO -   Model test accuracy: 0.8548
2025-08-20 01:47:29,181 - INFO -   Detected architecture: d_model=512, n_layers=12
2025-08-20 01:47:29,188 - INFO - Starting V10 Optimized experiment: stage4_v10_optimized_20250820_014729
2025-08-20 01:47:29,188 - INFO - ================================================================================
2025-08-20 01:47:29,188 - INFO - Strategy: Use final_test_90_fixed architecture without progressive classification
2025-08-20 01:47:29,432 - INFO - Created OptimizedSequentialMAMBAFORMER_V10:
2025-08-20 01:47:29,432 - INFO -   - Complete architecture from final_test_90_fixed
2025-08-20 01:47:29,432 - INFO -   - Direct classification without progressive mechanism
2025-08-20 01:47:29,432 - INFO -   - d_model=512, n_heads=32, n_layers=12
2025-08-20 01:47:29,433 - INFO -   - Parameters: 38,698,821
2025-08-20 01:47:29,491 - INFO - Successfully loaded 3 models
2025-08-20 01:47:29,492 - INFO - Model weights based on accuracy: [0.33432417 0.33374618 0.33192965]
2025-08-20 01:47:29,494 - INFO - Loading test dataset from 4 files...
2025-08-20 01:47:29,818 - INFO - 从 4 个文件加载了 4727 个epochs, 创建了 4703 个序列
2025-08-20 01:47:29,819 - INFO - 创建序列数据集: 4703个序列, 序列长度=7, 通道数=3, 总epochs=4727
2025-08-20 01:47:29,819 - INFO - Test dataset size: 4703
2025-08-20 01:47:29,819 - INFO - 
================================================================================
2025-08-20 01:47:29,820 - INFO - Evaluating ensemble strategies...
2025-08-20 01:47:29,820 - INFO - ================================================================================
2025-08-20 01:47:29,820 - INFO - 
1. Simple Average Ensemble:
