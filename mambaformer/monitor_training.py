#!/usr/bin/env python3
"""
监控所有Stage的训练进度
"""
import os
import glob
import time
from datetime import datetime

def get_latest_log(pattern):
    """获取最新的日志文件"""
    dirs = glob.glob(pattern)
    if not dirs:
        return None
    latest = max(dirs, key=os.path.getmtime)
    log_file = os.path.join(latest, "training.log")
    return log_file if os.path.exists(log_file) else None

def parse_log_line(line):
    """解析日志行获取准确率信息"""
    info = {}
    if "测试 - Acc:" in line:
        # 提取测试准确率
        acc_str = line.split("Acc: ")[1].split("%")[0]
        info['test_acc'] = float(acc_str)
    if "Epoch" in line and "/" in line:
        # 提取epoch信息
        epoch_part = line.split("Epoch ")[1].split("/")
        info['current_epoch'] = int(epoch_part[0])
        info['total_epochs'] = int(epoch_part[1])
    if "Alpha值:" in line:
        # 提取alpha值
        alpha_str = line.split("Alpha值: ")[1].split()[0]
        info['alpha'] = float(alpha_str)
    return info

def monitor_stage(stage_name, log_pattern, target_acc):
    """监控单个Stage的训练"""
    log_file = get_latest_log(log_pattern)
    if not log_file:
        return f"Stage {stage_name}: 未找到日志文件"
    
    # 读取最后几行日志
    try:
        with open(log_file, 'r') as f:
            lines = f.readlines()
            
        # 查找最新的测试准确率
        latest_test_acc = None
        latest_epoch = None
        total_epochs = None
        latest_alpha = None
        
        for line in reversed(lines[-100:]):  # 只看最后100行
            info = parse_log_line(line)
            if 'test_acc' in info and latest_test_acc is None:
                latest_test_acc = info['test_acc']
            if 'current_epoch' in info and latest_epoch is None:
                latest_epoch = info['current_epoch']
                total_epochs = info['total_epochs']
            if 'alpha' in info and latest_alpha is None:
                latest_alpha = info['alpha']
        
        # 构建状态信息
        status_parts = [f"Stage {stage_name}:"]
        if latest_epoch:
            status_parts.append(f"Epoch {latest_epoch}/{total_epochs}")
        if latest_test_acc:
            status_parts.append(f"Test Acc: {latest_test_acc:.2f}%")
            if latest_test_acc >= target_acc:
                status_parts.append("✅ 达到目标!")
            else:
                gap = target_acc - latest_test_acc
                status_parts.append(f"(距离目标: {gap:.2f}%)")
        if latest_alpha:
            status_parts.append(f"Alpha: {latest_alpha:.4f}")
        
        return " | ".join(status_parts)
    except Exception as e:
        return f"Stage {stage_name}: 读取日志失败 - {e}"

def main():
    """主监控循环"""
    log_dir = "/media/main/ypf/eeg/Cross-Modal-Transformer/mambaformer/logs"
    
    print("="*80)
    print("Progressive Fusion Training Monitor")
    print("="*80)
    print("按 Ctrl+C 退出监控\n")
    
    stages = [
        ("1 (Modal Separation)", f"{log_dir}/stage1_modal_separation_*", 88),
        ("2 (Modal Refinement)", f"{log_dir}/stage2_*", 86),
        ("3 (Cross-Modal)", f"{log_dir}/stage3_crossmodal_*", 85),
        ("4 (Global Temporal)", f"{log_dir}/stage4_*", 85),
        ("5 (Progressive Classification)", f"{log_dir}/stage5_*", 85),
    ]
    
    try:
        while True:
            # 清屏
            os.system('clear' if os.name == 'posix' else 'cls')
            
            print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("-"*80)
            
            for stage_name, pattern, target in stages:
                status = monitor_stage(stage_name, pattern, target)
                print(status)
            
            print("-"*80)
            print("刷新间隔: 30秒")
            
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n监控已停止")

if __name__ == "__main__":
    main()