# 渐进式融合方案：将原始创新点融入高性能代码

## 目标与约束
- **性能目标**: 准确率 ≥ 85%，不显著低于当前86.89%
- **创新目标**: 逐步融入原始方案的架构创新点，增强论文价值
- **实施策略**: 每个阶段独立验证，像消融实验一样逐步推进

## 当前基准性能
- **模型**: sequential_mambaformer_v2.py
- **配置**: final_test_90_fixed.py
- **性能**: 86.89% accuracy
- **关键参数**: d_model=512, n_heads=32, n_layers=12, seq_len=7

## 阶段性融合计划

### 第1阶段：多模态特征精炼 [预计影响: +0.5%]
**目标**: 实现模态内特征精炼，让每个模态先"审视自身"

**实施内容**:
1. 分离三个通道（EEG, EOG, EMG）的处理
2. EEG使用局部注意力机制（窗口大小=100采样点）
3. EOG/EMG使用轻量级CNN（保持原有结构）
4. 添加模态特征归一化

**验证指标**:
- 各模态特征的区分度（t-SNE可视化）
- 训练收敛速度
- N1类别召回率（关键指标）

**代码修改**:
- 创建 `MultiModalFeatureExtractor` 类
- 替换原有的 `EnhancedEpochFeatureExtractor`

---

### 第2阶段：EEG中心的跨模态注意力 [预计影响: +1.0%]
**目标**: 实现以EEG为主导的跨模态信息交互

**实施内容**:
1. 实现两个并行的跨模态注意力模块
   - EEG→EOG查询
   - EEG→EMG查询
2. 使用缩放点积注意力
3. 保持计算效率（使用d_model/4的隐藏维度）

**验证指标**:
- 注意力权重分布分析
- REM期EOG注意力权重应显著提高
- 整体准确率提升

**代码修改**:
- 添加 `CrossModalAttention` 模块
- 在特征提取后、Transformer之前插入

---

### 第3阶段：自适应门控融合 [预计影响: +0.5%]
**目标**: 动态调节不同模态的贡献权重

**实施内容**:
1. 实现门控网络（FC + Softmax）
2. 为每个时间步计算三个权重：g_eeg, g_eog, g_emg
3. 加权融合：fused = g_eeg * eeg + g_eog * eog_attn + g_emg * emg_attn

**验证指标**:
- 门控权重的时序变化
- 不同睡眠阶段的权重分布差异
- 类别特定的改进（特别是N1和REM）

**代码修改**:
- 添加 `AdaptiveGatingFusion` 模块
- 替换简单的特征拼接

---

### 第4阶段：Mamba长程依赖建模 [预计影响: +1.5%]
**目标**: 引入Mamba捕捉长程时序依赖

**实施内容**:
1. 在Transformer之后添加Mamba层
2. 使用较小的Mamba状态维度（d_state=16）
3. 实现Mamba-Transformer的残差连接

**验证指标**:
- 睡眠阶段转换的预测准确性
- 长序列（seq_len=7）的性能提升
- 计算效率（推理时间）

**代码修改**:
- 集成Mamba模块（使用mamba-ssm库）
- 修改前向传播逻辑

---

### 第5阶段：渐进式分类策略 [预计影响: +1.0%]
**目标**: 实现基于不确定性的渐进式分类

**实施内容**:
1. 第一阶段：粗分类（W vs NREM vs REM）
2. 不确定性评估（MC Dropout或熵）
3. 第二阶段：细分类（N1, N2, N3）
4. 动态路由机制

**验证指标**:
- 粗分类准确率（应>95%）
- 不确定性校准质量
- N1类别F1分数提升
- 推理时间增加<20%

**代码修改**:
- 创建 `ProgressiveClassifier` 类
- 修改损失函数支持多阶段训练

---

## 实施时间表

| 阶段 | 预计时间 | 关键风险 | 回退方案 |
|------|---------|---------|---------|
| 第1阶段 | 2小时 | 特征维度不匹配 | 保持原CNN结构，只改注意力 |
| 第2阶段 | 3小时 | 计算开销过大 | 减少注意力头数 |
| 第3阶段 | 2小时 | 门控退化 | 添加residual连接 |
| 第4阶段 | 4小时 | Mamba库兼容性 | 使用简化的状态空间模型 |
| 第5阶段 | 3小时 | 训练不稳定 | 使用预训练的粗分类器 |

## 评估标准

### 必须达到的指标
- 总体准确率 ≥ 85%
- N1召回率 ≥ 40%（当前46.2%）
- 推理时间 < 2倍基准

### 期望达到的指标
- 总体准确率 ≥ 87%
- N1 F1分数 ≥ 55%
- 所有类别F1分数 ≥ 70%

## 风险管理

### 高风险项
1. **Mamba集成复杂性**: 可能需要重写部分代码
2. **训练时间增加**: 可能需要减少训练epochs
3. **过拟合风险**: 模型复杂度增加

### 缓解措施
1. 每个阶段保存checkpoint
2. 使用git commit记录有效版本
3. 准备简化版本作为备选
4. 增加正则化（dropout, weight decay）

## 实验记录模板

```markdown
### 阶段X实验记录
- **开始时间**: 
- **配置修改**: 
- **训练日志**: 
- **性能指标**: 
  - Accuracy: 
  - N1 F1: 
  - 训练时间: 
- **决策**: [继续/回退/调整]
- **Git commit**: 
```

## 下一步行动
1. 备份当前最佳模型
2. 创建实验分支
3. 开始第1阶段实施