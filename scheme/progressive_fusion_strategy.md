# 渐进式融合策略方案

## 背景
- **目标论文**: ICASSP 2026
- **基准性能**: final_test_90_fixed (90% accuracy)  
- **通道配置**: EEG(2) + EOG(1) + EMG(1) = 4通道
- **核心要求**: 
  - 保持性能 ≥ 85%
  - 逐步融入原始方案的创新点
  - 每个Stage独立训练
  - 新增模块初始权重很小，对原版本影响最小化

## 原始方案核心创新点

### 架构创新
1. **模态内特征精炼**
   - EEG: 局部注意力(Local Attention) - 捕捉短程事件
   - EOG/EMG: 轻量级Mamba - 捕捉长程依赖

2. **EEG中心的跨模态交互**
   - 并行的Cross-Attention查询
   - EEG→EOG查询模块
   - EEG→EMG查询模块

3. **自适应门控融合**
   - 可学习的模态权重
   - 动态调整不同模态的贡献

4. **全局时序建模**
   - 主Mamba模块处理长程依赖
   - Transformer处理短程模式

### 决策创新
1. **渐进式分类策略**
   - 第一阶段: 粗分类 (W vs NREM vs REM)
   - 第二阶段: 基于不确定性的细分类

## 渐进式融合策略

### Stage 1: 建立基线并引入模态分离处理 (目标: ≥88%)
**改动最小，影响可控**

```python
# 核心改动
1. 数据: 使用全部4通道
2. 特征提取: 分离EEG(2通道)和辅助模态(EOG+EMG, 2通道)
3. 融合: 简单的加权平均，初始权重α=0.1 (90%原始+10%新特征)
```

**实现要点**:
- 保留原始CNN backbone
- 添加轻量级的模态分离模块
- 可学习权重α初始化为很小值(0.01~0.1)
- 损失函数: `Loss = (1-α)*original_loss + α*modal_loss`

**实验指标**:
- 准确率是否保持≥88%
- 各模态权重的演化
- 收敛速度

### Stage 2: 引入模态内精炼 (目标: ≥86%)
**逐步增加模态特定处理**

```python
# 核心改动
1. EEG: 添加局部注意力模块 (window_size=50, 约0.2秒)
2. EOG/EMG: 添加轻量级Mamba (d_state=16, d_conv=4)
3. 权重控制: α初始化为0.15
```

**实现要点**:
- 局部注意力只处理EEG通道
- Mamba只处理辅助通道
- 残差连接保留原始特征
- 渐进式增加α: 从0.15逐步增加到0.3

**消融实验**:
- 只添加局部注意力
- 只添加Mamba
- 两者结合

### Stage 3: 引入跨模态交互 (目标: ≥85%)
**核心创新点融入**

```python
# 核心改动  
1. 并行Cross-Attention:
   - EEG→EOG查询
   - EEG→EMG查询
2. 自适应门控融合
3. 权重控制: α初始化为0.2
```

**实现要点**:
- Cross-Attention使用scaled dot-product
- 门控网络: 2层MLP + Softmax
- 多头注意力: n_heads=4 (轻量级)
- 保留Stage 2的所有改进

**风险控制**:
- 如果性能下降>3%，减小Cross-Attention维度
- 使用dropout=0.3防止过拟合

### Stage 4: 全局时序建模优化 (目标: ≥85%)
**长短程结合**

```python
# 核心改动
1. 主Mamba模块: 处理融合后的长程依赖
2. Transformer保留: 处理短程模式
3. 混合架构: Mamba + Transformer并行
```

**实现要点**:
- Mamba处理全序列
- Transformer处理局部窗口
- 两路输出加权融合
- 权重根据任务自适应

### Stage 5: 渐进式分类策略 (目标: ≥85%)
**决策层面创新**

```python
# 核心改动
1. 粗分类器: 3类 (W vs NREM vs REM)
2. 细分类器: 基于不确定性激活
3. 不确定性估计: MC Dropout + 温度缩放
```

**实现要点**:
- 粗分类器共享特征提取器
- 细分类器针对NREM细分(N1/N2/N3)
- 不确定性阈值: 初始0.3，逐步调整
- 训练策略: 先训练粗分类，再训练细分类

## 实验监控指标

### 每个Stage需要记录
1. **性能指标**
   - 总体准确率
   - 各类别准确率(特别是N1)
   - F1分数
   - Cohen's Kappa

2. **模型行为**
   - 模态权重分布
   - 注意力权重可视化
   - 不确定性分布(Stage 5)

3. **训练动态**
   - 收敛速度
   - 过拟合程度
   - 梯度流

## 风险管理

### 性能下降应对策略
1. **轻微下降(1-2%)**
   - 调整学习率
   - 增加训练epochs
   - 微调权重初始化

2. **中度下降(2-5%)**
   - 减小新模块复杂度
   - 增加残差连接
   - 调整α权重

3. **严重下降(>5%)**
   - 回退到上一个Stage
   - 重新设计该Stage
   - 考虑跳过该创新点

## 实施计划

### Week 1: Stage 1
- [ ] 实现模态分离
- [ ] 训练并验证基线
- [ ] 调整α权重

### Week 2: Stage 2
- [ ] 实现局部注意力
- [ ] 实现轻量级Mamba
- [ ] 消融实验

### Week 3: Stage 3
- [ ] 实现Cross-Attention
- [ ] 实现门控融合
- [ ] 性能优化

### Week 4: Stage 4-5
- [ ] 混合架构实现
- [ ] 渐进式分类器
- [ ] 整体调优

## 成功标准
1. 最终准确率 ≥ 85%
2. 至少3个创新点成功融入
3. 可解释性增强(模态权重、注意力可视化)
4. 论文贡献点清晰

## 备选方案
如果某个Stage失败:
1. 尝试该Stage的简化版本
2. 跳过该Stage，继续下一个
3. 组合不同Stage的成功部分
4. 保留最好的Stage组合作为最终方案