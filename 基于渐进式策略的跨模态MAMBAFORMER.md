## **基于渐进式策略的跨模态MAMBAFORMER**

这个想法的核心是希望解决目前方法中普遍存在的两个关键挑战：

1. 如何高效地融合多模态信号，并同时捕捉睡眠中长时程的慢转换和短时程的关键事件？
2. 如何处理睡眠阶段分类难度不一以及类别不平衡的问题，特别是像N1这种模糊且样本少的阶段？

为了应对这两个挑战，计划在两个层面解决问题：一个是在模型架构层面，另一个是在决策策略层面。

#### **一、 架构上：自底而上的深度融合的跨模态MAMBAFORMER**

目前很多模型都是先从各个模态（EEG, EOG, EMG）提取特征，最后再进行简单的拼接或融合，这种“后期融合”可能会丢失模态间深层的交互信息。

核心想法是，将多模态融合的过程直接嵌入到特征提取的每一层中，实现一种贯穿始终的深度交互。基于Mamba和Transformer混合架构来实现。

长短时程兼顾：睡眠是一个既有缓慢阶段转换（长程依赖），又包含大量瞬时关键事件（如纺锤波、K复合波等短程模式）的过程。Mamba的线性复杂度和状态空间特性使其非常擅长捕捉长程依赖，而Transformer的注意力机制则精于发现短程的、显著的模式。将两者结合，对睡眠信号进行建模。


**步骤A：模态内特征精炼 **

在进行模态间交互之前，我们首先让每个模态的信号“审视自身”，提炼出各自内部最重要的时序特征。这里采用非对称的设计：

* 对于EEG：使用一个局部注意力（Local Attention）模块。EEG中的关键判据（如纺锤波、K复合波）是持续时间为零点几秒到两秒的短程事件。全局注意力在这里不仅计算开销大，而且容易被无关信息干扰。局部注意力能精准地捕捉这些微观事件模式，同时保持计算高效。
* 对于EOG和EMG：这两个辅助模态的特征相对更宏观，例如EOG的慢速/快速眼动和EMG的肌张力水平变化。我们为它们各自分配一个独立的、轻量级的Mamba模块。Mamba的线性复杂度和长程依赖捕捉能力，使其能高效地对这两个信号的完整序列进行平滑和上下文编码。

产出：经过这一步，得到了三个经过初步精炼的特征序列：`refined_EEG`, `refined_EOG`, `refined_EMG`。


**步骤B：EEG中心的跨模态信息交互**

采用并行的、多次独立的跨模态查询。
    设计了两个独立的跨模态注意力（Cross-Attention）模块，并行工作：
        1.  EEG→EOG查询模块：`fused_eog = CrossAttention(Q=refined_EEG, K=refined_EOG, V=refined_EOG)`。
        2.  EEG→EMG查询模块：`fused_emg = CrossAttention(Q=refined_EEG, K=refined_EMG, V=refined_EMG)`。
        
这个非对称设计模拟了临床诊断的逻辑：以EEG为主要分析对象，主动地去EOG和EMG中寻找确认或排除的证据。例如，当EEG的局部注意力模块捕捉到一个疑似REM期的模式时，它会通过跨模态注意力去查询EOG中是否有快速眼动、EMG中是否有肌张力失弛缓，从而做出更可靠的判断。

产出：一个以EEG为主导，但已经融合了EOG和EMG关键信息的特征序列 `fused_eog`和`fused_emg`


**步骤C：自适应门控融合**

在EEG分别查询完两个辅助模态后，过一个轻量级的门控网络（一个简单的全连接层+Softmax）将这三股信息流（原始的EEG特征、融合了EOG的特征、融合了EMG的特征）结合起来。

这个门控机制使得模型可以在每一层、每一个时间点，都自主学习当前最依赖哪种信息。例如，在判断REM期时，`fused_eog` 的权重可能会被显著提高。

产出：`fused_features = g_eeg * refined_EEG + g_eog * fused_eog + g_emg * fused_emg`


**步骤D：全局时序上下文建模 **

现在有了一个融合了多模-态短程信息的特征序列，接下来需要理解它在更长时间维度上的意义。

将上一步得到的 `fused_features` 序列送入一个主Mamba模块。
注意力机制已经完成了它最擅长的短程和跨模态“特征匹配”工作。现在轮到Mamba发挥其捕捉长程依赖的优势。这个Mamba模块负责将融合后的特征放在整个睡眠周期的宏观背景下进行建模，学习睡眠阶段之间缓慢的、时序性的转换规律（比如从N2到N3，再到REM的演变）。

产出：一个包含了长短时程、多模态信息的最终上下文表示 `contextual_features`。


时序上的分层：整个架构在时间维度上也是分层的。Mamba模块负责捕捉睡眠阶段之间长时程的、缓慢的转换（宏观），而注意力模块则聚焦于时期（epoch）内部的关键事件（微观）。

通过这种方式，每一层网络都在进行一次“多专家会诊”，而不是各自为战。

#### **二、 决策上：自顶而下的基于不确定性的渐进式分类策略**

传统的分类器对所有样本都“一视同仁”，用一个分类头去解决所有问题。但这不符合睡眠分期的实际情况：区分“清醒”、“非快速眼动”和“快速眼动”这三大类相对容易，但要细分N1, N2, N3则要困难得多。

模仿人类专家的诊断逻辑，先做简单的判断，对于没有把握的“疑难样本”，再投入更多精力进行精细分析。

- **第一阶段：高层语义的快速判断（粗分类）**

    - 模型首先会进行一个简单的三分类（W vs. NREM vs. REM）。这个高层级的、初步的判断可以看作是一个“自顶向下的任务假设”。例如，模型初步判断“当前很可能是NREM阶段”。
    
- **第二阶段：由任务假设引导的细粒度分析（细分类）**
    - 接下来，评估模型对这个初步判断的不确定性。使用用MC Dropout和经过温度缩放校准的概率来确保这个不确定性是可靠的。
    - 如果模型对初步判断非常自信（低不确定性），就直接采纳结果，这大大提升了效率。
    - 如果模型判断为高不确定性，那么第一阶段的“任务假设”（比如“这是NREM”）就会发出信号，去激活一个专门的细分类器。这个NREM细分类器接下来会被引导着去重点关注那些能够区分N1、N2、N3的细微特征，而不是被W或REM的特征干扰。

通过这种渐进式（Progressive）的分类策略，模型可以将更多的计算资源和注意力集中在那些真正困难的分类边界上，从而提升整体，尤其是稀有类别的分类性能。