## 统一要求（两模块通用）

**目标**
在现有框架内，新增两个可开关的机制：

1. 分层注意力（层内局部 + 跨模态 +  epoch 间）；
2. 自顶向下的阶段条件注意力（由粗分类先验引导）。
   默认关闭，打开时**不破坏现有训练脚本**。

**兼容与边界**

* 不改 dataloader；输入张量形状保持：`eeg_tok:[B,Teeg,D]`, `eog_tok:[B,Teog,D]`, `emg_tok:[B,Temg,D]`（已做 patch-embed / 下采样）。
* 缺失模态要优雅处理（零向量 + key\_padding\_mask）。
* 与 AMP/torch.compile 兼容；不引入非必要大依赖。

**配置开关（新增）**

```yaml
attn.hier.enable: false
attn.hier.win: 32           # 局部窗口大小
attn.hier.heads: 4
attn.hier.sparse: false     # 用 entmax15 代替 softmax
attn.inter_epoch.layers: 1  # epoch 间层数(0=关闭)
attn.modal_gate.beta: 0.01  # 门熵正则系数

topdown.enable: false
topdown.mode: "bias"        # "bias" 或 "query"
topdown.alpha_init: 0.5     # 先验强度，可学习
topdown.tau_high: 0.6       # 启用阈值(基于粗分类置信度)
```

**返回与日志**
`model.forward` 增加可选 `aux`：`{'attn_intra','attn_cross','gates','topdown_alpha'}` 供可视化与验收。关闭时不影响现有返回。

**验收标准（Definition of Done）**

* 形状不变、显存不爆；默认关闭时结果与 baseline 一致（数值差<1e-6）。
* 打开分层注意力：训练可稳定收敛；`g1+g2+g3≈1`，门权重在 REM/W 阶段呈合理变化。
* 打开自顶向下注意力：仅在高置信或触发样本启用；REM 片段的 EOG 注意力占比↑，W 片段的 EMG 占比↑；无显著延时增加。
* 提供 3 个单元/集成测试（见文末）。

---

## 模块 1：分层注意力（Hierarchical / Layered）

**功能概述**
在每层主干加入一个“层内局部自注意 + 跨模态注意 + 门控融合”的块；可选在 epoch 间再做 1–2 层自注意。

**接口**
新增类 `HierAttnBlock(d_model, heads, win, use_cross, use_sparse)`，在主干里以模块形式调用：

```python
eeg_out, aux = hier_block(eeg_tok, eog_tok, emg_tok)
```

**行为要求**

1. **模态内局部注意**（EEG）：仅在窗口 `win` 内做自注意，复杂度 O(T\*win)。
2. **跨模态注意**：`Q=EEG, K/V=EOG` 与 `Q=EEG, K/V=EMG` 两路；
3. **自适应模态门控**：用 2 层 MLP 产生 `g=[g1,g2,g3]`（sigmoid 后归一化），融合 `g1*EEG_self + g2*EEG_EOG + g3*EEG_EMG`；

   * 加门熵正则：`L_gate = -β Σ g_i log g_i`（β 用配置）。
4. **稀疏注意（可选）**：当 `attn.hier.sparse=true` 时，用 entmax15 替代 softmax。
5. **epoch 间层（可选）**：对每个 epoch 的输出先池化（mean+max），再过 `attn.inter_epoch.layers` 层轻量 MHSA；若为 0 则跳过。

**形状与容错**

* EOG/EMG 长度可与 EEG 不同；内部用 `adaptive_pool` 对齐或用 `key_padding_mask`。
* 模态缺失时，跨模态分支输出零向量，门控仍归一化。

**监控/日志**

* 保存平均 `attn_intra`、`attn_cross` 热图（按阶段分桶）；
* 保存门权重在 W/N1/N2/N3/REM 的均值曲线；
* 记录层级开关对应的 FLOPs/epoch（粗略统计）。

---

## 模块 2：自顶向下注意力（Top-Down / Stage-Conditioned）

**功能概述**
用**粗分类头**的分布 `p_coarse=[p_W,p_NREM,p_REM]` 引导底层注意力，仅在高置信/触发样本开启。

**两种实现（二选一或都实现）**

### 2A. Bias 模式（优先实现，轻量）

* 将阶段先验转换为 attention **偏置矩阵** `B` 并加到 `scores = QKᵀ/√d`：

  * W：提升 EMG 相关；NREM：提升 EEG；REM：提升 EOG。
  * 实现为几种“模态/时间掩码”的线性组合，权重由 `p_coarse` 计算；整体强度乘以可学习标量 `alpha`。
* 启用条件：`conf = 1 - entropy(p_coarse)`；当 `conf > tau_high` 或样本在渐进细分类的 **hard mask** 中时启用。
* 稳定性：训练时 **detach** `p_coarse`；`alpha` 在 \[0,2] 内可学习。

### 2B. Query 模式（可选）

* 为每阶段准备 learnable query，按 `p_coarse` 混合得到条件 query，对底层特征做一轮 cross-attn，残差回写。

**日志与监控**

* 记录 `alpha`、`启用比例`、不同阶段的模态注意力占比变化；
* 校准后 ECE 变化（配合现有温度缩放）。

---

## 最小测试清单（交付时必须包含）

1. **形状单测**：随机张量走 `HierAttnBlock` 和 Top-Down（bias），输出与输入形状一致；缺 EOG/EMG 不报错。
2. **行为单测**：构造“伪 REM”样本（EOG 强、EMG 弱），开启 Top-Down 后统计 EOG 注意力均值 ↑；“伪 W”则 EMG 注意力均值 ↑。
3. **集成小跑**：Sleep-EDF fold0 训练 1–2 个 epoch：

   * 开关分层注意力/Top-Down 时训练不崩；
   * 门权重非退化（非全 0/1）；
   * FLOPs 变化与配置一致（局部窗口生效）。

**交付物**

* 新增/修改文件列表与函数签名；
* 配置示例（YAML）；
* 简短 README（如何打开两模块、如何导出注意力热图）；
* 若触及训练循环，附最小 diff 和回滚说明。

