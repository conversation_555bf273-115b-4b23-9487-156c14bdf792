# 睡眠数据集完整分析报告

## 数据集文件清单和格式

### 1. NPZ格式数据（Stage 1成功使用的格式）✅
**位置**: `/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/`

**文件格式**:
- 文件名: `SC4xxxE0.npz` (例如: SC4001E0.npz)
- 包含键值: `['x', 'y', 'fs', 'ch_label', 'header_raw', 'header_annotation']`
- 数据形状:
  - `x`: (n_epochs, 3000, 4) - 4通道EEG/EOG数据
  - `y`: (n_epochs,) - 标签 (0-4对应Wake, N1, N2, N3, REM)
  - 数据类型: x为float32, y为int32

**Stage 1使用的文件列表**:
- 训练集: 29个文件
- 验证集: 6个文件  
- 测试集: 4个文件

### 2. H5格式数据（原始CMT使用的格式）
**位置**: `/media/main/ypf/eeg/Cross-Modal-Transformer-ori/datasets/real_sleep_edf/`

**文件结构**:
- EEG数据: `x1.h5` - `x5.h5` (形状: (43754, 1, 3000))
- EOG数据: `eog1.h5` - `eog5.h5` (形状: (43754, 1, 3000))
- 标签: `y1.h5` - `y5.h5` (形状: (43754,))
- 均值: `mean1.h5` - `mean5.h5`, `eog_m1.h5` - `eog_m5.h5`
- 标准差: `std1.h5` - `std5.h5`, `eog_std1.h5` - `eog_std5.h5`

### 3. NPY格式数据（MSA-CNN使用的格式）
**位置**: `/media/main/ypf/eeg/MSA-CNN/data/sleep_edf_20/`

**文件结构**:
- `sleep_edf_20_X.npy`: (42308, 4, 3000) - 通道在第二维
- `sleep_edf_20_y.npy`: (42308, 5) - one-hot编码
- `sleep_edf_20_idx_fold.npy`: 折叠索引
- `sleep_edf_20_idx_part.npy`: 部分索引
- `sleep_edf_20_idx_epoch.npy`: epoch索引

## 正确的数据加载方式

### Stage 1成功方案（推荐）✅

```python
from mambaformer.utils.sequence_dataset import SequenceSleepDataset

# 数据目录
data_dir = "/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/"

# 文件列表
train_files = [
    "SC4181E0.npz", "SC4182E0.npz", "SC4161E0.npz", "SC4162E0.npz",
    # ... 更多文件
]

# 构建完整路径
train_files = [os.path.join(data_dir, f) for f in train_files]

# 创建数据集
train_dataset = SequenceSleepDataset(
    train_files,
    seq_len=7,  # 序列长度
    max_samples_per_file=None,  # 不限制
    is_training=True,
    use_channels=3  # 使用前3个通道
)

# 创建数据加载器
train_loader = DataLoader(
    train_dataset,
    batch_size=16,
    shuffle=True,
    num_workers=4,
    pin_memory=True
)
```

### 原始CMT加载方式（需要H5文件）

```python
from datasets.sleep_edf import SleepEDF_MultiChan_Dataset

# 需要H5格式文件
train_dataset = SleepEDF_MultiChan_Dataset(
    eeg_file=train_eeg_list,  # H5文件列表
    eog_file=train_eog_list,
    label_file=train_label_list,
    device=device,
    mean_eeg_l=train_mean_eeg_list,
    sd_eeg_l=train_sd_eeg_list,
    mean_eog_l=train_mean_eog_list,
    sd_eog_l=train_sd_eog_list,
    sub_wise_norm=True
)
```

## 数据集特点

1. **NPZ格式（推荐）**:
   - 每个文件包含完整的多通道数据
   - 数据已经预处理好
   - 直接包含标签信息
   - 适合序列建模

2. **H5格式**:
   - EEG和EOG分开存储
   - 包含均值和标准差文件用于归一化
   - 适合原始CMT模型

3. **NPY格式**:
   - 所有数据在一个大文件中
   - 包含折叠索引信息
   - 适合交叉验证

## 建议

1. **继续使用NPZ格式数据**，因为Stage 1已经成功达到88%准确率
2. 数据路径: `/media/main/ypf/eeg/data-npz-4-channel/data/Sleep-EDF-20/`
3. 使用`SequenceSleepDataset`类加载数据
4. 序列长度设为7（已验证有效）
5. 批量大小16（GPU内存优化）
6. 使用前3个通道（EEG+EOG）

## 关键发现

- Stage 1成功的关键是使用了正确的NPZ数据格式
- 数据已经过预处理，包含4个通道，前3个用于训练
- 每个epoch是30秒的睡眠记录（3000个采样点，100Hz采样率）
- 标签0-4对应: Wake, N1, N2, N3, REM五个睡眠阶段